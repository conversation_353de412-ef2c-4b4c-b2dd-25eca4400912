# 3PL Warehouse Management System Overview

## System Purpose
A comprehensive warehouse management system designed for third-party logistics (3PL) providers to manage inventory, operations, and client relationships efficiently.

## Core Features
- Multi-tenant warehouse management
- Inventory tracking and management
- Order fulfillment and processing
- Client portal and reporting
- Real-time analytics and dashboards
- Integration capabilities with shipping carriers

## Tech Stack
- Backend: Django 5.2 (Python 3.12+)
- Frontend: Tailwind CSS v4
- Interactivity: Alpine.js v3 + HTMX v2
- Template Engine: Django Templates with Crispy tailwind form
- Database: PostgreSQL
- Caching: Redis
- Task Queue: Celery

## System Architecture
```mermaid
graph TD
    A[Client Portal] --> B[Django Backend]
    B --> C[PostgreSQL Database]
    B --> D[Redis Cache]
    E[Warehouse Staff UI] --> B
    F[Admin Dashboard] --> B
    B --> G[Celery Tasks]
    B --> H[External APIs]
    H --> I[Shipping Carriers]
    H --> J[Client Systems]
```


## Domain Model Overview
- Users
- Inventory Items
- Storage Locations
- Orders
- Consignees
- Consignors
- Dashboard

---

# Technical Conventions and Best Practices

## Django Application Structure
```
project_root/
├── .envs/
├── config/
│   ├── settings/
│   │   ├── __init__.py
│   │   ├── base.py
│   │   ├── local.py
│   │   └── production.py
│   ├── asgi.py
│   └── wsgi.py
├── app_root/
│   ├── apps/ # model apps
│   │   ├── users/
│   │   ├── dashboards/
│   │   ├── inventories/
│   │   └── apps1/
│   ├── cores/  # Core functionality and shared utilities
│   ├── static/
│   └── templates/
├── requirements/
│   ├── local.txt
│   └── production.txt
└── tests/
```
---

## Database Design Principles
1. Use UUID for primary keys
2. Implement soft deletes for all major entities (SoftDeleteModel)
3. Include audit fields (AbstractBaseModel)
4. Use appropriate indexes for frequently queried fields
5. Use `select_related` and `prefetch_related` to optimize database queries.
6. Avoid raw SQL queries unless absolutely necessary.

## API Design Guidelines
1. RESTful endpoints for all major resources
2. Versioned API paths (/api/v1/)
3. JWT authentication for client portal
4. Rate limiting for external access

## Security Considerations
1. Role-based access control (RBAC)
2. Audit logging for sensitive operations
3. Input validation and sanitization

## Performance Optimization
1. Database query optimization
2. Caching strategy
3. Background task processing
4. Pagination for large datasets

---

# Development Workflow and Guidelines

## Code Organization
1. Models
   - Use abstract base classes for common fields
   - Implement model managers for complex queries
   - Define clear relationships between entities
   - Use plural for the model's `verbose_name_plural` to match Django's conventions.
   - Keep model names singular and descriptive (e.g., `Post`, `UserProfile`).

2. Views
   - Use class-based views
   - Implement viewsets for REST APIs
   - Separate business logic into services
   - Separate table and filter into tables.py and filters.py
   - Use `forms.Form` or `forms.ModelForm` for handling form validations and user input
   - Keep views modular by organizing them into separate files (e.g., `views.py`, `views_auth.py`, `views_api.py`).
   - Group related URLs under app-specific namespaces (e.g., `apps:post_list`).

3. Services
    - Create service classes for business logic
    - Implement transaction management
    - Handle external integrations


## Testing Strategy

1. Unit tests for:
   - Models
   - Services
   - API endpoints
   - Utility functions

2. Integration tests for:
   - Workflow processes
   - External integrations


## Alpine.js Best Practices

1. Use Alpine for Declarative Behavior
   - Leverage Alpine.js for adding interactivity with minimal JavaScript code.
   - Use Alpine.js to handle client-side behavior (e.g., toggling visibility, handling forms) rather than writing custom JavaScript.
2. Keep Alpine Data Attributes Separate
   - Use `x-data` for state initialization and avoid embedding large blocks of logic inside HTML attributes. Keep code modular and clear.
3. Avoid Complex Logic Inside Templates**
   - Try not to overcomplicate your templates too many nested `x-if` or `x-show` directives. Keep logic in separate JavaScript files for clarity.

## Code Formatting & Structure

1. Indentation
   - Use **4 spaces** for indentation (no tabs).
2. Line Length
   - Keep lines to a maximum of **100 characters** for readability.
3. Use of Comments
   - Add comments to complex logic but avoid over-commenting obvious code.
   - Use **docstrings** for any functions or classes in Python files.
4. Keep JavaScript Minimal
   - Avoid large, complex JavaScript code blocks. Use Alpine.js for simpler client-side interactions.

## Git Workflow

1. Branch naming:
   - feature/[ticket-number]-description
   - bugfix/[ticket-number]-description
   - hotfix/[ticket-number]-description

2. Commit messages:
   - feat: Add new feature
   - fix: Bug fix
   - docs: Documentation changes
   - refactor: Code refactoring
   - test: Adding tests
   - chore: Maintenance tasks

---
Styleguide Reference:
https://github.com/HackSoftware/Django-Styleguide

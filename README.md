# wms

Warehouse Management System

[![Built with Cookiecutter Django](https://img.shields.io/badge/built%20with-Cookiecutter%20Django-ff69b4.svg?logo=cookiecutter)](https://github.com/cookiecutter/cookiecutter-django/)
[![Ruff](https://img.shields.io/endpoint?url=https://raw.githubusercontent.com/astral-sh/ruff/main/assets/badge/v2.json)](https://github.com/astral-sh/ruff)

## Settings

Moved to [settings](https://cookiecutter-django.readthedocs.io/en/latest/1-getting-started/settings.html).

## Basic Commands

### Setting Up Your Users

- To create a **normal user account**, just go to Sign Up and fill out the form. Once you submit it, you'll see a "Verify Your E-mail Address" page. Go to your console to see a simulated email verification message. Copy the link into your browser. Now the user's email should be verified and ready to go.

- To create a **superuser account**, use this command:

      $ python manage.py createsuperuser

For convenience, you can keep your normal user logged in on Chrome and your superuser logged in on Firefox (or similar), so that you can see how the site behaves for both kinds of users.

### Type checks

Running type checks with mypy:

    $ mypy wms

### Test coverage

To run the tests, check your test coverage, and generate an HTML coverage report:

    $ coverage run -m pytest
    $ coverage html
    $ open htmlcov/index.html

#### Running tests with pytest

    $ pytest

### Live reloading and Sass CSS compilation

Moved to [Live reloading and SASS compilation](https://cookiecutter-django.readthedocs.io/en/latest/2-local-development/developing-locally.html#using-webpack-or-gulp).

### Email Server

In development, it is often nice to be able to see emails that are being sent from your application. For that reason local SMTP server [Mailpit](https://github.com/axllent/mailpit) with a web interface is available as docker container.

Container mailpit will start automatically when you will run all docker containers.
Please check [cookiecutter-django Docker documentation](https://cookiecutter-django.readthedocs.io/en/latest/2-local-development/developing-locally-docker.html) for more details how to start all containers.

With Mailpit running, to view messages that are sent by your application, open your browser and go to `http://127.0.0.1:8025`

## Deployment

The following details how to deploy this application.

### Docker

See detailed [cookiecutter-django Docker documentation](https://cookiecutter-django.readthedocs.io/en/latest/3-deployment/deployment-with-docker.html).

#### To connect PostgreSQL in Docker

PostgreSQL in Docker mapped to port 8080. You can connect via:
<!-- - Host: `wss.snapdec.local`
- Port: `8080`
- Username: `cratepilot`
- Password: `izWAe^RxJQyLWB!SFhptjr3y`
- Database: `sinoflex_wms` -->

# Updated 27/2/2025
#### To create database

```sql
CREATE ROLE superpsql WITH LOGIN PASSWORD 'password';
ALTER ROLE superpsql CREATEDB;
CREATE DATABASE wms_db;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO superpsql;
GRANT ALL PRIVILEGES ON DATABASE wms_db TO superpsql;
```

# Updated 27/2/2025
#### Enable pg_trgm in PSQL (for GinIndex)
step 1: psql -U your_user -d your_database (in my case, "psql wms_db")

step 2 (Enable pg_trgm):
```sql
CREATE EXTENSION IF NOT EXISTS pg_trgm;
```
step 3 (verify if db extended):
```sql
SELECT * FROM pg_extension WHERE extname = 'pg_trgm';
```
step 4 (migrate):

    $ python manage.py makemigrations
    $ python manage.py migrate

<!-- step 5 (for Actstream, manually create a Json column for old_obj VS new_obj)
```sql
-- ALTER TABLE actstream_action ADD COLUMN data JSONB;
ALTER TABLE actstream_action ADD COLUMN data JSONB DEFAULT '{}'::jsonb;
``` -->

#### To import database

```bash
$ gunzip -c /path/to/DB_20230116_1418.sql.gz | psql -U sinoflex -W sinoflex
# Enter Password - izWAe^RxJQyLWB!SFhptjr3y
```

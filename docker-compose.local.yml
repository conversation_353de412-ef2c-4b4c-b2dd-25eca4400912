volumes:
  wms_local_postgres_data: { }
  wms_local_postgres_data_backups: { }
  wms_local_redis_data: { }

services:
  django: &django
    build:
      context: .
      dockerfile: ./compose/local/django/Dockerfile
    image: wms_local_django
    container_name: wms_local_django
    depends_on:
      - postgres
      - redis
    volumes:
      - .:/app:z
    env_file:
      - ./.envs/.local/.django
      - ./.envs/.local/.postgres
    ports:
      - '8000:8000'
    command: /start

  postgres:
    build:
      context: .
      dockerfile: ./compose/production/postgres/Dockerfile
    image: wms_production_postgres
    container_name: wms_local_postgres
    volumes:
      - wms_local_postgres_data:/var/lib/postgresql/data
      - wms_local_postgres_data_backups:/backups
      - ./compose/production/postgres/init-extensions.sql:/docker-entrypoint-initdb.d/init-extensions.sql
    env_file:
      - ./.envs/.local/.postgres

  redis:
    image: docker.io/redis:7

    volumes:
      - wms_local_redis_data:/data

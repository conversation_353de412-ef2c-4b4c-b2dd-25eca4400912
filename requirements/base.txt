python-slugify==8.0.4  # https://github.com/un33k/python-slugify
Pillow==11.1.0  # https://github.com/python-pillow/Pillow
rcssmin==1.1.2  # https://github.com/ndparker/rcssmin
argon2-cffi==23.1.0  # https://github.com/hynek/argon2_cffi
redis==5.2.1  # https://github.com/redis/redis-py
hiredis==3.1.0  # https://github.com/redis/hiredis-py
aiohttp==3.11.14 # https://github.com/aio-libs/aiohttp
google-cloud-tasks==2.19.2 # https://github.com/googleapis/google-cloud-python

# Django
# ------------------------------------------------------------------------------
django==5.1.7  # https://www.djangoproject.com/
django-environ==0.12.0  # https://github.com/joke2k/django-environ
django-model-utils==5.0.0  # https://github.com/jazzband/django-model-utils
django-allauth[mfa]==65.8.1  # https://github.com/pennersr/django-allauth
django-crispy-forms==2.3  # https://github.com/django-crispy-forms/django-crispy-forms
crispy-tailwind==1.0.3  # https://github.com/django-crispy-forms/crispy-tailwind
django-redis==5.4.0  # https://github.com/jazzband/django-redis
# Django REST Framework
djangorestframework==3.15.2  # https://github.com/encode/django-rest-framework
django-cors-headers==4.7.0  # https://github.com/adamchainz/django-cors-headers
# DRF-spectacular for api documentation
drf-spectacular==0.28.0  # https://github.com/tfranzel/drf-spectacular

django-admin-list-filter-dropdown==1.0.3
django-admin-autocomplete-filter==0.7.1
django-phonenumber-field~=8.0.0
sorl-thumbnail~=12.11.0
django-activity-stream==2.0.0
django-ckeditor==6.7.2
django-extra-settings==0.13.0
django-filter==25.1
django-htmx==1.22.0
django-import-export==4.3.5
django-json-widget==2.0.1
django-phonenumber-field==8.0.0
django-notifications-hq==1.8.3
django-pyas2==1.2.3
django-reversion==5.1.0
django-tables2==2.7.5
django-treebeard==4.7.1
django-weasyprint==2.4.0
django-post-office==3.9.1
phonenumbers==8.13.54
qrcode==8.0
psycopg2==2.9.10
python-barcode==0.15.1
xmltodict==0.14.2
tqdm==4.67.1
openpyxl==3.1.5

# Move to base.py, product using django-extensions.db.fields
django-extensions==3.2.3  # https://github.com/django-extensions/django-extensions

# pyas2 debian OpenSSL issue, need wait realease > 1.3.0
git+https://github.com/wbond/oscrypto.git@d5f3437 # explicit checkout commit - https://github.com/wbond/oscrypto/issues/78

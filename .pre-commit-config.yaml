exclude: '^docs/|/migrations/|devcontainer.json'
default_stages: [pre-commit]
minimum_pre_commit_version: "3.2.0"

default_language_version:
  python: python3.12

repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-json
      - id: check-toml
      - id: check-xml
      - id: check-yaml
      - id: debug-statements
      - id: check-builtin-literals
      - id: check-case-conflict
      - id: check-docstring-first
      - id: detect-private-key

  - repo: https://github.com/adamchainz/django-upgrade
    rev: '1.22.2'
    hooks:
      - id: django-upgrade
        args: ['--target-version', '5.1']

  # Run the Ruff linter.
#  - repo: https://github.com/astral-sh/ruff-pre-commit
#    rev: v0.9.5
#    hooks:
#      # Linter
#      - id: ruff
#        args: [--fix, --exit-non-zero-on-fix, --ignore=ERA001,E501]
#      # Formatter
#      - id: ruff-format
# sets up .pre-commit-ci.yaml to ensure pre-commit dependencies stay up to date
ci:
  autoupdate_schedule: weekly
  skip: []
  submodules: false

#!/bin/bash

set -o errexit
set -o pipefail
set -o nounset

# Determine CPU count and set WEB_CONCURRENCY
if [ -z "$WEB_CONCURRENCY" ]; then
  export WEB_CONCURRENCY=$(( $(nproc) * 2 + 1 ))
  echo "Setting WEB_CONCURRENCY to $WEB_CONCURRENCY"
fi


# Use PORT environment variable provided by Cloud Run, default to 5000 if not set
PORT=${PORT:-5000}

exec /usr/local/bin/gunicorn config.wsgi --bind 0.0.0.0:${PORT} --workers $WEB_CONCURRENCY --chdir=/app

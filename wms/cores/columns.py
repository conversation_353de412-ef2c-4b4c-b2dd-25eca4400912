from urllib.parse import urlparse

from django_tables2 import Column
from django.urls import reverse
from django.utils.html import format_html


class HTMXColumn(Column):
    """
    A reusable column class that renders HTMX functionality using anchor tags
    """

    def __init__(
        self,
        url_name,
        target_id,
        swap_method="innerHTML",
        push_url=True,
        push_url_name=None,
        preserve_anchor=True,
        **kwargs
    ):
        self.url_name = url_name
        self.target_id = target_id
        self.swap_method = swap_method
        self.push_url = push_url
        self.push_url_name = push_url_name
        self.preserve_anchor = preserve_anchor
        super().__init__(**kwargs)

    def render(self, record, table, value, **kwargs):
        """Render the cell content as an HTMX-enabled anchor tag"""
        url = reverse(self.url_name, args=[record.pk])
        base_url = reverse(self.push_url_name, args=[record.pk]) if self.push_url_name else url

        checked = 'checked' if table.object_pk == record.pk else ''
        return format_html(
            '<input type="radio" id="{}" class="sr-only" name="htmx-radio" value="{}" {} '
            'hx-get="{}" hx-target="#{}" hx-swap="{}" hx-push-url="false" '
            '@click="let url = \'{}\' + window.location.hash; history.pushState(null, \'\', url)">'
            '<label for="{}" class="cursor-pointer">{}</label>',
            record.pk, record.pk, checked, url, self.target_id, self.swap_method,
            base_url, record.pk, value
        )

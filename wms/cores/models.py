from typing import Any

from decimal import Decimal

from django.contrib.auth.models import UserManager
from django.conf import settings
from django.contrib import admin
from django.contrib.postgres.indexes import GinIndex
from django.core.exceptions import ValidationError
from django.db import models
from django.db.models import Q
from django.utils.formats import localize
from django.utils.functional import cached_property
from django.utils.html import format_html
from django.utils.safestring import mark_safe
from django.utils import timezone
from django.utils.timezone import localtime, now
from django.utils.translation import gettext_lazy as _

from django_extensions.db.models import ActivatorModel, ActivatorModelManager
from model_utils import Choices
from model_utils.models import SoftDeletableModel, StatusModel, TimeStampedModel
from phonenumber_field.modelfields import PhoneNumberField

from wms.apps.inventories.utils import single_create_or_update_daily_stock_balances

DISPLAY_EMPTY_VALUE = "---"

DISPLAY_EXCLUDE_FIELDS = [
    "id",
    "slug",
    "created_by",
    "modified_by",
    "created",
    "modified",
]


def greater_than_zero(value):
    if value <= 0:
        raise ValidationError("The value should be greater than zero.", code="value_gt")


def not_equal_zero(value):
    if value == 0:
        raise ValidationError("The value should be greater or smaller than zero.", code="not_eq")


class SoftDeleteManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(deleted_at__isnull=True)

    def deleted(self):
        return super().get_queryset().filter(deleted_at__isnull=False)

    def active(self):
        return self.get_queryset()


class TimestampModel(models.Model):
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    deleted_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        abstract = True


class SoftDeleteModel(TimestampModel):
    objects = SoftDeleteManager()
    all_objects = models.Manager()

    class Meta:
        abstract = True

    def soft_delete(self, using: Any = None, keep_parents: bool = False) -> None:
        self.deleted_at = timezone.now()
        self.save(using=using)

    def restore(self, using: Any = None) -> None:
        self.deleted_at = None
        self.save(using=using)

    def delete(self, using: Any = None, keep_parents: bool = False) -> None:
        """
        Override delete method to perform soft deletion by default
        """
        self.soft_delete(using=using, keep_parents=keep_parents)

    def hard_delete(self, using: Any = None, keep_parents: bool = False) -> None:
        """
        Permanently delete the record from the database
        """
        super().delete(using=using, keep_parents=keep_parents)


###############################################################
# Base Model
###############################################################


class AbstractBaseModel(TimeStampedModel):
    """An abstract base class model that provides self updating `created` and `modified` fields.

    Available fields:

    * created       (TimeStampedModel)
    * modified      (TimeStampedModel)

    """

    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        verbose_name=_("Created by"),
        related_name="+",
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
    )
    modified_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        verbose_name=_("Modified by"),
        related_name="+",
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
    )

    class Meta:
        abstract = True
        get_latest_by = "created"
        ordering = ["-created"]

    @admin.display(ordering="created", description=_("Created"))
    def admin_created(self):
        """Return admin display created."""
        if self.created_by:
            return format_html(mark_safe(f"{self.created_by}<br /> {localize(localtime(self.created))}"))
        else:
            return format_html(mark_safe(f"{localize(localtime(self.created))}"))

    @admin.display(ordering="modified", description=_("Modified"))
    def admin_modified(self):
        """Return admin display modified."""
        if self.modified_by:
            return format_html(mark_safe(f"{self.modified_by}<br /> {localize(localtime(self.modified))}"))
        else:
            return DISPLAY_EMPTY_VALUE

    def display_fields(self):
        """Use to display all fields on template."""
        for field in self._meta.fields:
            if field.name not in DISPLAY_EXCLUDE_FIELDS:
                if field.remote_field is not None:
                    value_dict = dict(field.get_choices(include_blank=False))
                    yield (
                        field.verbose_name.title(),
                        value_dict.get(field.value_from_object(self), "-"),
                    )
                else:
                    yield (
                        field.verbose_name.title(),
                        field.value_from_object(self) or "-",
                    )


###############################################################
# Sortable
###############################################################


class AbstractSortableModel(models.Model):
    """An abstract model that provides orderable function based on `sort_order` fields."""

    sort_order = models.PositiveIntegerField(verbose_name=_("Ordering"), default=0)

    class Meta:
        abstract = True
        ordering = ["sort_order"]
        indexes = [
            models.Index(fields=["sort_order"]),
        ]

    def save(self, *args, **kwargs):
        model = self.__class__

        # Auto calculate sort_order
        if self.sort_order == 0:
            try:
                last = model.objects.order_by("-sort_order")[0]
                self.sort_order = last.sort_order + 1
            except IndexError:
                # This item is first row
                self.sort_order = 1

        super().save(*args, **kwargs)


###############################################################
# Activator
###############################################################


class ActivatorQuerySet(models.QuerySet):
    """Activator QuerySet."""

    def active(self):
        """Return active query set"""
        return self.filter(status=AbstractActivatableModel.ACTIVE_STATUS, activate_date__lte=now()).exclude(
            deactivate_date__lte=now()
        )

    def inactive(self):
        """Return inactive query set"""
        return self.filter(Q(status=AbstractActivatableModel.INACTIVE_STATUS) | Q(deactivate_date__lte=now()))


class ActivatorManager(ActivatorModelManager.from_queryset(ActivatorQuerySet)):
    """Manager to return instances of AbstractActivatableModel."""

    pass


class AbstractActivatableModel(ActivatorModel):
    """An abstract model that provides activate and deactivate fields.

    Available fields:

    * status            (ActivatorModel)
    * activate_date     (ActivatorModel)
    * deactivate_date   (ActivatorModel)

    """

    objects = ActivatorManager()

    class Meta:
        ordering = ["status", "-activate_date"]
        abstract = True


###############################################################
# System Number
###############################################################


class AbstractSystemNumberModel(models.Model):
    """An abstract model that provides system_number function based on `system_number` fields."""

    system_number = models.CharField(verbose_name=_("System Number"), max_length=32, blank=True)

    class Meta:
        abstract = True
        ordering = ["-system_number"]
        indexes = [
            # GinIndex for `icontains` searches
            GinIndex(name="system_number_gin", fields=["system_number"], opclasses=["gin_trgm_ops"]),
            # single index
            models.Index(fields=["system_number"]),
        ]

    def generate_system_number(self, override=False):
        """Function to generate system_number on self object."""

        if not self.system_number or override is True:
            # Generate system_number and save it
            from wms.cores.utils import generate_system_number

            self.system_number = generate_system_number(self)
            self.save(update_fields=["system_number"])

        return self.system_number


###############################################################
# Address
###############################################################


class AbstractAddressModel(models.Model):
    """An abstract model that provides general address fields."""

    address_attention = models.CharField(verbose_name=_("Attention"), max_length=256, blank=True)
    address_street_1 = models.CharField(verbose_name=_("Street 1"), max_length=128, blank=True)
    address_street_2 = models.CharField(verbose_name=_("Street 2"), max_length=128, blank=True)
    address_postal_code = models.CharField(verbose_name=_("Postal Code"), max_length=32, blank=True)
    address_district = models.CharField(verbose_name=_("District"), max_length=128, blank=True)
    address_city = models.CharField(verbose_name=_("City"), max_length=128, blank=True)
    address_state = models.CharField(verbose_name=_("State"), max_length=128, blank=True)
    address_country = models.CharField(verbose_name=_("Country"), max_length=128, blank=True)
    address_phone = PhoneNumberField(
        verbose_name=_("Phone"), blank=True, help_text=_("Phone number (e.g. +60128585299).")
    )
    is_primary = models.BooleanField(verbose_name=_("Is primary?"), default=False)

    class Meta:
        abstract = True

    @cached_property
    def get_address(self):
        data = [
            self.address_street_1.strip() or None,
            self.address_street_2.strip() or None,
            self.address_postal_code.strip() or None,
            self.address_city.strip() or None,
            self.address_state.strip() or None,
            self.address_country.strip() or None,
        ]
        address = ", ".join(filter(lambda x: x is not None, data))
        return address



class AbstractStockInModel(models.Model):
    """An abstract model that provides stock in function.

    Models that going to use extend this class would be:
    - GRN

    This model's transaction strictly control on
    only accept POSITIVE number from frontend,
    couldn't control existing data have negative value in DB.
    """

    # Transaction Info
    transaction = models.OneToOneField("inventories.Transaction", on_delete=models.PROTECT, null=True, blank=True)

    # Stock information
    deliver_to = models.ForeignKey(
        "settings.Warehouse", limit_choices_to={"is_storage": True}, on_delete=models.PROTECT
    )
    batch_no = models.CharField(verbose_name=_("Batch No."), max_length=255, default="N/A")
    expiry_date = models.DateField(verbose_name=_("Expiry Date"), blank=True, null=True)
    item = models.ForeignKey("inventories.Item", on_delete=models.RESTRICT)
    uom = models.ForeignKey("settings.UnitOfMeasure", verbose_name=_("UOM"), on_delete=models.RESTRICT)

    # Stock In Info
    stock_in_datetime = models.DateTimeField(verbose_name=_("Stock In Date Time"))
    approved_by = models.ForeignKey(settings.AUTH_USER_MODEL, related_name="+", on_delete=models.RESTRICT)
    approved_quantity = models.DecimalField(
        verbose_name=_("Approved Quantity"), max_digits=19, decimal_places=6, default=Decimal("0")
    )
    remark = models.TextField(verbose_name=_("Remark"), max_length=512, blank=True)

    class Meta:
        abstract = True

    def clean(self):
        # Dont allow 0 value on approved_quantity
        if self.approved_quantity.is_zero() is True:
            raise ValidationError({"approved_quantity": _("Quantity cannot be 0.")})

    def save(self, stock_in_channel=None, *args, **kwargs):

        from wms.apps.inventories.models import Stock, Transaction

        if self.transaction is None and self.approved_by:
            stock, _ = Stock.objects.get_or_create(
                item=self.item,
                warehouse=self.deliver_to,
                batch_no=self.batch_no,
                expiry_date=self.expiry_date,
            )

            if stock_in_channel == "adjustment":
                self.transaction = Transaction.objects.create(
                    stock=stock,
                    transaction_datetime=self.stock_in_datetime,
                    quantity=self.approved_quantity,
                    uom=self.uom,
                    is_adjustment=True,
                    system_number_ref=self.adjustment_item.adjustment.system_number,
                    created_by=self.approved_by,
                )
            elif stock_in_channel == "goods_received_note":
                self.transaction = Transaction.objects.create(
                    stock=stock,
                    transaction_datetime=self.stock_in_datetime,
                    quantity=self.converted_quantity,
                    uom=self.converted_uom,
                    is_grn=True,
                    created_by=self.approved_by,
                    system_number_ref=self.goods_received_note_item.goods_received_note.system_number,
                    customer_reference=self.goods_received_note_item.goods_received_note.customer_reference,
                )
            else:
                self.transaction = Transaction.objects.create(
                    stock=stock,
                    transaction_datetime=self.stock_in_datetime,
                    quantity=self.converted_quantity,
                    uom=self.converted_uom,
                    created_by=self.approved_by,
                )

            # handle the logic of scenarios on:
            # 1) first stock+transaction, create daily_stock_balances
            # 2) existing stock+transaction+daily_stock_balances and then backdate transaction
            # 3) if there is future transaction, DON'T update daily_stock_balances's balance.
            single_create_or_update_daily_stock_balances(stock, self.transaction)

        super().save(*args, **kwargs)


class AbstractDefectStockInModel(models.Model):
    """An abstract model that provides stock in function.

    Models that going to use extend this class would be:
    - GRN's defect flow

    This model's transaction strictly control on
    only accept POSITIVE number from frontend,
    couldn't control existing data have negative value in DB.
    """

    # Transaction Info
    transaction = models.OneToOneField("inventories.Transaction", on_delete=models.PROTECT, null=True, blank=True)

    # Stock information
    deliver_to = models.ForeignKey(
        "settings.Warehouse", limit_choices_to={"is_storage": True}, on_delete=models.PROTECT
    )
    batch_no = models.CharField(verbose_name=_("Batch No."), max_length=255, default="N/A")
    expiry_date = models.DateField(verbose_name=_("Expiry Date"), blank=True, null=True)
    item = models.ForeignKey("inventories.Item", on_delete=models.RESTRICT)
    uom = models.ForeignKey("settings.UnitOfMeasure", verbose_name=_("UOM"), on_delete=models.RESTRICT)

    # Stock In Info
    stock_in_datetime = models.DateTimeField(verbose_name=_("Stock In Date Time"))
    approved_by = models.ForeignKey(settings.AUTH_USER_MODEL, related_name="+", on_delete=models.RESTRICT)
    approved_quantity = models.DecimalField(
        verbose_name=_("Approved Quantity"), max_digits=19, decimal_places=6, default=Decimal("0")
    )
    remark = models.TextField(verbose_name=_("Remark"), max_length=512, blank=True)

    class Meta:
        abstract = True

    def clean(self):
        # Dont allow 0 value on approved_quantity
        if self.approved_quantity.is_zero() is True:
            raise ValidationError({"approved_quantity": _("Quantity cannot be 0.")})

    def save(self, stock_in_channel=None, *args, **kwargs):

        from wms.apps.inventories.models import Stock, Transaction
        from wms.apps.settings.models import Warehouse

        if self.transaction is None and self.approved_by:

            try:
                destination_all_children = self.deliver_to.get_children()
                defect_warehouse = destination_all_children.get(name__iexact="defect")

            except Warehouse.DoesNotExist:
                defect_warehouse = self.deliver_to.add_child(
                    name="Defect",
                    branch=self.deliver_to.branch,
                    is_storage=True,
                )

            stock, created = Stock.objects.get_or_create(
                item=self.item,
                warehouse=defect_warehouse,
                batch_no=self.batch_no,
                expiry_date=self.expiry_date,
            )

            self.transaction = Transaction.objects.create(
                stock=stock,
                transaction_datetime=self.stock_in_datetime,
                quantity=self.converted_quantity,
                uom=self.converted_uom,
                created_by=self.approved_by,
                is_grn_defect=True,
                system_number_ref=self.goods_received_note_item.goods_received_note.system_number,
                customer_reference=self.goods_received_note_item.goods_received_note.customer_reference,
            )

        super().save(*args, **kwargs)


class AbstractStockOutModel(models.Model):
    """An abstract model that provides stock out function."""

    # Transaction Info
    transaction = models.OneToOneField("inventories.Transaction", on_delete=models.PROTECT, null=True, blank=True)

    # Stock information
    stock = models.ForeignKey("stocks.Stock", on_delete=models.CASCADE)
    uom = models.ForeignKey("items.UnitOfMeasure", verbose_name=_("UOM"), on_delete=models.RESTRICT)

    # Stock Out Info
    stock_out_datetime = models.DateTimeField(verbose_name=_("Stock Out Date Time"))
    released_by = models.ForeignKey(settings.AUTH_USER_MODEL, related_name="+", on_delete=models.RESTRICT)
    released_quantity = models.DecimalField(
        verbose_name=_("Released Quantity"), max_digits=19, decimal_places=6, default=Decimal("0")
    )
    remark = models.TextField(verbose_name=_("Remark"), max_length=512, blank=True)
    # transaction = models.OneToOneField("stocks.Transaction", on_delete=models.PROTECT, null=True, blank=True)

    class Meta:
        abstract = True
        ordering = ["stock_out_datetime", "pk"]

    def clean(self):
        # Dont allow 0 value on released_quantity
        if self.released_quantity.is_zero() is True:
            raise ValidationError({"released_quantity": _("Quantity cannot be 0.")})

    def save(self, stock_out_channel=None, *args, **kwargs):
        from wms.apps.inventories.models import Transaction

        if self.transaction is None and self.released_by:
            if stock_out_channel == "warehouse_release_order":
                calculated_converted_release_quantity = uom_converter(
                    origin_uom=self.uom, target_uom=self.release_order_item.item.uom, quantity=self.released_quantity
                )

                self.transaction = Transaction.objects.create(
                    stock=self.stock,
                    transaction_datetime=self.stock_out_datetime,
                    quantity=-(calculated_converted_release_quantity),
                    uom=self.release_order_item.item.uom,
                    created_by=self.released_by,
                    is_wro=True,
                    system_number_ref=self.release_order_item.release_order.system_number,
                    customer_reference=self.release_order_item.release_order.customer_reference,
                )
            else:
                pass

        super().save(*args, **kwargs)

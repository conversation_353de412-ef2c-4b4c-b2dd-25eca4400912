import json
import re
from datetime import datetime
from decimal import Decimal
from typing import Any

from django.apps import apps
from django.conf import settings
from django.contrib.auth.models import AnonymousUser
from django.contrib.contenttypes.models import ContentType
from django.core.exceptions import ObjectDoesNotExist
from django.core.serializers.json import DjangoJSONEncoder
from django.db.models import Model, Q, QuerySet
from django.db.models.base import ModelBase
from django.db.models.fields.files import FieldFile
from django.db.models.signals import m2m_changed, post_save, pre_delete, pre_save
from django.utils import timezone

import phonenumbers
import reversion
from actstream import action
from actstream.models import Action

from wms.cores.utils import convert_camel_case_to_space

from wms.apps.users.models import User

# from djmoney.models.fields import Money


class ExtendedEncoder(DjangoJSONEncoder):
    """Override DjangoJSONEncoder to handle FieldFile json convertion."""

    def default(self, obj):
        if isinstance(obj, FieldFile):
            return str(obj)
        else:
            return super().default(obj)


def clean_obj_dict(obj):
    """Remove key '_state' from obj.__dict__ and convert to json and then back to dict."""
    obj_dict = {}
    options = obj._meta

    for field in options.concrete_fields:
        obj_dict[field.name] = field.value_from_object(obj)

        # Handle datetime with timezone object's conversion to string
        if isinstance(obj_dict[field.name], datetime):
            obj_dict[field.name] = str(timezone.localtime(obj_dict[field.name]))

        # Handle PhoneNumber object's conversion to string
        if isinstance(obj_dict[field.name], phonenumbers.PhoneNumber):
            obj_dict[field.name] = str(obj_dict[field.name])

        # # Handle Money object's conversion to Decimal
        # if isinstance(obj_dict[field.name], Money):
        #     obj_dict[field.name] = obj_dict[field.name].amount

        # Handle Decimal comparison between Decimal("0") and Decimal("0.000000")
        if isinstance(obj_dict[field.name], Decimal):
            obj_dict[field.name] = f"{float(obj_dict[field.name]):.6f}"

    for field in options.many_to_many:
        obj_dict[field.name] = [related_obj.pk for related_obj in field.value_from_object(obj)]

    ACTSTREAM_EXCLUDE_KEYS = getattr(
        settings, "ACTSTREAM_EXCLUDE_KEYS", {"users.User": ["_state", "_password", "backend", "last_login"]}
    )

    obj_app_object = f"{obj._meta.app_label}.{obj._meta.object_name}"
    exclude_keys = ACTSTREAM_EXCLUDE_KEYS.get(obj_app_object, [])

    for key in exclude_keys:
        if obj_dict.get(key):
            del obj_dict[key]

    return json.loads(json.dumps(obj_dict, cls=ExtendedEncoder))


def dict_diff(from_dict, to_dict, drop_same_value=True):
    """Find difference betwee `first_dict` and `second_dict`.

    :param from_dict:       The from dict
    :param to_dict:         The to dict
    :param drop_same_value: To indicate the differences dict do not include the same value
    :return:                A dict with keys and tuples value which shows `from_dict` changes to `to_dict` changes
    """
    differences = from_dict.copy()

    for k in differences:
        if k not in to_dict:
            differences[k] = (differences[k], None)

    for k in to_dict:
        if k in differences:
            differences[k] = (differences[k], to_dict[k])
        else:
            differences[k] = (None, to_dict[k])

    if drop_same_value:
        differences = {k: v for k, v in differences.items() if v[0] != v[1]}

    return differences


def get_current_user_from_request():
    """Get user from current request.

    This function relies on Python stack frame support in the interpreter,
    which isn’t guaranteed to exist in all implementations of Python.

    Known issues:
    - Performance hit (has to scan the call stack potentially for each signal), won't work via tests
    """
    current_user = None
    request = None

    import inspect

    for frame_record in inspect.stack():
        if frame_record[3] == "get_response":
            request = frame_record[0].f_locals["request"]
            break

    if request:
        current_user = request.user

    return current_user


def update_description_instance(description, instance):
    """Update the description

    if `- ::` found, replace with `- {instance} ::`
    """
    substring_to_replace = "-  :: "
    replacement_string = f"- {instance} :: "
    result = description.replace(substring_to_replace, replacement_string)

    return result


def query_actstream(
    target: Model | list[Model] | QuerySet = None,
    action_object: Model | list[Model] | QuerySet = None,
    current_user: User = None,
) -> QuerySet[Action]:
    """
    Retrieve the Action queryset for the given target or action_object.

    :param target (Model | list[Model] | QuerySet): Filter action queryset based on target
    :param action_object (Model | list[Model] | QuerySet): Filter action queryset based on action_object
    :param current_user (User): Filter action queryset based on current_user
    """
    assert target is not None or action_object is not None, "Either target or action_object must be provided."

    action_filter_kwargs = {}

    if target is not None:
        if isinstance(target, QuerySet):
            target_ct = ContentType.objects.get_for_model(target.model)
            target_pks = list(target.values_list("pk", flat=True))
        elif isinstance(target, list):
            target_ct = ContentType.objects.get_for_model(target[0])
            target_pks = [obj.pk for obj in target]
        else:
            target_ct = ContentType.objects.get_for_model(target)
            target_pks = [target.pk]

        action_filter_kwargs.update(
            target_content_type=target_ct,
            target_object_id__in=target_pks,
        )

    if action_object is not None:
        if isinstance(action_object, QuerySet):
            action_object_ct = ContentType.objects.get_for_model(action_object.model)
            action_object_pks = list(action_object.values_list("pk", flat=True))
        elif isinstance(action_object, list):
            action_object_ct = ContentType.objects.get_for_model(action_object[0])
            action_object_pks = [obj.pk for obj in action_object]
        else:
            action_object_ct = ContentType.objects.get_for_model(action_object)
            action_object_pks = [action_object.pk]

        action_filter_kwargs.update(
            action_object_content_type=action_object_ct,
            action_object_object_id__in=action_object_pks,
        )

    if current_user and not isinstance(current_user, AnonymousUser):
        action_filter_kwargs.update(actor_object_id=current_user.pk)

    action_qs = Action.objects.filter(**action_filter_kwargs)

    return action_qs


def merge_actstream_to_parent(
    instances: Model | list[Model] | QuerySet,
    parent: Model,
    action_verb: str = "created",
) -> None:
    """
    Merge (and delete) the actstream of the provided instances into the
    create actstream of their respective parent instance.

    :param instances (Model | list[Model] | QuerySet): Instances whose actstreams are to be merged
    :param parent (Model): Parent instance where all the actstreams will be consolidated into
    :param action_verb (str): Filter child actions based on the action_verb
    """
    if not instances or not parent:
        return

    parent_action_qs = query_actstream(target=parent).filter(verb__startswith="created").order_by("timestamp")
    instances_action_qs = (
        query_actstream(target=parent, action_object=instances)
        .filter(verb__startswith=action_verb)
        .order_by("timestamp")
    )

    if parent_action_qs.exists() and instances_action_qs.exists():
        descriptions = []
        for description in instances_action_qs.values_list("description", flat=True):
            if description:
                # Add a two space indentation at the start for each line in the description
                descriptions.append(re.sub(r"^", "\u00A0\u00A0", description, flags=re.MULTILINE))
        merged_description = "\n".join(descriptions)
        verb = convert_camel_case_to_space(instances_action_qs.first().verb)
        verb = verb[0].upper() + verb[1:]

        parent_action = parent_action_qs.first()
        parent_action.description += f"\n- {verb}\n{merged_description}"
        parent_action.save(update_fields=["description"])

        instances_action_qs.delete()


def create_action_event(instance, parent_model_obj=None):
    """Create action event with given instance.

    The action event represent as an activity log for SnapCore Smart System.

    Notes:
    * If the instance is registered with reversion, then we can use reversion to get current user
    """
    models_has_no_created_by = ["Setting"]
    if str(instance.__class__.__name__) in models_has_no_created_by:
        current_user = None
    else:
        current_user = instance.created_by

    changes_dict = {
        "new_obj": clean_obj_dict(instance),
        "old_obj": None,
        "differences": None,
    }

    if current_user is None and reversion.is_active():
        current_user = reversion.get_user()

    if current_user is None:
        current_user = get_current_user_from_request()

    if current_user and not isinstance(current_user, AnonymousUser):
        if parent_model_obj:
            action_verb = f"created {parent_model_obj.__class__.__name__}'s {instance.__class__.__name__}"
        else:
            action_verb = f"created {instance.__class__.__name__}"
        action.send(
            current_user,
            verb=action_verb,
            action_object=instance if parent_model_obj else None,
            target=parent_model_obj if parent_model_obj else instance,
            description=f"- {instance} :: {instance.pk}",
            changes=changes_dict,
        )


def update_action_event(instance, parent_model_obj=None, exclude_fields: list = []):
    """
    Update action event with given instance.

    The action event represent as an activity log for SnapCore Smart System.

    Notes:
    * If the instance is registered with reversion, then we can use reversion to get current user
    """
    if instance._state.adding:
        return

    models_has_no_modified_by = ["Setting"]
    if str(instance.__class__.__name__) in models_has_no_modified_by:
        current_user = None
    else:
        current_user = instance.modified_by

    changes_dict = {
        "new_obj": None,
        "old_obj": None,
        "differences": None,
    }

    if current_user is None and reversion.is_active():
        current_user = reversion.get_user()

    if current_user is None:
        current_user = get_current_user_from_request()

    # Ensure current_user is not AnonymousUser to fix the reset password issues.
    if current_user and not isinstance(current_user, AnonymousUser):
        # Ensure that an existing object has been created
        try:
            old_obj = instance.__class__.objects.get(pk=instance.pk)
        except ObjectDoesNotExist:
            return

        if parent_model_obj:
            action_verb = f"modified {parent_model_obj.__class__.__name__}'s {instance.__class__.__name__}"
        else:
            action_verb = f"modified {instance.__class__.__name__}"
        changes_dict["old_obj"] = clean_obj_dict(old_obj)
        changes_dict["new_obj"] = clean_obj_dict(instance)

        if changes_dict["old_obj"] and changes_dict["old_obj"] != changes_dict["new_obj"]:
            differences = dict_diff(changes_dict["old_obj"], changes_dict["new_obj"])
            # Remove excluded fields from differences
            changes_dict["differences"] = {k: v for k, v in differences.items() if k not in exclude_fields}

            if not changes_dict["differences"]:
                return

            # Check if ONLY modified_by or system_number is changing, otherwise create a new update action
            has_other_changes = bool(set(changes_dict["differences"]).difference(["modified_by", "system_number"]))
            if not has_other_changes:
                modified_by_diff = changes_dict["differences"].get("modified_by", None)
                system_number_diff = changes_dict["differences"].get("system_number", None)
                new_modified_by, new_system_number = None, None

                # Check if modified_by is changing from None to a value
                if modified_by_diff and modified_by_diff[0] is None:
                    new_modified_by = modified_by_diff[1]

                # Check if system_number is changing from None/Empty to a value
                if system_number_diff and not system_number_diff[0]:
                    new_system_number = system_number_diff[1]

                if new_modified_by or new_system_number:
                    if parent_model_obj:
                        latest_actions = query_actstream(
                            target=parent_model_obj,
                            action_object=instance,
                            current_user=current_user,
                        ).order_by("-timestamp")
                    else:
                        latest_actions = query_actstream(
                            target=instance,
                            current_user=current_user,
                        ).order_by("-timestamp")

                    # Check if the latest action is not an update action
                    latest_update_action = latest_actions.filter(verb=action_verb)
                    if not latest_update_action.exists():
                        if parent_model_obj:
                            create_action_verb = (
                                f"created {parent_model_obj.__class__.__name__}'s {instance.__class__.__name__}"
                            )
                        else:
                            create_action_verb = f"created {instance.__class__.__name__}"

                        # Check if the latest create action is not already updated on modified_by or system_number
                        latest_create_action = latest_actions.filter(verb=create_action_verb).exclude(
                            Q(description__contains="modified_by") & Q(description__contains="system_number")
                        )
                        if latest_create_action.exists():
                            current_action = latest_create_action.first()
                            update_description = False

                            if new_modified_by and current_action.description.find("modified_by") == -1:
                                current_action.description = update_description_instance(
                                    current_action.description, instance
                                )
                                current_action.description += "\n- Updated on modified_by"
                                update_description = True

                            if new_system_number and current_action.description.find("system_number") == -1:
                                current_action.description = update_description_instance(
                                    current_action.description, instance
                                )
                                current_action.description += "\n- Updated on system_number"
                                update_description = True

                            if update_description:
                                current_action.save(update_fields=["description"])

                            return

            action.send(
                current_user,
                verb=action_verb,
                action_object=instance if parent_model_obj else None,
                target=parent_model_obj if parent_model_obj else instance,
                description=f"- {instance} :: {instance.pk}",
                changes=changes_dict,
            )


def delete_action_event(instance, parent_model_obj=None):
    """Delete action event with given instance.

    The action event represent as an activity log for SnapCore Smart System.

    Notes:
    * If the instance is registered with reversion, then we can use reversion to get current user

    Known issues:
    * `reversion.get_user()` is not working if delete object from edit page in Django admin
    """
    current_user = None
    if reversion.is_active():
        current_user = reversion.get_user()

    if current_user is None:
        current_user = get_current_user_from_request()

    if current_user and not isinstance(current_user, AnonymousUser):
        changes_dict = {
            "new_obj": None,
            "old_obj": clean_obj_dict(instance),
            "differences": None,
        }
        if parent_model_obj:
            action_verb = f"deleted {parent_model_obj.__class__.__name__}'s {instance.__class__.__name__}"
        else:
            action_verb = f"deleted {instance.__class__.__name__}"

        action.send(
            current_user,
            verb=action_verb,
            action_object=instance if parent_model_obj else None,
            target=parent_model_obj if parent_model_obj else instance,
            description=f"- {instance} :: {instance.pk}",
            changes=changes_dict,
        )


def m2m_action_event(parent_model_obj, m2m_objects_qs, verb):
    """Add / Delete Relation action event with given m2m QuerySet.

    The action event represent as an activity log for SnapCore Smart System.

    Notes:
    * If the instance is registered with reversion, then we can use reversion to get current user
    """
    first_instance_in_m2m = m2m_objects_qs.first()
    models_has_no_created_by = ["Setting"]
    if str(first_instance_in_m2m.__class__.__name__) in models_has_no_created_by:
        current_user = None
    else:
        current_user = first_instance_in_m2m.created_by

    if current_user is None and reversion.is_active():
        current_user = reversion.get_user()

    if current_user is None:
        current_user = get_current_user_from_request()

    if current_user and not isinstance(current_user, AnonymousUser):
        prepared_action_description = []
        separator = " | "

        if m2m_objects_qs:
            for m2m_object in m2m_objects_qs:
                prepared_action_description.append(m2m_object.name)

        action_verb = f"{verb} {parent_model_obj.__class__.__name__}'s {first_instance_in_m2m.__class__.__name__}"
        action.send(
            current_user,
            verb=action_verb,
            target=parent_model_obj,
            description=f"- {separator.join(prepared_action_description)}",
        )


def _create_stream(sender, instance, created=False, **kwargs) -> None:
    """
    For audit trail create action.
    """
    logger = kwargs.get("logger", None)
    parent_field = kwargs.get("parent_field", None)

    if settings.ACTSTREAM_ENABLED and created:
        parent = getattr(instance, parent_field, None) if parent_field else None
        create_action_event(instance, parent_model_obj=parent)
        if logger:
            logger.info(f"SIGNAL: {sender.__name__}: create_stream for {instance}")


def _update_stream(sender, instance, **kwargs) -> None:
    """
    For audit trail update action.

    Notes:
    * This function will not be triggered if the instance is created
    """
    logger = kwargs.get("logger", None)
    parent_field = kwargs.get("parent_field", None)
    exclude_fields = kwargs.get("exclude_fields", [])

    if settings.ACTSTREAM_ENABLED and not instance._state.adding:
        parent = getattr(instance, parent_field, None) if parent_field else None
        update_action_event(instance, parent_model_obj=parent, exclude_fields=exclude_fields)
        if logger:
            logger.info(f"SIGNAL: {sender.__name__}: update_stream for {instance}")


def _delete_stream(sender, instance, **kwargs) -> None:
    """
    For audit trail delete action.
    """
    logger = kwargs.get("logger", None)
    parent_field = kwargs.get("parent_field", None)

    if settings.ACTSTREAM_ENABLED:
        parent = getattr(instance, parent_field, None) if parent_field else None
        delete_action_event(instance, parent_model_obj=parent)
        if logger:
            logger.info(f"SIGNAL: {sender.__name__}: delete_stream for {instance}")


def _m2m_changes_stream(sender, instance, action, reverse, model, pk_set, **kwargs) -> None:
    """
    For audit trail m2m add/remove action.

    available action in m2m_changed signal: ['post_add', 'post_remove', 'post_clear']
    - 'post_add': This signal is sent after one or more objects are added to the many-to-many relationship.
    - 'post_remove': This signal is sent after one or more objects are removed from the many-to-many relationship.
    - 'post_clear': This signal is sent after all objects are removed from the many-to-many relationship.

    pk_set consist of the current add/remove action's all affected m2m's objects.
    Scenario:
        1) In 1 form update, user might add multiple objects in m2m form field
        2) In 1 form update, user might remove multiple objects in m2m form field
    """
    logger = kwargs.get("logger", None)

    if settings.ACTSTREAM_ENABLED:
        # m2m's model query
        affected_m2m_qs = model.objects.filter(pk__in=pk_set)

        if action == "post_add":
            m2m_action_event(instance, affected_m2m_qs, "added")
            if logger:
                logger.info(f"SIGNAL: {sender.__name__}: m2m_action_event for {affected_m2m_qs}")

        elif action == "post_remove":
            m2m_action_event(instance, affected_m2m_qs, "deleted")
            if logger:
                logger.info(f"SIGNAL: {sender.__name__}: m2m_action_event for {affected_m2m_qs}")


def register_stream(
    model: ModelBase | str,
    create: bool = True,
    update: bool = True,
    delete: bool = True,
    m2m: bool = False,
    logger: Any = None,
    parent_field: str = None,
    exclude_fields: list = [],
):
    """
    Register actstream for a given model.

    Notes:
    * Function must be called before the other signals are connected for actstream to work properly.
    """

    def wrapper(handler):
        def custom_handler(sender, instance, **kwargs):
            handler(
                sender,
                instance,
                logger=logger,
                parent_field=parent_field,
                exclude_fields=exclude_fields,
                **kwargs,
            )

        return custom_handler

    if isinstance(model, str):
        model = apps.get_model(model)

    if create:
        post_save.connect(
            wrapper(_create_stream), sender=model, dispatch_uid=f"{model.__name__}_create_stream", weak=False
        )
    if update:
        pre_save.connect(
            wrapper(_update_stream), sender=model, dispatch_uid=f"{model.__name__}_update_stream", weak=False
        )
    if delete:
        pre_delete.connect(
            wrapper(_delete_stream), sender=model, dispatch_uid=f"{model.__name__}_delete_stream", weak=False
        )
    if m2m:
        m2m_changed.connect(
            wrapper(_m2m_changes_stream), sender=model, dispatch_uid=f"{model.__name__}_m2m_changes_stream", weak=False
        )

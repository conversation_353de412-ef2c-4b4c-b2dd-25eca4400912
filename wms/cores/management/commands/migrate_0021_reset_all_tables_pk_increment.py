import psycopg2
from django.core.management.base import BaseCommand

DB_WMS_CONFIG = {
    "dbname": "wms_db",
    "user": "superpsql",
    "password": "password",
    "host": "localhost",
    "port": "5432",
}


class Command(BaseCommand):
    """Reset primary key sequences for all tables with a serial primary key."""

    help = "Reset primary key sequences for all tables in the database"

    def handle(self, *args, **options):
        """Fetch all tables with serial primary keys and reset their sequences."""
        self.stdout.write("Fetching tables with serial primary keys...")

        with psycopg2.connect(**DB_WMS_CONFIG) as conn:
            with conn.cursor() as cursor:
                # Query to fetch all tables with serial primary keys
                cursor.execute("""
                    SELECT tablename
                    FROM pg_tables
                    WHERE schemaname = 'public';
                """)

                tables = [row[0] for row in cursor.fetchall()]

                if not tables:
                    self.stdout.write(self.style.WARNING("No tables found!"))
                    return

                self.stdout.write(f"Found {len(tables)} tables. Resetting sequences...")

                for table in tables:
                    self.stdout.write(f"Resetting sequence for {table}...")

                    # Check if the table has an 'id' column before trying to reset it
                    cursor.execute(f"""
                        SELECT column_name
                        FROM information_schema.columns
                        WHERE table_name = '{table}' AND column_name = 'id';
                    """)
                    has_id_column = cursor.fetchone()

                    if has_id_column:
                        cursor.execute(f"""
                            SELECT setval(
                                pg_get_serial_sequence('{table}', 'id'),
                                COALESCE((SELECT MAX(id) FROM {table}), 1) + 1,
                                false
                            );
                        """)
                    else:
                        self.stdout.write(self.style.WARNING(f"Skipping {table}: No 'id' column found."))

                conn.commit()

        self.stdout.write(self.style.SUCCESS("All table sequences have been reset!"))

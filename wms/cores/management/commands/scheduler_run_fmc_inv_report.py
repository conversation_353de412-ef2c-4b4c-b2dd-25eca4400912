import logging

from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.management.base import BaseCommand
from django.db.models import signals

from pyas2.models import Message, Organization, Partner
from pyas2lib import Message as AS2Message
from xmltodict import unparse

from wms.cores.actstream import register_stream
# from wms.cores.signals import generate_numbering
from wms.cores.utils import localtime_now

from wms.apps.inventories.models import Item, Stock
from wms.apps.settings.models import Warehouse

# from datetime import datetime


logger = logging.getLogger(__name__)


UserModel = get_user_model()


class Command(BaseCommand):
    """Run the send inventory report XML to Fresenius command.

    Daily at 6:50AM for ./manage.py schefule_run_fmc_inv_report MY03
    Daily at 6:55AM for ./manage.py schefule_run_fmc_inv_report MY04
    Daily at 7:00AM for ./manage.py schefule_run_fmc_inv_report MY05

    This command is to prepare a XML report file to send to FMC SAP

    """

    help = "prepare a XML report file to send to FMC SAP"

    def add_arguments(self, parser):
        parser.add_argument("location")

    def handle(self, *args, **options):

        # Disable signals for create_stream and update_stream.
        skip_signals_models = [
            Item,
        ]

        for model in skip_signals_models:
            signals.post_save.disconnect(sender=model, dispatch_uid=f"{model.__name__}_create_stream")
            # signals.post_save.disconnect(generate_numbering, sender=model)
            signals.pre_save.disconnect(sender=model, dispatch_uid=f"{model.__name__}_update_stream")

        # Partner GLN.     Plant.   Storage Loc.    Storage Loc GLN.
        # 9900000700561    MY03     3PL2            9900000300600
        # 9900000700562    MY03     R3PL            9900000300600
        # 9900000700569    MY03     RSP1            9900000301000

        # 9900000700563    MY04     3PL1            9900000300800
        # 9900000700564    MY04     R3PL            9900000300800
        # 9900000700565    MY04     RSP1            9900000300800

        # 9900000700566    MY05     3PL1            9900000300900
        # 9900000700567    MY05     R3PL            9900000300900
        # 9900000700568    MY05     RSP1            9900000300900

        location = options["location"]

        if location in "MY03":
            # SA02 > 3PL2
            location_code = "9900000700569"
            parent_warehouse_name = "Warehouse SA02"
            child_warehouse_name = "3PL2"
        elif location == "MY04":
            # KK01 > 3PL1
            location_code = "9900000700563"
            parent_warehouse_name = "Warehouse KK01"
            child_warehouse_name = "3PL1"
        elif location == "MY05":
            # KCH01 > 3PL1
            location_code = "9900000700566"
            parent_warehouse_name = "Warehouse KCH01"
            child_warehouse_name = "3PL1"

        creation_datetime = localtime_now().strftime("%Y-%m-%dT%H:%M:%S")
        effective_date = localtime_now().strftime("%Y-%m-%d")
        effective_time = localtime_now().strftime("%H:%M:%S")

        prepared_basic_info_dict = {
            "inventory_report:inventoryReportMessage": {
                "@xmlns:inventory_report": "urn:gs1:ecom:inventory_report:xsd:3",
                "@xmlns:sh": "http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader",
                "sh:StandardBusinessDocumentHeader": {
                    "sh:HeaderVersion": "1.0",
                    "sh:Sender": {"sh:Identifier": {"@Authority": "GS1", "#text": location_code}},
                    "sh:Receiver": {"sh:Identifier": {"@Authority": "GS1", "#text": "4039361000004"}},
                    "sh:DocumentIdentification": {
                        "sh:Standard": "GS1",
                        "sh:TypeVersion": "3.1",
                        "sh:InstanceIdentifier": "18092023020901",
                        "sh:Type": "Inventory Report Message",
                        "sh:MultipleType": "false",
                        "sh:CreationDateAndTime": "2023-09-17T20:33:44",
                    },
                },
                "inventoryReport": {
                    "creationDateTime": creation_datetime,
                    "documentStatusCode": "ORIGINAL",
                    "documentStructureVersion": "Inventory Balance Comparison",
                    "documentEffectiveDate": {"date": effective_date, "time": effective_time},
                    "inventoryReportIdentification": {"entityIdentification": location_code},
                    "inventoryReportTypeCode": "INVENTORY_STATUS",
                    "structureTypeCode": "LOCATION_BY_ITEM",
                    "inventoryReportToParty": {"gln": "4039361000004"},
                    "inventoryReportingParty": {"gln": location_code},
                    "reportingPeriod": {"beginDate": effective_date, "beginTime": effective_time},
                    "inventoryItemLocationInformation": [],
                },
            },
        }

        # Basically 4 types of scenarios/structures needs to handle on batchManaged & SerialManaged

        # * multiple serialNumber with 1 combined total quantity
        # {
        #    "inventoryLocation":{
        #       "gln": "9900000700522"
        #    },
        #    "transactionalTradeItem":{
        #       "gtin":"99999999999999",
        #       "additionalTradeItemIdentification":{
        #          "@additionalTradeItemIdentificationTypeCode":"SUPPLIER_ASSIGNED",
        #          "#text":"*********"
        #       }
        #    },
        #    "inventoryStatusLineItem":{
        #       "lineItemNumber":"878",
        #       "inventoryStatusQuantitySpecification":{
        #          "inventoryStatusType":"ON_HAND",
        #          "quantityOfUnits":{
        #             "@measurementUnitCode":"PCE",
        #             "#text":"2"
        #          },
        #          "transactionalItemData":{
        #             "serialNumber":[
        #                "3BPS4457",
        #                "9BPS2358"
        #             ]
        #          }
        #       }
        #    }
        # },

        # * 1 serialNumber with 1 independent quantity
        # {
        #        "inventoryLocation":{
        #           "gln":"9900000700522"
        #        },
        #        "transactionalTradeItem":{
        #           "gtin":"99999999999999",
        #           "additionalTradeItemIdentification":{
        #              "@additionalTradeItemIdentificationTypeCode":"SUPPLIER_ASSIGNED",
        #              "#text":"IN000188"
        #           }
        #        },
        #        "inventoryStatusLineItem":{
        #           "lineItemNumber":"1299",
        #           "inventoryStatusQuantitySpecification":{
        #              "inventoryStatusType":"ON_HOLD",
        #              "quantityOfUnits":{
        #                 "@measurementUnitCode":"PCE",
        #                 "#text":"1"
        #              },
        #              "transactionalItemData":{
        #                 "serialNumber":"AE211"
        #              }
        #           }
        #        }
        #     },

        # * single batchNumber with own quantity
        # {
        #    "inventoryLocation":{
        #       "gln":"9900000700522"
        #    },
        #    "transactionalTradeItem":{
        #       "gtin":"99999999999999",
        #       "additionalTradeItemIdentification":{
        #          "@additionalTradeItemIdentificationTypeCode":"SUPPLIER_ASSIGNED",
        #          "#text":"5007161"
        #       }
        #    },
        #    "inventoryStatusLineItem":{
        #       "lineItemNumber":"63",
        #       "inventoryStatusQuantitySpecification":{
        #          "inventoryStatusType":"ON_HOLD",
        #          "quantityOfUnits":{
        #             "@measurementUnitCode":"PCE",
        #             "#text":"240"
        #          },
        #          "transactionalItemData":{
        #             "batchNumber":"B1BK09100",
        #             "bestBeforeDate":"2023-09-30"
        #          }
        #       }
        #    }
        # },

        # * multiple batchNumber with respective quantity
        # {
        #     "inventoryLocation":{
        #         "gln":"9900000700522"
        #     },
        #     "transactionalTradeItem":{
        #         "gtin":"99999999999999",
        #         "additionalTradeItemIdentification":{
        #             "@additionalTradeItemIdentificationTypeCode":"SUPPLIER_ASSIGNED",
        #             "#text":"IN00000074"
        #         }
        #     },
        #     "inventoryStatusLineItem":[
        #         {
        #             "lineItemNumber":"1208",
        #             "inventoryStatusQuantitySpecification":{
        #                 "inventoryStatusType":"ON_HAND",
        #                 "quantityOfUnits":{
        #                     "@measurementUnitCode":"PCE",
        #                     "#text":"1"
        #                 },
        #                 "transactionalItemData":{
        #                     "batchNumber":"2301988",
        #                     "bestBeforeDate":"2024-12-31"
        #                 }
        #             }
        #         },
        #         {
        #             "lineItemNumber":"1209",
        #             "inventoryStatusQuantitySpecification":{
        #                 "inventoryStatusType":"ON_HOLD",
        #                 "quantityOfUnits":{
        #                     "@measurementUnitCode":"PCE",
        #                     "#text":"2"
        #                 },
        #                 "transactionalItemData":{
        #                     "batchNumber":"2212887",
        #                     "bestBeforeDate":"2024-11-30"
        #                 }
        #             }
        #         },
        #     ]
        # }

        # * EXPECTED to build own dict to be be loop
        # prepared_stock_dict = {
        #     "*********": {
        #         "item": ItemObj,
        #         "is_serial_managed": True,
        #         "batch_list": [
        #             {
        #                 "warehouse": WarehouseObj,
        #                 "batch_no": ["3BPS4457", "9BPS2358"],
        #                 "balance": 2,
        #                 "expiry_date": "",
        #                 "status_type": "ON_HAND",
        #             }
        #         ]
        #     },
        #     "IN000188": {
        #         "item": ItemObj,
        #         "is_serial_managed": True,
        #         "batch_list": [
        #             {
        #                 "warehouse": WarehouseObj,
        #                 "batch_no": ["AE211"],
        #                 "balance": 1,
        #                 "expiry_date": "",
        #                 "status_type": "ON_HAND",
        #             }
        #         ]
        #     },
        #     "5007161": {
        #         "item": ItemObj,
        #         "is_serial_managed": False,
        #         "batch_list": [
        #             {
        #                 "warehouse": WarehouseObj,
        #                 "batch_no": ["B1BK09100"],
        #                 "balance": 10,
        #                 "expiry_date": "2023-09-30",
        #                 "status_type": "ON_HAND",
        #             }
        #         ]
        #     },
        #     "IN00000074": {
        #         "item": ItemObj,
        #         "is_serial_managed": False,
        #         "batch_list": [
        #             {
        #                 "warehouse": WarehouseObj,
        #                 "batch_no": ["2301988"],
        #                 "balance": 1,
        #                 "expiry_date": "2024-12-31",
        #                 "status_type": "ON_HAND",
        #             },
        #             {
        #                 "warehouse": WarehouseObj,
        #                 "batch_no": ["2212887"],
        #                 "balance": 2,
        #                 "expiry_date": "2024-11-30",
        #                 "status_type": "ON_HOLD",
        #             },
        #         ]
        #     },
        # }

        prepared_stock_dict = {}

        warehouse_obj = Warehouse.objects.get(name=parent_warehouse_name)
        on_hand_warehouse = warehouse_obj.get_descendants().get(name=child_warehouse_name)
        defect_warehouse = on_hand_warehouse.get_descendants().get(name="Defect")

        on_hand_stocks_qs = Stock.objects.filter(warehouse=on_hand_warehouse)
        defect_stocks_qs = Stock.objects.filter(warehouse=defect_warehouse)

        for stock in on_hand_stocks_qs:
            batch_no = stock.batch_no if stock.batch_no != "N/A" else ""
            expiry_date = stock.expiry_date.strftime("%Y-%m-%d") if stock.expiry_date else ""

            is_serial_managed = False
            if stock.item.manage_type == stock.item.ManageType.SERIAL_MANAGED:
                is_serial_managed = True

            if stock.item.code in prepared_stock_dict:
                if is_serial_managed:
                    prepared_stock_dict[stock.item.code]["batch_list"][0]["batch_no"].append(batch_no)
                    prepared_stock_dict[stock.item.code]["batch_list"][0]["balance"] += stock.balance
                else:
                    batch_dict = {
                        "warehouse": on_hand_warehouse,
                        "batch_no": [batch_no],
                        "balance": round(stock.balance, 3),
                        "expiry_date": expiry_date,
                        "status_type": "ON_HAND",
                    }
                    prepared_stock_dict[stock.item.code]["batch_list"].append(batch_dict)
            else:
                prepared_stock_dict[stock.item.code] = {
                    "item": stock.item,
                    "is_serial_managed": is_serial_managed,
                    "batch_list": [
                        {
                            "warehouse": on_hand_warehouse,
                            "batch_no": [batch_no],
                            "balance": round(stock.balance, 3),
                            "expiry_date": expiry_date,
                            "status_type": "ON_HAND",
                        }
                    ],
                }

        # print("prepared_stock_dict: ", prepared_stock_dict)

        for stock in defect_stocks_qs:
            batch_no = stock.batch_no if stock.batch_no != "N/A" else ""
            expiry_date = stock.expiry_date.strftime("%Y-%m-%d") if stock.expiry_date else ""

            is_serial_managed = False
            if stock.item.manage_type == stock.item.ManageType.SERIAL_MANAGED:
                is_serial_managed = True

            if stock.item.code in prepared_stock_dict:
                if is_serial_managed:
                    batch_list = prepared_stock_dict[stock.item.code]["batch_list"]

                    # to check if BLOCK batch exist in list or not first
                    defect_batch_dict_exist = False
                    for batch_dict in batch_list:
                        if batch_dict["status_type"] == "ON_HOLD":
                            defect_batch_dict_exist = True

                    if defect_batch_dict_exist is False:
                        defect_batch_dict = {
                            "warehouse": defect_warehouse,
                            "batch_no": [],
                            "balance": 0,
                            "expiry_date": "",
                            "status_type": "ON_HOLD",
                        }
                        prepared_stock_dict[stock.item.code]["batch_list"].append(defect_batch_dict)

                    if len(batch_list) == 1 and batch_list[0]["status_type"] == "ON_HOLD":
                        batch_list[0]["batch_no"].append(batch_no)
                        batch_list[0]["balance"] += round(stock.balance, 3)
                        batch_list[0]["expiry_date"] = stock.expiry_date if stock.expiry_date else ""

                    elif len(batch_list) == 2:
                        batch_list[1]["batch_no"].append(batch_no)
                        batch_list[1]["balance"] += round(stock.balance, 3)
                        batch_list[1]["expiry_date"] = stock.expiry_date if stock.expiry_date else ""
                else:
                    batch_dict = {
                        "warehouse": defect_warehouse,
                        "batch_no": [batch_no],
                        "balance": round(stock.balance, 3),
                        "expiry_date": expiry_date,
                        "status_type": "ON_HOLD",
                    }
                    prepared_stock_dict[stock.item.code]["batch_list"].append(batch_dict)
            else:
                prepared_stock_dict[stock.item.code] = {
                    "item": stock.item,
                    "is_serial_managed": is_serial_managed,
                    "batch_list": [
                        {
                            "warehouse": defect_warehouse,
                            "batch_no": [batch_no],
                            "balance": round(stock.balance, 3),
                            "expiry_date": expiry_date,
                            "status_type": "ON_HOLD",
                        }
                    ],
                }

        # print("=========================================")
        # print("after BLOCK prepared_stock_dict: ", prepared_stock_dict)

        inventory_item_location_info_dict = {}
        line_counter = 1
        for stock_key, stock_dict_value in prepared_stock_dict.items():
            inventory_item_location_info_dict = {
                "inventoryLocation": {
                    "gln": location_code,
                },
                "transactionalTradeItem": {
                    "gtin": "99999999999999",
                    "additionalTradeItemIdentification": {
                        "@additionalTradeItemIdentificationTypeCode": "SUPPLIER_ASSIGNED",
                        "#text": stock_key,
                    },
                },
                # "inventoryStatusLineItem": {
                #     "lineItemNumber": line_counter,
                #     "inventoryStatusQuantitySpecification": {
                #         "inventoryStatusType": "ON_HAND",
                #         "quantityOfUnits": {
                #             "@measurementUnitCode": "PCE",
                #             "#text": "2"
                #         },
                #         "transactionalItemData": {
                #             "serialNumber": [
                #                 "3BPS4457",
                #                 "9BPS2358"
                #             ]
                #         }
                #     }
                # }
            }

            # serial managed item
            if stock_dict_value["is_serial_managed"] is True:
                if len(stock_dict_value["batch_list"]) == 1:
                    inventory_item_location_info_dict["inventoryStatusLineItem"] = {
                        "lineItemNumber": str(line_counter),
                        "inventoryStatusQuantitySpecification": {
                            "inventoryStatusType": stock_dict_value["batch_list"][0]["status_type"],
                            "quantityOfUnits": {
                                "@measurementUnitCode": stock_dict_value["item"].uom.symbol,
                                "#text": str(round(stock_dict_value["batch_list"][0]["balance"], 3)),
                            },
                            # "transactionalItemData": {
                            #     "serialNumber": [
                            #         "3BPS4457",
                            #         "9BPS2358"
                            #     ]
                            # }
                        },
                    }

                    inventory_status_line = inventory_item_location_info_dict["inventoryStatusLineItem"]
                    inventory_status_qty_spec = inventory_status_line["inventoryStatusQuantitySpecification"]

                    if len(stock_dict_value["batch_list"][0]["batch_no"]) == 1:
                        inventory_status_qty_spec["transactionalItemData"] = {
                            "serialNumber": stock_dict_value["batch_list"][0]["batch_no"][0]
                        }
                    else:
                        inventory_status_qty_spec["transactionalItemData"] = {
                            "serialNumber": [],
                        }

                        for batch_no in stock_dict_value["batch_list"][0]["batch_no"]:
                            inventory_status_qty_spec["transactionalItemData"]["serialNumber"].append(batch_no)

                    line_counter += 1

                # serial managed with more than 1 batch
                else:
                    inventory_item_location_info_dict["inventoryStatusLineItem"] = []

                    for batch_dict in stock_dict_value["batch_list"]:
                        line_item_dict = {
                            "lineItemNumber": str(line_counter),
                            "inventoryStatusQuantitySpecification": {
                                "inventoryStatusType": batch_dict["status_type"],
                                "quantityOfUnits": {
                                    "@measurementUnitCode": stock_dict_value["item"].uom.symbol,
                                    "#text": str(round(batch_dict["balance"], 3)),
                                },
                                # "transactionalItemData": {
                                #     "serialNumber": [
                                #         "3BPS4457",
                                #         "9BPS2358"
                                #     ]
                                # }
                            },
                        }

                        inventory_status_qty_spec = line_item_dict["inventoryStatusQuantitySpecification"]
                        if len(batch_dict["batch_no"]) == 1:
                            inventory_status_qty_spec["transactionalItemData"] = {
                                "serialNumber": batch_dict["batch_no"][0]
                            }
                        else:
                            inventory_status_qty_spec["transactionalItemData"] = {
                                "serialNumber": [],
                            }

                            for batch_no in batch_dict["batch_no"]:
                                inventory_status_qty_spec["transactionalItemData"]["serialNumber"].append(batch_no)

                        inventory_item_location_info_dict["inventoryStatusLineItem"].append(line_item_dict)
                        line_counter += 1

            # batch managed item
            else:
                # single batch's structure

                # inventoryStatusLineItem = {
                #     "lineItemNumber":"1208",
                #     "inventoryStatusQuantitySpecification":{
                #         "inventoryStatusType":"ON_HAND",
                #         "quantityOfUnits":{
                #             "@measurementUnitCode":"PCE",
                #             "#text":"1"
                #         },
                #         "transactionalItemData":{
                #             "batchNumber":"2301988",
                #             "bestBeforeDate":"2024-12-31"
                #         }
                #     }
                # },
                if len(stock_dict_value["batch_list"]) == 1:
                    inventory_item_location_info_dict["inventoryStatusLineItem"] = {
                        "lineItemNumber": str(line_counter),
                        "inventoryStatusQuantitySpecification": {
                            "inventoryStatusType": stock_dict_value["batch_list"][0]["status_type"],
                            "quantityOfUnits": {
                                "@measurementUnitCode": stock_dict_value["item"].uom.symbol,
                                "#text": str(stock_dict_value["batch_list"][0]["balance"]),
                            },
                            "transactionalItemData": {
                                "batchNumber": stock_dict_value["batch_list"][0]["batch_no"][0],
                                "bestBeforeDate": stock_dict_value["batch_list"][0]["expiry_date"],
                            },
                        },
                    }
                    line_counter += 1

                # multiple batch structure
                else:
                    inventory_item_location_info_dict["inventoryStatusLineItem"] = []

                    for batch_dict in stock_dict_value["batch_list"]:
                        line_item_dict = {
                            "lineItemNumber": str(line_counter),
                            "inventoryStatusQuantitySpecification": {
                                "inventoryStatusType": batch_dict["status_type"],
                                "quantityOfUnits": {
                                    "@measurementUnitCode": stock_dict_value["item"].uom.symbol,
                                    "#text": str(batch_dict["balance"]),
                                },
                                "transactionalItemData": {
                                    "batchNumber": batch_dict["batch_no"][0],
                                    "bestBeforeDate": batch_dict["expiry_date"],
                                },
                            },
                        }

                        inventory_item_location_info_dict["inventoryStatusLineItem"].append(line_item_dict)
                        line_counter += 1

            # print("inventory_item_location_info_dict: ", inventory_item_location_info_dict)

            inventory_report = prepared_basic_info_dict["inventory_report:inventoryReportMessage"]["inventoryReport"]
            inventory_report["inventoryItemLocationInformation"].append(inventory_item_location_info_dict)

            # print("============== FINAL ================")
            # print(
            #     'inventory_report["inventoryItemLocationInformation"]: ',
            #     inventory_report["inventoryItemLocationInformation"],
            # )

        # Send the XML payload to FMC EDI through pyas2 module.
        # Requires 3 main data for sending: organization, partner, payload
        organization = Organization.objects.first()
        fmc_partner = Partner.objects.get(as2_name=settings.FMC_AS2_IDENTIFIER)

        # Convert payload dict into XML format
        prepared_dict = prepared_basic_info_dict
        payload = unparse(prepared_dict, pretty=True)
        payload = payload.replace('encoding="utf-8"', "")

        original_filename = f"InventoryReport_{localtime_now().strftime('%Y%m%d_%H%M')}.xml"
        as2message = AS2Message(sender=organization.as2org, receiver=fmc_partner.as2partner)

        # Build and send the AS2 message
        as2message.build(
            data=str.encode(payload),
            filename=original_filename,
            subject=fmc_partner.subject,
            content_type=fmc_partner.content_type,
            disposition_notification_to=organization.email_address or "<EMAIL>",
        )
        message, _ = Message.objects.create_from_as2message(
            as2message=as2message,
            payload=payload,
            filename=original_filename,
            direction="OUT",
            status="P",  # P stands for Pending status
        )
        message.send_message(as2message.headers, as2message.content)

        # Enable signals for create_stream and update_stream.
        for model in skip_signals_models:
            register_stream(model, delete=False)
            # signals.post_save.connect(generate_numbering, sender=model)

import psycopg2
import json

from psycopg2.extras import execute_values
from django.core.management.base import BaseCommand
from django.contrib.contenttypes.models import ContentType
from django.contrib.auth import get_user_model

from wms.apps.consignees.models import (
    <PERSON><PERSON><PERSON><PERSON><PERSON> as Consign<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>signee,
    ConsigneeCont<PERSON>,
    ShippingAddress as ConsigneeShippingAddress,
)
from wms.apps.consignors.models import (
    Billing<PERSON>dd<PERSON> as Consignor<PERSON><PERSON>ing<PERSON>ddress,
    Consignor,
    ConsignorContact,
    ShippingAddress as ConsignorShippingAddress,
)
from wms.apps.adjustments.models import Adjustment, AdjustmentItem, AdjustmentStockIn
from wms.apps.count_sheets.models import CountSheet
from wms.apps.inventories.models import Item, ItemCategory, ItemPhoto, Stock, Transaction
from wms.apps.pickings.models import PickingList, PickingListItem
from wms.apps.receives.models import (
    GoodsReceivedNote,
    GoodsReceivedNoteDefect,
    GoodsReceivedNoteItem,
    GoodsReceivedNoteStockIn,
    GoodsReceivedNoteDefectStockIn,
)
from wms.apps.releases.models import (
    DeliveryOrder,
    WarehouseReleaseOrder,
    WarehouseReleaseOrderItem,
    WarehouseReleaseOrderItemPicker,
    WarehouseReleaseOrderStockOut,
)
from wms.apps.settings.models import Organization, Branch, UnitConversion, UnitOfMeasure, Warehouse
from wms.apps.transfers.models import Transfer, TransferItem, TransferStockInOut



UserModel = get_user_model()


# Database connection settings
DB_SINOFLEX_CONFIG = {
    "dbname": "sinoflex",
    "user": "sinoflex",
    "password": "izWAe^RxJQyLWB!SFhptjr3y",
    "host": "localhost",
    "port": "5432",
}

DB_WMS_CONFIG = {
    "dbname": "wms_db",
    "user": "superpsql",
    "password": "password",
    "host": "localhost",
    "port": "5432",
}

table_list = [
    {
        "table_name_map": {
            "from": "actstream_action",
            "to": "actstream_action",
        },
        "table_column_map": {
            "id": "id",
            "actor_object_id": "actor_object_id",
            "verb": "verb",
            "description": "description",
            "target_object_id": "target_object_id",
            "action_object_object_id": "action_object_object_id",
            "timestamp": "timestamp",
            "public": "public",
            "data": "data",
            "action_object_content_type_id": "action_object_content_type_id",
            "actor_content_type_id": "actor_content_type_id",
            "target_content_type_id": "target_content_type_id",
        },
        "json_columns": ["data"]
    },
]

# Define your custom mapping for content type IDs (source: destination)
ACTION_CONTENT_TYPE_MAPPING = {
    1: 1,
    2: 2,
    3: 3,
    33: ContentType.objects.get_for_model(UserModel).id,  # users | user
    44: ContentType.objects.get_for_model(Consignor).id,  # consignors | consignor
    45: ContentType.objects.get_for_model(ConsignorShippingAddress).id,  # consignors | shippingaddress
    46: ContentType.objects.get_for_model(ConsignorContact).id,  # consignors | consignorcontact
    47: ContentType.objects.get_for_model(ConsignorBillingAddress).id,  # consignors | billingaddress
    48: ContentType.objects.get_for_model(Consignee).id,  # consignees | consignee
    49: ContentType.objects.get_for_model(ConsigneeShippingAddress).id,  # consignees | shippingaddress
    50: ContentType.objects.get_for_model(ConsigneeContact).id,  # consignees | consigneecontact
    51: ContentType.objects.get_for_model(ConsigneeBillingAddress).id,  # consignees | billingaddress
    34: ContentType.objects.get_for_model(Branch).id,  # settings | branch
    35: ContentType.objects.get_for_model(Warehouse).id,  # settings | warehouse
    36: ContentType.objects.get_for_model(UnitOfMeasure).id,  # settings | unitofmeasure
    37: ContentType.objects.get_for_model(Organization).id,  # settings | organization
    38: ContentType.objects.get_for_model(UnitConversion).id,  # settings | unitconversion
    39: ContentType.objects.get_for_model(Item).id,  # inventories | item
    40: ContentType.objects.get_for_model(Stock).id,  # inventories | stock
    41: ContentType.objects.get_for_model(Transaction).id,  # inventories | transaction
    42: ContentType.objects.get_for_model(ItemPhoto).id,  # inventories | itemphoto
    43: ContentType.objects.get_for_model(ItemCategory).id,  # inventories | itemcategory
    52: ContentType.objects.get_for_model(GoodsReceivedNote).id,  # receives | goodsreceivednote
    53: ContentType.objects.get_for_model(GoodsReceivedNoteItem).id,  # receives | goodsreceivednoteitem
    75: ContentType.objects.get_for_model(GoodsReceivedNoteDefectStockIn).id,  # receives | goodsreceivednotedefectstockin
    55: ContentType.objects.get_for_model(GoodsReceivedNoteDefect).id,  # receives | goodsreceivednotedefect
    54: ContentType.objects.get_for_model(GoodsReceivedNoteStockIn).id,  # receives | goodsreceivednotestockin
    56: ContentType.objects.get_for_model(WarehouseReleaseOrder).id,  # releases | warehousereleaseorder
    60: ContentType.objects.get_for_model(DeliveryOrder).id,  # releases | deliveryorder
    57: ContentType.objects.get_for_model(WarehouseReleaseOrderItem).id,  # releases | warehousereleaseorderitem
    59: ContentType.objects.get_for_model(WarehouseReleaseOrderItemPicker).id,  # releases | warehousereleaseorderitempicker
    58: ContentType.objects.get_for_model(WarehouseReleaseOrderStockOut).id,  # releases | warehousereleaseorderstockout
    61: ContentType.objects.get_for_model(Adjustment).id,  # adjustments | adjustnote, adjustment
    62: ContentType.objects.get_for_model(AdjustmentItem).id,  # adjustments | adjustnoteitem, adjustmentitem
    63: ContentType.objects.get_for_model(AdjustmentStockIn).id,  # adjustments | adjustnotestockin, adjustmentstockin
    64: ContentType.objects.get_for_model(Transfer).id,  # transfers | transfer
    65: ContentType.objects.get_for_model(TransferItem).id,  # transfers | transferitem
    66: ContentType.objects.get_for_model(TransferStockInOut).id,  # transfers | transferstockinout
    82: ContentType.objects.get_for_model(PickingList).id,  # pickings | pickinglist
    83: ContentType.objects.get_for_model(PickingListItem).id,  # pickings | pickinglistitem
    84: ContentType.objects.get_for_model(CountSheet).id,  # count_sheets | countsheet
}
ACTOR_CONTENT_TYPE_MAPPING = {
    # user CT source: user CT destination
    33: 31,  # Example: Convert 33 -> 31
}
TARGET_CONTENT_TYPE_MAPPING = {
    1: 1,
    2: 2,
    3: 3,
    33: ContentType.objects.get_for_model(UserModel).id,  # users | user
    44: ContentType.objects.get_for_model(Consignor).id,  # consignors | consignor
    45: ContentType.objects.get_for_model(ConsignorShippingAddress).id,  # consignors | shippingaddress
    46: ContentType.objects.get_for_model(ConsignorContact).id,  # consignors | consignorcontact
    47: ContentType.objects.get_for_model(ConsignorBillingAddress).id,  # consignors | billingaddress
    48: ContentType.objects.get_for_model(Consignee).id,  # consignees | consignee
    49: ContentType.objects.get_for_model(ConsigneeShippingAddress).id,  # consignees | shippingaddress
    50: ContentType.objects.get_for_model(ConsigneeContact).id,  # consignees | consigneecontact
    51: ContentType.objects.get_for_model(ConsigneeBillingAddress).id,  # consignees | billingaddress
    34: ContentType.objects.get_for_model(Branch).id,  # settings | branch
    35: ContentType.objects.get_for_model(Warehouse).id,  # settings | warehouse
    36: ContentType.objects.get_for_model(UnitOfMeasure).id,  # settings | unitofmeasure
    37: ContentType.objects.get_for_model(Organization).id,  # settings | organization
    38: ContentType.objects.get_for_model(UnitConversion).id,  # settings | unitconversion
    39: ContentType.objects.get_for_model(Item).id,  # inventories | item
    40: ContentType.objects.get_for_model(Stock).id,  # inventories | stock
    41: ContentType.objects.get_for_model(Transaction).id,  # inventories | transaction
    42: ContentType.objects.get_for_model(ItemPhoto).id,  # inventories | itemphoto
    43: ContentType.objects.get_for_model(ItemCategory).id,  # inventories | itemcategory
    52: ContentType.objects.get_for_model(GoodsReceivedNote).id,  # receives | goodsreceivednote
    53: ContentType.objects.get_for_model(GoodsReceivedNoteItem).id,  # receives | goodsreceivednoteitem
    75: ContentType.objects.get_for_model(GoodsReceivedNoteDefectStockIn).id,  # receives | goodsreceivednotedefectstockin
    55: ContentType.objects.get_for_model(GoodsReceivedNoteDefect).id,  # receives | goodsreceivednotedefect
    54: ContentType.objects.get_for_model(GoodsReceivedNoteStockIn).id,  # receives | goodsreceivednotestockin
    56: ContentType.objects.get_for_model(WarehouseReleaseOrder).id,  # releases | warehousereleaseorder
    60: ContentType.objects.get_for_model(DeliveryOrder).id,  # releases | deliveryorder
    57: ContentType.objects.get_for_model(WarehouseReleaseOrderItem).id,  # releases | warehousereleaseorderitem
    59: ContentType.objects.get_for_model(WarehouseReleaseOrderItemPicker).id,  # releases | warehousereleaseorderitempicker
    58: ContentType.objects.get_for_model(WarehouseReleaseOrderStockOut).id,  # releases | warehousereleaseorderstockout
    61: ContentType.objects.get_for_model(Adjustment).id,  # adjustments | adjustnote, adjustment
    62: ContentType.objects.get_for_model(AdjustmentItem).id,  # adjustments | adjustnoteitem, adjustmentitem
    63: ContentType.objects.get_for_model(AdjustmentStockIn).id,  # adjustments | adjustnotestockin, adjustmentstockin
    64: ContentType.objects.get_for_model(Transfer).id,  # transfers | transfer
    65: ContentType.objects.get_for_model(TransferItem).id,  # transfers | transferitem
    66: ContentType.objects.get_for_model(TransferStockInOut).id,  # transfers | transferstockinout
    82: ContentType.objects.get_for_model(PickingList).id,  # pickings | pickinglist
    83: ContentType.objects.get_for_model(PickingListItem).id,  # pickings | pickinglistitem
    84: ContentType.objects.get_for_model(CountSheet).id,  # count_sheets | countsheet
}


class Command(BaseCommand):
    """Run the Migrate Data command.

    This command is to migrate All existing data from Old DB to New DB

    """

    help = "Migrate Data To Warehouse Management System"

    def handle(self, *args, **options):
        """Main function to handle data migration"""
        self.stdout.write("Starting data migration...")

        for table_dict in table_list:
            data = self.fetch_data(table_dict)
            if data:
                self.insert_data(data, table_dict)
                self.reset_sequence(table_dict)
                self.stdout.write(
                    self.style.SUCCESS(
                        f"Successfully migrated {len(data)} rows from {table_dict["table_name_map"]["from"]} "
                        f"to {table_dict["table_name_map"]["to"]}."
                    )
                )
            else:
                self.stdout.write(self.style.WARNING("No data found in source table."))


    def fetch_data(self, table_dict=None):
        """Fetch selected columns from db_a"""
        source_columns = ", ".join(table_dict["table_column_map"].keys())  # Get source columns
        query = f"SELECT {source_columns} FROM {table_dict["table_name_map"]["from"]};"

        self.stdout.write("Fetching data from source Table...")
        with psycopg2.connect(**DB_SINOFLEX_CONFIG) as conn:
            with conn.cursor() as cursor:
                cursor.execute(query)
                data = cursor.fetchall()

        # Convert JSON fields into a string format
        json_columns = table_dict.get("json_columns", [])  # Get JSON fields (if any)
        formatted_data = []
        self.stdout.write("Appending fetched data into a list...")
        for row in data:
            row = list(row)  # Convert tuple to list for modification
            for index, column_name in enumerate(table_dict["table_column_map"].keys()):
                if column_name in json_columns and isinstance(row[index], dict):
                    row[index] = json.dumps(row[index])  # Convert dict to JSON string

                # Apply content type ID mapping action_object_content_type_id
                if column_name in ["action_object_content_type_id"]:
                    row[index] = ACTION_CONTENT_TYPE_MAPPING.get(row[index], row[index])  # Map value or keep original

                # Apply content type ID mapping actor_content_type_id
                if column_name in ["actor_content_type_id"]:
                    row[index] = ACTOR_CONTENT_TYPE_MAPPING.get(row[index], row[index])  # Map value or keep original

                # Apply content type ID mapping target_content_type_id
                if column_name in ["target_content_type_id"]:
                    row[index] = TARGET_CONTENT_TYPE_MAPPING.get(row[index], row[index])  # Map value or keep original

            # temporary skip for racking first.
            skip_rackings_ct = [
                78,  # rackstorage (this might rename to RackStock)
                79,  # rack
                80,  # racktransaction (this will be different)
                81,  # rackbalance (this will be different)
            ]
            if row[9] in skip_rackings_ct or row[11] in skip_rackings_ct:
                pass
            else:
                formatted_data.append(tuple(row))  # Convert back to tuple

        return formatted_data

    def insert_data(self, data, table_dict=None):
        """Insert selected data into db_b with column mapping"""
        dest_columns = list(table_dict["table_column_map"].values())  # Get destination columns
        default_false_columns = table_dict.get("default_false_columns", [])  # Get boolean columns to default to False

        # Add missing boolean columns if they are not in the original column mapping
        for col in default_false_columns:
            if col not in dest_columns:
                dest_columns.append(col)

        placeholders = ", ".join(["%s"] * len(dest_columns))  # Generate %s placeholders

        updated_data = []
        for row in data:
            row_dict = dict(zip(table_dict["table_column_map"].values(), row))  # Convert row to dict

            # Assign False to any missing boolean columns
            for col in default_false_columns:
                row_dict.setdefault(col, False)

            updated_data.append(tuple(row_dict[col] for col in dest_columns))  # Convert back to tuple

        self.stdout.write("Inserting data to Destination Table...")
        query = f"""
        INSERT INTO {table_dict["table_name_map"]["to"]} ({", ".join(dest_columns)}) VALUES %s
        ON CONFLICT (id) DO UPDATE SET
        {", ".join([f"{col} = EXCLUDED.{col}" for col in dest_columns if col != "id"])}
        RETURNING id;
        """

        with psycopg2.connect(**DB_WMS_CONFIG) as conn:
            with conn.cursor() as cursor:
                execute_values(cursor, query, updated_data)
            conn.commit()

    def reset_sequence(self, table_dict):
        """Reset sequence for auto-incrementing PK in db_b"""
        query = f"""
        SELECT setval(pg_get_serial_sequence('{table_dict["table_name_map"]["to"]}', 'id'),
                      COALESCE((SELECT MAX(id) FROM {table_dict["table_name_map"]["to"]}), 1), false);
        """

        with psycopg2.connect(**DB_WMS_CONFIG) as conn:
            with conn.cursor() as cursor:
                cursor.execute(query)
            conn.commit()

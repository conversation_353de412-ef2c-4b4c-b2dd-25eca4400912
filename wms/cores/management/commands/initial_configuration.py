import logging

from django.contrib.auth import get_user_model
from django.core.management import call_command
from django.core.management.base import BaseCommand, CommandError
from django.db.models import signals
from django.db.utils import IntegrityError

# from wss.cores.actstream import register_stream
# from wss.cores.signals import generate_numbering

# from wss.apps.consignees.models import Consignee
# from wss.apps.consignors.models import Consignor
# from wss.apps.inventories.models import Item, Stock, Transaction
# from wss.apps.settings.models import Branch, Organization, UnitConversion, UnitOfMeasure, Warehouse

logger = logging.getLogger(__name__)


UserModel = get_user_model()


class Command(BaseCommand):
    """Run the inital configuration command.

    This command is for first time configuration on project only.
    It will configure a superuser with username `<EMAIL>`, and
    the following initial fixtures:

    * settings.uom.json
    * consignors.json
    * consignees.json
    * inventories.item.json

    """

    # help = "Initial configuration for Warehouse Management System"
    help = "Runs multiple management commands in sequence."

    # List of commands to execute (in order)
    commands_to_run = [
        "migrate_0001_1_users",
        "migrate_0002_consignors",
        "migrate_0003_consignees",
        "migrate_0004_settings",
        "migrate_0005_user_warehouses",
        "migrate_0006_inventories",
        "migrate_0007_adjustments",
        "migrate_0008_transfers",
        "migrate_0009_releases",
        "migrate_0010_pickings",
        "migrate_0011_releases_pickinglist",
        "migrate_0012_receives",
        "migrate_0013_count_sheets",
        "migrate_0014_rackings",
        "migrate_0015_actstream",
        "migrate_0016_rebuild_index",
        "migrate_0017_patch_transaction_channel",
        "migrate_0018_initial_daily_stock_balance",
        "migrate_0019_extra_settings",
        "migrate_0020_patch_extra_settings_email",
        "migrate_0021_reset_all_tables_pk_increment",
        "migrate_0022_patch_item_outbound_display",
        "migrate_0023_2_patch_company_user",
    ]

    def handle(self, *args, **options):
        """
        Execute the list of management commands.
        """

        # try:
        #     superuser = UserModel.objects.create_superuser(
        #         username="<EMAIL>",
        #         email="<EMAIL>",
        #         # first_name="Support",
        #         # last_name="Rebellion",
        #         password="Rebellion!",
        #         is_active=True,
        #         is_staff=True,
        #     )
        # except IntegrityError:
        #     self.stdout.write(self.style.WARNING("Superuser <EMAIL> exist."))
        # else:
        #     self.stdout.write(self.style.SUCCESS(f"Successfully created superuser {superuser.username}"))

        self.stdout.write(self.style.NOTICE("Starting batch execution of management commands...\n"))

        for cmd in self.commands_to_run:
            try:
                self.stdout.write(self.style.SQL_KEYWORD(f"Running: {cmd}"))
                cmd_parts = cmd.split()  # Split command in case of arguments
                call_command(*cmd_parts)  # Run the command dynamically
                self.stdout.write(self.style.SUCCESS(f"✔ Successfully ran: {cmd}\n"))
            except CommandError as e:
                self.stderr.write(self.style.ERROR(f"Error running {cmd}: {e}"))
            except Exception as e:
                self.stderr.write(self.style.ERROR(f"Unexpected error in {cmd}: {str(e)}"))
                traceback.print_exc(file=sys.stderr)

        self.stdout.write(self.style.SUCCESS("\n All commands executed!"))

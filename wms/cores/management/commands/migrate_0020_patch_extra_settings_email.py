from django.core.management.base import BaseCommand

from extra_settings.models import Setting

from wms.apps.inventories.models import DailyStockBalance, Stock

class Command(BaseCommand):
    """Run the Patch all the existing extra_setting's email controls on EDI.

    This command is Patch all the existing extra_setting's email controls on EDI.

    """

    help = "Patch all the existing extra_setting's email controls on EDI"

    def handle(self, *args, **options):
        """To Patch all the existing extra_setting's email controls on EDI"""

        # NOTE*: define cratepilot's general email
        wild_card = "tyler"
        fmc_cratepilot_email = f"{wild_card}+<EMAIL>"
        mmm_cratepilot_email = f"{wild_card}+<EMAIL>"

        fmc_grn_error_notification_email = Setting.objects.get(name="FMC_GRN_ERROR_NOTIFICATION_EMAIL")
        fmc_wro_error_notification_email = Setting.objects.get(name="FMC_WRO_ERROR_NOTIFICATION_EMAIL")
        mitsubishi_grn_error_notification_email = Setting.objects.get(name="MITSUBISHI_GRN_ERROR_NOTIFICATION_EMAIL")
        mitsubishi_wro_error_notification_email = Setting.objects.get(name="MITSUBISHI_WRO_ERROR_NOTIFICATION_EMAIL")

        fmc_grn_error_notification_email.value = [fmc_cratepilot_email, "<EMAIL>", "<EMAIL>", "<EMAIL>"]
        fmc_wro_error_notification_email.value = [fmc_cratepilot_email, "<EMAIL>", "<EMAIL>", "<EMAIL>"]
        mitsubishi_grn_error_notification_email.value = [mmm_cratepilot_email, "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]
        mitsubishi_wro_error_notification_email.value = [mmm_cratepilot_email, "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]

        fmc_grn_error_notification_email.save()
        fmc_wro_error_notification_email.save()
        mitsubishi_grn_error_notification_email.save()
        mitsubishi_wro_error_notification_email.save()

        self.stdout.write(
            self.style.SUCCESS(
                f"Successfully patched all the existing extra_setting's email controls on EDI"
            )
        )

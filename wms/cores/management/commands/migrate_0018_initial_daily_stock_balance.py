from decimal import Decimal

from django.core.management.base import BaseCommand
from django.db.models import Sum

from wms.apps.inventories.models import DailyStockBalance, Stock

class Command(BaseCommand):
    """Run the DailyStockBalance creation command.

    This command is to create all the stock's latest transaction balance on new DB.

    """

    help = "Create all DailyStockBalance on the new DB"

    def handle(self, *args, **options):
        """Initialize DailyStockBalance's all objects based on all stocks."""
        all_stock = Stock.objects.all()

        for stock in all_stock:
            if stock.transaction_set.exists():
                latest_transaction = stock.transaction_set.all().order_by(
                    "-transaction_datetime"
                ).first()
                latest_balance = stock.transaction_set.all().aggregate(Sum("system_quantity")).get(
                    "system_quantity__sum", Decimal("0")
                ) or Decimal("0")

                daily_stock_bal, created = DailyStockBalance.objects.get_or_create(
                    stock=stock,
                    transaction_state=latest_transaction,
                    balance=latest_balance,
                )
            else:
                print("stock with no transaction", stock)

from decimal import Decimal

from django.core.management.base import BaseCommand
from django.db.models import Sum, OuterRef, Subquery, Value, DecimalField

from wms.apps.inventories.models import Transaction
from wms.apps.receives.models import GoodsReceivedNoteStockIn, GoodsReceivedNoteDefectStockIn
from wms.apps.releases.models import WarehouseReleaseOrderStockOut
from wms.apps.adjustments.models import AdjustmentStockIn
from wms.apps.transfers.models import TransferStockInOut


class Command(BaseCommand):
    """Run the Patch all the existing transaction pointing to the correct channel's flag command.

    This command is Patch all the existing transaction pointing to the correct channel's flag.

    """

    help = "Patch all the existing transaction pointing to the correct channel's flag."

    def handle(self, *args, **options):
        """To Patch all the existing transaction pointing to the correct channel's flag."""

        # GRN
        grn_transaction_qs = Transaction.objects.filter(
            is_internal_transfer=False,
            is_adjustment=False,
            is_grn=False,
            is_grn_defect=False,
            is_wro=False,
            warehousereleaseorderstockout__isnull=True,
            goodsreceivednotestockin__isnull=False,
            goodsreceivednotedefectstockin__isnull=True,
        )
        grn_running_number_subquery = GoodsReceivedNoteStockIn.objects.filter(
            transaction=OuterRef("id")
        ).values("goods_received_note_item__goods_received_note__system_number")[:1]
        grn_transaction_qs.update(is_grn=True, system_number_ref=Subquery(grn_running_number_subquery))

        # GRN defect
        grn_defect_transaction_qs = Transaction.objects.filter(
            is_internal_transfer=False,
            is_adjustment=False,
            is_grn=False,
            is_grn_defect=False,
            is_wro=False,
            warehousereleaseorderstockout__isnull=True,
            goodsreceivednotestockin__isnull=True,
            goodsreceivednotedefectstockin__isnull=False,
        )
        grn_defect_running_number_subquery = GoodsReceivedNoteDefectStockIn.objects.filter(
            transaction=OuterRef("id")
        ).values("goods_received_note_item__goods_received_note__system_number")[:1]
        grn_defect_transaction_qs.update(
            is_grn_defect=True,
            system_number_ref=Subquery(grn_defect_running_number_subquery)
        )

        # WRO
        wro_transaction_qs = Transaction.objects.filter(
            is_internal_transfer=False,
            is_adjustment=False,
            is_grn=False,
            is_grn_defect=False,
            is_wro=False,
            warehousereleaseorderstockout__isnull=False,
            goodsreceivednotestockin__isnull=True,
            goodsreceivednotedefectstockin__isnull=True,
        )
        # Subquery to fetch `running_number` from `WarehouseReleaseOrderStockOut`
        wro_running_number_subquery = WarehouseReleaseOrderStockOut.objects.filter(
            transaction=OuterRef("id")
        ).values("release_order_item__release_order__system_number")[:1]  # Only take the first matching value
        # Perform bulk update on `is_wro` and `system_number_ref`
        wro_transaction_qs.update(
            is_wro=True,
            system_number_ref=Subquery(wro_running_number_subquery)
        )

        # Adjustment
        adjustment_transaction_qs = Transaction.objects.filter(
            is_adjustment=True,
        )
        # Subquery to fetch `running_number` from `WarehouseReleaseOrderStockOut`
        adjustment_running_number_subquery = AdjustmentStockIn.objects.filter(
            transaction=OuterRef("id")
        ).values("adjustment_item__adjustment__system_number")[:1]  # Only take the first matching value
        # Perform bulk update on `is_wro` and `system_number_ref`
        adjustment_transaction_qs.update(
            system_number_ref=Subquery(adjustment_running_number_subquery)
        )

        # internal transfer IN
        xfer_in_transaction_qs = Transaction.objects.filter(
            is_internal_transfer=True,
            system_number_ref__isnull=True,
        )
        xfer_in_running_number_subquery = TransferStockInOut.objects.filter(
            transaction_in=OuterRef("id")
        ).values("transfer_item__transfer__system_number")[:1]  # Only take the first matching value
        xfer_in_transaction_qs.update(
            system_number_ref=Subquery(xfer_in_running_number_subquery)
        )

        # internal transfer OUT
        xfer_out_transaction_qs = Transaction.objects.filter(
            is_internal_transfer=True,
            system_number_ref__isnull=True,
        )
        xfer_out_running_number_subquery = TransferStockInOut.objects.filter(
            transaction_out=OuterRef("id")
        ).values("transfer_item__transfer__system_number")[:1]  # Only take the first matching value
        xfer_out_transaction_qs.update(
            system_number_ref=Subquery(xfer_out_running_number_subquery)
        )


import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
from django.core.management.base import BaseCommand

DB_WMS_CONFIG = {
    "dbname": "wms_db",
    "user": "superpsql",
    "password": "password",
    "host": "localhost",
    "port": "5432",
}


class Command(BaseCommand):
    """Run the Rebuild Index command.

    This command is to rebuild all the indexing on new DB.

    """

    help = "Rebuild all indexes on the new DB"

    def handle(self, *args, **options):
        """Main function to rebuild indexes"""
        try:
            # Connect to the database
            conn = psycopg2.connect(**DB_WMS_CONFIG)
            conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)  # ✅ Ensure no transaction is used

            with conn.cursor() as cursor:
                print(f"Rebuilding index on DB {DB_WMS_CONFIG['dbname']}")
                cursor.execute("REINDEX DATABASE wms_db;")  # ✅ Works outside transaction

            print(f"Successfully rebuilt all indexes for DB {DB_WMS_CONFIG['dbname']}.")

            with conn.cursor() as cursor:
                # Query to get all GIN indexes in the database
                cursor.execute("""
                    SELECT indexname
                    FROM pg_indexes
                    WHERE schemaname = 'public'
                    AND indexdef ILIKE '%USING gin%';
                """)
                gin_indexes = cursor.fetchall()

                # Loop through all GIN indexes and rebuild them
                for index in gin_indexes:
                    index_name = index[0]
                    print(f"Rebuilding GIN index: {index_name}")
                    cursor.execute(f"REINDEX INDEX {index_name};")

            print("Successfully rebuilt all GIN indexes.")

            conn.close()  # ✅ Close connection after execution

        except psycopg2.Error as e:
            print(f"Error rebuilding indexes: {e}")

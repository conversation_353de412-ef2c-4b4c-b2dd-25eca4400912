from django.core.management.base import BaseCommand
from django.utils.translation import gettext_lazy as _

from extra_settings.models import Setting

from wms.apps.inventories.models import Item, ItemOutboundUOMDisplayConversion

class Command(BaseCommand):
    """Run the Patch all the existing item_obj's ItemOutboundUOMDisplayConversion.

    This command is Patch all the existing item_obj's ItemOutboundUOMDisplayConversion.

    """

    help = "Patch all the existing item_obj's ItemOutboundUOMDisplayConversion"

    def handle(self, *args, **options):
        """To Patch all the existing item_obj's ItemOutboundUOMDisplayConversion"""

        involved_item_qs = Item.objects.filter(outbound_uom_conversion__isnull=False)

        for involved_item in involved_item_qs:
            item_outbound_display_obj, _ = ItemOutboundUOMDisplayConversion.objects.get_or_create(
                base_value=involved_item.outbound_uom_conversion,
                uom_display_name="CT"
            )
            if item_outbound_display_obj in involved_item.outbound_uom_display_conversions.all():
                pass
            else:
                involved_item.outbound_uom_display_conversions.add(item_outbound_display_obj)

        self.stdout.write(
            self.style.SUCCESS(
                f"Successfully patched all the existing item_obj's ItemOutboundUOMDisplayConversion"
            )
        )

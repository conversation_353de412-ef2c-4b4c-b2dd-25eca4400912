import psycopg2
from psycopg2.extras import execute_values
from django.core.management.base import BaseCommand


# Database connection settings
DB_SINOFLEX_CONFIG = {
    "dbname": "sinoflex",
    "user": "sinoflex",
    "password": "izWAe^RxJQyLWB!SFhptjr3y",
    "host": "localhost",
    "port": "5432",
}

DB_WMS_CONFIG = {
    "dbname": "wms_db",
    "user": "superpsql",
    "password": "password",
    "host": "localhost",
    "port": "5432",
}

table_list = [
    {
        "table_name_map": {
            "from": "users_user_warehouses",
            "to": "users_user_warehouses",
        },
        "table_column_map": {
            "id": "id",
            "user_id": "user_id",
            "warehouse_id": "warehouse_id",
        }
    },
]


class Command(BaseCommand):
    """Run the Migrate Data command.

    This command is to migrate All existing data from Old DB to New DB

    """

    help = "Migrate Data To Warehouse Management System"

    def handle(self, *args, **options):
        """Main function to handle data migration"""
        self.stdout.write("Starting data migration...")

        for table_dict in table_list:
            data = self.fetch_data(table_dict)
            if data:
                self.insert_data(data, table_dict)
                self.reset_sequence(table_dict)
                self.stdout.write(
                    self.style.SUCCESS(
                        f"Successfully migrated {len(data)} rows from {table_dict["table_name_map"]["from"]} "
                        f"to {table_dict["table_name_map"]["to"]}."
                    )
                )
            else:
                self.stdout.write(self.style.WARNING("No data found in source table."))


    def fetch_data(self, table_dict=None):
        """Fetch selected columns from db_a"""
        source_columns = ", ".join(table_dict["table_column_map"].keys())  # Get source columns
        query = f"SELECT {source_columns} FROM {table_dict["table_name_map"]["from"]};"

        with psycopg2.connect(**DB_SINOFLEX_CONFIG) as conn:
            with conn.cursor() as cursor:
                cursor.execute(query)
                data = cursor.fetchall()

        # Convert JSON fields into a string format
        json_columns = table_dict.get("json_columns", [])  # Get JSON fields (if any)
        formatted_data = []
        for row in data:
            row = list(row)  # Convert tuple to list for modification
            for index, column_name in enumerate(table_dict["table_column_map"].keys()):
                if column_name in json_columns and isinstance(row[index], dict):
                    row[index] = json.dumps(row[index])  # Convert dict to JSON string
            formatted_data.append(tuple(row))  # Convert back to tuple

        return formatted_data

    def insert_data(self, data, table_dict=None):
        """Insert selected data into db_b with column mapping"""
        dest_columns = list(table_dict["table_column_map"].values())  # Get destination columns
        default_false_columns = table_dict.get("default_false_columns", [])  # Get boolean columns to default to False

        # Add missing boolean columns if they are not in the original column mapping
        for col in default_false_columns:
            if col not in dest_columns:
                dest_columns.append(col)

        placeholders = ", ".join(["%s"] * len(dest_columns))  # Generate %s placeholders

        updated_data = []
        for row in data:
            row_dict = dict(zip(table_dict["table_column_map"].values(), row))  # Convert row to dict

            # Assign False to any missing boolean columns
            for col in default_false_columns:
                row_dict.setdefault(col, False)

            updated_data.append(tuple(row_dict[col] for col in dest_columns))  # Convert back to tuple

        query = f"""
        INSERT INTO {table_dict["table_name_map"]["to"]} ({", ".join(dest_columns)}) VALUES %s
        ON CONFLICT (id) DO UPDATE SET
        {", ".join([f"{col} = EXCLUDED.{col}" for col in dest_columns if col != "id"])}
        RETURNING id;
        """

        with psycopg2.connect(**DB_WMS_CONFIG) as conn:
            with conn.cursor() as cursor:
                execute_values(cursor, query, updated_data)
            conn.commit()

    def reset_sequence(self, table_dict):
        """Reset sequence for auto-incrementing PK in db_b"""
        query = f"""
        SELECT setval(pg_get_serial_sequence('{table_dict["table_name_map"]["to"]}', 'id'),
                      COALESCE((SELECT MAX(id) FROM {table_dict["table_name_map"]["to"]}), 1), false);
        """

        with psycopg2.connect(**DB_WMS_CONFIG) as conn:
            with conn.cursor() as cursor:
                cursor.execute(query)
            conn.commit()

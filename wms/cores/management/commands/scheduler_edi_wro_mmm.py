import logging
import os
import shutil
from pathlib import Path
from typing import Any

from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.files import File
from django.core.mail import send_mail
from django.core.management.base import BaseCommand

from extra_settings.models import Setting

from wms.integrated_clients.mmm.inbound_actions.wro.outbound_fmc import ImportOutboundMitsubishi

logger = logging.getLogger(__name__)


UserModel = get_user_model()


class Command(BaseCommand):
    """Run the Auto creation of WRO/DO for MMM EDI (FTP channel) command.

    This command is to Auto creation of WRO/DO for MMM EDI (FTP channel)

    *NOTE:
    - this script should run once in interval of 15mins

    """

    help = "Auto creation of WRO/DO for MMM EDI (FTP channel)"

    def create_wro(self, importoutboundmitsubishi_obj: ImportOutboundMitsubishi) -> None:
        """Function to create WRO & WROItems for valid lines in *.txt file."""

        importoutboundmitsubishi_obj.cleaned()
        mitsubishi_support = UserModel.objects.get(email="<EMAIL>")

        importoutboundmitsubishi_obj.process_internal(
            user=mitsubishi_support,
            customer_document_no="",
            customer_reference="",
        )

    def send_error_email(self, importoutboundmitsubishi_obj: ImportOutboundMitsubishi, entry: Any) -> None:
        """Sends out error email if there's any invalid lines in *.txt file."""

        subject_text = f"[Warehouse Smart System - Sinoflex] Error On Importing {entry.name}"
        from_email = settings.DEFAULT_FROM_EMAIL

        long_error_string = ""
        for error in importoutboundmitsubishi_obj.error_messages_list:
            long_error_string += "- " + error + "\n"

        msg_text = (
            "Dear MMM,\n"
            "\n"
            f"There are error occurs while importing {entry.name} file and errors are on:\n"
            f"{long_error_string}"
            "\n"
            "This is an auto-generated message do not reply to this email.\n"
            f"{from_email}"
        )
        to_email = Setting.get("MITSUBISHI_WRO_ERROR_NOTIFICATION_EMAIL", default=[])

        send_mail(
            subject_text,
            msg_text,
            from_email,
            to_email,
            fail_silently=False,
        )

    def handle(self, *args, **options):

        ftp_basepath = settings.MITSUBISHI_FTP_MMM_PICK_LIST_PATH
        with os.scandir(ftp_basepath) as entries:
            for entry in entries:
                if not entry.name.startswith(".") and entry.is_file():

                    full_path = Path(f"{ftp_basepath}/{entry.name}")

                    with full_path.open(mode="rb") as f:
                        file_obj = File(f, name=full_path.name)

                        importoutboundmitsubishi_obj = ImportOutboundMitsubishi(file_obj)

                        src_path = f"{settings.MITSUBISHI_FTP_MMM_PICK_LIST_PATH}/{file_obj.name}"
                        dest_error_path = f"{settings.MITSUBISHI_FTP_MMM_PICK_LIST_ERROR_PATH}/{file_obj.name}"
                        dest_history_path = f"{settings.MITSUBISHI_FTP_MMM_PICK_LIST_HISTORY_PATH}/{file_obj.name}"

                        valid_ppl_dict = importoutboundmitsubishi_obj.is_valid()
                        entirely_valid = all(valid_ppl_dict.values())
                        entirely_invalid = not any(valid_ppl_dict.values())
                        partially_valid = any(valid_ppl_dict.values())

                        # 3 scenarios:
                        # if entirely valid -> create WRO, move file to history subdir
                        # elif entirely invalid -> send error email & move file to error subdir
                        # elif partially valid/invalid -> perform combination of both steps above
                        if entirely_valid:
                            self.create_wro(importoutboundmitsubishi_obj)
                            shutil.move(src_path, dest_history_path)
                        elif entirely_invalid:
                            self.send_error_email(importoutboundmitsubishi_obj, entry)
                            shutil.move(src_path, dest_error_path)
                        elif partially_valid:
                            self.create_wro(importoutboundmitsubishi_obj)
                            self.send_error_email(importoutboundmitsubishi_obj, entry)
                            shutil.copyfile(src_path, dest_history_path)
                            shutil.copyfile(src_path, dest_error_path)
                            os.remove(src_path)

import logging

from allauth.account.models import EmailAddress
from django.contrib.auth import get_user_model
from django.core.management.base import BaseCommand

logger = logging.getLogger(__name__)


UserModel = get_user_model()


class Command(BaseCommand):
    """Run command to update all company's email

    """

    help = "to update all company's email"

    def handle(self, *args, **options):

        try:
            # superuser
            superuser = UserModel.objects.filter(
                username="<EMAIL>",
                email="<EMAIL>",
            ).first()
            if superuser is None:
                superuser = UserModel.objects.get(email="<EMAIL>")

            email_obj_superuser = EmailAddress.objects.filter(
                user__pk=superuser.pk
            ).first()

            superuser.username = "<EMAIL>"
            superuser.email = "<EMAIL>"
            superuser.set_password("Logstack!")
            superuser.save()
            print("email_obj_superuser: ", email_obj_superuser)
            email_obj_superuser.user = superuser
            email_obj_superuser.email = superuser.email
            email_obj_superuser.save()

            # superadmin
            superadmin = UserModel.objects.filter(
                username="<EMAIL>",
                email="<EMAIL>",
            ).first()
            if superadmin is None:
                superadmin = UserModel.objects.get(email="<EMAIL>")

            email_obj_superadmin = EmailAddress.objects.filter(
                user__pk=superadmin.pk
            ).first()

            superadmin.username = "<EMAIL>"
            superadmin.email = "<EMAIL>"
            superadmin.set_password("Logstack!")
            superadmin.save()
            print("email_obj_superadmin: ", email_obj_superadmin)
            email_obj_superadmin.user = superadmin
            email_obj_superadmin.email = superadmin.email
            email_obj_superadmin.save()

            # admin
            admin = UserModel.objects.filter(
                username="<EMAIL>",
                email="<EMAIL>",
            ).first()
            if admin is None:
                admin = UserModel.objects.get(email="<EMAIL>")

            email_obj_admin = EmailAddress.objects.filter(
                user__pk=admin.pk
            ).first()

            admin.username = "<EMAIL>"
            admin.email = "<EMAIL>"
            admin.set_password("Logstack!")
            admin.save()
            print("email_obj_admin: ", email_obj_admin)
            email_obj_admin.user = admin
            email_obj_admin.email = admin.email
            email_obj_admin.save()

            # picker
            picker = UserModel.objects.filter(
                username="<EMAIL>",
                email="<EMAIL>",
            ).first()
            if picker is None:
                picker = UserModel.objects.get(email="<EMAIL>")

            email_obj_picker = EmailAddress.objects.filter(
                user__pk=picker.pk
            ).first()

            picker.username = "<EMAIL>"
            picker.email = "<EMAIL>"
            picker.set_password("Logstack!")
            picker.save()
            email_obj_picker.user = picker
            email_obj_picker.email = picker.email
            email_obj_picker.save()

            # checker
            checker = UserModel.objects.filter(
                username="<EMAIL>",
                email="<EMAIL>",
            ).first()
            if checker is None:
                checker = UserModel.objects.get(email="<EMAIL>")

            email_obj_checker = EmailAddress.objects.filter(
                user__pk=checker.pk
            ).first()

            checker.username = "<EMAIL>"
            checker.email = "<EMAIL>"
            checker.set_password("Logstack!")
            checker.save()
            email_obj_checker.user = checker
            email_obj_checker.email = checker.email
            email_obj_checker.save()

            # planner
            planner = UserModel.objects.filter(
                username="<EMAIL>",
                email="<EMAIL>",
            ).first()
            if planner is None:
                planner = UserModel.objects.get(email="<EMAIL>")

            email_obj_planner = EmailAddress.objects.filter(
                user__pk=planner.pk
            ).first()

            if email_obj_planner is None:
                email_obj_planner = EmailAddress.objects.create(
                    user=planner,
                    email="<EMAIL>",
                    verified=True,
                    primary=True,
                )

            planner.username = "<EMAIL>"
            planner.email = "<EMAIL>"
            planner.set_password("Logstack!")
            planner.save()
            email_obj_planner.user = planner
            email_obj_planner.email = planner.email
            email_obj_planner.save()

            # inventory
            inventory = UserModel.objects.filter(
                username="<EMAIL>",
                email="<EMAIL>",
            ).first()
            if inventory is None:
                inventory = UserModel.objects.get(email="<EMAIL>")

            email_obj_inventory = EmailAddress.objects.filter(
                user__pk=inventory.pk
            ).first()

            inventory.username = "<EMAIL>"
            inventory.email = "<EMAIL>"
            inventory.set_password("Logstack!")
            inventory.save()
            print("email_obj_inventory: ", email_obj_inventory)
            email_obj_inventory.user = inventory
            email_obj_inventory.email = inventory.email
            email_obj_inventory.save()

            # fmc api
            fmc = UserModel.objects.filter(
                username="<EMAIL>",
                email="<EMAIL>",
            ).first()
            if fmc is None:
                fmc = UserModel.objects.get(email="<EMAIL>")

            # email_obj_fmc = EmailAddress.objects.filter(
            #     user__pk=fmc.pk
            # ).first()

            fmc.username = "<EMAIL>"
            fmc.email = "<EMAIL>"
            fmc.set_password("Logstack!")
            fmc.save()
            # print("email_obj_fmc: ", email_obj_fmc)
            # email_obj_fmc.user = fmc
            # email_obj_fmc.email = fmc.email
            # email_obj_fmc.save()

            # mitsubishi api
            mitsubishi = UserModel.objects.filter(
                username="<EMAIL>",
                email="<EMAIL>",
            ).first()
            if mitsubishi is None:
                mitsubishi = UserModel.objects.get(email="<EMAIL>")

            # email_obj_mitsubishi = EmailAddress.objects.filter(
            #     user__pk=mitsubishi.pk
            # ).first()

            mitsubishi.username = "<EMAIL>"
            mitsubishi.email = "<EMAIL>"
            mitsubishi.set_password("Logstack!")
            mitsubishi.save()
            # print("email_obj_mitsubishi: ", email_obj_mitsubishi)
            # email_obj_mitsubishi.user = mitsubishi
            # email_obj_mitsubishi.email = mitsubishi.email
            # email_obj_mitsubishi.save()

        except Exception as e:
            self.stdout.write(self.style.WARNING(str(e)))
        else:
            self.stdout.write(self.style.SUCCESS(f"Successfully modified superuser {superuser.username}"))
            self.stdout.write(self.style.SUCCESS(f"Successfully modified superadmin {superadmin.username}"))
            self.stdout.write(self.style.SUCCESS(f"Successfully modified admin {admin.username}"))
            self.stdout.write(self.style.SUCCESS(f"Successfully modified picker {picker.username}"))
            self.stdout.write(self.style.SUCCESS(f"Successfully modified checker {checker.username}"))

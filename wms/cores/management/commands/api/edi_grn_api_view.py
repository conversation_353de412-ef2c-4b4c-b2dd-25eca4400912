""" Setup sftp server and register the view url and call the view endpoint
gcloud scheduler jobs create http process-edi-grn-mmm \
  --schedule="*/15 * * * *" \
  --uri="https://YOUR_CLOUD_RUN_URL/api/edi/process-grn-mmm/" \
  --http-method=POST \
  --attempt-deadline=540s \
  --time-zone="Asia/Singapore"
"""

from rest_framework.decorators import api_view
from rest_framework.response import Response
from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.files import File
from django.core.mail import send_mail
import logging
import os
import shutil
from pathlib import Path
from extra_settings.models import Setting
from wms.integrated_clients.mmm.inbound_actions.grn.inbound_mitsubishi import ImportInboundMitsubishi

logger = logging.getLogger(__name__)
UserModel = get_user_model()


@api_view(['POST'])
def process_edi_grn_mmm(request):
    """HTTP endpoint for processing EDI GRN files from MMM.

    This endpoint is called by Cloud Scheduler every 15 minutes.
    """
    try:
        ftp_basepath = settings.MITSUBISHI_FTP_MMM_SINO_STRF_PATH
        processed_files = 0
        error_files = 0

        with os.scandir(ftp_basepath) as entries:
            for entry in entries:
                if not entry.name.startswith(".") and entry.is_file():
                    full_path = Path(f"{ftp_basepath}/{entry.name}")

                    with full_path.open(mode="rb") as f:
                        file_obj = File(f, name=full_path.name)
                        importinboundmitsubishi_obj = ImportInboundMitsubishi(file_obj)

                        if importinboundmitsubishi_obj.is_valid():
                            # Process valid file
                            importinboundmitsubishi_obj.cleaned()
                            mitsubishi_support = UserModel.objects.get(
                                email="<EMAIL>"
                            )
                            grn_obj = importinboundmitsubishi_obj.process_internal(
                                user=mitsubishi_support,
                            )
                            send_notification(
                                instance=grn_obj,
                                message="NEW GRN created. Ready to check!",
                                user_role_list=["Checker"],
                                level="info",
                            )

                            # Move to history folder
                            shutil.move(
                                f"{settings.MITSUBISHI_FTP_MMM_SINO_STRF_PATH}/{file_obj.name}",
                                f"{settings.MITSUBISHI_FTP_MMM_SINO_STRF_HISTORY_PATH}/{file_obj.name}",
                            )
                            processed_files += 1
                        else:
                            # Handle invalid file
                            shutil.move(
                                f"{settings.MITSUBISHI_FTP_MMM_SINO_STRF_PATH}/{file_obj.name}",
                                f"{settings.MITSUBISHI_FTP_MMM_SINO_STRF_ERROR_PATH}/{file_obj.name}",
                            )

                            # Send error notification
                            long_error_string = "\n".join(
                                [f"- {error}" for error in importinboundmitsubishi_obj.error_messages_list]
                            )

                            send_mail(
                                subject=f"[Warehouse Smart System - Sinoflex] Error On Importing {entry.name}",
                                message=(
                                    f"Dear MMM,\n\n"
                                    f"There are error occurs while importing {entry.name} file and errors are on:\n"
                                    f"{long_error_string}\n\n"
                                    f"This is an auto-generated message do not reply to this email.\n"
                                    f"<EMAIL>"
                                ),
                                from_email=settings.DEFAULT_FROM_EMAIL,
                                recipient_list=Setting.get("MITSUBISHI_GRN_ERROR_NOTIFICATION_EMAIL", default=[]),
                                fail_silently=False,
                            )
                            error_files += 1

        return Response({
            "status": "success",
            "processed_files": processed_files,
            "error_files": error_files
        })

    except Exception as e:
        logger.error(f"Error processing EDI GRN files: {str(e)}")
        return Response({
            "status": "error",
            "message": str(e)
        }, status=500)

import logging
import os
import shutil
from pathlib import Path

from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.files import File
from django.core.mail import send_mail
from django.core.management.base import BaseCommand

from extra_settings.models import Setting

# from wms.cores.utils import send_notification

from wms.integrated_clients.mmm.inbound_actions.grn.inbound_mitsubishi import ImportInboundMitsubishi

logger = logging.getLogger(__name__)


UserModel = get_user_model()


class Command(BaseCommand):
    """Run the Auto creation of GRN for MMM EDI (FTP channel) command.

    This command is to Auto creation of GRN for MMM EDI (FTP channel)

    *NOTE:
    - this script should run once in interval of 15mins

    """

    help = "Auto creation of GRN for MMM EDI (FTP channel)"

    def handle(self, *args, **options):

        ftp_basepath = settings.MITSUBISHI_FTP_MMM_SINO_STRF_PATH
        with os.scandir(ftp_basepath) as entries:
            for entry in entries:
                if not entry.name.startswith(".") and entry.is_file():

                    full_path = Path(f"{ftp_basepath}/{entry.name}")

                    with full_path.open(mode="rb") as f:
                        file_obj = File(f, name=full_path.name)

                        importinboundmitsubishi_obj = ImportInboundMitsubishi(file_obj)

                        if importinboundmitsubishi_obj.is_valid() is True:
                            importinboundmitsubishi_obj.cleaned()
                            mitsubishi_support = UserModel.objects.get(email="<EMAIL>")

                            grn_obj = importinboundmitsubishi_obj.process_internal(
                                user=mitsubishi_support,
                            )
                            send_notification(
                                instance=grn_obj,
                                message="NEW GRN created. Ready to check!",
                                user_role_list=["Checker"],
                                level="info",
                            )

                            shutil.move(
                                f"{settings.MITSUBISHI_FTP_MMM_SINO_STRF_PATH}/{file_obj.name}",
                                f"{settings.MITSUBISHI_FTP_MMM_SINO_STRF_HISTORY_PATH}/{file_obj.name}",
                            )
                        else:
                            shutil.move(
                                f"{settings.MITSUBISHI_FTP_MMM_SINO_STRF_PATH}/{file_obj.name}",
                                f"{settings.MITSUBISHI_FTP_MMM_SINO_STRF_ERROR_PATH}/{file_obj.name}",
                            )

                            subject_text = f"[Warehouse Smart System - Sinoflex] Error On Importing {entry.name}"

                            long_error_string = ""
                            for error in importinboundmitsubishi_obj.error_messages_list:
                                long_error_string += "- " + error + "\n"

                            msg_text = (
                                "Dear MMM,\n"
                                "\n"
                                f"There are error occurs while importing {entry.name} file and errors are on:\n"
                                f"{long_error_string}"
                                "\n"
                                "This is an auto-generated message do not reply to this email.\n"
                                "<EMAIL>"
                            )
                            from_email = settings.DEFAULT_FROM_EMAIL
                            to_email = Setting.get("MITSUBISHI_GRN_ERROR_NOTIFICATION_EMAIL", default=[])

                            send_mail(
                                subject_text,
                                msg_text,
                                from_email,
                                to_email,
                                fail_silently=False,
                            )

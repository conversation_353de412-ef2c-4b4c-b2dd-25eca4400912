import logging
import os
import shutil
from pathlib import Path

from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.files import File
from django.core.mail import send_mail
from django.core.management.base import BaseCommand

from extra_settings.models import Setting

from wms.apps.rackings.imports.racks import ImportRacksExcel


logger = logging.getLogger(__name__)


UserModel = get_user_model()


class Command(BaseCommand):
    """Run the Initial racking structure for level 3A command.

    This command is to Initial racking structure for level 3A

    *NOTE:
    - this script should run once in interval of 15mins

    """

    help = "Initial racking structure for level 3A"

    def handle(self, *args, **options):
        # Get the path to the file in the same directory as this script
        file_path = Path(__file__).resolve().parent / "3A CHAMBER 2 - 2025.04.28.xlsx"

        self.stdout.write(
            self.style.WARNING(f"xlsx file from path: `{file_path}`")
        )
        # Open as file object in binary mode
        with open(file_path, "rb") as file_obj:
            self.imported_object = ImportRacksExcel(data=file_obj)

            if self.imported_object.is_valid():
                self.stdout.write(self.style.WARNING(f"xlsx file is valid, cleaning the file now..."))
                # To build cleaned data in import class
                self.imported_object.cleaned()
                self.stdout.write(self.style.WARNING(f"processing xlsx file now..."))
                self.imported_object.process_create_racks()

                self.stdout.write(self.style.SUCCESS(f"SUCCESSFULLY created Floor Count: {self.imported_object.summary["floor_count"]}"))
                self.stdout.write(self.style.SUCCESS(f"SUCCESSFULLY created Chamber Count: {self.imported_object.summary["chamber_count"]}"))
                self.stdout.write(self.style.SUCCESS(f"SUCCESSFULLY created Rack Count: {self.imported_object.summary["rack_count"]}"))
                self.stdout.write(self.style.SUCCESS(f"SUCCESSFULLY created Bay Count: {self.imported_object.summary["bay_count"]}"))
                self.stdout.write(self.style.SUCCESS(f"SUCCESSFULLY created Level Count: {self.imported_object.summary["level_count"]}"))
                self.stdout.write(self.style.SUCCESS(f"SUCCESSFULLY created Pallet Count: {self.imported_object.summary["pallet_count"]}"))

        file_path_2 = Path(__file__).resolve().parent / "3A CHAMBER 3 - 2025.04.28.xlsx"

        self.stdout.write(
            self.style.WARNING(f"xlsx file from path: `{file_path_2}`")
        )
        # Open as file object in binary mode
        with open(file_path_2, "rb") as file_obj_2:
            self.imported_object_2 = ImportRacksExcel(data=file_obj_2)

            if self.imported_object_2.is_valid():
                self.stdout.write(self.style.WARNING(f"xlsx file is valid, cleaning the file now..."))
                # To build cleaned data in import class
                self.imported_object_2.cleaned()
                self.stdout.write(self.style.WARNING(f"processing xlsx file now..."))
                self.imported_object_2.process_create_racks()

                self.stdout.write(self.style.SUCCESS(f"SUCCESSFULLY created Floor Count: {self.imported_object_2.summary["floor_count"]}"))
                self.stdout.write(self.style.SUCCESS(f"SUCCESSFULLY created Chamber Count: {self.imported_object_2.summary["chamber_count"]}"))
                self.stdout.write(self.style.SUCCESS(f"SUCCESSFULLY created Rack Count: {self.imported_object_2.summary["rack_count"]}"))
                self.stdout.write(self.style.SUCCESS(f"SUCCESSFULLY created Bay Count: {self.imported_object_2.summary["bay_count"]}"))
                self.stdout.write(self.style.SUCCESS(f"SUCCESSFULLY created Level Count: {self.imported_object_2.summary["level_count"]}"))
                self.stdout.write(self.style.SUCCESS(f"SUCCESSFULLY created Pallet Count: {self.imported_object_2.summary["pallet_count"]}"))


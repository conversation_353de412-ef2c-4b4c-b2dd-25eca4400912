from django import forms
from typing import Optional, Type

from django.forms import Widget

from wms.cores.forms.widget import (
    CoreTextWidget, CoreTextAreaWidget, CoreDateWidget, CoreDateTimeWidget,
    CoreNumberWidget, CoreEmailWidget, CoreURLWidget, CorePasswordWidget, CoreFileWidget,
    CoreClearableFileWidget, CoreTimeWidget, CoreCheckboxWidget, CoreSelectWidget,
    CoreSelectMultipleWidget, CoreRadioSelectWidget, CoreCheckboxSelectMultipleWidget, CoreHiddenWidget
)

from wms.cores.mixins import FormRequestMixin


class FormFieldSize:
    """Constants for form field sizes"""
    XS = "w-xs"  # 20rem - 320px
    SM = "w-sm"  # 24rem - 384px
    MD = "w-md"  # 28rem - 448px
    LG = "w-lg"  # 32rem - 512px
    XL = "w-xl"  # 36rem - 576px
    XXL = "w-2xl"  # 42rem - 672px
    FULL = "w-full"


class SizedFormField:
    """Mixin to add size support to form fields"""
    widget_class = None

    def __init__(self, *args, size: Optional[str] = None, widget_class: Optional[Type[Widget]] = None, **kwargs):

        widget_attrs = {}
        widget_class = widget_class or self.widget_class

        if widget_class:
            # Create a base widget to get its default attributes
            widget = widget_class()
            base_class = widget.attrs.get('class', '')
            widget_classes = [base_class]

            # Add disabled styling if needed
            if kwargs.get('disabled'):
                widget_classes.append('bg-theme-input-disabled cursor-not-allowed')

            # Add size class if provided
            if size:
                widget_classes.append(size)

            # Set the combined classes
            if len(widget_classes) > 1:
                widget_attrs['class'] = ' '.join(filter(None, widget_classes)).strip()
                kwargs['widget'] = widget_class(attrs=widget_attrs)

        super().__init__(*args, **kwargs)


class CoreCharField(SizedFormField, forms.CharField):
    widget_class = CoreTextWidget


class CoreTextField(SizedFormField, forms.CharField):
    widget_class = CoreTextAreaWidget


class CoreDateField(SizedFormField, forms.DateField):
    widget_class = CoreDateWidget


class CoreDateTimeField(SizedFormField, forms.DateTimeField):
    widget_class = CoreDateTimeWidget


class CoreTimeField(SizedFormField, forms.TimeField):
    widget_class = CoreTimeWidget


class CoreIntegerField(SizedFormField, forms.IntegerField):
    widget_class = CoreNumberWidget


class CoreDecimalField(SizedFormField, forms.DecimalField):
    widget_class = CoreNumberWidget


class CoreFloatField(SizedFormField, forms.FloatField):
    widget_class = CoreNumberWidget


class CoreEmailField(SizedFormField, forms.EmailField):
    widget_class = CoreEmailWidget


class CoreURLField(SizedFormField, forms.URLField):
    widget_class = CoreURLWidget


class CorePasswordField(SizedFormField, forms.CharField):
    widget_class = CorePasswordWidget


class CoreFileField(SizedFormField, forms.FileField):
    widget_class = CoreFileWidget


class CoreImageField(SizedFormField, forms.ImageField):
    widget_class = CoreClearableFileWidget


class CoreBooleanField(SizedFormField, forms.BooleanField):
    widget_class = CoreCheckboxWidget


class CoreChoiceField(SizedFormField, forms.ChoiceField):
    widget_class = CoreSelectWidget


class CoreMultipleChoiceField(SizedFormField, forms.MultipleChoiceField):
    widget_class = CoreSelectMultipleWidget


class CoreModelChoiceField(SizedFormField, forms.ModelChoiceField):
    widget_class = CoreSelectWidget


class CoreModelMultipleChoiceField(SizedFormField, forms.ModelMultipleChoiceField):
    widget_class = CoreSelectMultipleWidget


class CoreRadioChoiceField(SizedFormField, forms.ChoiceField):
    widget_class = CoreRadioSelectWidget


class CoreMultipleRadioChoiceField(SizedFormField, forms.MultipleChoiceField):
    widget_class = CoreRadioSelectWidget


class CoreCheckboxChoiceField(SizedFormField, forms.MultipleChoiceField):
    widget_class = CoreCheckboxSelectMultipleWidget


class CoreModelRadioChoiceField(SizedFormField, forms.ModelChoiceField):
    widget_class = CoreRadioSelectWidget


class CoreModelCheckboxChoiceField(SizedFormField, forms.ModelMultipleChoiceField):
    widget_class = CoreCheckboxSelectMultipleWidget


class CoreFormMixin:
    """Mixin providing core widget functionality for forms"""

    widget_map = {
        forms.TextInput: CoreTextWidget,
        forms.Textarea: CoreTextAreaWidget,
        forms.DateInput: CoreDateWidget,
        forms.DateTimeInput: CoreDateTimeWidget,
        forms.TimeInput: CoreTimeWidget,
        forms.NumberInput: CoreNumberWidget,
        forms.EmailInput: CoreEmailWidget,
        forms.URLInput: CoreURLWidget,
        forms.FileInput: CoreFileWidget,
        forms.ClearableFileInput: CoreClearableFileWidget,
        forms.CheckboxInput: CoreCheckboxWidget,
        forms.Select: CoreSelectWidget,
        forms.SelectMultiple: CoreSelectMultipleWidget,
        forms.RadioSelect: CoreRadioSelectWidget,
        forms.CheckboxSelectMultiple: CoreCheckboxSelectMultipleWidget,
        forms.HiddenInput: CoreHiddenWidget,
    }

    field_widget_map = {
        forms.CharField: CoreTextWidget,
        forms.DateField: CoreDateWidget,
        forms.DateTimeField: CoreDateTimeWidget,
        forms.TimeField: CoreTimeWidget,
        forms.IntegerField: CoreNumberWidget,
        forms.DecimalField: CoreNumberWidget,
        forms.FloatField: CoreNumberWidget,
        forms.EmailField: CoreEmailWidget,
        forms.URLField: CoreURLWidget,
        forms.FileField: CoreFileWidget,
        forms.ImageField: CoreClearableFileWidget,
        forms.BooleanField: CoreCheckboxWidget,
        forms.ChoiceField: CoreSelectWidget,
        forms.MultipleChoiceField: CoreSelectMultipleWidget,
        forms.ModelChoiceField: CoreSelectWidget,
        forms.ModelMultipleChoiceField: CoreSelectMultipleWidget,
    }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._apply_widgets()

    def _apply_widgets(self):
        """
        Apply appropriate widgets based on field type and handle special cases.
        Preserves existing widget attributes while applying core widgets.
        """
        for field_name, field in self.fields.items():
            # Skip if field already has a core widget
            if any(isinstance(field.widget, core_widget) for core_widget in
                   list(self.field_widget_map.values()) + list(self.widget_map.values())):
                continue

            # First try to match widget type
            widget_type = type(field.widget)
            if widget_type in self.widget_map:
                existing_attrs = field.widget.attrs.copy()
                field.widget = self.widget_map[widget_type]()
                field.widget.attrs.update(existing_attrs)
                continue

            # If no widget match, try field type
            field_type = type(field)
            if field_type in self.field_widget_map:
                existing_attrs = field.widget.attrs.copy()
                field.widget = self.field_widget_map[field_type]()
                field.widget.attrs.update(existing_attrs)


class CoreForm(CoreFormMixin, FormRequestMixin, forms.Form):
    """Base form class with core widget functionality
    Example:
        class MyForm(CoreForm):
            name = forms.CharField()
            email = forms.EmailField()
    """
    pass


class CoreModelForm(CoreFormMixin, FormRequestMixin, forms.ModelForm):
    """Model form class with core widget functionality
    Example:
        class MyModelForm(CoreModelForm):
            class Meta:
                model = MyModel
                fields = ['name', 'email']
    """

    def save(self, *args, **kwargs):
        """
        Override save method to track the created_by and modified_by.
        """
        if hasattr(self, "request"):
            if hasattr(self.request, "user") and self.instance.pk and hasattr(self.instance, "modified_by"):
                self.instance.modified_by = self.request.user

            if hasattr(self.request, "user") and hasattr(self.instance, "created_by") and not self.instance.created_by:
                self.instance.created_by = self.request.user
                self.instance.modified_by = None

            return super().save(*args, **kwargs)

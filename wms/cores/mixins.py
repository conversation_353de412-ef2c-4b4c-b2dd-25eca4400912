import json

from django.db.models import QuerySet
from django_tables2.export.export import TableExport
from django.http import HttpResponse
from django.db.models import Q
from django.utils.html import strip_tags
from typing import Any


class ExportTableMixin:
    """
    Enhanced mixin for table exports that ensures consistency between
    table view and exported data.
    """
    export_formats = ['csv']
    export_permission = None  # Set this to required permission string
    export_name = None
    model = None

    def get_table(self, **kwargs):
        """
        Override get_table to add export capability flag
        """
        table = super().get_table(**kwargs)
        table.isExportable = self._check_export_permission()
        return table

    def _check_export_permission(self) -> bool:
        """
        Check if current user has permission to export
        """
        if not self.export_permission:
            return True
        return self.request.user.has_perm(self.export_permission)

    def get_table_data(self) -> Any:
        """Get the data for both table display and export"""
        if not self.model:
            raise ValueError("Model attribute must be set on the view")

        data = super().get_table_data()

        # Handle search
        query = self.request.GET.get('query')
        if query and hasattr(self, 'search_fields'):
            q_objects = Q()
            for field in self.search_fields:
                q_objects |= Q(**{f"{field}__icontains": query})
            data = data.filter(q_objects)

        # Handle selected items for export
        if 'selected_items' in self.request.GET:
            selected_ids = self.request.GET['selected_items'].split(',')
            if selected_ids[0]:
                data = data.filter(id__in=selected_ids)

        return data

    def get_excluded_columns(self) -> list[str]:
        """Get all columns that should be excluded from the export.
        Returns:
            - Columns marked with exclude_from_export=True
            - Special columns (selection, actions)
            - Columns not selected in visible_columns
        """
        table = self.get_table()
        all_columns = set(table.columns.names())

        # Get columns marked for exclusion
        excluded_columns = {
            name for name, column in table.columns.items()
            if getattr(column, 'exclude_from_export', False)
        }

        # Add special columns to excluded set
        special_columns = {'selection', 'actions'}
        excluded_columns.update(special_columns)

        # Check visible columns from request
        visible_columns_param = self.request.GET.get('visible_columns', '')
        if visible_columns_param:
            try:
                visible_columns = set(json.loads(visible_columns_param))
                # Add columns that are not in visible_columns to excluded set
                excluded_columns.update(all_columns - visible_columns)
            except json.JSONDecodeError:
                pass

        return list(excluded_columns)

    def _generate_export_filename(self, export_format: str) -> str:
        """
        Generate a sanitized filename for exports with timestamp.

        Args:
            export_format: The format of the export (e.g., 'csv')

        Returns:
            str: Sanitized filename with extension
        """
        from datetime import datetime
        import re

        # Get base filename from export_name or model
        if self.export_name:
            base_name = self.export_name
        else:
            try:
                base_name = self.model._meta.verbose_name_plural
            except AttributeError:
                base_name = "export"

        # Sanitize filename
        sanitized_name = re.sub(r'[^\w\-_.]', '_', base_name.lower())

        # Add timestamp
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        return f"{sanitized_name}_{timestamp}.{export_format}"

    def render_to_response(self, context: dict, **response_kwargs: Any) -> HttpResponse:
        """Handle the export response"""
        if not self.model:
            raise ValueError("Model attribute must be set on the view")

        export_format = self.request.GET.get('export')
        if export_format:
            if not self._check_export_permission():
                return HttpResponse(
                    'You do not have permission to export this data.',
                    status=403
                )

            if export_format not in self.export_formats:
                return HttpResponse(
                    f'Export format {export_format} not supported.',
                    status=400
                )

            table = self.get_table()

            # Get columns to include in export (visible columns)
            excluded_columns = self.get_excluded_columns()

            # Create export instance with only the visible columns
            exporter = HTMLStrippedTableExport(
                export_format=export_format,
                table=table,
                exclude_columns=excluded_columns
            )

            # Generate the export response
            filename = self._generate_export_filename(export_format)
            return exporter.response(filename=filename)

        return super().render_to_response(context, **response_kwargs)


class HTMLStrippedTableExport(TableExport):
    """
    Custom TableExport class that strips HTML tags from all values.
    """

    def __init__(self, export_format, table, exclude_columns=None, dataset_kwargs=None):
        super().__init__(export_format, table, exclude_columns, dataset_kwargs)

        # Process the dataset to strip HTML from all values
        self._strip_html_from_dataset()

    def _strip_html_from_dataset(self):
        """
        Strip HTML tags from all values in the dataset.
        """
        # Get the dataset from the parent class
        dataset = self.dataset

        # Process all rows in the dataset
        for i, row in enumerate(dataset):
            # Create a new row with HTML stripped from each value and remove extra whitespace
            new_row = [strip_tags(str(value)).strip() if value is not None else value for value in row]

            # Replace the row in the dataset
            dataset[i] = new_row


class FormKwargsRequestMixin:
    """CBV mixin which puts the request into the form kwargs.
    Note: Using this mixin requires you to pop the `request` kwarg
    out of the dict in the super of your form's `__init__`.
    """

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        if hasattr(self, "request"):
            # Update the existing form kwargs dict with the request.
            kwargs.update({"request": self.request})
        return kwargs


class FormRequestMixin:
    """Takes the request from kwargs and pops it into the class attributes"""

    def __init__(self, *args, **kwargs):
        self.request = kwargs.pop("request", None)

        super().__init__(*args, **kwargs)


class MPNodeFormMixin:
    """Mixin for model that use MP_Node."""

    def _clean_cleaned_data(self):
        """delete auxilary fields not belonging to node model"""
        reference_node_id = None

        if "_ref_node_id" in self.cleaned_data:
            if self.cleaned_data["_ref_node_id"] != "0":
                reference_node_id = self.cleaned_data["_ref_node_id"]
                if reference_node_id.isdigit():
                    reference_node_id = int(reference_node_id)
            del self.cleaned_data["_ref_node_id"]

        position_type = self.cleaned_data.get("_position")
        if position_type:
            del self.cleaned_data["_position"]

        return position_type, reference_node_id

    def save(self, *args, **kwargs):
        if hasattr(self, "request"):
            if hasattr(self.request, "user") and self.instance.pk and hasattr(self.instance, "modified_by"):
                self.instance.modified_by = self.request.user

            if hasattr(self.request, "user") and hasattr(self.instance, "created_by") and not self.instance.created_by:
                self.instance.created_by = self.request.user
                self.instance.modified_by = None

        position_type, reference_node_id = self._clean_cleaned_data()

        if self.instance._state.adding:
            cl_data = {}
            for field in self.cleaned_data:
                if not isinstance(self.cleaned_data[field], (list, QuerySet)):
                    cl_data[field] = self.cleaned_data[field]
            if reference_node_id:
                reference_node = self._meta.model.objects.get(pk=reference_node_id)
                self.instance = reference_node.add_child(**cl_data)
                self.instance.move(reference_node, pos=position_type)
            else:
                self.instance = self._meta.model.add_root(**cl_data)
        else:
            self.instance.save()
            if reference_node_id:
                reference_node = self._meta.model.objects.get(pk=reference_node_id)
                self.instance.move(reference_node, pos=position_type)

        # Reload the instance
        self.instance.refresh_from_db()
        return self.instance

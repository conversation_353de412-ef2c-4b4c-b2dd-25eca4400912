from django.contrib import admin
from django.contrib.auth import get_user_model
from django.template.defaultfilters import linebreaksbr
from django.utils.translation import gettext as _

from actstream.admin import ActionAdmin
from actstream.models import Action
# from notifications.admin import Notification
# from notifications.base.admin import AbstractNotificationAdmin

from .mixins import AdminJSONFieldMixin

UserModel = get_user_model()


class UserListFilter(admin.SimpleListFilter):
    """Filter by available actor (user)."""

    title = _("actor")
    parameter_name = "actor"

    def lookups(self, request, model_admin):
        distinct_actor_object_ids = (
            model_admin.model.objects.order_by("actor_object_id")
            .values_list("actor_object_id", flat=True)
            .distinct("actor_object_id")
        )

        dropdown_list = []
        for actor_object_id in distinct_actor_object_ids:
            user = UserModel.objects.get(pk=actor_object_id)
            dropdown_list.append((actor_object_id, f"{user.__str__()} <{user.email}>"))

        return dropdown_list

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(actor_object_id=self.value())


class CustomActionAdmin(AdminJSONFieldMixin, ActionAdmin):
    """Override for ActionAdmin"""

    list_display = [
        "__str__",
        "actor",
        "verb",
        "formatted_description",
        "target",
        "public",
        "timestamp",
    ]
    list_editable = []
    list_filter = [
        UserListFilter,
        "timestamp",
        "verb",
        "target_content_type",
    ]
    search_fields = ("verb",)
    fieldsets = (
        (
            _("Action By Whom?"),
            {
                "fields": (
                    "actor_content_type",
                    "actor_object_id",
                )
            },
        ),
        (
            _("Action description."),
            {
                "fields": (
                    "verb",
                    "description",
                )
            },
        ),
        (
            _("Targeting which ContentType?"),
            {
                "fields": (
                    "target_content_type",
                    "target_object_id",
                )
            },
        ),
        (
            _("Action towards which ContentType?"),
            {
                "fields": (
                    "action_object_content_type",
                    "action_object_object_id",
                ),
            },
        ),
        (
            _("Changes's Differences"),
            {
                "fields": (
                    "data",
                ),
            },
        ),
        (
            _("Meta data"),
            {
                "fields": (
                    "public",
                    "timestamp",
                )
            },
        ),
    )

    @admin.display(
        ordering="description",
        description=_("Description"),
    )
    def formatted_description(self, obj):
        return linebreaksbr(obj.description or "")


admin.site.unregister(Action)

admin.site.register(Action, CustomActionAdmin)


# class CustomNotificationAdmin(AbstractNotificationAdmin):
#     """Override for NotificationAdmin"""

#     raw_id_fields = ("recipient",)
#     list_display = ("recipient", "actor", "level", "target", "unread", "public")
#     list_filter = (
#         "level",
#         "unread",
#         "public",
#         "timestamp",
#     )
#     search_fields = [
#         "recipient__first_name",
#         "recipient__last_name",
#         "recipient__email",
#     ]

#     def get_queryset(self, request):
#         qs = super().get_queryset(request)
#         return qs.prefetch_related("actor")


# admin.site.unregister(Notification)

# admin.site.register(Notification, CustomNotificationAdmin)

from django import forms
from django.contrib.admin import widgets
from django.contrib.admin.options import get_ul_class
from django.contrib.admin.widgets import AutocompleteSelect
from django.db.models import <PERSON><PERSON><PERSON><PERSON>
from django.utils.translation import gettext as _

from ckeditor.fields import RichText<PERSON>ield
from ckeditor.widgets import CKEditorWidget
from django_json_widget.widgets import JSONEditorWidget
from sorl.thumbnail.fields import <PERSON><PERSON>ield

from ..widgets import BaseAdminImageWidget


class AdminImageMixin:
    """This is a mix-in for admin to change `ImageField` to use BaseAdminImageWidget."""

    def formfield_for_dbfield(self, db_field, **kwargs):
        if isinstance(db_field, ImageField):
            return db_field.formfield(widget=BaseAdminImageWidget)

        return super().formfield_for_dbfield(db_field, **kwargs)


class AdminJSONFieldMixin:
    """This is a mix-in for admin to change `<PERSON><PERSON><PERSON><PERSON>` to use JSONEditorWidget."""

    def formfield_for_dbfield(self, db_field, **kwargs):
        if isinstance(db_field, JSONField):
            return db_field.formfield(widget=JSONEditorWidget())

        return super().formfield_for_dbfield(db_field, **kwargs)


class AdminTextFieldMixin:
    """This is a mix-in for admin to change `TextField` to use CKEditorWidget."""

    def formfield_for_dbfield(self, db_field, **kwargs):
        if isinstance(db_field, RichTextField):
            return db_field.formfield(widget=CKEditorWidget())

        return super().formfield_for_dbfield(db_field, **kwargs)


class AdminChoiceFieldMixin:
    """This is a mix-in for admin to add select2 class to `ChoiceField`."""

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        """
        Set formfield for ForeignKey to use Select2.
        """
        db = kwargs.get("using")

        if "widget" not in kwargs:
            if db_field.name in self.raw_id_fields:
                kwargs["widget"] = widgets.ForeignKeyRawIdWidget(db_field.remote_field, self.admin_site, using=db)
            elif db_field.name in self.radio_fields:
                kwargs["widget"] = widgets.AdminRadioSelect(
                    attrs={
                        "class": get_ul_class(self.radio_fields[db_field.name]),
                    }
                )
                kwargs["empty_label"] = _("None") if db_field.blank else None
            else:
                kwargs["widget"] = AutocompleteSelect(db_field, self.admin_site, using=db)

        if "queryset" not in kwargs:
            queryset = self.get_field_queryset(db, db_field, request)
            if queryset is not None:
                kwargs["queryset"] = queryset

        return db_field.formfield(**kwargs)

    def formfield_for_choice_field(self, db_field, request, **kwargs):
        """
        Set formfield that has declared choices to use Select2.
        """
        # If the field is named as a radio_field, use a RadioSelect
        if db_field.name in self.radio_fields:
            # Avoid stomping on custom widget/choices arguments.
            if "widget" not in kwargs:
                kwargs["widget"] = widgets.AdminRadioSelect(
                    attrs={
                        "class": get_ul_class(self.radio_fields[db_field.name]),
                    }
                )
            if "choices" not in kwargs:
                kwargs["choices"] = db_field.get_choices(include_blank=db_field.blank, blank_choice=[("", _("None"))])
        else:
            kwargs["widget"] = forms.Select(attrs={"class": "admin-select2"})

        return db_field.formfield(**kwargs)

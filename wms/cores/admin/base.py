from django.contrib import admin

from import_export.admin import ExportMixin, ImportMixin
from reversion.admin import VersionAdmin

from ..models import DISPLAY_EMPTY_VALUE
from .mixins import AdminChoiceFieldMixin, AdminImageMixin, AdminJSONFieldMixin, AdminTextFieldMixin


class ImportExportVersionModelAdmin(ImportMixin, ExportMixin, VersionAdmin):
    """
    Import, export and Version admin.
    Fixes missing link in change_list admin view :)
    """

    #: template for change_list view
    change_list_template = "admin/change_list_import_export_version.html"


class BaseModelAdmin(
    AdminImageMixin, AdminJSONFieldMixin, AdminTextFieldMixin, AdminChoiceFieldMixin, admin.ModelAdmin
):
    """Override admin to include created and modified for ModelAdmin."""

    empty_value_display = DISPLAY_EMPTY_VALUE
    exclude = [
        "created_by",
        "modified_by",
    ]

    # class Media:
    #     js = (
    #         "cores/js/fancybox/jquery.fancybox.min.js",  # fancyBox v3.5.7
    #         "cores/js/scripts.js",  # Custom admin scripts
    #     )
    #     css = {
    #         "all": (
    #             "cores/js/fancybox/jquery.fancybox.min.css",  # fancyBox v3.5.7
    #             "plugins/fontawesome-free/css/all.min.css",  # Font Awesome Free 5.15.4
    #         )
    #     }

    def get_list_display(self, request):
        """Append additional fields to the self.list_display."""
        list_display = self.list_display

        if "admin_created" not in list_display:
            list_display += ("admin_created",)
        if "admin_modified" not in list_display:
            list_display += ("admin_modified",)

        return list_display

    def get_form(self, request, *args, **kwargs):
        form = super().get_form(request, *args, **kwargs)
        form.current_user = request.user

        return form

    def save_model(self, request, obj, form, change):
        obj.modified_by = request.user

        if not obj.created_by:
            obj.created_by = request.user
            obj.modified_by = None

        super().save_model(request, obj, form, change)

    def save_formset(self, request, form, formset, change):
        instances = formset.save(commit=False)

        for obj in formset.deleted_objects:
            obj.delete()

        for instance in instances:
            if instance.pk and hasattr(instance, "modified_by"):
                instance.modified_by = request.user

            if hasattr(instance, "created_by") and not instance.created_by:
                instance.created_by = request.user
                instance.modified_by = None

            instance.save()

        formset.save_m2m()


class BaseTabularInline(
    AdminImageMixin, AdminJSONFieldMixin, AdminTextFieldMixin, AdminChoiceFieldMixin, admin.TabularInline
):
    """Override admin to include created and modified for TabularInline."""

    readonly_fields = [
        "created_by",
        "created",
        "modified_by",
        "modified",
    ]


class BaseStackedInline(
    AdminImageMixin, AdminJSONFieldMixin, AdminTextFieldMixin, AdminChoiceFieldMixin, admin.StackedInline
):
    """Override admin to include created and modified for StackedInline."""

    readonly_fields = [
        "created_by",
        "created",
        "modified_by",
        "modified",
    ]

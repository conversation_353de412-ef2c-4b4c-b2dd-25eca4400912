from typing import List
import logging
from django.core.mail import send_mail
from django.conf import settings
from django.template.loader import render_to_string

class GroupEmailHandler(logging.Handler):
    """
    A custom logging handler that sends emails to a group of recipients.
    """
    def __init__(self, emails: List[str] = None, subject_prefix: str = None):
        super().__init__()
        self.emails = emails or []
        self.subject_prefix = subject_prefix or settings.EMAIL_SUBJECT_PREFIX

    def emit(self, record):
        try:
            subject = f"{self.subject_prefix}{record.levelname}"

            # Create message context
            context = {
                'level': record.levelname,
                'message': self.format(record),
                'module': record.module,
                'function': record.funcName,
                'path': record.pathname,
                'line': record.lineno,
            }

            # Render email templates
            html_message = render_to_string('logging/integration_error_email.html', context)
            plain_message = render_to_string('logging/integration_error_email.txt', context)

            send_mail(
                subject=subject,
                message=plain_message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=self.emails,
                html_message=html_message,
            )
        except Exception as e:
            # Don't let a mail failure cause the entire logging to fail
            print(f"Failed to send integration error email: {str(e)}")

import logging
from pathlib import Path

from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.files import File
from django.core.mail import send_mail
from django.db import transaction
from django.db.models.signals import post_save
from django.dispatch import receiver

from extra_settings.models import Setting
from pyas2.models import Mdn

from wms.cores.utils import localtime_now

from wms.integrated_clients.fmc.inbound_actions.item.material_master_fmc import ImportMaterialMasterFMC

from wms.integrated_clients.fmc.inbound_actions.grn.inbound_fmc import ImportInboundFMC
from wms.integrated_clients.fmc.inbound_actions.wro.outbound_fmc import ImportOutboundFMC

logger = logging.getLogger(__name__)

UserModel = settings.AUTH_USER_MODEL


@receiver(post_save, sender=Mdn)
def trigger_action_from_as2(sender, instance: Mdn, created=False, **kwargs) -> None:
    # Setting.get("EDI_ENABLED", default=False) must be first condition.
    if Setting.get("EDI_ENABLED", default=False):

        def perform_action():

            if instance.message.direction == "IN":
                UserModel = get_user_model()

                uploaded_xml_file = instance.message.payload

                xml_full_path = Path(f"{settings.MEDIA_ROOT}/{uploaded_xml_file.name}")

                if "INB" in (uploaded_xml_file.name):

                    with xml_full_path.open(mode="r") as fileptr:
                        file_obj = File(fileptr, name=xml_full_path.name)

                        importinboundfmc_obj = ImportInboundFMC(file_obj)

                        if importinboundfmc_obj.is_valid() is True:
                            importinboundfmc_obj.cleaned()
                            fmc_support = UserModel.objects.get(email="<EMAIL>")

                            grn_obj = importinboundfmc_obj.process_internal(
                                user=fmc_support,
                            )
                            # send_notification(
                            #     instance=grn_obj,
                            #     message="NEW GRN created. Ready to check!",
                            #     user_role_list=["Checker"],
                            #     level="info",
                            # )
                        else:
                            subject_text = f"[Warehouse Smart System - Sinoflex] Error On FMC EDI {file_obj.name}"

                            long_error_string = ""
                            for error in importinboundfmc_obj.error_messages_list:
                                long_error_string += "- " + error + "\n"

                            msg_text = (
                                "Dear Sinoflex,\n"
                                "\n"
                                f"There are error occurs on decoding IBD({importinboundfmc_obj.inbound_delivery_no}) "
                                f"in {file_obj.name} file and errors are:\n"
                                f"{long_error_string}"
                                "\n"
                                "Download XML: "
                                f"https://sinoflex.snapdec.com/pyas2/download/message_payload/{instance.message.pk}/"
                                "\n"
                                "\n"
                                "This is an auto-generated message do not reply to this email.\n"
                                "<EMAIL>"
                            )
                            from_email = settings.DEFAULT_FROM_EMAIL
                            to_email = Setting.get("FMC_GRN_ERROR_NOTIFICATION_EMAIL", default=[])

                            send_mail(
                                subject_text,
                                msg_text,
                                from_email,
                                to_email,
                                fail_silently=False,
                            )

                elif "OUT" in (uploaded_xml_file.name):

                    with xml_full_path.open(mode="r") as fileptr:
                        file_obj = File(fileptr, name=xml_full_path.name)

                        importoutboundfmc_obj = ImportOutboundFMC(file_obj)
                        fmc_support = UserModel.objects.get(email="<EMAIL>")

                        if importoutboundfmc_obj.is_valid(user=fmc_support) is True:
                            importoutboundfmc_obj.cleaned()

                            warehouse_release_order = importoutboundfmc_obj.process_internal(
                                user=fmc_support,
                            )
                            # send_notification(
                            #     instance=warehouse_release_order,
                            #     message=f"NEW WRO created ({warehouse_release_order.numbering}). Ready to pick!",
                            #     user_role_list=["Picker"],
                            #     warehouses=warehouse_release_order.warehouses.all(),
                            #     level="info",
                            # )
                        else:
                            subject_text = f"[Warehouse Smart System - Sinoflex] Error On FMC EDI {file_obj.name}"

                            long_error_string = ""
                            for error in importoutboundfmc_obj.error_messages_list:
                                long_error_string += "- " + error + "\n"

                            msg_text = (
                                "Dear Sinoflex,\n"
                                "\n"
                                f"There are error occurs on decoding DN({importoutboundfmc_obj.outbound_delivery_no}) "
                                f"in {file_obj.name} file and errors are:\n"
                                f"{long_error_string}"
                                "\n"
                                "Download XML: "
                                f"https://sinoflex.snapdec.com/pyas2/download/message_payload/{instance.message.pk}/"
                                "\n"
                                "\n"
                                "This is an auto-generated message do not reply to this email.\n"
                                "<EMAIL>"
                            )
                            from_email = settings.DEFAULT_FROM_EMAIL
                            to_email = Setting.get("FMC_WRO_ERROR_NOTIFICATION_EMAIL", default=[])

                            send_mail(
                                subject_text,
                                msg_text,
                                from_email,
                                to_email,
                                fail_silently=False,
                            )

                elif "MM" in (uploaded_xml_file.name):

                    with xml_full_path.open(mode="r") as fileptr:
                        file_obj = File(fileptr, name=xml_full_path.name)

                        import_material_fmc_obj = ImportMaterialMasterFMC(file_obj)
                        fmc_support = UserModel.objects.get(email="<EMAIL>")

                        if import_material_fmc_obj.is_valid() is True:
                            import_material_fmc_obj.cleaned(mdn=instance)
                            import_material_fmc_obj.process_internal(
                                user=fmc_support,
                            )
                        else:
                            subject_text = f"[Warehouse Smart System - Sinoflex] Error On FMC EDI {file_obj.name}"

                            long_error_string = ""
                            for error in import_material_fmc_obj.error_messages_list:
                                long_error_string += "- " + error + "\n"

                            msg_text = (
                                "Dear Sinoflex,\n"
                                "\n"
                                f"There are error occurs while decoding the {file_obj.name} file and errors are on:\n"
                                f"{long_error_string}"
                                "\n"
                                "Download XML: "
                                f"https://sinoflex.snapdec.com/pyas2/download/message_payload/{instance.message.pk}/"
                                "\n"
                                "This is an auto-generated message do not reply to this email.\n"
                                "<EMAIL>"
                            )
                            from_email = settings.DEFAULT_FROM_EMAIL
                            to_email = Setting.get("FMC_GRN_ERROR_NOTIFICATION_EMAIL", default=[])

                            send_mail(
                                subject_text,
                                msg_text,
                                from_email,
                                to_email,
                                fail_silently=False,
                            )
                elif "smime" in (uploaded_xml_file.name):
                    pass
                else:
                    subject_text = f"[Warehouse Smart System - Sinoflex] Error On FMC EDI {uploaded_xml_file.name}"

                    msg_text = (
                        "Dear Sinoflex,\n"
                        "\n"
                        f"There are error occurs while decoding the {uploaded_xml_file.name} file and errors are:\n"
                        "File name is invalid. System only accept file name with prefix of 'INB_' / 'OUT_' / 'MM_' ."
                        "\n"
                        "This is an auto-generated message do not reply to this email.\n"
                        "<EMAIL>"
                    )
                    from_email = settings.DEFAULT_FROM_EMAIL
                    to_email = Setting.get("FMC_GRN_ERROR_NOTIFICATION_EMAIL", default=[])

                    send_mail(
                        subject_text,
                        msg_text,
                        from_email,
                        to_email,
                        fail_silently=False,
                    )

        if created:
            transaction.on_commit(perform_action)

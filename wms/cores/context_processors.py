from django.conf import settings
from wms.cores.models import DISPLAY_EMPTY_VALUE


def settings_context(_request):
    """Settings available by default to the templates context."""
    # Note: we intentionally do NOT expose the entire settings
    # to prevent accidental leaking of sensitive information
    return {
        "DEBUG": settings.DEBUG,
        "DISPLAY_EMPTY_VALUE": DISPLAY_EMPTY_VALUE,
    }

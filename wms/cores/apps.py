import contextlib

from django.apps import AppConfig
from django.conf import settings
from django.utils.translation import gettext_lazy as _


class CoresConfig(AppConfig):
    name = "wms.cores"
    verbose_name = _("Cores")

    def ready(self):
        try:
            import wms.cores.signals  # noqa F401
        except ImportError:
            pass

        if settings.DEBUG:
            from wms.cores.local_tasks import local_task_runner
            local_task_runner.start()


        from actstream import registry

        from wms.apps.adjustments.models import Adjustment, AdjustmentItem, AdjustmentStockIn
        from wms.apps.consignees.models import BillingAddress as ConsigneeBillingAddress
        from wms.apps.consignees.models import Consignee, ConsigneeContact
        from wms.apps.consignees.models import ShippingAddress as ConsigneeShippingAddress
        from wms.apps.consignors.models import BillingAddress as ConsignorBillingAddress
        from wms.apps.consignors.models import Consignor, ConsignorContact
        from wms.apps.consignors.models import ShippingAddress as ConsignorShippingAddress
        # from wms.apps.count_sheets.models import CountSheet
        from wms.apps.inventories.models import Item, ItemCategory, ItemPhoto, Stock, Transaction
        from wms.apps.receives.models import (
            GoodsReceivedNote,
            GoodsReceivedNoteDefect,
            GoodsReceivedNoteItem,
            GoodsReceivedNoteStockIn,
        )
        from wms.apps.releases.models import (
            DeliveryOrder,
            WarehouseReleaseOrder,
            WarehouseReleaseOrderItem,
            WarehouseReleaseOrderItemPicker,
            WarehouseReleaseOrderStockOut,
        )
        from wms.apps.settings.models import Branch, Organization, UnitConversion, UnitOfMeasure, Warehouse
        from wms.apps.transfers.models import Transfer, TransferItem, TransferStockInOut
        from wms.apps.pickings.models import PickingList, PickingListItem
        from wms.apps.users.models import User

        registry.register(
            # users apps
            User,
            # inventories apps
            ItemCategory,
            Item,
            ItemPhoto,
            Stock,
            Transaction,
            # # receives apps
            GoodsReceivedNote,
            GoodsReceivedNoteDefect,
            GoodsReceivedNoteItem,
            GoodsReceivedNoteStockIn,
            # releases apps
            DeliveryOrder,
            WarehouseReleaseOrder,
            WarehouseReleaseOrderItem,
            WarehouseReleaseOrderItemPicker,
            WarehouseReleaseOrderStockOut,
            # adjustment apps
            Adjustment,
            AdjustmentItem,
            AdjustmentStockIn,
            # transfers apps
            Transfer,
            TransferItem,
            TransferStockInOut,
            # consignors apps
            Consignor,
            ConsignorContact,
            ConsignorBillingAddress,
            ConsignorShippingAddress,
            # consignees apps
            Consignee,
            ConsigneeContact,
            ConsigneeBillingAddress,
            ConsigneeShippingAddress,
            # settings apps
            Branch,
            Organization,
            Warehouse,
            UnitOfMeasure,
            UnitConversion,
            # # count_sheets apps
            # CountSheet,
            # picking_lists apps
            PickingList,
            PickingListItem
        )

        # To prevent related activity stream being removed when object is deleted.

        def not_target_actions(field):
            return field.name not in ["target_actions", "action_object_actions"]

        # users apps
        User._meta.private_fields = list(filter(not_target_actions, User._meta.private_fields))
        # inventories apps
        ItemCategory._meta.private_fields = list(filter(not_target_actions, ItemCategory._meta.private_fields))
        Item._meta.private_fields = list(filter(not_target_actions, Item._meta.private_fields))
        ItemPhoto._meta.private_fields = list(filter(not_target_actions, ItemPhoto._meta.private_fields))
        Stock._meta.private_fields = list(filter(not_target_actions, Stock._meta.private_fields))
        Transaction._meta.private_fields = list(filter(not_target_actions, Transaction._meta.private_fields))
        # # receives apps
        GoodsReceivedNote._meta.private_fields = list(
            filter(not_target_actions, GoodsReceivedNote._meta.private_fields)
        )
        GoodsReceivedNoteDefect._meta.private_fields = list(
            filter(not_target_actions, GoodsReceivedNoteDefect._meta.private_fields)
        )
        GoodsReceivedNoteItem._meta.private_fields = list(
            filter(not_target_actions, GoodsReceivedNoteItem._meta.private_fields)
        )
        GoodsReceivedNoteStockIn._meta.private_fields = list(
            filter(not_target_actions, GoodsReceivedNoteStockIn._meta.private_fields)
        )
        # # releases apps
        # DeliveryOrder._meta.private_fields = list(filter(not_target_actions, DeliveryOrder._meta.private_fields))
        # WarehouseReleaseOrder._meta.private_fields = list(
        #     filter(not_target_actions, WarehouseReleaseOrder._meta.private_fields)
        # )
        # WarehouseReleaseOrderItem._meta.private_fields = list(
        #     filter(not_target_actions, WarehouseReleaseOrderItem._meta.private_fields)
        # )
        # WarehouseReleaseOrderItemPicker._meta.private_fields = list(
        #     filter(not_target_actions, WarehouseReleaseOrderItemPicker._meta.private_fields)
        # )
        # WarehouseReleaseOrderStockOut._meta.private_fields = list(
        #     filter(not_target_actions, WarehouseReleaseOrderStockOut._meta.private_fields)
        # )
        # adjustment apps
        Adjustment._meta.private_fields = list(filter(not_target_actions, Adjustment._meta.private_fields))
        AdjustmentItem._meta.private_fields = list(filter(not_target_actions, AdjustmentItem._meta.private_fields))
        AdjustmentStockIn._meta.private_fields = list(
            filter(not_target_actions, AdjustmentStockIn._meta.private_fields)
        )
        # transfers apps
        Transfer._meta.private_fields = list(filter(not_target_actions, Transfer._meta.private_fields))
        TransferItem._meta.private_fields = list(filter(not_target_actions, TransferItem._meta.private_fields))
        TransferStockInOut._meta.private_fields = list(
            filter(not_target_actions, TransferStockInOut._meta.private_fields)
        )
        # consignors apps
        Consignor._meta.private_fields = list(filter(not_target_actions, Consignor._meta.private_fields))
        ConsignorContact._meta.private_fields = list(filter(not_target_actions, ConsignorContact._meta.private_fields))
        ConsignorBillingAddress._meta.private_fields = list(
            filter(not_target_actions, ConsignorBillingAddress._meta.private_fields)
        )
        ConsignorShippingAddress._meta.private_fields = list(
            filter(not_target_actions, ConsignorShippingAddress._meta.private_fields)
        )
        # consignees apps
        Consignee._meta.private_fields = list(filter(not_target_actions, Consignee._meta.private_fields))
        ConsigneeContact._meta.private_fields = list(filter(not_target_actions, ConsigneeContact._meta.private_fields))
        ConsigneeBillingAddress._meta.private_fields = list(
            filter(not_target_actions, ConsigneeBillingAddress._meta.private_fields)
        )
        ConsigneeShippingAddress._meta.private_fields = list(
            filter(not_target_actions, ConsigneeShippingAddress._meta.private_fields)
        )
        # settings apps
        Branch._meta.private_fields = list(filter(not_target_actions, Branch._meta.private_fields))
        Organization._meta.private_fields = list(filter(not_target_actions, Organization._meta.private_fields))
        Warehouse._meta.private_fields = list(filter(not_target_actions, Warehouse._meta.private_fields))
        UnitOfMeasure._meta.private_fields = list(filter(not_target_actions, UnitOfMeasure._meta.private_fields))
        UnitConversion._meta.private_fields = list(filter(not_target_actions, UnitConversion._meta.private_fields))
        # # count_sheets apps
        # CountSheet._meta.private_fields = list(filter(not_target_actions, CountSheet._meta.private_fields))
        # Picking apps
        PickingList._meta.private_fields = list(filter(not_target_actions, PickingList._meta.private_fields))
        PickingListItem._meta.private_fields = list(filter(not_target_actions, PickingListItem._meta.private_fields))

import base64
import logging
import os
from decimal import Decimal
from io import BytesIO

from django.conf import settings  # noqa F401 : Needed for generate_system_number
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group
from django.contrib.contenttypes.models import ContentType
from django.core.exceptions import ImproperlyConfigured
from django.db.models import Q
from django.shortcuts import resolve_url
from django.utils import timezone
from django.utils.http import url_has_allowed_host_and_scheme
from django.utils.safestring import mark_safe

import barcode
import qrcode
import qrcode.image.svg
from barcode.writer import SVGWriter
from extra_settings.models import Setting
# from notifications.models import Notification
# from notifications.signals import notify
from sorl.thumbnail.shortcuts import get_thumbnail

logger = logging.getLogger(__name__)


def safe_referrer(request, default):
    """Takes the request and a default URL. Returns HTTP_REFERER if it's safe
    to use and set, and the default URL otherwise.

    The default URL can be a model with get_absolute_url defined, a urlname,
    or a regular URL.
    """
    referrer = request.headers.get("referer")
    if referrer and url_has_allowed_host_and_scheme(referrer, allowed_hosts={request.get_host()}):
        return referrer
    if default:
        # Try to resolve. Can take a model instance, Django URL name or URL.
        return resolve_url(default)
    else:
        # Allow passing in '' and None as default.
        return default


def get_img_extension(img):
    """Return ext based on given image."""
    ext = "JPEG"
    try:
        aux_ext = str(img).split(".")
        if aux_ext[len(aux_ext) - 1].lower() == "png":
            ext = "PNG"
        elif aux_ext[len(aux_ext) - 1].lower() == "gif":
            ext = "GIF"
    except Exception:  # pragma: no cover
        pass

    return ext


def generate_thumbnail(img, img_size="x32"):
    """Generate image thumbnail based on given image.

    Return mark_safe string.
    """
    from .models import DISPLAY_EMPTY_VALUE

    if img and hasattr(img, "url"):
        ext = get_img_extension(img)
        thumb = get_thumbnail(img, img_size, upscale=False, crop="center", format=ext)
        filename = os.path.basename(img.name)

        return mark_safe(
            f'<a href="{img.url}" data-fancybox="gallery" data-caption="{filename}" style="display: inline-block;">'
            f'<img width="{thumb.width}" height="{thumb.height}" src="{thumb.url}" '
            'style="border: 1px solid #CCC; padding: 2px;" />'
            "</a>"
        )
    else:
        return DISPLAY_EMPTY_VALUE


def localtime_now():
    """Return local datetime object.

    Example:

    timezone.now()
    >>> datetime.datetime(2021, 9, 19, 13, 46, 56, 499167, tzinfo=<UTC>)

    timezone.localtime(timezone.now())
    >>> datetime.datetime(2021, 9, 19, 21, 47, 5, 693533, tzinfo=<DstTzInfo 'Asia/Kuala_Lumpur' +08+8:00:00 STD>)
    """
    return timezone.localtime(timezone.now())


def localtime_now_date_datetime():
    """Return local datetime object but reset to date info only."""
    return localtime_now().replace(hour=0, minute=0, second=0, microsecond=0)


def generate_system_number(obj):
    """To generate system_number for given obj.

    :param obj:         Object, from any model based on settings
    :return:            string

    Available object to use in format:
    * today
    """
    today = localtime_now()  # noqa
    # system_number_settings = eval(f"settings.NUMBERING_{obj.__class__.__name__.upper()}")  # noqa
    # system_number_pattern = eval(f"f'{system_number_settings}'")

    # return system_number_pattern

    system_number_settings = Setting.get(f"NUMBERING_{obj.__class__.__name__.upper()}", default=None)  # noqa

    if system_number_settings is None:
        raise ImproperlyConfigured(f"Please setup settings for `NUMBERING_{obj.__class__.__name__.upper()}`.")

    system_number_pattern = eval(f"f'{system_number_settings}'")

    return system_number_pattern


def generate_qr(qr_string, box_size=10):
    """To generate QR svg with given string.

    :param qr_string:       String
    :param box_size:        Integer, size of the QR
    :return:                str in base64 format
    """
    # Write the QR to a binary stream
    stream = BytesIO()
    factory = qrcode.image.svg.SvgImage

    code = qrcode.make(qr_string, image_factory=factory, box_size=box_size)
    code.save(stream)

    base64_image = base64.b64encode(stream.getvalue()).decode()
    return "data:image/svg+xml;utf8;base64," + base64_image


def generate_barcode(barcode_text, options={}):
    """To generate barcode svg with given string.

    :param barcode_text:    String
    :param options:         Optional settings for the barcode
    :return:                svg code

    Example of options:
    - https://python-barcode.readthedocs.io/en/stable/writers.html#common-writer-options
      options = {
          "module_width": 0.8,
          "module_height": 15.0,
          "quiet_zone": 2.5,
          "font_size": 16,
          "text_distance": 5,
      }
    """
    # Write the barcode to a binary stream
    stream = BytesIO()

    code = barcode.get("code128", barcode_text, writer=SVGWriter())
    code.write(stream, options=options)

    svg = stream.getvalue().decode()

    return svg


# def send_notification(instance, message="", user_role_list=[], warehouses=None, level="info"):
#     """
#     general send notification function.

#     Usage:
#     - instance is the trigger point object
#     - message is the prepared string to be send to the receiver
#     - user_role_list is the list that holds a list of strings that points to the roles
#         - Example: user_role_list = ["Superadmin", "Admin", "Picker", "Checker"]
#     - warehouses is the Warehouse QuerySet to be filter on the user.
#         - Example: QuerySet of ["KL", "PJ"]. user that belongs to
#                    one of the warehouse will be able to receive the notification
#     - level is how the colors that the notification will look like
#         - Example: "info", "success", "warning", "error"
#     """
#     from wms.apps.settings.models import Warehouse

#     UserModel = get_user_model()

#     if warehouses is None:
#         warehouses = Warehouse.objects.all()
#     today_date = localtime_now().strftime("%d/%m/%Y")
#     final_message = f"[{today_date}] - {message}"

#     group = Group.objects.filter(name__in=user_role_list)
#     content_type = ContentType.objects.get_for_model(instance)
#     notifications = Notification.objects.filter(
#         actor_content_type=content_type,
#         actor_object_id=instance.pk,
#         unread=True,
#         verb=final_message,
#         level="info",
#         public=False,
#     )

#     users = (
#         UserModel.objects.filter(warehouses__in=warehouses, groups__in=group)
#         .exclude(notifications__in=notifications)
#         .distinct()
#     )
#     if users.exists():
#         # Create notification
#         notify.send(instance, recipient=users, verb=final_message, level=level, public=False)


def lf_to_crlf(final_file_path):
    """
    general convert LF(unix) file format to CRLF(Windows) format function.

    Usage:
    - final_file_path = relative or absolute file path
    """

    # replacement strings
    WINDOWS_LINE_ENDING = b"\r\n"
    UNIX_LINE_ENDING = b"\n"

    with open(final_file_path, "rb") as open_file:
        content = open_file.read()

    # Unix ➡ Windows
    content = content.replace(UNIX_LINE_ENDING, WINDOWS_LINE_ENDING)

    with open(final_file_path, "wb") as open_file:
        open_file.write(content)


def crlf_to_lf(final_file_path):
    """
    general convert CRLF(Windows)format to LF(unix) file format function.

    Usage:
    - final_file_path = relative or absolute file path
    """

    # replacement strings
    WINDOWS_LINE_ENDING = b"\r\n"
    UNIX_LINE_ENDING = b"\n"

    with open(final_file_path, "rb") as open_file:
        content = open_file.read()

    # Windows ➡ Unix
    content = content.replace(WINDOWS_LINE_ENDING, UNIX_LINE_ENDING)

    with open(final_file_path, "wb") as open_file:
        open_file.write(content)


def convert_camel_case_to_space(input_string):
    """
    Convert camel case string to space.
    Input: BillingAddress
    Output: Billing Address
    """
    result = ""
    for char in input_string:
        if char.isupper():
            result += " " + char
        else:
            result += char
    return result.strip()


def uom_choices_symbol(item=None) -> list:
    """Prepare UOM choices symbol for forms (with blank choice dash)."""
    from django.db.models import BLANK_CHOICE_DASH

    from wms.apps.inventories.models import Item
    from wms.apps.inventories.models.item import UnitOfMeasure

    if item is not None and isinstance(item, Item):
        uom_queryset = item.get_available_uom().all()
    else:
        uom_queryset = UnitOfMeasure.objects.all()

    uom_choices = BLANK_CHOICE_DASH + list(uom_queryset.values_list("pk", "symbol"))
    return uom_choices


def get_item_choices() -> list:
    """Function to return list of ITEM choices."""
    from wms.apps.inventories.models.item import Item

    return list(Item.objects.values_list("pk", "code"))


def get_uom_choices() -> list:
    """Function to return list of UOM choices."""
    from wms.apps.inventories.models.item import UnitOfMeasure

    return list(UnitOfMeasure.objects.values_list("pk", "symbol"))


def get_rack_choices(item) -> list:
    """Function to return list of rack dropdown choices based on given item."""
    rack_choices = item.leaf_node_racks.distinct().values_list("pk", "full_name")
    return rack_choices


def get_user_warehouse_choices(user) -> list:
    """Function to return list of warehouse dropdown choices based on the user in ListView's advance filter."""
    from wms.apps.settings.models import Warehouse

    if user.is_superuser is True:
        warehouses = Warehouse.objects.filter(is_storage=True).all()
    else:
        warehouses = user.warehouses.filter(is_storage=True)

    warehouse_choices = [(warehouse.pk, warehouse.full_name) for warehouse in warehouses]

    return warehouse_choices

def get_item_consignor_choices() -> list:
    """Function to return list of Item's consignor dropdown choices in ListView's advance filter."""
    from wms.apps.inventories.models import Item

    items = Item.objects.values(
        "consignor__code",
        "consignor__display_name"
    ).order_by("consignor__display_name").distinct()

    consignor_choices = [
        (item["consignor__code"], item["consignor__display_name"])
        for item in items
        if item["consignor__code"] and item["consignor__display_name"]
    ]

    return consignor_choices

def get_item_brand_choices() -> list:
    """Function to return list of Item's brand dropdown choices in ListView's advance filter."""
    from wms.apps.inventories.models import Item

    items = Item.objects.values("brand").order_by("brand").distinct()

    item_brand_choices = [(item["brand"], item["brand"]) for item in items if item["brand"]]

    return item_brand_choices


def get_group_choices() -> list:
    """Function to return list of warehouse dropdown choices in ListView's advance filter."""
    from django.contrib.auth.models import Group

    groups = Group.objects.exclude(Q(name="SINOFLEX") | Q(name="Consignor")).all()

    group_choices = [(group.pk, group.name) for group in groups]

    return group_choices


def get_warehouse_choices() -> list:
    """Function to return list of warehouse choices for filter dropdowns.

    Returns a list of tuples (id, full_name) for warehouses with is_storage=True.
    """
    from wms.apps.settings.models import Warehouse

    warehouses = Warehouse.objects.filter(is_storage=True).all()
    warehouse_choices = [(warehouse.pk, warehouse.full_name) for warehouse in warehouses]

    return warehouse_choices


def get_consignor_choices() -> list:
    """Function to return list of Item's consignor dropdown choices in ListView's advance filter."""
    from wms.apps.consignors.models import Consignor

    consignors = Consignor.objects.values(
        "pk",
        "display_name"
    ).order_by("display_name").distinct()

    consignor_choices = [
        (consignor["pk"], consignor["display_name"])
        for consignor in consignors
        if consignor["pk"] and consignor["display_name"]
    ]

    return consignor_choices


def get_rackstorage_choices() -> list:
    """Function to return list of rackstorage choices for filter dropdowns.

    Returns a list of tuples (id, full_name) for rackstorages with is_storage=True.
    """
    from wms.apps.rackings.models import RackStorage

    rackstorages = RackStorage.objects.all()
    rackstorage_choices = [(rackstorage.pk, rackstorage.__str__) for rackstorage in rackstorages]

    return rackstorage_choices


def format_decimal_values(value: Decimal) -> Decimal:
    """
    Returns the formatted argument `value` based on
    simplest decimal values without trailing zeros.
    Example:

    >>> format_decimal_values(Decimal("10.000000"))
    >>> Decimal('10')
    >>>
    >>> format_decimal_values(Decimal("10.003300"))
    >>> Decimal('10.0033')
    >>>
    >>> format_decimal_values(Decimal("10.120000"))
    >>> Decimal('10.12')
    """
    return value.quantize(Decimal(1)) if value == value.to_integral() else value.normalize()


def normalize_decimal(value) -> Decimal:
    # if isinstance(value, Decimal) and Setting.get("NORMALIZE_QUANTITY") is True:
    if isinstance(value, Decimal):
        return format_decimal_values(value)
    return value


def calculate_base_uom_to_expected_conversion_uom(uom_display_quantity, uom_display_obj_base_value):
    uom_display_number = int(uom_display_quantity)
    base_display_number = (uom_display_quantity - uom_display_number) * uom_display_obj_base_value
    return uom_display_number, int(base_display_number)

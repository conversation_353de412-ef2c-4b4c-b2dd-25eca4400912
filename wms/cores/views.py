import json
from typing import Optional, Dict, Any, List, Type

from django.contrib import messages
from django.contrib.auth.mixins import PermissionRequiredMixin
from django.core.exceptions import ImproperlyConfigured
from django.template.loader import render_to_string
from django.urls import reverse, NoReverseMatch
from django.views.generic import ListView, CreateView, DetailView, UpdateView, FormView, TemplateView
from django_filters import FilterSet
from django_tables2 import SingleTableView, SingleTableMixin
from django.db.models import QuerySet, Q
from django.http import HttpResponse
from django.shortcuts import get_object_or_404
from rest_framework.pagination import PageNumberPagination
from rest_framework.response import Response

from wms.cores.mixins import ExportTableMixin, FormKwargsRequestMixin


class ObjectIdentifierMixin:
    """
    Mixin that provides methods for getting object identifiers for breadcrumbs.

    This mixin adds a method to get the appropriate identifier for an object,
    which is useful for displaying in breadcrumbs or other UI elements.
    """

    def get_object_identifier(self):
        """
        Returns the appropriate identifier for the object.
        Tries common attributes in order: system_number, code, name, id.
        Override this method in subclasses for custom behavior.

        Returns:
            str: The identifier for the object, or None if no object or identifier is found.
        """
        if not hasattr(self, 'object') or self.object is None:
            return None

        # Try common identifier attributes
        for attr in ['system_number', 'code', 'name', 'id']:
            if hasattr(self.object, attr) and getattr(self.object, attr):
                return getattr(self.object, attr)

        return str(self.object)


class CoreSingleTableView(PermissionRequiredMixin, SingleTableView):
    """
    - Integrated filtering with django-filters
    - Search functionality
    - Pagination
    - Column visibility management
    - HTMX support through partial templates

    Usage:
        class YourListView(CoreSingleTableView):
            model = YourModel
            table_class = YourTable
            filterset_class = YourFilter  # Optional
            search_fields = ["field1", "field2"]  # Optional

            # HTMX configuration (optional)
            htmx_target = "some-element-id"  # Element to target with HTMX requests
            htmx_base_url = None  # Custom URL for HTMX requests (defaults to current URL)
    """

    context_table_name = "table"
    table_pagination = None
    partial_template_name = None
    default_per_page = 25
    search_fields: List[str] = []
    default_visible_columns: List[str] = []  # Default columns to show if not specified
    excluded_visibility_columns: List[str] = ['selection', 'actions']
    hide_paginator_count = False
    permission_required = []

    # FilterSet attributes
    filterset_class = None
    filterset = None
    _base_queryset = None

    # HTMX configuration
    htmx_target = None  # Element to target with HTMX requests
    htmx_base_url = None  # Custom URL for HTMX requests
    table_height = None

    def get_filterset_class(self) -> Optional[Type[FilterSet]]:
        """Returns the filterset class to use for filtering."""
        return self.filterset_class

    def get_filterset(self, filterset_class: Type[FilterSet]) -> Optional[FilterSet]:
        """Returns an instance of the filterset."""
        if filterset_class is None:
            return None

        kwargs = {
            "data": self.request.GET,
            "request": self.request,
            "queryset": self._get_base_queryset()
        }
        return filterset_class(**kwargs)

    def get_table_pagination(self, table) -> Dict[str, Any] | bool:
        """Configure table pagination settings."""
        if self.table_pagination is False:
            return False

        paginate = {
            "per_page": self.get_paginate_by(table.data)
        }

        if self.table_pagination:
            paginate.update(self.table_pagination)

        return paginate

    def get_paginate_by(self, queryset) -> int:
        """Get the number of items to paginate by."""
        return int(self.request.GET.get('per_page', self.default_per_page))

    def _get_base_queryset(self) -> QuerySet:
        """
        Returns the base queryset before filtering.
        Caches the result to prevent recursive filtering.
        """
        if self._base_queryset is None:
            self._base_queryset = super().get_queryset()

        return self._base_queryset

    def get_queryset(self) -> QuerySet:
        """Get the filtered queryset with search applied."""
        queryset = self._get_base_queryset()

        # Apply FilterSet if configured
        filterset_class = self.get_filterset_class()
        if filterset_class:
            self.filterset = self.get_filterset(filterset_class)
            if self.filterset:
                queryset = self.filterset.qs

        # Apply search if configured
        query = self.request.GET.get('query', '').strip()
        if query and self.search_fields:
            q_objects = Q()
            for field in self.search_fields:
                q_objects |= Q(**{f"{field}__icontains": query})
            queryset = queryset.filter(q_objects)

        return queryset

    def get_htmx_target(self) -> Optional[str]:
        """
        Get the HTMX target element ID.
        Override this method to provide dynamic target IDs.
        """
        return self.htmx_target

    def get_htmx_base_url(self) -> Optional[str]:
        """
        Get the base URL for HTMX requests.
        Override this method to provide dynamic URLs.
        """
        return self.htmx_base_url

    def get_all_columns(self) -> Dict[str, str]:
        """
        Get all available columns from the table class as a dictionary
        mapping column name (key) to its verbose name (value).
        Excludes columns listed in `excluded_visibility_columns`.
        """
        table_class = self.get_table_class()
        if not table_class:
            return {}

        # Instantiate the table with minimal data to access column definitions
        table = table_class(data=[])

        all_columns = {}
        for name, column in table.columns.items():
            # Exclude specific columns that should not be toggleable
            if name not in self.excluded_visibility_columns:
                # Use column.verbose_name. Title case the name as fallback.
                verbose_name = getattr(column, 'verbose_name', name.replace('_', ' ').title())
                all_columns[name] = verbose_name

        return all_columns

    def get_visible_columns(self) -> List[str]:
        """
        Get the list of visible column *names* from the request (comma-separated)
        or use defaults. Validates against available toggleable columns.
        """
        visible_columns_param = self.request.GET.get('visible_columns', '')
        all_available_columns = self.get_all_columns()
        valid_column_names = list(all_available_columns.keys())
        if visible_columns_param:
            visible_columns = json.loads(visible_columns_param)
            # Filter out any invalid or non-toggleable column names
            visible_columns = [col for col in visible_columns if col in valid_column_names]
            if visible_columns:
                return visible_columns

        # Use default visible columns if defined and they are valid
        if self.default_visible_columns:
            validated_defaults = [col for col in self.default_visible_columns if col in valid_column_names]
            if validated_defaults:
                return validated_defaults

        # If no valid columns from request or defaults, return all valid toggleable column names
        return valid_column_names

    def get_table(self, **kwargs):
        """
        Get the table instance with column visibility and HTMX attributes applied.
        """
        table = super().get_table(**kwargs)
        visible_columns = self.get_visible_columns()
        all_columns_dict = self.get_all_columns()
        all_toggleable_column_names = list(all_columns_dict.keys())

        # Apply column visibility by excluding columns that aren't visible
        if visible_columns:
            for column_name in all_toggleable_column_names:
                if column_name not in visible_columns and column_name in table.columns:
                    table.columns.hide(column_name)

        # Apply HTMX attributes if configured
        htmx_target = self.get_htmx_target()
        if htmx_target:
            table.htmx_target = htmx_target

        htmx_base_url = self.get_htmx_base_url()
        if htmx_base_url:
            table.htmx_base_url = htmx_base_url

        table.table_height = self.table_height
        table.hide_paginator_count = self.hide_paginator_count
        return table

    def get_context_data(self, **kwargs) -> Dict[str, Any]:
        """Add common context data for table views."""
        context = super().get_context_data(**kwargs)

        # Add filterset to context if available
        if self.filterset:
            context['filterset'] = self.filterset

        # Create a copy of the current GET parameters to build URLs
        query_params = self.request.GET.copy()

        # Get column information
        visible_columns_names = self.get_visible_columns()
        all_columns_dict = self.get_all_columns()

        # Update query_params with current state for URL generation
        if visible_columns_names:
            # Use comma-separated string for the 'visible_columns' GET parameter
            query_params['visible_columns'] = ','.join(visible_columns_names)
        else:
            # If list is empty, remove param from query_params if present
            if 'visible_columns' in query_params:
                del query_params['visible_columns']

        if self.request.GET.get('query'):
            query_params['query'] = self.request.GET['query']
        if self.request.GET.get('per_page'):
            query_params['per_page'] = self.request.GET['per_page']

        # Add parameters to context
        context.update({
            'query_params': query_params,
            'all_columns': all_columns_dict,
            'visible_columns': visible_columns_names,
            'current_query': self.request.GET.get('query', ''),
            'current_per_page': self.get_paginate_by(None)
        })

        return context

    def get_template_names(self) -> List[str]:
        """
        Determine which template to use for rendering the response.

        The logic works as follows:
        1. Verifies this is an HTMX request (self.request.htmx is True)
        2. Checks if there are no hxf [hx first load] parameters in the request ('hxf' is None)
        3. Confirms the view has a partial template defined (hasattr(self, 'partial_template_name'))

        Returns:
            List[str]: A list of template names to be used for rendering the response.
        """
        if self.request.htmx and not self.request.GET.get('hxf', None) and self.partial_template_name:
            return [self.partial_template_name]
        return super().get_template_names()

    def dispatch(self, request, *args, **kwargs):
        """Handle dispatch with state persistence."""
        # Check if export is requested but view doesn't support it
        if request.GET.get('export') and not isinstance(self, ExportTableMixin):
            return HttpResponse(
                'Export functionality not available for this view.',
                status=403
            )

        return super().dispatch(request, *args, **kwargs)


class CoreListView(PermissionRequiredMixin, ObjectIdentifierMixin, ListView):
    permission_required = []


class CoreDetailView(PermissionRequiredMixin, ObjectIdentifierMixin, DetailView):
    permission_required = []

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_type'] = 'detail'
        context['object_identifier'] = self.get_object_identifier()
        return context

    def render_to_response(self, context, **response_kwargs):
        """Add object identifier to response headers for HTMX updates."""
        response = super().render_to_response(context, **response_kwargs)

        # Add object identifier to response headers for HTMX to use
        if self.object and hasattr(response, 'headers'):
            object_identifier = self.get_object_identifier()
            if object_identifier:
                response.headers['X-Object-Identifier'] = str(object_identifier)

        return response


class CoreUpdateView(PermissionRequiredMixin, ObjectIdentifierMixin, FormKwargsRequestMixin, UpdateView):
    """
    Base update view that includes section title and description functionality.

    This view extends Django's UpdateView to add consistent section title and
    description handling across the application.

    Attributes:
        section_title (str): Title to display at the top of the form
        section_desc (str): Description text to display below the title
        submit_text (str): Text to display on the submit button
        cancel_url (str): URL name for the cancel button
    """
    section_title = None
    section_desc = None
    submit_text = "Update"
    cancel_url = None
    action_type = "update"
    permission_required = []
    success_url = None

    def form_valid(self, form):
        """
        Handle successful form validation.

        Adds a success message after successful update.
        """
        response = super().form_valid(form)
        messages.success(self.request, f"{form.instance} was updated successfully!")
        return response

    def get_context_data(self, **kwargs):
        """
        Add section_title and section_desc to the template context.

        Returns:
            dict: The updated context dictionary
        """
        context = super().get_context_data(**kwargs)
        context['section_title'] = self.section_title
        context['section_desc'] = self.section_desc
        context['submit_text'] = self.submit_text
        context['cancel_url'] = self.cancel_url
        context['action_type'] = self.action_type
        context['view_type'] = 'update'
        context['object_identifier'] = self.get_object_identifier()
        return context

    def get_success_url(self):
        """
        Get the URL to redirect to after successful form submission.

        By default, returns the success_url class attribute.
        Override this method to provide dynamic success URLs.

        Returns:
            str: URL to redirect to
        """
        if not self.success_url:
            # Provide a sensible default or raise an error if not set
            # Defaulting to object's absolute URL if available
            if hasattr(self.object, 'get_absolute_url'):
                return self.object.get_absolute_url()
            raise ImproperlyConfigured("No URL to redirect to. Provide a success_url.")
        # Assuming success_url is a URL name that takes 'pk'
        try:
            return reverse(self.success_url, kwargs={"pk": self.object.pk})
        except NoReverseMatch:
            # Handle cases where the URL name might not need 'pk' or is incorrect
            return reverse(self.success_url)


class CoreCreateView(PermissionRequiredMixin, ObjectIdentifierMixin, FormKwargsRequestMixin, CreateView):
    """
    Base create view that includes section title and description functionality.

    This view extends Django's CreateView to add consistent section title and
    description handling across the application.

    Attributes:
        section_title (str): Title to display at the top of the form
        section_desc (str): Description text to display below the title
    """
    section_title = None
    section_desc = None
    submit_text = "Create"
    cancel_url = None
    action_type = "create"
    permission_required = []
    success_url = None

    def form_valid(self, form):
        response = super().form_valid(form)
        if not self.request.htmx:
            messages.success(self.request, f"{form.instance}' was created successfully!")
        return response

    def get_context_data(self, **kwargs):
        """
        Add section_title and section_desc to the template context.

        Returns:
            dict: The updated context dictionary
        """
        context = super().get_context_data(**kwargs)
        context['section_title'] = self.section_title
        context['section_desc'] = self.section_desc
        context['submit_text'] = self.submit_text
        context['cancel_url'] = self.cancel_url
        context['action_type'] = self.action_type
        context['view_type'] = 'create'
        # For create views, there's no object yet, so we don't set object_identifier
        return context

    def get_success_url(self):
        """
        Get the URL to redirect to after successful form submission.

        By default, returns the success_url class attribute.
        Override this method to provide dynamic success URLs.

        Returns:
            str: URL to redirect to
        """
        if not self.success_url:
            if hasattr(self.object, 'get_absolute_url'):
                return self.object.get_absolute_url()
            raise ImproperlyConfigured("No URL to redirect to. Provide a success_url.")
        # Assuming success_url might take 'pk'
        try:
            return reverse(self.success_url, kwargs={"pk": self.object.pk})
        except NoReverseMatch:
            # Fallback if 'pk' is not needed or URL name is different
            try:
                return reverse(self.success_url)
            except NoReverseMatch:
                raise ImproperlyConfigured(f"Could not reverse success_url '{self.success_url}'")


class CoreDataTableDetailView(PermissionRequiredMixin, ObjectIdentifierMixin, SingleTableMixin, DetailView):
    """
    A standalone view that combines a detail view with a table.
    Provides a split panel layout with table (30%) and detail view (70%).

    Features:
    - Integrated filtering with django-filters
    - Search functionality
    - Pagination
    - HTMX support
    """
    template_name = 'cores/datatable_detail_view.html'
    partial_view = None
    context_object_name = 'object'
    table_template_name = 'tables/table_htmx.html'
    table_class = None
    search_fields = []
    default_per_page = 25
    context_table_name = "table"
    hide_paginator_count = True
    permission_required = []

    # FilterSet attributes
    filterset_class = None
    filterset = None

    # Caching attributes
    _table_data = None
    _base_queryset = None  # Cache for DetailView's main object queryset

    def setup(self, request, *args, **kwargs):
        """Initialize view attributes"""
        super().setup(request, *args, **kwargs)
        # Reset caches per request
        self._table_data = None
        self._base_queryset = None

    def get_queryset(self):
        """
        Get the base queryset for the DetailView.
        Uses caching to prevent recursive calls.
        """
        if self._base_queryset is None:
            self._base_queryset = super().get_queryset()
        return self._base_queryset

    def get_filterset_class(self):
        """Returns the filterset class to use for filtering."""
        return self.filterset_class

    def get_filterset(self, filterset_class):
        """Returns an instance of the filterset."""
        if filterset_class is None:
            return None

        kwargs = {
            "data": self.request.GET,
            "request": self.request,
            "queryset": self.get_queryset()
        }
        return filterset_class(**kwargs)

    def get_table_kwargs(self):
        kwargs = super().get_table_kwargs()
        kwargs["object_pk"] = self.object.pk  # Pass the pk to the table
        return kwargs

    def get_table(self, **kwargs):
        table = super().get_table(**kwargs)
        table.hide_paginator_count = self.hide_paginator_count
        return table

    def get_table_data(self):
        """
        Get data for the table with filtering and search applied.
        Caches the result to prevent recursive calls.
        """
        if self._table_data is None:
            queryset = self.get_queryset()

            # Apply FilterSet if configured
            filterset_class = self.get_filterset_class()
            if filterset_class:
                self.filterset = self.get_filterset(filterset_class)
                if self.filterset:
                    queryset = self.filterset.qs

            # *NOTE-liyao[4/4/2025]: add filter on recursively ForeignKey's consideration:
            #                       i.e: (UnitConversion's POV) - origin__unit_name
            # Apply search if configured
            query = self.request.GET.get('query', '').strip()
            if query and self.search_fields:
                q_objects = Q()
                for field in self.search_fields:
                    q_objects |= Q(**{f"{field}__icontains": query})
                queryset = queryset.filter(q_objects)

            self._table_data = queryset

        return self._table_data

    def get_table_pagination(self, table):
        """Configure table pagination."""
        return {
            'per_page': int(self.request.GET.get('per_page', self.default_per_page))
        }

    def _render_detail_content(self):
        """Render detail content using partial view"""
        if not self.partial_view:
            raise ValueError("partial_view must be specified")

        partial_view = self.partial_view()
        partial_view.object = self.object
        partial_view.request = self.request

        context = partial_view.get_context_data()
        return render_to_string(
            template_name=partial_view.template_name,
            context=context,
            request=self.request
        )

    def get(self, request, *args, **kwargs):
        """Handle GET requests including HTMX"""
        self.object = self.get_object()
        context = self.get_context_data()

        if request.headers.get('HX-Request'):
            return HttpResponse(
                render_to_string(
                    self.table_template_name,
                    context=context,
                    request=request
                )
            )

        return self.render_to_response(context)

    def get_context_data(self, **kwargs):
        """Get context data for both table and detail view"""
        context = super().get_context_data(**kwargs)

        # Add filterset to context if available
        if hasattr(self, 'filterset') and self.filterset:
            context['filterset'] = self.filterset

        # Add table-specific context
        context.update({
            'detail_content': self._render_detail_content(),
            'view_type': 'detail',
            'object_identifier': self.get_object_identifier(),
        })

        return context

    def render_to_response(self, context, **response_kwargs):
        """Add object identifier to response headers for HTMX updates."""
        response = super().render_to_response(context, **response_kwargs)

        # Add object identifier to response headers for HTMX to use
        if self.object and hasattr(response, 'headers'):
            object_identifier = self.get_object_identifier()
            if object_identifier:
                response.headers['X-Object-Identifier'] = str(object_identifier)

        return response


class Select2Pagination(PageNumberPagination):
    """
    Pagination class for Select2 compatibility.
    """
    page_size = 10
    page_size_query_param = 'page_size'
    max_page_size = 20

    def get_paginated_response(self, data):
        return Response({
            'results': data,
            'pagination': {
                'more': self.page.has_next(),
                'total_pages': self.page.paginator.num_pages,
                'current_page': self.page.number,
                'count': self.page.paginator.count
            }
        })


class CoreTemplateView(PermissionRequiredMixin, ObjectIdentifierMixin, TemplateView):
    permission_required = []


class CoreFormView(PermissionRequiredMixin, ObjectIdentifierMixin, FormKwargsRequestMixin, FormView):
    permission_required = []

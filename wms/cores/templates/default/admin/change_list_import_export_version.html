{% extends "admin/import_export/change_list.html" %}

{% load i18n admin_urls %}

{% block object-tools-items %}
  <li>
    <a href="import/" class="import_link">{% trans "Import" %}</a>
  </li>
  <li>
    <a href="export/{{ cl.get_query_string }}" class="export_link">{% trans "Export" %}</a>
  </li>
  {% if not is_popup and has_add_permission and has_change_permission %}
    <li>
      <a href="{% url opts|admin_urlname:'recoverlist' %}" class="recoverlink">{% blocktrans with cl.opts.verbose_name_plural|escape as name %}Recover deleted {{ name }}{% endblocktrans %}</a>
    </li>
  {% endif %}
  {{ block.super }}
{% endblock object-tools-items %}

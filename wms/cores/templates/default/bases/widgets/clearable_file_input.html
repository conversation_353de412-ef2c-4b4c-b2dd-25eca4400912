<div class="row">
  {% if thumbnail_image %}
    <div class="col-12">
      <a data-fancybox="gallery"
         data-caption="{{ filename }}"
         href="{{ widget.value.url }}">
        <img src="{{ thumbnail_image.url }}" class="img-thumbnail"  alt="" />
      </a>
    </div>
  {% endif %}
  <div class="col-12">
    {% if widget.is_initial %}
      <strong>{{ widget.initial_text }}:</strong>
      {% if not thumbnail_image %}
        <a data-fancybox="gallery"
           data-caption="{{ filename }}"
           href="{{ widget.value.url }}">
        {% endif %}
        {{ filename }}
        {% if not thumbnail_image %}</a>{% endif %}
    </div>
    <div class="col-12">
      {% if not widget.required %}
        <div class="checkbox">
          <label for="{{ widget.checkbox_id }}">
            <input type="checkbox"
                   name="{{ widget.checkbox_name }}"
                   id="{{ widget.checkbox_id }}"
                   style="vertical-align: middle;
                          margin-right: 5px;
                          margin-top: -3px" />
            {{ widget.clear_checkbox_label }}
          </label>
        </div>
      {% endif %}
    {% endif %}
    <div class="custom-file">
      <input type="{{ widget.type }}"
             name="{{ widget.name }}"
             {% for name, value in widget.attrs.items %}{% if value is not False %} {{ name }}{% if value is not True %}="{{ value|stringformat:'s' }}"{% endif %}
             {% endif %}
             {% endfor %} />
      <label class="custom-file-label" for="id_{{ widget.name }}">Choose file</label>
    </div>
  </div>
</div>

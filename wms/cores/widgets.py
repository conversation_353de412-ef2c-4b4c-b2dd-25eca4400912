import logging
import os

from django.conf import settings
from django.forms import ClearableFileInput
from django.forms.widgets import CheckboxSelectMultiple
from django.template import loader
from django.utils.safestring import mark_safe
from django.utils.translation import gettext_lazy as _

from PIL import Image
from sorl.thumbnail.shortcuts import get_thumbnail

from .utils import get_img_extension

logger = logging.getLogger(__name__)


class BaseImageRender:
    """Abstract render."""

    def render(self, name, value, attrs=None, renderer=None):
        context = self.get_context(name, value, attrs)

        if value and hasattr(value, "url"):
            try:
                Image.open(value)
            except OSError:
                context["thumb_img"] = None
                context["filename"] = os.path.basename(value.name)
            else:
                ext = get_img_extension(value)

                try:
                    mini = get_thumbnail(value, settings.ADMIN_THUMB_SIZE, upscale=False, format=ext)
                except Exception as e:
                    logger.warning("Unable to get the thumbnail", exc_info=e)
                else:
                    context["thumbnail_image"] = mini
                    context["filename"] = os.path.basename(value.name)

        template = loader.get_template(self.template_name).render(context)
        return mark_safe(template)


class BaseAdminImageWidget(BaseImageRender, ClearableFileInput):
    """An ImageField Widget for django.contrib.admin that shows a thumbnailed
    image as well as a link to the current one if it hase one.
    """

    template_name = "django/forms/widgets/clearable_file_input.html"


class BaseImageWidget(BaseImageRender, ClearableFileInput):
    """An ImageField Widget shows a thumbnailed image as well as
    a link to the current one if it hase one.
    """

    clear_checkbox_label = _("Remove this file")
    template_name = "cores/widgets/clearable_file_input.html"

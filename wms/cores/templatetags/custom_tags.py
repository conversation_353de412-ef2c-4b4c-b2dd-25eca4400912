from typing import Any

from django import template
from django.urls import reverse, NoReverseMatch
from importlib import import_module

from wms.apps.rackings.models.rack import Rack, RackStorage

register = template.Library()


@register.simple_tag(takes_context=True)
def get_user_menus(context):
    user = context['request'].user
    request = context['request']
    current_app = request.resolver_match.app_name if request.resolver_match else None
    current_path = request.path_info

    apps = ['users', 'receives', 'releases', 'inventories', 'consignors', 'consignees', 'reports', 'settings']  # Add other app names here
    menus = []

    active_module = None
    active_submenu = None
    active_submenu_dropdown = None

    for app_name in apps:
        try:
            menu_module = import_module(f"wms.apps.{app_name}.menu")
            menu_data = menu_module.MENU_DATA

            # Handle menu with no submenus (direct link)
            if "url_name" in menu_data and not menu_data.get("submenus"):
                try:
                    menu_url = reverse(menu_data["url_name"])
                    is_active = current_path.startswith(menu_url)

                    # Check permissions if specified
                    if not menu_data.get("permissions") or all(user.has_perm(perm) for perm in menu_data.get("permissions", [])):
                        menus.append({
                            "label": menu_data["label"],
                            "icon": menu_data["icon"],
                            "url": menu_url,
                            "active": is_active,
                            "submenus": [],
                        })

                        if is_active:
                            active_module = menu_data["label"]
                except NoReverseMatch:
                    continue
                continue

            # Handle menu with submenus (dropdown)
            has_main_menu_perm = False
            first_available_submenu_url = None
            filtered_submenus = []

            for submenu in menu_data.get("submenus", []):
                if all(user.has_perm(perm) for perm in submenu.get("permissions", [])):
                    has_main_menu_perm = True
                    try:
                        submenu_url = reverse(submenu["url_name"])
                    except NoReverseMatch:
                        submenu_url = None

                    if submenu_url and first_available_submenu_url is None:
                        first_available_submenu_url = submenu_url

                    if submenu.get("dropdown", False):
                        filtered_nested_submenus = []
                        for nested_submenu in submenu.get("submenus", []):
                            if all(user.has_perm(perm) for perm in nested_submenu.get("permissions", [])):
                                try:
                                    nested_submenu_url = reverse(nested_submenu["url_name"])
                                except NoReverseMatch:
                                    nested_submenu_url = None

                                if nested_submenu_url:
                                    filtered_nested_submenus.append({
                                        "label": nested_submenu["label"],
                                        "url": nested_submenu_url,
                                        "active": nested_submenu_url == current_path,  # Check for active nested submenu
                                    })
                                    # Set activeSubmoduleDropdown if this nested submenu is active
                                    if nested_submenu_url == current_path:
                                        active_submenu_dropdown = True
                        submenu["submenus"] = filtered_nested_submenus
                        if not submenu["submenus"]:
                            continue

                    if submenu_url:
                        # Check if current path starts with submenu URL or if they're exactly equal
                        is_active_submenu = current_path.startswith(submenu_url) or current_path == submenu_url

                        # For create/update/detail URLs, also check if the base URL is part of the path
                        # This helps with breadcrumbs for detail/create/update views
                        if not is_active_submenu:
                            # Check for common URL patterns like /create/, /update/, /detail/
                            base_path = submenu_url.rstrip('/')
                            path_parts = current_path.split('/')

                            # If we have a path like /releases/orders/123/update/
                            # and submenu_url is /releases/orders/
                            # This should match
                            if len(path_parts) >= 4 and current_path.startswith(base_path):
                                is_active_submenu = True

                        filtered_submenus.append({
                            "label": submenu["label"],
                            "url": submenu_url,
                            "dropdown": submenu.get("dropdown", False),
                            "submenus": submenu.get("submenus", []),
                            "active": is_active_submenu,  # Add active status
                        })

                        if is_active_submenu:
                            active_submenu = submenu["label"]

            if has_main_menu_perm:
                # Check if current_app matches the module name
                is_active_module = current_app == menu_data["label"].lower()

                # Also check if any submenu is active
                has_active_submenu = any(submenu.get('active', False) for submenu in filtered_submenus)

                # Set active module if either condition is true
                if is_active_module or has_active_submenu:
                    active_module = menu_data["label"]

                menus.append({
                    "label": menu_data["label"],
                    "icon": menu_data["icon"],
                    "url": first_available_submenu_url or "#",
                    "submenus": filtered_submenus,
                    "active": is_active_module or has_active_submenu,  # Add active status
                })

        except (ImportError, AttributeError, NoReverseMatch) as e:
            print(f"Error loading menu for {app_name}: {e}")
            continue

    return {
        "menus": menus,
        "active_module": active_module,
        "active_submenu": active_submenu,
        "active_submenu_dropdown": active_submenu_dropdown,
    }


@register.simple_tag
def get_count_sheet_pdf_data(value) -> dict[str, Any]:
    """
    Returns dictionary containing mapping to columns in CountSheet's PDF.

    Function argument represents each row that is being evaluted based on
    filtered list.
    """

    results = {}

    # LOCATION
    if isinstance(value, Rack):
        location = value.full_name
    elif isinstance(value, RackStorage):
        location = value.rack.full_name
    else:
        location = ""

    results["location"] = location

    # PRODUCT CODE
    product_code = value.stock.item.code if isinstance(value, RackStorage) else ""
    results["product_code"] = product_code

    # DESCRIPTION
    description = value.stock.item.name if isinstance(value, RackStorage) else ""
    results["description"] = description

    # BATCH
    batch_no = value.stock.batch_no if isinstance(value, RackStorage) else ""
    results["batch_no"] = batch_no

    # EXPIRY DATE
    expiry_date = value.stock.expiry_date if isinstance(value, RackStorage) else ""
    results["expiry_date"] = expiry_date

    return results

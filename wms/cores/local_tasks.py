import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, Any
import aiohttp
import threading

logger = logging.getLogger(__name__)


class LocalTaskRunner:
    """Local implementation of Cloud Tasks for development."""

    def __init__(self):
        self.tasks = []
        self._running = False
        self._thread = None
        self.event_loop = None

    async def _execute_task(self, task: Dict[str, Any]):
        """Execute a single task."""
        try:
            url = task['http_request']['url']
            method = task['http_request']['http_method']
            headers = task['http_request'].get('headers', {})
            body = task['http_request'].get('body')

            if isinstance(body, bytes):
                body = body.decode('utf-8')

            async with aiohttp.ClientSession() as session:
                async with session.request(
                    method=method,
                    url=url,
                    headers=headers,
                    json=json.loads(body) if body else None
                ) as response:
                    logger.info(f"Local task executed: {url} - Status: {response.status}")
                    return response.status

        except Exception as e:
            logger.error(f"Error executing local task: {str(e)}")
            return None

    async def _run_tasks(self):
        """Main task execution loop."""
        while self._running:
            current_time = datetime.now()

            # Find tasks that are ready to execute
            ready_tasks = [
                task for task in self.tasks
                if 'schedule_time' not in task or
                   datetime.fromtimestamp(task['schedule_time']) <= current_time
            ]

            # Remove executed tasks from the queue
            for task in ready_tasks:
                self.tasks.remove(task)
                await self._execute_task(task)

            await asyncio.sleep(1)  # Check every second

    def start(self):
        """Start the local task runner."""
        if self._running:
            return

        self._running = True

        def run_event_loop():
            self.event_loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self.event_loop)
            self.event_loop.run_until_complete(self._run_tasks())

        self._thread = threading.Thread(target=run_event_loop, daemon=True)
        self._thread.start()
        logger.info("Local task runner started")

    def stop(self):
        """Stop the local task runner."""
        self._running = False
        if self.event_loop:
            self.event_loop.stop()
        if self._thread:
            self._thread.join()
        logger.info("Local task runner stopped")

    def enqueue_task(self, queue_name: str, task: Dict[str, Any]):
        """Add a task to the queue."""
        self.tasks.append(task)
        logger.info(f"Task enqueued to {queue_name}: {task}")


# Global instance
local_task_runner = LocalTaskRunner()

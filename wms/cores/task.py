"""Usage Example

from wms.core.tasks import TaskHandler
# Create a task
TaskHandler.create_task(
    queue_name='default',
    relative_uri='/api/some-endpoint/',
    payload={'key': 'value'},
    schedule_time_seconds=30  # optional: delay in seconds
)
"""
from google.protobuf import timestamp_pb2
from google.cloud import tasks_v2
from django.conf import settings
from typing import Optional, Dict, Any

from .local_tasks import local_task_runner


class TaskHandler:
    """Handles task creation for both local development and production."""

    @staticmethod
    def create_task(
        relative_uri: str,
        payload: Optional[Dict[str, Any]] = None,
        schedule_time_seconds: Optional[int] = None,
        queue_name: Optional[str] = None,
    ) -> None:
        """
        Create a task that works in both development and production.

        Args:
            relative_uri: The URI of the endpoint to be called
            payload: Dictionary of data to be sent to the endpoint
            schedule_time_seconds: Seconds from now to schedule the task
            queue_name: Name of the task queue
        """
        if settings.DEBUG:
            # Local development
            task = {
                'http_request': {
                    'http_method': 'POST',
                    'url': f'http://localhost:8000{relative_uri}',
                    'headers': {'Content-type': 'application/json'},
                }
            }

            if payload is not None:
                import json
                task['http_request']['body'] = json.dumps(payload)

            if schedule_time_seconds:
                from time import time
                task['schedule_time'] = int(time()) + schedule_time_seconds

            local_task_runner.enqueue_task(queue_name, task)

        else:
            # Production environment
            # Use settings queue name if none provided
            queue_name = queue_name or settings.GCP_TASKS_QUEUE

            client = tasks_v2.CloudTasksClient()
            parent = client.queue_path(
                settings.GCP_PROJECT_ID,
                settings.GCP_REGION,
                queue_name
            )

            task = {
                'http_request': {
                    'http_method': tasks_v2.HttpMethod.POST,
                    'url': f'https://{settings.DOMAIN_NAME}{relative_uri}',
                }
            }

            if payload is not None:
                import json
                task['http_request']['headers'] = {
                    'Content-type': 'application/json',
                }
                task['http_request']['body'] = json.dumps(payload).encode()

            if schedule_time_seconds:
                timestamp = timestamp_pb2.Timestamp()
                timestamp.FromSeconds(schedule_time_seconds)
                task['schedule_time'] = timestamp

            client.create_task(request={'parent': parent, 'task': task})

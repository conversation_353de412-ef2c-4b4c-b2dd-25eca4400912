// Breadcrumb updater for HTMX table interactions
document.addEventListener('DOMContentLoaded', function() {
    // Listen for HTMX after-request events on table rows
    document.body.addEventListener('htmx:afterRequest', function(event) {
        // Check if this is a table row click that loads detail content
        if (event.detail.target &&
            (event.detail.target.id === 'tab-content' ||
             event.detail.target.id === 'detail-panel')) {

            // Get the object ID from the response
            const objectId = event.detail.xhr.getResponseHeader('X-Object-Identifier');
            if (objectId) {
                // Update all breadcrumb identifier spans
                const identifierSpans = [
                    document.getElementById('breadcrumb-object-identifier-detail'),
                    document.getElementById('breadcrumb-object-identifier-fallback'),
                    document.getElementById('breadcrumb-object-identifier-update-main'),
                    document.getElementById('breadcrumb-object-identifier-detail-module'),
                    document.getElementById('breadcrumb-object-identifier-update')
                ];

                identifierSpans.forEach(function(span) {
                    if (span) {
                        // Create the HTML for the identifier
                        span.innerHTML = `
                            <span class="text-theme-border-secondary px-2">/</span>
                            <a href="${window.location.pathname}" class="text-theme-link font-medium hover:text-theme-link-hover transition-colors">
                                ${objectId}
                            </a>
                        `;
                    }
                });
            }
        }
    });
});

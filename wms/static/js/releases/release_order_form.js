document.addEventListener('DOMContentLoaded', function() {
  const consignorSelect = document.getElementById('id_consignor');
  const consigneeSelect = document.getElementById('id_consignee');
  const consignorIdField = document.getElementById('id_consignor_id');
  const consigneeIdField = document.getElementById('id_consignee_id');
  const formsetContainer = document.querySelector('#release_order_form');
  const DEFAULT_PLACEHOLDER = '---------';

  // Function to update item select options based on consignor
  function updateItemSelectApiUrlByConsignor(itemSelectElement, consignorId) {
    if (!consignorId) {
      itemSelectElement.disabled = true;
      return;
    }

    // Enable the item select
    itemSelectElement.disabled = false;

    // Update the data-api-url attribute with the actual consignor ID
    const apiUrlTemplate = itemSelectElement.getAttribute(
            'data-api-url-template') ||
        itemSelectElement.getAttribute('data-api-url');

    // Store the original template if not already stored
    if (!itemSelectElement.hasAttribute('data-api-url-template')) {
      itemSelectElement.setAttribute('data-api-url-template', apiUrlTemplate);
    }

    const apiUrl = apiUrlTemplate.replace('{consignor_id}', consignorId);
    itemSelectElement.setAttribute('data-api-url', apiUrl);

    // Destroy and reinitialize Select2 to apply the new API URL
    $(itemSelectElement).select2('destroy');

    // Use the global initializeSelect2 function from select2.js
    window.initializeSelect2(itemSelectElement);
  }

  // Function to update batch options based on selected item
  function updateBatchOptions(itemSelect, batchSelect, selectedBatchNo) {
    // Clear existing options
    batchSelect.innerHTML = `<option value="">${DEFAULT_PLACEHOLDER}</option>`;

    // Disable by default
    batchSelect.disabled = true;

    // Get the selected item data
    let selectedItem = $(itemSelect).select2('data')[0];
    if (!selectedItem || !selectedItem.batches ||
        selectedItem.batches.length === 0) {
      return;
    }

    // Enable the batch select
    batchSelect.disabled = false;

    // Add batch options
    selectedItem.batches.forEach(batch => {
      const option = document.createElement('option');
      option.value = batch.batch_no;
      option.textContent = batch.batch_no;
      option.dataset.expiryDates = JSON.stringify(batch.expiry_dates);
      batchSelect.appendChild(option);
    });

    if (selectedBatchNo) {
      $(batchSelect).val(selectedBatchNo);
    }
    // Trigger change event to refresh Select2
    $(batchSelect).trigger('change');
  }

  // Function to update expiry date based on selected batch
  function updateExpiryDate(batchSelect, expiryDateInput, selectedExpiryDate) {
    // Clear existing options
    expiryDateInput.innerHTML = `<option value="">${DEFAULT_PLACEHOLDER}</option>`;

    // Disable by default
    expiryDateInput.disabled = true;

    // Get the selected batch data
    const selectedOption = batchSelect.options[batchSelect.selectedIndex];

    if (!selectedOption || !selectedOption.dataset.expiryDates) {
      return;
    }

    // Parse the expiry dates
    let expiryDates = [];
    try {
      expiryDates = JSON.parse(selectedOption.dataset.expiryDates);
    } catch (e) {
      console.error('Error parsing expiry dates:', e);
      return;
    }

    if (expiryDates.length === 0) {
      return;
    }

    // Enable the expiry date input
    expiryDateInput.disabled = false;

    // Add expiry date options
    expiryDates.forEach(expiryDate => {
      const option = document.createElement('option');
      option.value = expiryDate;
      option.textContent = expiryDate;
      expiryDateInput.appendChild(option);
    });

    // Select the first option
    if (expiryDates.length > 0) {
      if (selectedExpiryDate) {
        expiryDateInput.value = selectedExpiryDate;
      } else {
        // Set the value to the first expiry date
        expiryDateInput.value = expiryDates[0];
      }

      // Update the hidden field
      const row = expiryDateInput.closest('tr');
      if (row) {
        const expiryDateHiddenInput = row.querySelector('input[name$="-expiry_date_hidden"]');
        if (expiryDateHiddenInput) {
          expiryDateHiddenInput.value = selectedExpiryDate || expiryDates[0];
        }
      }
    }

    // Trigger change event to refresh Select2
    $(expiryDateInput).trigger('change');
  }

  // Function to reset dependent fields
  function resetDependentFields(row, skipHiddenFields = false) {
    const batchSelect = row.querySelector('.release-order-batch');
    const expiryDateInput = row.querySelector('.transfer-item-expiry');
    const itemIdInput = row.querySelector('input[name$="-item_id"]');
    const uomInput = row.querySelector('.transfer-item-uom');
    const uomIdInput = row.querySelector('input[name$="-uom_id"]');

    if (batchSelect) {
      batchSelect.innerHTML = `<option value="">${DEFAULT_PLACEHOLDER}</option>`;
      batchSelect.disabled = true;
      $(batchSelect).val(null).trigger('change');
    }

    if (expiryDateInput) {
      // Clear existing options
      expiryDateInput.innerHTML = `<option value="">${DEFAULT_PLACEHOLDER}</option>`;
      expiryDateInput.disabled = true;
      $(expiryDateInput).val(null).trigger('change');
    }

    // Reset UOM field
    if (uomInput) {
      uomInput.value = '';
    }

    // Reset hidden inputs
    if (itemIdInput) {
      itemIdInput.value = '';
    }

    if (uomIdInput) {
      uomIdInput.value = '';
    }

    // Reset batch_no and expiry_date hidden inputs only if not skipping hidden fields
    if (!skipHiddenFields) {
      const batchNoHiddenInput = row.querySelector('input[name$="-batch_no_hidden"]');
      if (batchNoHiddenInput) {
        batchNoHiddenInput.value = '';
      }

      const expiryDateHiddenInput = row.querySelector('input[name$="-expiry_date_hidden"]');
      if (expiryDateHiddenInput) {
        expiryDateHiddenInput.value = '';
      }
    }
  }

  // Function to update consignor select options
  function updateConsignorSelectApiUrl(consignorSelectElement, consignorId) {
    if (!consignorSelectElement) {
      return;
    }

    // If we have a consignor ID, we need to fetch the consignor data
    if (consignorId) {
      // Fetch the consignor data from the API
      fetch(`/api/consignors/${consignorId}/`).then(response => {
        if (!response.ok) {
          throw new Error('Failed to fetch consignor data');
        }
        return response.json();
      }).then(data => {
        // Create a new option for the consignor
        const option = new Option(data.name, data.id, true, true);

        // Destroy and reinitialize Select2
        $(consignorSelectElement).select2('destroy');

        // Clear existing options and add the new one
        consignorSelectElement.innerHTML = '';
        consignorSelectElement.appendChild(option);

        // Initialize Select2
        window.initializeSelect2(consignorSelectElement);

        // Trigger change event to update dependent fields
        $(consignorSelectElement).trigger('change');
      }).catch(error => {
        console.error('Error fetching consignor data:', error);
        // Just initialize Select2 without the data
        $(consignorSelectElement).select2('destroy');
        window.initializeSelect2(consignorSelectElement);
      });
    } else {
      // Just initialize Select2 without the data
      $(consignorSelectElement).select2('destroy');
      window.initializeSelect2(consignorSelectElement);
    }
  }

  // Function to fetch a specific consignee by ID and initialize the select
  function fetchAndInitializeConsignee(consigneeSelectElement, consigneeId) {
    if (!consigneeSelectElement || !consigneeId) {
      return;
    }

    // Fetch the consignee data from the API
    fetch(`/api/consignees/${consigneeId}/`).then(response => {
      if (!response.ok) {
        throw new Error('Failed to fetch consignee data');
      }
      return response.json();
    }).then(data => {
      // Create a new option for the consignee
      const option = {
        id: data.id,
        text: data.name,
      };

      $(consigneeSelectElement).select2('trigger', 'select', {
        data: option,
      });

      // Enable the select
      consigneeSelectElement.disabled = false;
    }).catch(error => {
      console.error('Error fetching consignee data:', error);
    });
  }

  // Function to update consignee select options based on consignor
  function updateConsigneeSelectApiUrl(consigneeSelectElement, consignorId) {

    consigneeSelectElement.disabled = true;
    if (!consignorId) {
      return;
    }
    consigneeSelectElement.disabled = false;

    // Update the data-api-url attribute with the actual consignor ID
    const apiUrlTemplate = consigneeSelectElement.getAttribute(
            'data-api-url-template') ||
        consigneeSelectElement.getAttribute('data-api-url');

    // Store the original template if not already stored
    if (!consigneeSelectElement.hasAttribute('data-api-url-template')) {
      consigneeSelectElement.setAttribute('data-api-url-template',
          apiUrlTemplate);
    }

    const apiUrl = apiUrlTemplate.replace('{consignor_id}', consignorId);
    consigneeSelectElement.setAttribute('data-api-url', apiUrl);

    // Destroy and reinitialize Select2 to apply the new API URL
    $(consigneeSelectElement).select2('destroy');

    // Use the global initializeSelect2 function from select2.js
    window.initializeSelect2(consigneeSelectElement);
  }

  // Simple function to update hidden ID field with the select value
  function updateHiddenIdField(hiddenField, selectValue) {
    if (hiddenField) {
      hiddenField.value = selectValue || '';
    }
  }

  // --- Event Listener for Consignor Change ---
  if (consignorSelect && consigneeSelect) {
    $(consignorSelect).on('select2:select select2:clear', function(e) {
      const selectedConsignorId = this.value;

      updateHiddenIdField(consignorIdField, selectedConsignorId);

      // Reset and update consignee select
      $(consigneeSelect).val(null).trigger('change');
      if (consigneeIdField) {
        consigneeIdField.value = '';
      }
      updateConsigneeSelectApiUrl(consigneeSelect, selectedConsignorId);

      // Update all item selects within the formset table
      formsetContainer.querySelectorAll('.release-order-item').
          forEach(itemSelect => {
            // Reset the item select value
            itemSelect.value = '';
            $(itemSelect).val(null).trigger('change');

            // Update API URL with the new consignor ID
            updateItemSelectApiUrlByConsignor(itemSelect, selectedConsignorId);

            // Reset dependent fields
            const row = itemSelect.closest('tr');
            if (row) {
              resetDependentFields(row);
            }
          });
    });

    // Event listener for consignee change
    $(consigneeSelect).on('select2:select select2:clear', function(e) {
      updateHiddenIdField(consigneeIdField, this.value);
    });

    // --- Initial load for consignor/consignee ---
    if (consignorIdField.value) {
      const selectedConsignorId = consignorIdField.value;

      // Preselect the consignor dropdown based on the consignorIdField value
      if (consignorSelect) {
        // Initialize the Select2 for consignor with the selected consignor ID
        updateConsignorSelectApiUrl(consignorSelect, selectedConsignorId);
      }

      updateConsigneeSelectApiUrl(consigneeSelect, selectedConsignorId);
      // Check if we have a consignee ID
      if (consigneeIdField && consigneeIdField.value) {
        // If we have a consignee ID, fetch and initialize the consignee select
        fetchAndInitializeConsignee(consigneeSelect, consigneeIdField.value);
      }

      // Update all item selects with the consignor ID
      formsetContainer.querySelectorAll('.release-order-item').
          forEach(itemSelect => {
            const row = itemSelect.closest('tr');
            if (row) {
              updateItemSelectApiUrlByConsignor(itemSelect, selectedConsignorId);
              // Initialize existing values if needed
              if (row.querySelector('input[name$="-item_id"]')?.value) {
                initializeExistingValues(row, selectedConsignorId);
              } else {
                const batchSelect = row.querySelector('.release-order-batch');
                const expiryDateInput = row.querySelector('.transfer-item-expiry');
                updateBatchOptions(itemSelect, batchSelect);
                updateExpiryDate(batchSelect, expiryDateInput);
              }
            }
          });
    } else {
      // Disable all item selects if no consignor is selected
      formsetContainer.querySelectorAll('.release-order-item').
          forEach(itemSelect => {
            itemSelect.disabled = true;
            const row = itemSelect.closest('tr');
            if (row) {
              resetDependentFields(row);
            }
          });
    }
  }

  // Function to handle initial form load with existing values
  async function initializeExistingValues(row, consignorId) {
    const itemSelect = row.querySelector('.release-order-item');
    const batchSelect = row.querySelector('.release-order-batch');
    const expiryDateInput = row.querySelector('.transfer-item-expiry');
    const itemIdInput = row.querySelector('input[name$="-item_id"]');
    const uomInput = row.querySelector('.transfer-item-uom');
    const uomIdInput = row.querySelector('input[name$="-uom_id"]');

    if (!itemSelect || !itemIdInput) return;

    // If we have a stored item_id and consignor_id, fetch the item data
    const itemId = itemIdInput.value;

    if (consignorId && itemId) {
      try {
        // Wait for Select2 initialization
        await new Promise(resolve => setTimeout(resolve, 300));

        // Fetch item data from API
        const response = await fetch(`/api/consignors/${consignorId}/items/${itemId}/`);
        const itemData = await response.json();
        if (itemData && Object.keys(itemData).length) {
          // Create the Select2 option data
          const itemOption = {
            id: itemData.id,
            text: itemData.name,
            batches: itemData.batches,
            uom: itemData.uom,
            uom_id: itemData.uom_id,
          };

          // Set the item select value with a flag to indicate initialization
          $(itemSelect).select2('trigger', 'select', {
            data: itemOption,
            isInitializing: true,  // Add a flag to indicate this is an initialization
          });

          // Update UOM field
          if (uomInput && itemData.uom) {
            uomInput.value = itemData.uom;
          }

          // Update UOM ID hidden input
          if (uomIdInput && itemData.uom_id) {
            uomIdInput.value = itemData.uom_id;
          }

          // Update batch options
          if (batchSelect && itemData.batches) {
            // Get the stored batch_no from the form data
            const batchNoHiddenInput = row.querySelector('input[name$="-batch_no_hidden"]');
            const storedBatchNo = batchNoHiddenInput ? batchNoHiddenInput.value : null;

            // Update batch options and select the stored batch
            updateBatchOptions(itemSelect, batchSelect, storedBatchNo);

            // Update expiry date if needed
            if (expiryDateInput && storedBatchNo) {
              // Get the stored expiry_date from the form data
              const expiryDateHiddenInput = row.querySelector('input[name$="-expiry_date_hidden"]');
              const expiryDateValue = expiryDateHiddenInput ? expiryDateHiddenInput.value : null;

              updateExpiryDate(batchSelect, expiryDateInput, expiryDateValue);
            }
          }
        }
      } catch (error) {
        console.error('Error initializing existing values:', error);
      }
    }
  }

  // --- Event Delegation for Item and Batch Changes ---
  if (formsetContainer) {
    // Item select events
    $(formsetContainer).
        on('select2:select select2:clear', '.release-order-item', function(e) {
          const row = this.closest('tr');
          if (row) {
            let event_this = this;

            // Check if this is an initialization event
            const isInitializing = e.params && e.params.isInitializing;

            // Skip resetting hidden fields if we're initializing existing values
            resetDependentFields(row, isInitializing);

            if (e.type === 'select2:select') {
              const batchSelect = row.querySelector('.release-order-batch');
              const itemIdInput = row.querySelector('input[name$="-item_id"]');
              const uomInput = row.querySelector('.transfer-item-uom');
              const uomIdInput = row.querySelector('input[name$="-uom_id"]');

              if (batchSelect) {
                updateBatchOptions(event_this, batchSelect);
              }

              // Get the selected item data
              const selectedItem = $(event_this).select2('data')[0];
              if (selectedItem && selectedItem.id) {
                // Update item_id hidden input
                if (itemIdInput) {
                  itemIdInput.value = selectedItem.id;
                }

                // Update UOM field
                if (uomInput && selectedItem.uom) {
                  uomInput.value = selectedItem.uom;
                }

                // Update UOM ID hidden input
                if (uomIdInput && selectedItem.uom_id) {
                  uomIdInput.value = selectedItem.uom_id;
                }
              }
            }
          }
        });

    // Batch select events
    $(formsetContainer).
        on('select2:select select2:clear', '.release-order-batch', function(e) {
          const row = this.closest('tr');
          if (row) {
            const expiryDateInput = row.querySelector('.transfer-item-expiry');
            const batchNoHiddenInput = row.querySelector('input[name$="-batch_no_hidden"]');

            // Reset expiry date
            if (expiryDateInput) {
              // Clear existing options
              expiryDateInput.innerHTML = `<option value="">${DEFAULT_PLACEHOLDER}</option>`;
              expiryDateInput.disabled = true;
              $(expiryDateInput).val(null).trigger('change');
            }

            // Update batch_no_hidden field
            if (batchNoHiddenInput) {
              if (e.type === 'select2:select') {
                batchNoHiddenInput.value = this.value;
              } else {
                batchNoHiddenInput.value = '';
              }
            }

            // If this is a select event (not clear), update the expiry date
            if (e.type === 'select2:select' && expiryDateInput) {
              // Get the stored expiry date if this is an initialization event
              const expiryDateHiddenInput = row.querySelector('input[name$="-expiry_date_hidden"]');
              const storedExpiryDate = expiryDateHiddenInput ? expiryDateHiddenInput.value : null;

              // Pass the stored expiry date to updateExpiryDate
              updateExpiryDate(this, expiryDateInput, storedExpiryDate);
            }
          }
        });

    // Expiry date select events
    $(formsetContainer).
        on('select2:select select2:clear', '.transfer-item-expiry', function(e) {
          const row = this.closest('tr');
          if (row) {
            const expiryDateHiddenInput = row.querySelector('input[name$="-expiry_date_hidden"]');

            // Update expiry_date_hidden field
            if (expiryDateHiddenInput) {
              if (e.type === 'select2:select') {
                expiryDateHiddenInput.value = this.options[this.selectedIndex].textContent;
              } else {
                expiryDateHiddenInput.value = '';
              }
            }
          }
        });
  }
});

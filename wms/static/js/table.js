// Column Visibility Management for Tables
let visibleColumns = new Set();
let selectedRows = new Set();

// Register Alpine.js components
document.addEventListener('alpine:init', function() {
  registerAlpineFilterForm();
});

// Initialize visible columns on page load
document.addEventListener('DOMContentLoaded', function() {
  setupHtmxHandlers();
  // initializeVisibleColumns();
  setupExportMenu();
  setupDropdownHandlers();
  setupRadioButtonState();
});

function setupHtmxHandlers() {
  // Add CSRF token to all HTMX requests
  const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value;
  if (csrfToken) {
    htmx.on('htmx:configRequest', (evt) => {
      evt.detail.headers['X-CSRFToken'] = csrfToken;
    });
  }

  // Handle HTMX after swap events
  htmx.on('htmx:afterSwap', (evt) => {
    if (evt.detail.target.id === 'table-content') {
      updateExportLinks();
      updateColumnVisibilityCheckboxes();
      restoreRowSelections();
      setupRadioButtonState();
    }
  });
}

function updateExportLinks() {
  // Update export links to include visible columns
  const exportLinks = document.querySelectorAll('.export-link');
  const visibleColumnsParam = Array.from(visibleColumns).join(',');

  exportLinks.forEach(link => {
    const url = new URL(link.href);
    url.searchParams.set('visible_columns', visibleColumnsParam);
    link.href = url.toString();
  });
}

// Row selection functionality
function handleRowCheckbox(checkbox) {
  const rowId = checkbox.value;

  if (checkbox.checked) {
    selectedRows.add(rowId);
  } else {
    selectedRows.delete(rowId);
  }

  // Update the "select all" checkbox state
  updateSelectAllCheckbox();
}

function handleSelectAll(checkbox) {
  const rowCheckboxes = document.querySelectorAll('.row-checkbox');

  rowCheckboxes.forEach(rowCheckbox => {
    rowCheckbox.checked = checkbox.checked;

    const rowId = rowCheckbox.value;
    if (checkbox.checked) {
      selectedRows.add(rowId);
      rowCheckbox.closest('tr')?.classList.add('bg-theme-table-checked');
    } else {
      selectedRows.delete(rowId);
      rowCheckbox.closest('tr')?.classList.remove('bg-theme-table-checked');
    }
  });
}

function updateSelectAllCheckbox() {
  const selectAllCheckbox = document.getElementById('select-all');
  if (!selectAllCheckbox) return;

  const rowCheckboxes = document.querySelectorAll('.row-checkbox');
  const checkedRowCheckboxes = document.querySelectorAll(
      '.row-checkbox:checked');

  if (rowCheckboxes.length === 0) {
    selectAllCheckbox.checked = false;
    selectAllCheckbox.indeterminate = false;
  } else if (checkedRowCheckboxes.length === 0) {
    selectAllCheckbox.checked = false;
    selectAllCheckbox.indeterminate = false;
  } else if (checkedRowCheckboxes.length === rowCheckboxes.length) {
    selectAllCheckbox.checked = true;
    selectAllCheckbox.indeterminate = false;
  } else {
    selectAllCheckbox.checked = false;
    selectAllCheckbox.indeterminate = true;
  }
}

function restoreRowSelections() {
  // After table content is updated, restore the selected state of rows
  const rowCheckboxes = document.querySelectorAll('.row-checkbox');

  rowCheckboxes.forEach(checkbox => {
    const rowId = checkbox.value;
    if (selectedRows.has(rowId)) {
      checkbox.checked = true;
      checkbox.closest('tr')?.classList.add('bg-theme-table-checked');
    }
  });

  updateSelectAllCheckbox();
}

function setupDropdownHandlers() {
  // Close dropdown when clicking outside
  document.addEventListener('click', function(event) {
    const dropdown = document.getElementById('column-visibility-menu');
    const toggleButton = document.getElementById('columnVisibilityDropdown');

    if (dropdown && !dropdown.contains(event.target) &&
        !toggleButton.contains(event.target)) {
      dropdown.classList.add('hidden');
    }
  });

  // Prevent dropdown from closing when clicking inside
  const dropdown = document.getElementById('column-visibility-menu');
  if (dropdown) {
    dropdown.addEventListener('click', function(event) {
      event.stopPropagation();
    });
  }
}

// Export menu functionality
function setupExportMenu() {
  // Close export menu when clicking outside
  document.addEventListener('click', function(event) {
    const exportMenu = document.getElementById('export-dropdown');
    const exportButton = document.getElementById('export-menu');

    if (exportMenu && exportButton) {
      if (!exportButton.contains(event.target) &&
          !exportMenu.contains(event.target)) {
        exportMenu.classList.add('hidden');
      }
    }
  });
}

function toggleExportMenu() {
  const exportMenu = document.getElementById('export-dropdown');
  if (exportMenu) {
    exportMenu.classList.toggle('hidden');
  }
}

function handleExportFiltered(element, target) {
  const params = new URLSearchParams();
  params.append('export', 'csv');

  // Determine which filter form to use based on target
  const filterFormId = target === '#table-content' ?
      '#filter-form' :
      '#partial-filter-form';

  // Get values from all required elements
  const querySearch = document.querySelector('#query-search')?.value;
  const perPage = document.querySelector('#per-page')?.value;
  const visibleColumns = document.querySelector(
      '[name="visible_columns"]')?.value;
  const filterForm = document.querySelector(filterFormId);
  // Add basic parameters if they exist
  if (querySearch) params.append('query', querySearch);
  if (perPage) params.append('per_page', perPage);
  if (visibleColumns) params.append('visible_columns', visibleColumns);

  // Add filter form data if it exists
  if (filterForm) {
    const formData = new FormData(filterForm);
    for (const [key, value] of formData.entries()) {
      if (value) {  // Only add non-empty values
        params.append(key, value);
      }
    }
  }

  // Update the href and navigate
  const baseUrl = element.getAttribute('href').split('?')[0];
  const newUrl = `${baseUrl}?${params.toString()}`;
  element.href = newUrl;

  return true; // Allow the link to proceed with navigation
}

function handleExportSelected() {
  // Get selected items
  if (selectedRows.size === 0) {
    alert('Please select at least one item to export.');
    return;
  }

  // Create export URL with selected IDs and visible columns
  const currentUrl = new URL(window.location.href);
  const params = new URLSearchParams(currentUrl.search);

  params.set('export', 'csv');
  params.set('selected_items', Array.from(selectedRows).join(','));
  params.set('visible_columns', Array.from(visibleColumns).join(','));

  // Navigate to export URL
  window.location.href = `${currentUrl.pathname}?${params.toString()}`;
}

function resetFilters() {
  // Reset all form inputs
  const form = document.getElementById('filter-form');
  const inputs = form.querySelectorAll('input, select');
  inputs.forEach(input => {
    if (input.type === 'checkbox' || input.type === 'radio') {
      input.checked = false;
    } else if (input.tagName.toLowerCase() === 'select') {
      // For select elements, set to first option if available
      if (input.options.length > 0) {
        input.selectedIndex = 0;

        // Special handling for Select2
        if ($(input).hasClass('django-select2') || $(input).data('select2')) {
          // For Select2 single select
          if (!$(input).prop('multiple')) {
            const firstOptionValue = input.options[0].value;
            $(input).val(firstOptionValue).trigger('change.select2');
          } else {
            // For Select2 multiple select
            $(input).val(null).trigger('change.select2');
          }
        }
      } else {
        input.value = '';
      }
    } else {
      input.value = '';
    }
  });

  // Get current URL and clear search parameters while preserving visible columns
  const currentUrl = new URL(window.location.href);
  const params = new URLSearchParams();

  // Preserve visible columns parameter if it exists
  const visibleColumnsArray = Array.from(visibleColumns);
  if (visibleColumnsArray.length > 0) {
    params.set('visible_columns', visibleColumnsArray.join(','));
  }
}

function setupRadioButtonState() {
  const currentPk = window.currentObjectPk; // We'll set this in the template
  const radios = document.querySelectorAll(
      'input[type="radio"][name="htmx-radio"]');
  radios.forEach(radio => {
    if (parseInt(radio.value) === currentPk) {
      radio.checked = true;
    }
  });
}

// Register the Alpine.js filter form component
function registerAlpineFilterForm() {
  Alpine.data('filterForm', (formId) => ({
      formId: formId,
      debounceTimer: null,

      submitForm() {
        const form = document.getElementById(this.formId);
        if (form) {
          htmx.trigger(form, 'submit');
        }
      },

      handleInputChange() {
        clearTimeout(this.debounceTimer);
        this.debounceTimer = setTimeout(() => this.submitForm(), 500);
      },

      initializeForm() {
        // Initialize Select2 event handlers
        $(document).on('select2:close', 'select[data-auto-submit="true"][multiple]', () => {
          // Only submit when multiple select dropdown closes
          this.submitForm();
        });

        // For clear
        $(document).on('select2:unselect', 'select[data-auto-submit="true"][multiple]', () => {
          this.submitForm();
        });

        // For single selects, keep the immediate submission
        $(document).on('select2:select select2:unselect', 'select[data-auto-submit="true"]:not([multiple])', () => {
          this.submitForm();
        });

        // Get form element using dynamic ID
        const form = document.getElementById(this.formId);
        if (!form) return;

        // Handle regular select fields
        form.querySelectorAll('select:not([data-auto-submit="true"])').forEach(select => {
          select.addEventListener('change', () => this.submitForm());
        });

        // Handle checkboxes and radio buttons
        form.querySelectorAll('input[type="checkbox"], input[type="radio"]').forEach(input => {
          input.addEventListener('change', () => this.submitForm());
        });

        // Handle text, date, and number inputs with debounce
        form.querySelectorAll('input[type="text"], input[type="date"], input[type="number"]').forEach(input => {
          input.addEventListener('input', () => this.handleInputChange());
        });
      },
    }));
}

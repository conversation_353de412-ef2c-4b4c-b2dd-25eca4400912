document.addEventListener('alpine:init', () => {
  Alpine.data('toast', () => ({
    toasts: [],
    counter: 0,

    show(detail) {
      // Handle case when detail is undefined or not properly formatted
      if (!detail) detail = {};

      const message = detail.message || 'Notification';
      const type = detail.type || 'info';
      const duration = detail.duration || 3000;
      const title = detail.title || '';

      // Use a combination of timestamp and counter for truly unique IDs
      const id = `${Date.now()}-${this.counter++}`;

      this.toasts.push({id, message, type, title, duration, visible: true});

      setTimeout(() => {
        const index = this.toasts.findIndex(toast => toast.id === id);
        if (index > -1) {
          this.remove(index);
        }
      }, duration);
    },

    remove(index) {
      if (index >= 0 && index < this.toasts.length) {
        this.toasts.splice(index, 1);
      }
    }
  }));
});

class FormsetRowHandler {
  constructor(
      prefix, addButtonClass = 'add-row', deleteButtonClass = 'delete-row') {
    this.prefix = prefix;
    this.formsetTable = $(`#${prefix}-formset-table`);
    this.addButtonClass = addButtonClass;
    this.deleteButtonClass = deleteButtonClass;
    this.totalFormsInput = $(`#id_${prefix}-TOTAL_FORMS`);
    this.initializeHandlers();
  }

  initializeHandlers() {
    // Use jQuery for event handling
    $(document).on('click', '.' + this.addButtonClass, (e) => {
      this.addRow(e);
    });

    $(document).on('click', '.' + this.deleteButtonClass, (e) => {
      this.deleteRow(e);
    });
  }

  addRow(event) {
    event.preventDefault();
    const $targetRow = this.formsetTable.find('tbody tr:last-child');
    if (!$targetRow) return;

    // Get the current form count before adding a new row
    const formCount = parseInt(this.totalFormsInput.val());

    // Clone the target row with its data and events
    const $newRow = $targetRow.clone(false, false);

    // Update form index in the cloned row
    this.updateFormIndex($newRow, formCount);

    // Clean up Select2 artifacts in the cloned row
    $newRow.find('select.django-select2').each(function() {
      const $select = $(this);

      // Remove Select2-specific attributes
      $select.removeAttr('data-select2-id').
          removeClass('select2-hidden-accessible').
          removeAttr('aria-hidden').
          removeAttr('tabindex');

      // Clear the selection
      $select.find('option[data-select2-id]').remove();
      $select.val('').empty();

      // Remove the Select2 container
      $select.siblings('.select2-container').remove();

    });

    // Reset other input values in the new row
    $newRow.find('input:not([type="hidden"]), textarea').each(function() {
      const $input = $(this);
      if ($input.is(':checkbox, :radio')) {
        $input.prop('checked', false);
      } else {
        $input.val('');
      }
    });

    // Clean up any error messages and highlights
    this.cleanupErrors($newRow);
    // Insert the new row after the target row
    $targetRow.after($newRow);

    // Update total forms count
    this.totalFormsInput.val(formCount + 1);

    // Initialize Select2 on the new row's select elements
    const $newRowSelect2 = $newRow.find('.django-select2');
    if ($newRowSelect2.length > 0) {
      // Generate new unique IDs for the select elements
      $newRowSelect2.each((index, element) => {
        const $element = $(element);
        const originalId = $element.attr('id');
        if (originalId) {
          const newId = originalId.replace(
              new RegExp(`${this.prefix}-(\\d+)-`),
              `${this.prefix}-${formCount}-`,
          );
          $element.attr('id', newId);
        }
      });

      // Initialize Select2 on the new row
      initializeSelect2($newRowSelect2);
    }

    // Initialize calendars in the new row if any exist
    const newRowElement = $newRow[0]; // Get the DOM element from jQuery object
    if (newRowElement) {
      // Check if there are any calendar inputs in the new row
      const hasDateInputs = newRowElement.querySelector('[data-calendar-date="true"]');
      const hasDateTimeInputs = newRowElement.querySelector('[data-calendar-datetime="true"]');

      if (hasDateInputs || hasDateTimeInputs) {
        // Call the global initializeCalendars function with the new row as container
        if (typeof initializeCalendars === 'function') {
          initializeCalendars(newRowElement);
        }
      }
    }
  }

  deleteRow(event) {
    event.preventDefault();
    const $row = $(event.target).closest('tr');
    const $tbody = this.formsetTable.find('tbody');
    const rowCount = $tbody.children().length;

    // Find and destroy Select2 instances in the row to be deleted
    const $rowSelect2 = $row.find('.django-select2');
    if ($rowSelect2.length > 0) {
      $rowSelect2.select2('destroy');
    }

    // Clean up error messages and highlights
    this.cleanupErrors($row);

    // Don't delete if it's the last row
    if (rowCount <= 1) {
      this.resetRow($row);

      // Reinitialize Select2 on the reset row
      if ($rowSelect2.length > 0) {
        initializeSelect2($rowSelect2);
      }
      return;
    }

    // Delete the row and update form count
    $row.remove();
    this.totalFormsInput.val(rowCount - 1);

    // Reindex remaining rows
    this.reindexRows();
  }

  cleanupErrors($row) {
    // Remove error messages
    $row.find('.text-theme-status-error').remove();

    // Remove error highlights from inputs
    $row.find('input, select, textarea').removeClass('border-red-500');
    $row.find('input, select, textarea').addClass('border-theme-input-border-primary');

    $row.find('.select2-container--error').removeClass('select2-container--error');
    // Remove any error-related classes from the cells
    $row.find('td').removeClass('has-error error');

    // Clean up any error state classes from the form elements
    $row.find('.form-error').removeClass('form-error');

    // Remove any error message containers
    $row.find('.error-container').empty();
  }

  resetRow($row) {
    // Reset all inputs in the row
    $row.find('input, select, textarea').each(function() {
      const $input = $(this);
      if ($input.is(':checkbox, :radio')) {
        $input.prop('checked', false);
      } else {
        $input.val('');
      }
    });

    // Clear Select2 selections
    const $selects = $row.find('.django-select2');
    if ($selects.length > 0) {
      $selects.val(null).trigger('select2:clear');
    }

    // Clean up any error messages and highlights
    this.cleanupErrors($row);

    // Reinitialize calendars if any exist in the row
    const rowElement = $row[0]; // Get the DOM element from jQuery object
    if (rowElement) {
      const hasDateInputs = rowElement.querySelector('[data-calendar-date="true"]');
      const hasDateTimeInputs = rowElement.querySelector('[data-calendar-datetime="true"]');

      if (hasDateInputs || hasDateTimeInputs) {
        // Call the global initializeCalendars function with the row as container
        if (typeof initializeCalendars === 'function') {
          initializeCalendars(rowElement);
        }
      }
    }
  }

  updateFormIndex($row, newIndex) {
    $row.find('input, select, textarea, label').each((index, element) => {
      const $element = $(element);
      const name = $element.attr('name');
      const id = $element.attr('id');
      const forAttr = $element.attr('for');

      if (name) {
        // Update name attribute (e.g., prefix-0-field to prefix-1-field)
        $element.attr('name', name.replace(
            new RegExp(`${this.prefix}-(\\d+)-`),
            `${this.prefix}-${newIndex}-`,
        ));
      }
      if (id) {
        // Update id attribute (e.g., id_prefix-0-field to id_prefix-1-field)
        $element.attr('id', id.replace(
            new RegExp(`id_${this.prefix}-(\\d+)-`),
            `id_${this.prefix}-${newIndex}-`,
        ));
      }
      if (forAttr) {
        // Update for attribute in labels
        $element.attr('for', forAttr.replace(
            new RegExp(`id_${this.prefix}-(\\d+)-`),
            `id_${this.prefix}-${newIndex}-`,
        ));
      }
    });
  }

  reindexRows() {
    const $rows = this.formsetTable.find('tbody tr');

    // First destroy all Select2 instances to avoid conflicts during reindexing
    $rows.each((index, row) => {
      const $rowSelect2 = $(row).find('.django-select2');
      if ($rowSelect2.length > 0) {
        $rowSelect2.select2('destroy');
      }
    });

    // Reindex all rows
    $rows.each((index, row) => {
      this.updateFormIndex($(row), index);
    });

    // Reinitialize Select2 on all rows after reindexing
    $rows.each((index, row) => {
      const $rowSelect2 = $(row).find('.django-select2');
      if ($rowSelect2.length > 0) {
        initializeSelect2($rowSelect2);
      }

      // Reinitialize calendars if any exist in the row
      const hasDateInputs = row.querySelector('[data-calendar-date="true"]');
      const hasDateTimeInputs = row.querySelector('[data-calendar-datetime="true"]');

      if (hasDateInputs || hasDateTimeInputs) {
        // Call the global initializeCalendars function with the row as container
        if (typeof initializeCalendars === 'function') {
          initializeCalendars(row);
        }
      }
    });
  }
}

(function() {
  // Define constants
  const DEFAULT_PLACEHOLDER = '---------';

  // Initialize the form immediately
  setTimeout(function() {
    initializeItemAddForm();
    initializeExistingValues();
  }, 100); // Small delay to ensure DOM is ready

  // Handle form validation errors
  document.addEventListener('htmx:beforeSwap', function(evt) {
    // If the response contains a form with errors, we want to clean up Select2 instances
    if (evt.detail.xhr.status === 200 &&
        evt.detail.xhr.responseText.includes('goods-received-note-item') &&
        evt.detail.xhr.responseText.includes('text-red-500')) {

      // Clean up Select2 instances to prevent duplicates
      const selects = ['goods-received-note-item', 'goods-received-note-batch', 'goods-received-note-expiry'];
      selects.forEach(className => {
        const select = document.querySelector('.' + className);
        if (select && $(select).data('select2')) {
          try {
            $(select).select2('destroy');
          } catch (e) {
            console.log(`Select2 not initialized yet or already destroyed for ${className}`);
          }
        }
      });
    }
  });

  // Initialize the form when it's loaded
  // Helper function to check if a form field has a value
  function hasValue(field) {
    return field && field.value && field.value.trim() !== '';
  }

  function initializeItemAddForm() {
    const itemSelect = document.querySelector('.goods-received-note-item');
    const batchSelect = document.querySelector('.goods-received-note-batch');
    const expirySelect = document.querySelector('.goods-received-note-expiry');
    const uomField = document.querySelector('.goods-received-note-uom');
    const itemIdField = document.querySelector('input[name="item_id"]');
    const uomIdField = document.querySelector('input[name="uom_id"]');
    const batchHiddenField = document.querySelector('input[name="batch_no_hidden"]');
    const expiryHiddenField = document.querySelector('input[name="expiry_date_hidden"]');

    if (!itemSelect) return;

    // Get the API URL and consignor ID from the data attributes
    const apiUrl = itemSelect.getAttribute('data-api-url');
    const consignorId = itemSelect.getAttribute('data-consignor-id');

    if (!apiUrl || !consignorId) {
      console.error('API URL or consignor ID not found');
      return;
    }

    // Initialize batch and expiry selects
    if (batchSelect) {
      // Only initialize if we don't have existing values or the batch select is empty
      if (!batchSelect.options.length) {
        batchSelect.disabled = true;
        $(batchSelect).empty();
        $(batchSelect).append(new Option(DEFAULT_PLACEHOLDER, '', true, true));
      }
    }

    if (expirySelect) {
      if (!expirySelect.options.length) {
        expirySelect.disabled = true;
        $(expirySelect).empty();
        $(expirySelect).append(new Option(DEFAULT_PLACEHOLDER, '', true, true));
      }
    }

    // Handle item selection
    $(itemSelect).on('select2:select', function(e) {
      const item = $(this).select2('data')[0];

      // Set the item ID
      if (itemIdField) {
        itemIdField.value = item.id;
      }

      // Set the UOM
      if (uomField) {
        uomField.value = '';

        if (item.uom) {
          uomField.value = item.uom;
        }
      }

      if (uomIdField) {
        uomIdField.value = '';
        if (item.uom_id) {
          uomIdField.value = item.uom_id;
        }
      }

      // Update batch options if the item has batches
      if (batchSelect && item.batches && item.batches.length > 0) {
        // Clear previous options
        $(batchSelect).empty();
        $(batchSelect).append(new Option(DEFAULT_PLACEHOLDER, '', true, true));

        // Enable the select
        batchSelect.disabled = false;

        // Add options for each batch
        item.batches.forEach(batch => {
          const option = document.createElement('option');
          option.value = batch.batch_no;
          option.textContent = batch.batch_no;
          option.dataset.expiryDates = JSON.stringify(batch.expiry_dates || []);
          batchSelect.appendChild(option);
        });

        // Refresh Select2
        $(batchSelect).trigger('change');
      } else {
        // Disable and clear batch select if no batches
        batchSelect.disabled = true;
        $(batchSelect).empty();
        $(batchSelect).append(new Option(DEFAULT_PLACEHOLDER, '', true, true));
        $(batchSelect).trigger('change');
      }

      // Clear and disable expiry select
      if (expirySelect) {
        expirySelect.disabled = true;
        $(expirySelect).empty();
        $(expirySelect).append(new Option(DEFAULT_PLACEHOLDER, '', true, true));
        $(expirySelect).trigger('change');
      }
    });

    // Handle batch selection
    $(batchSelect).on('select2:select', function(e) {
      const batchNo = e.target.value;

      // Set the hidden field
      if (batchHiddenField) {
        batchHiddenField.value = batchNo;
      }

      // Update expiry date options
      if (expirySelect) {
        // Clear previous options
        $(expirySelect).empty();
        $(expirySelect).append(new Option(DEFAULT_PLACEHOLDER, '', true, true));

        // Always enable the expiry select when a batch is selected (for free text entry)
        expirySelect.disabled = false;

        // Get the selected batch option
        const selectedOption = batchSelect.options[batchSelect.selectedIndex];

        // If we don't have stock data, we still enable the expiry field for manual entry
        // but we don't populate it with options
        if (!selectedOption || !selectedOption.dataset.expiryDates) {
          $(expirySelect).trigger('change');
          return;
        }

        // Parse the stocks data
        let expiryDates = [];
        try {
          expiryDates = JSON.parse(selectedOption.dataset.expiryDates);
        } catch (e) {
          console.error('Error parsing stocks data:', e);
          expirySelect.disabled = true;
          $(expirySelect).trigger('change');
          return;
        }

        // Add options for each stock entry
        expiryDates.forEach(expiryDate => {
          const option = document.createElement('option');
          option.value = expiryDate;
          option.textContent = expiryDate;
          expirySelect.appendChild(option);
        });

        // Select the first option if available
        if (expiryDates.length > 0) {
          expirySelect.value = expiryDates[0];
          if (expiryHiddenField) {
            expiryHiddenField.value = expiryDates[0];
          }
        }

        // Refresh Select2
        $(expirySelect).trigger('change');
      }
    });

    // Handle expiry date selection
    $(expirySelect).on('select2:select', function(e) {
      const expiryDate = e.target.value;

      // Set the hidden field
      if (expiryHiddenField) {
        expiryHiddenField.value = expiryDate;
      }
    });

    // Handle clearing selections
    $(itemSelect).on('select2:clear', function() {
      if (itemIdField) itemIdField.value = '';
      if (uomField) uomField.value = '';
      if (uomIdField) uomIdField.value = '';

      // Disable and clear batch and expiry selects
      if (batchSelect) {
        batchSelect.disabled = true;
        $(batchSelect).empty();
        $(batchSelect).append(new Option(DEFAULT_PLACEHOLDER, '', true, true));
        $(batchSelect).trigger('change');
      }

      if (expirySelect) {
        expirySelect.disabled = true;
        $(expirySelect).empty();
        $(expirySelect).append(new Option(DEFAULT_PLACEHOLDER, '', true, true));
        $(expirySelect).trigger('change');
      }

    });

    $(batchSelect).on('select2:clear', function() {
      if (batchHiddenField) batchHiddenField.value = '';

      // Disable and clear expiry select
      if (expirySelect) {
        expirySelect.disabled = true;
        $(expirySelect).empty();
        $(expirySelect).append(new Option(DEFAULT_PLACEHOLDER, '', true, true));
        $(expirySelect).trigger('change');
      }

    });

    $(expirySelect).on('select2:clear', function() {
      if (expiryHiddenField) expiryHiddenField.value = '';
    });
  }

  async function initializeExistingValues() {
    const itemSelect = document.querySelector('.goods-received-note-item');
    const batchSelect = document.querySelector('.goods-received-note-batch');
    const expirySelect = document.querySelector('.goods-received-note-expiry');
    const itemIdField = document.querySelector('input[name="item_id"]');
    const batchHiddenField = document.querySelector('input[name="batch_no_hidden"]');
    const expiryHiddenField = document.querySelector('input[name="expiry_date_hidden"]');
    const uomField = document.querySelector('.goods-received-note-uom');
    const uomIdField = document.querySelector('input[name="uom_id"]');

    // Get existing values from hidden fields or selects
    const existingBatchNo = batchSelect && $(batchSelect).val() ? $(batchSelect).val() :
        (batchHiddenField ? batchHiddenField.value : null);
    const existingExpiryDate = expirySelect && expirySelect.value ? expirySelect.value :
        (expiryHiddenField ? expiryHiddenField.value : null);

    if (!itemSelect || !hasValue(itemIdField)) return;

    const consignorId = itemSelect.getAttribute('data-consignor-id');
    if (!consignorId) {
      console.error('Consignor ID not found');
      return;
    }

    try {
      // Fetch item data from API
      const response = await fetch(`/api/consignors/${consignorId}/items/${itemIdField.value}/`);
      if (!response.ok) {
        throw new Error(`API request failed with status ${response.status}`);
      }

      const itemData = await response.json();
      if (!itemData || !Object.keys(itemData).length) {
        console.error('No item data returned from API');
        return;
      }

      // Create the Select2 option data
      const itemOption = {
        id: itemData.id,
        text: itemData.name,
        batches: itemData.batches || [],
        uom: itemData.uom,
        uom_id: itemData.uom_id,
      };

      // Set the item in the Select2 dropdown
      if (!$(itemSelect).find(`option[value="${itemData.id}"]`).length) {
        // Create a new option if it doesn't exist
        const newOption = new Option(itemData.name, itemData.id, true, true);
        $(itemSelect).append(newOption);
      }

      // Trigger the select event to update the UI
      $(itemSelect).val(itemData.id).trigger('change');

      // Set UOM values
      if (uomField && itemData.uom) {
        uomField.value = itemData.uom;
      }

      if (uomIdField && itemData.uom_id) {
        uomIdField.value = itemData.uom_id;
      }

      // Handle batch selection if we have a batch value
      if (batchSelect && itemData.batches) {
        // Enable the batch select
        batchSelect.disabled = false;

        // Clear and populate batch options
        $(batchSelect).empty();
        $(batchSelect).append(new Option(DEFAULT_PLACEHOLDER, '', false, false));

        // Add all batch options
        itemData.batches.forEach(batch => {
          const isSelected = batch.batch_no === existingBatchNo;
          const option = new Option(batch.batch_no, batch.batch_no, isSelected, isSelected);
          option.dataset.expiryDates = JSON.stringify(batch.expiry_dates || []);
          $(batchSelect).append(option);
        });

        // Check if the existing batch value exists in the returned batches
        // If not, add it as an option
        if (existingBatchNo) {
          const batchExists = Array.from(itemData.batches).some(batch => batch.batch_no === existingBatchNo);
          if (!batchExists) {
            const option = new Option(existingBatchNo, existingBatchNo, true, true);
            option.dataset.expiryDates = JSON.stringify([]);
            $(batchSelect).append(option);
          }

          // Set the selected batch
          $(batchSelect).val(existingBatchNo).trigger('change');
        }

        // Now handle expiry date selection if we have an expiry value
        if (expirySelect && existingBatchNo) {
          const selectedBatchOption = Array.from(batchSelect.options).find(opt => opt.value === existingBatchNo);

          if (selectedBatchOption && selectedBatchOption.dataset.expiryDates) {
            try {
              const expiryDates = JSON.parse(selectedBatchOption.dataset.expiryDates);

              // Always enable the expiry select when a batch is selected (for free text entry)
              expirySelect.disabled = false;

              // Clear and populate expiry options
              $(expirySelect).empty();
              $(expirySelect).append(new Option(DEFAULT_PLACEHOLDER, '', false, false));

              // Add all expiry options
              expiryDates.forEach(expiryDate => {
                const isSelected = expiryDate === existingExpiryDate;
                const option = new Option(expiryDate, expiryDate, isSelected, isSelected);
                $(expirySelect).append(option);
              });

              // Check if the existing expiry value exists in the returned stocks
              // If not, add it as an option
              if (existingExpiryDate) {
                const expiryExists = Array.from(expiryDates).some(date => date === existingExpiryDate);
                if (!expiryExists) {
                  const option = new Option(existingExpiryDate, existingExpiryDate, true, true);
                  $(expirySelect).append(option);
                }

                // Set the selected expiry date
                $(expirySelect).val(existingExpiryDate).trigger('change');
              }
            } catch (e) {
              console.error('Error parsing stocks data:', e);
            }
          }
        }
      }
    } catch (error) {
      console.error('Error initializing existing values:', error);
    }
  }

})();

document.addEventListener('DOMContentLoaded', function() {
  const consignorSelect = document.getElementById('id_consignor');
  const consignorIdField = document.getElementById('id_consignor_id');
  const formsetContainer = document.querySelector('#goods_received_note_form');
  const DEFAULT_PLACEHOLDER = '---------';

  // Function to update item select options based on consignor
  function updateItemSelectApiUrlByConsignor(itemSelectElement, consignorId) {
    if (!consignorId) {
      itemSelectElement.disabled = true;
      return;
    }

    // Enable the item select
    itemSelectElement.disabled = false;

    // Update the data-api-url attribute with the actual consignor ID
    const apiUrlTemplate = itemSelectElement.getAttribute(
            'data-api-url-template') ||
        itemSelectElement.getAttribute('data-api-url');

    // Store the original template if not already stored
    if (!itemSelectElement.hasAttribute('data-api-url-template')) {
      itemSelectElement.setAttribute('data-api-url-template', apiUrlTemplate);
    }

    const apiUrl = apiUrlTemplate.replace('{consignor_id}', consignorId);
    itemSelectElement.setAttribute('data-api-url', apiUrl);

    // Destroy and reinitialize Select2 to apply the new API URL
    $(itemSelectElement).select2('destroy');

    // Use the global initializeSelect2 function from select2.js
    window.initializeSelect2(itemSelectElement);

    // Make sure batch and expiry selects have tags enabled
    const row = itemSelectElement.closest('tr');
    if (row) {
      const batchSelect = row.querySelector('.goods-received-note-batch');
      const expirySelect = row.querySelector('.goods-received-note-expiry');

      if (batchSelect && !batchSelect.hasAttribute('data-tags')) {
        batchSelect.setAttribute('data-tags', 'true');
        $(batchSelect).select2('destroy');
        window.initializeSelect2(batchSelect);
      }

      if (expirySelect && !expirySelect.hasAttribute('data-tags')) {
        expirySelect.setAttribute('data-tags', 'true');
        $(expirySelect).select2('destroy');
        window.initializeSelect2(expirySelect);
      }
    }
  }

  // Function to update batch options based on a selected item
  function updateBatchOptions(itemSelect, batchSelect, selectedBatchNo) {
    // Clear existing options
    batchSelect.innerHTML = `<option value="">${DEFAULT_PLACEHOLDER}</option>`;

    // Enable the batch select after item is selected
    batchSelect.disabled = false;

    // Get the selected item data
    let selectedItem = $(itemSelect).select2('data')[0];

    // Add batch options
    selectedItem.batches.forEach(batch => {
      const option = document.createElement('option');
      option.value = batch.batch_no;
      option.textContent = batch.batch_no;
      option.dataset.expiryDates = JSON.stringify(batch.expiry_dates || []);
      batchSelect.appendChild(option);
    });

    if (selectedBatchNo) {
      const batchExists = Array.from(selectedItem.batches).some(opt => opt.batch_no === selectedBatchNo);
      if (!batchExists) {
        const option = document.createElement('option');
        option.value = selectedBatchNo;
        option.textContent = selectedBatchNo;
        option.dataset.expiryDates = JSON.stringify([]);
        batchSelect.appendChild(option);
      }
    }

    if (selectedBatchNo) {
      const batchHiddenInput = itemSelect.closest('tr').querySelector('input[name$="-batch_no_hidden"]');
      $(batchSelect).val(selectedBatchNo);
      if (batchHiddenInput) {
        batchHiddenInput.value = selectedBatchNo;
      }
    }

    // Trigger change event to refresh Select2
    $(batchSelect).trigger('change');
  }

  // Function to update expiry date options based on selected batch
  function updateExpiryOptions(batchSelect, expiryDateInput, selectedExpiryDate) {
    // Clear existing options
    expiryDateInput.innerHTML = `<option value="">${DEFAULT_PLACEHOLDER}</option>`;

    // Always enable the expiry select when a batch is selected (for free text entry)
    expiryDateInput.disabled = false;

    // Get the selected batch data
    const selectedOption = batchSelect.options[batchSelect.selectedIndex];

    // Parse the stocks data
    const expiryDates = JSON.parse(selectedOption.dataset.expiryDates || '[]');

    // Add expiry date options
    expiryDates.forEach(expiryDate => {
      const option = document.createElement('option');
      option.value = expiryDate;
      option.textContent = expiryDate;
      expiryDateInput.appendChild(option);
    });

    if (selectedExpiryDate) {
      const expiryExists = Array.from(expiryDates).some(date => date === selectedExpiryDate);
      if (!expiryExists) {
        const option = document.createElement('option');
        option.value = selectedExpiryDate;
        option.textContent = selectedExpiryDate;
        expiryDateInput.appendChild(option);
      }
    }

    const expiryDateHiddenInput = batchSelect.closest('tr').querySelector('input[name$="-expiry_date_hidden"]');
    if (selectedExpiryDate) {
      $(expiryDateInput).val(selectedExpiryDate);
      if (expiryDateHiddenInput) {
        expiryDateHiddenInput.value = selectedExpiryDate;
      }
    } else if (expiryDates.length > 0) {
      expiryDateInput.value = expiryDates[0];
      if (expiryDateHiddenInput) {
        expiryDateHiddenInput.value = expiryDates[0];
      }
    }

    // Trigger change event to refresh Select2
    $(expiryDateInput).trigger('change');
  }

  // Function to update UOM based on selected item
  function updateUom(itemSelect, uomInput, uomHiddenInput) {
    // Get the selected item data
    const selectedItem = $(itemSelect).select2('data')[0];

    if (!selectedItem || !selectedItem.uom) {
      // Clear and disable the UOM input
      uomInput.value = '';
      uomHiddenInput.value = '';
      uomInput.disabled = true;
      return;
    }

    // Set the UOM value and ensure it's disabled (read-only)
    uomInput.value = selectedItem.uom;
    uomHiddenInput.value = selectedItem.uom_id;
    uomInput.disabled = true;
  }

  // Function to reset dependent fields
  function resetDependentFields(row, skipHiddenFields = false) {
    const batchSelect = row.querySelector('.goods-received-note-batch');
    const expiryDateInput = row.querySelector('.goods-received-note-expiry');
    const itemIdInput = row.querySelector('input[name$="-item_id"]');
    const uomInput = row.querySelector('.goods-received-note-uom');
    const uomIdInput = row.querySelector('input[name$="-uom_id"]');

    if (batchSelect) {
      batchSelect.innerHTML = `<option value="">${DEFAULT_PLACEHOLDER}</option>`;
      batchSelect.disabled = true;
      $(batchSelect).val(null).trigger('change');
    }

    if (expiryDateInput) {
      // Clear existing options
      expiryDateInput.innerHTML = `<option value="">${DEFAULT_PLACEHOLDER}</option>`;
      expiryDateInput.disabled = true;
      $(expiryDateInput).val(null).trigger('change');
    }

    // Reset UOM field
    if (uomInput) {
      uomInput.value = '';
      uomInput.disabled = true;
    }

    // Reset hidden inputs
    if (itemIdInput) {
      itemIdInput.value = '';
    }

    if (uomIdInput) {
      uomIdInput.value = '';
    }

    // Reset batch_no and expiry_date hidden inputs only if not skipping hidden fields
    if (!skipHiddenFields) {
      const batchNoHiddenInput = row.querySelector('input[name$="-batch_no_hidden"]');
      if (batchNoHiddenInput) {
        batchNoHiddenInput.value = '';
      }

      const expiryDateHiddenInput = row.querySelector('input[name$="-expiry_date_hidden"]');
      if (expiryDateHiddenInput) {
        expiryDateHiddenInput.value = '';
      }
    }
  }

  // Function to update consignor select options
  function updateConsignorSelectApiUrl(consignorSelectElement, consignorId) {
    if (!consignorSelectElement) {
      return;
    }

    // If we have a consignor ID, we need to fetch the consignor data
    if (consignorId) {
      // Fetch the consignor data from the API
      fetch(`/api/consignors/${consignorId}/`).then(response => {
        if (!response.ok) {
          throw new Error('Failed to fetch consignor data');
        }
        return response.json();
      }).then(data => {
        // Create a new option for the consignor
        const option = new Option(data.name, data.id, true, true);

        // Destroy and reinitialize Select2
        $(consignorSelectElement).select2('destroy');

        // Clear existing options and add the new one
        consignorSelectElement.innerHTML = '';
        consignorSelectElement.appendChild(option);

        // Initialize Select2
        window.initializeSelect2(consignorSelectElement);

        // Trigger change event to update dependent fields
        $(consignorSelectElement).trigger('change');
      }).catch(error => {
        console.error('Error fetching consignor data:', error);
        // Just initialize Select2 without the data
        $(consignorSelectElement).select2('destroy');
        window.initializeSelect2(consignorSelectElement);
      });
    } else {
      // Just initialize Select2 without the data
      $(consignorSelectElement).select2('destroy');
      window.initializeSelect2(consignorSelectElement);
    }
  }

  // Simple function to update hidden ID field with the select value
  function updateHiddenIdField(hiddenField, selectValue) {
    if (hiddenField) {
      hiddenField.value = selectValue || '';
    }
  }

  // --- Event Listener for Consignor Change ---
  if (consignorSelect) {
    $(consignorSelect).on('select2:select select2:clear', function(e) {
      const selectedConsignorId = this.value;

      updateHiddenIdField(consignorIdField, selectedConsignorId);

      // Update all item selects within the formset table
      formsetContainer.querySelectorAll('.goods-received-note-item').
          forEach(itemSelect => {
            // Reset the item select value
            itemSelect.value = '';
            $(itemSelect).val(null).trigger('change');

            // Update API URL with the new consignor ID
            updateItemSelectApiUrlByConsignor(itemSelect, selectedConsignorId);

            // Reset dependent fields
            const row = itemSelect.closest('tr');
            if (row) {
              resetDependentFields(row);
            }
          });
    });

    // --- Initial load for consignor ---
    if (consignorIdField.value) {
      const selectedConsignorId = consignorIdField.value;

      // Preselect the consignor dropdown based on the consignorIdField value
      if (consignorSelect) {
        // Initialize the Select2 for consignor with the selected consignor ID
        updateConsignorSelectApiUrl(consignorSelect, selectedConsignorId);
      }

      // Update all item selects with the consignor ID
      formsetContainer.querySelectorAll('.goods-received-note-item').
          forEach(itemSelect => {
            const row = itemSelect.closest('tr');
            if (row) {
              updateItemSelectApiUrlByConsignor(itemSelect, selectedConsignorId);
              // Initialize existing values if needed
              if (row.querySelector('input[name$="-item_id"]')?.value) {
                initializeExistingValues(row, selectedConsignorId);
              }
            }
          });
    } else {
      // Disable all item selects if no consignor is selected
      formsetContainer.querySelectorAll('.goods-received-note-item').
          forEach(itemSelect => {
            itemSelect.disabled = true;
            const row = itemSelect.closest('tr');
            if (row) {
              resetDependentFields(row);
            }
          });
    }
  }

  // Function to handle initial form load with existing values
  async function initializeExistingValues(row, consignorId) {
    const itemSelect = row.querySelector('.goods-received-note-item');
    const batchSelect = row.querySelector('.goods-received-note-batch');
    const expiryDateInput = row.querySelector('.goods-received-note-expiry');
    const itemIdInput = row.querySelector('input[name$="-item_id"]');
    const batchHiddenInput = row.querySelector('input[name$="-batch_no_hidden"]');
    const expiryDateHiddenInput = row.querySelector('input[name$="-expiry_date_hidden"]');

    const existingBatchNo = batchSelect && $(batchSelect).val() ? $(batchSelect).val() :
        (batchHiddenInput ? batchHiddenInput.value : null);
    const existingExpiryDate = expiryDateInput && expiryDateInput.value ? expiryDateInput.value :
        (expiryDateHiddenInput ? expiryDateHiddenInput.value : null);

    if (!itemSelect || !itemIdInput) return;

    // If we have a stored item_id and consignor_id, fetch the item data
    const itemId = itemIdInput.value;

    if (consignorId && itemId) {
      try {
        // Wait for Select2 initialization
        await new Promise(resolve => setTimeout(resolve, 300));

        // Fetch item data from API
        const response = await fetch(`/api/consignors/${consignorId}/items/${itemId}/`);
        const itemData = await response.json();
        if (itemData && Object.keys(itemData).length) {
          // Create the Select2 option data
          const itemOption = {
            id: itemData.id,
            text: itemData.name,
            batches: itemData.batches,
            uom: itemData.uom,
            uom_id: itemData.uom_id,
          };

          // Set the item select value with a flag to indicate initialization
          $(itemSelect).select2('trigger', 'select', {
            data: itemOption,
            isInitializing: true,  // Add a flag to indicate this is an initialization
          });

          await new Promise(resolve => setTimeout(resolve, 50));

          // Update batch options
          if (batchSelect && itemData.batches) {
            updateBatchOptions(itemSelect, batchSelect, existingBatchNo);

            if (expiryDateInput && existingBatchNo) {
              updateExpiryOptions(batchSelect, expiryDateInput, existingExpiryDate);
            }
          }
        }
      } catch (error) {
        console.error('Error initializing existing values:', error);
      }
    }
  }

  // --- Event Delegation for Item and Batch Changes ---
  if (formsetContainer) {
    // Item select events
    $(formsetContainer).
        on('select2:select select2:clear', '.goods-received-note-item', function(e) {
          const row = this.closest('tr');
          if (row) {
            let event_this = this;

            // Check if this is an initialization event
            const isInitializing = e.params && e.params.isInitializing;

            // Skip resetting hidden fields if we're initializing existing values
            resetDependentFields(row, isInitializing);

            if (e.type === 'select2:select') {
              const batchSelect = row.querySelector('.goods-received-note-batch');
              const expiryDateInput = row.querySelector('.goods-received-note-expiry');
              const itemIdInput = row.querySelector('input[name$="-item_id"]');
              const uomInput = row.querySelector('.goods-received-note-uom');
              const uomIdInput = row.querySelector('input[name$="-uom_id"]');

              // Make sure batch has tags enabled
              if (batchSelect) {
                if (!batchSelect.hasAttribute('data-tags')) {
                  batchSelect.setAttribute('data-tags', 'true');
                  $(batchSelect).select2('destroy');
                  window.initializeSelect2(batchSelect);
                }
                updateBatchOptions(event_this, batchSelect);
              }

              // Make sure expiry has tags enabled
              if (expiryDateInput) {
                if (!expiryDateInput.hasAttribute('data-tags')) {
                  expiryDateInput.setAttribute('data-tags', 'true');
                  $(expiryDateInput).select2('destroy');
                  window.initializeSelect2(expiryDateInput);
                }
              }

              // Update UOM field
              if (uomInput) {
                updateUom(event_this, uomInput, uomIdInput);
              }

              // Update item_id hidden field
              if (itemIdInput) {
                const selectedItem = $(event_this).select2('data')[0];
                if (selectedItem && selectedItem.id) {
                  itemIdInput.value = selectedItem.id;
                }
              }
            }
          }
        });

    // Batch select events
    $(formsetContainer).on('select2:select select2:clear', '.goods-received-note-batch', function(e) {
      const row = this.closest('tr');
      if (row) {
        const expirySelect = row.querySelector('.goods-received-note-expiry');
        const expiryDateHiddenInput = row.querySelector('input[name$="-expiry_date_hidden"]');
        const batchNoHiddenInput = row.querySelector('input[name$="-batch_no_hidden"]');

        // Reset expiry date
        if (expirySelect) {
          expirySelect.value = '';
          expirySelect.disabled = true;
        }

        if (expiryDateHiddenInput) {
          expiryDateHiddenInput.value = '';
        }

        if (this.value) {
          if (batchNoHiddenInput) {
            batchNoHiddenInput.value = this.value;
          }

          if (expirySelect) {
            updateExpiryOptions(this, expirySelect);
          }
        } else {
          if (batchNoHiddenInput) {
            batchNoHiddenInput.value = '';
          }
        }
      }
    });

    // Expiry date select events
    $(formsetContainer).
        on('select2:select select2:clear', '.goods-received-note-expiry', function(e) {
          const row = this.closest('tr');
          if (row) {
            const expiryDateHiddenInput = row.querySelector('input[name$="-expiry_date_hidden"]');

            // Update expiry_date_hidden field
            if (expiryDateHiddenInput) {
              if (e.type === 'select2:select') {
                expiryDateHiddenInput.value = this.options[this.selectedIndex].textContent;
              } else {
                expiryDateHiddenInput.value = '';
              }
            }
          }
        });
  }
});

document.addEventListener('DOMContentLoaded', function() {
  const transferFromSelect = document.getElementById('id_transfer_from');
  const formsetContainer = document.querySelector('#transfer_form');
  const DEFAULT_PLACEHOLDER = '---------';

  // Function to update stock select options based on warehouse
  function updateStockSelectApiUrl(stockSelectElement, warehouseId) {
    if (!warehouseId) {
      stockSelectElement.disabled = true;
      return;
    }

    // Enable the stock select
    stockSelectElement.disabled = false;

    // Update the data-api-url attribute with the actual warehouse ID
    const apiUrlTemplate = stockSelectElement.getAttribute(
            'data-api-url-template') ||
        stockSelectElement.getAttribute('data-api-url');

    // Store the original template if not already stored
    if (!stockSelectElement.hasAttribute('data-api-url-template')) {
      stockSelectElement.setAttribute('data-api-url-template', apiUrlTemplate);
    }

    const apiUrl = apiUrlTemplate.replace('{warehouse_id}', warehouseId);
    stockSelectElement.setAttribute('data-api-url', apiUrl);

    // Destroy and reinitialize Select2 to apply the new API URL
    $(stockSelectElement).select2('destroy');

    // Use the global initializeSelect2 function from select2.js
    window.initializeSelect2(stockSelectElement);
  }

  // Function to update batch options based on selected stock
  function updateBatchOptions(stockSelect, batchSelect, selectedBatchNo) {
    // Clear existing options
    batchSelect.innerHTML = `<option value="">${DEFAULT_PLACEHOLDER}</option>`;
    // Disable by default
    batchSelect.disabled = true;

    // Get the selected stock data
    let selectedStock = $(stockSelect).select2('data')[0];
    if (!selectedStock || !selectedStock.batches ||
        selectedStock.batches.length === 0) {
      return;
    }

    // Enable the batch select
    batchSelect.disabled = false;

    // Add batch options
    selectedStock.batches.forEach(batch => {
      const option = document.createElement('option');
      option.value = batch.batch_no;
      option.textContent = batch.batch_no;
      option.dataset.stocks = JSON.stringify(batch.stocks);
      batchSelect.appendChild(option);
    });

    if (selectedBatchNo) {
      $(batchSelect).val(selectedBatchNo);
    }
    // Trigger change event to refresh Select2
    $(batchSelect).trigger('change');
  }

  // Function to update expiry date options based on selected batch
  function updateExpiryOptions(batchSelect, expirySelect, stockId) {
    // Clear existing options
    expirySelect.innerHTML = `<option value="">${DEFAULT_PLACEHOLDER}</option>`;

    // Disable by default
    expirySelect.disabled = true;

    // Get the selected batch data
    const selectedOption = batchSelect.options[batchSelect.selectedIndex];

    if (!selectedOption || !selectedOption.dataset.stocks) {
      return;
    }

    // Parse the stocks data
    const stocks = JSON.parse(selectedOption.dataset.stocks);

    if (stocks.length === 0) {
      return;
    }

    // Enable the expiry select
    expirySelect.disabled = false;

    // Add expiry date options
    stocks.forEach(stock => {
      const option = document.createElement('option');
      option.value = stock.id; // Use stock ID as the value
      option.textContent = stock.expiry_date;
      option.dataset.availableBalance = stock.available_balance;
      option.dataset.uomId = stock.uom_id;  // Store UOM ID in dataset
      expirySelect.appendChild(option);
    });

    // Auto-select the first expiry date option if available
    if (stocks.length > 0) {
      if (stockId) {
        $(expirySelect).val(stockId);
      } else {
        // Set the value to the first stock's ID
        expirySelect.value = stocks[0].id;
      }

      // Trigger the change event for Select2 to update
      $(expirySelect).trigger('change');

      // Also trigger the select2:select event to ensure any listeners respond
      const selectEvent = new Event('select2:select', {bubbles: true});
      expirySelect.dispatchEvent(selectEvent);
    }
  }

  // Function to update UOM based on selected stock
  function updateUom(stockSelect, uomInput) {
    // Get the selected stock data
    const selectedStock = $(stockSelect).select2('data')[0];

    if (!selectedStock || !selectedStock.uom) {
      // Clear and disable the UOM input
      uomInput.value = '';
      uomInput.disabled = true;
      return;
    }

    // Set the UOM value and ensure it's disabled (read-only)
    uomInput.value = selectedStock.uom;
    uomInput.disabled = true;
  }

  // Function to update item_id based on selected stock
  function updateItemId(stockSelect, itemIdInput) {
    // Get the selected stock data
    const selectedStock = $(stockSelect).select2('data')[0];

    if (!selectedStock || !selectedStock.id) {
      // Clear the item_id input
      itemIdInput.value = '';
      return;
    }

    // Set the item_id value (the stock select2 data returns item.id as the id)
    itemIdInput.value = selectedStock.id;
  }

  // Function to update balance field with available balance
  function updateBalance(expirySelect, balanceInput) {

    // Get the selected option
    const selectedOption = expirySelect.options[expirySelect.selectedIndex];

    if (!selectedOption || !selectedOption.dataset.availableBalance) {
      balanceInput.value = '';
      return;
    }

    // Set the balance field to the available balance
    balanceInput.value = selectedOption.dataset.availableBalance;

    // Make sure the balance field is disabled (read-only)
    balanceInput.disabled = true;
  }

  // Function to reset dependent fields
  function resetDependentFields(row) {
    const batchSelect = row.querySelector('.transfer-item-batch');
    const expirySelect = row.querySelector('.transfer-item-expiry');
    const uomInput = row.querySelector('.transfer-item-uom');
    const balanceInput = row.querySelector('.transfer-item-balance');
    const stockIdInput = row.querySelector('input[name$="-stock_id"]');
    const itemIdInput = row.querySelector('input[name$="-item_id"]');
    const uomIdInput = row.querySelector('input[name$="-uom_id"]');

    if (batchSelect) {
      batchSelect.innerHTML = `<option value="">${DEFAULT_PLACEHOLDER}</option>`;
      batchSelect.disabled = true;
      $(batchSelect).val(null).trigger('change');
    }

    if (expirySelect) {
      expirySelect.innerHTML = `<option value="">${DEFAULT_PLACEHOLDER}</option>`;
      expirySelect.disabled = true;
      $(expirySelect).val(null).trigger('change');
    }

    if (uomInput) {
      uomInput.value = '';
      uomInput.disabled = true;
    }

    if (balanceInput) {
      balanceInput.value = '';
      balanceInput.disabled = true;
    }

    // Reset hidden inputs
    if (stockIdInput) {
      stockIdInput.value = '';
    }

    if (itemIdInput) {
      itemIdInput.value = '';
    }

    if (uomIdInput) {
      uomIdInput.value = '';
    }
  }

  // --- Event Listener for Warehouse Change ---
  if (transferFromSelect) {
    $(transferFromSelect).on('select2:select select2:clear', function() {
      const selectedWarehouseId = this.value;

      // Update all stock selects within the formset table
      formsetContainer.querySelectorAll('.transfer-item-stock').
          forEach(stockSelect => {
            // Reset the stock select value
            stockSelect.value = '';
            $(stockSelect).val(null).trigger('change');

            // Update API URL with the new warehouse ID
            updateStockSelectApiUrl(stockSelect, selectedWarehouseId);

            // Reset dependent fields
            const row = stockSelect.closest('tr');
            if (row) {
              resetDependentFields(row);
            }
          });
    });

    // Function to handle initial form load with existing values
    async function initializeExistingValues(row) {
      const stockSelect = row.querySelector('.transfer-item-stock');
      const batchSelect = row.querySelector('.transfer-item-batch');
      const expirySelect = row.querySelector('.transfer-item-expiry');
      const stockIdInput = row.querySelector('input[name$="-stock_id"]');
      const itemIdInput = row.querySelector('input[name$="-item_id"]');

      if (!stockSelect || !stockIdInput || !itemIdInput) return;

      // If we have a stored stock_id and warehouse_id, fetch the stock data
      const warehouseId = transferFromSelect.value;
      const stockId = stockIdInput.value;
      const itemId = itemIdInput.value;

      if (warehouseId && itemId) {
        try {
          // Wait for Select2 initialization
          await new Promise(resolve => setTimeout(resolve, 300));

          // Set the stock Select2 value
          const stockUrl = stockSelect.getAttribute('data-api-url').
              replace('{warehouse_id}', warehouseId);

          // Fetch stock data from API
          const response = await fetch(`${stockUrl}${itemId}`);
          const stockData = await response.json();

          if (stockData && Object.keys(stockData).length) {
            // Create the Select2 option data
            const stockOption = {
              id: stockData.id,
              text: stockData.name,
              batches: stockData.batches,
              uom: stockData.uom,
            };
            $(stockSelect).select2('trigger', 'select', {
              data: stockOption,
            });

            // Update batch options
            if (batchSelect) {
              // Find and select the correct batch
              const selectedBatchNo = stockData.batches.find(
                  batch => batch.stocks.some(stock => stock.id === parseInt(stockId)),
              )?.batch_no;

              updateBatchOptions(stockSelect, batchSelect, selectedBatchNo);

              // Update expiry options and select the correct one
              if (expirySelect) {
                updateExpiryOptions(batchSelect, expirySelect, stockId);
              }
            }
          }
        } catch (error) {
          console.error('Error initializing existing values:', error);
        }
      }
    }

    // --- Initial load ---
    if (transferFromSelect.value) {
      const warehouseId = transferFromSelect.value;
      // Use querySelectorAll with specific class instead of 'tr'
      formsetContainer.querySelectorAll('.transfer-item-stock').
          forEach(stockSelect => {
            const row = stockSelect.closest('tr');
            if (row) {
              updateStockSelectApiUrl(stockSelect, warehouseId);
              // Only initialize if we have existing values
              if (row.querySelector('input[name$="-stock_id"]')?.value) {
                initializeExistingValues(row);
              }
            }
          });
    } else {
      formsetContainer.querySelectorAll('.transfer-item-stock').
          forEach(stockSelect => {
            stockSelect.disabled = true;
            const row = stockSelect.closest('tr');
            if (row) {
              resetDependentFields(row);
            }
          });
    }
  }

  // --- Event Delegation for Stock, Batch, and Expiry Changes ---
  if (formsetContainer) {
    // Stock select events
    $(formsetContainer).
        on('select2:select select2:clear', '.transfer-item-stock', function(e) {
          const row = this.closest('tr');
          if (row) {
            let event_this = this;
            resetDependentFields(row);

            if (e.type === 'select2:select') {
              const batchSelect = row.querySelector('.transfer-item-batch');
              const uomInput = row.querySelector('.transfer-item-uom');
              const itemIdInput = row.querySelector('input[name$="-item_id"]');

              if (batchSelect) {
                updateBatchOptions(event_this, batchSelect);
              }

              if (uomInput) {
                updateUom(event_this, uomInput);
              }

              if (itemIdInput) {
                updateItemId(event_this, itemIdInput);
              }
            }
          }
        });

    // Batch select events
    $(formsetContainer).
        on('select2:select select2:clear', '.transfer-item-batch', function(e) {
          const row = this.closest('tr');
          if (row) {
            const expirySelect = row.querySelector('.transfer-item-expiry');
            const balanceInput = row.querySelector('.transfer-item-balance');
            const stockIdInput = row.querySelector('input[name$="-stock_id"]');
            const uomIdInput = row.querySelector('input[name$="-uom_id"]');

            // Reset expiry-related fields
            if (expirySelect) {
              expirySelect.innerHTML = `<option value="">${DEFAULT_PLACEHOLDER}</option>`;
              expirySelect.disabled = true;
              $(expirySelect).val(null).trigger('change');
            }

            if (balanceInput) {
              balanceInput.value = '';
              balanceInput.disabled = true;
            }

            // Reset hidden inputs
            if (stockIdInput) {
              stockIdInput.value = '';
            }

            if (uomIdInput) {
              uomIdInput.value = '';
            }

            // If this is a select event (not clear), update the expiry options
            if (e.type === 'select2:select' && expirySelect) {
              updateExpiryOptions(this, expirySelect);
            }
          }
        });

    // Expiry select events
    $(formsetContainer).
        on('select2:select select2:clear', '.transfer-item-expiry',
            function(e) {
              const row = this.closest('tr');
              if (row) {
                const balanceInput = row.querySelector(
                    '.transfer-item-balance');
                const stockIdInput = row.querySelector(
                    'input[name$="-stock_id"]');
                const uomIdInput = row.querySelector('input[name$="-uom_id"]');
                const itemIdInput = row.querySelector(
                    'input[name$="-item_id"]');

                // Always reset values first
                stockIdInput.value = '';
                uomIdInput.value = '';
                // Don't reset item_id here as it should persist

                if (e.type === 'select2:select') {
                  const selectedOption = this.options[this.selectedIndex];

                  if (selectedOption) {
                    stockIdInput.value = selectedOption.value;
                    if (selectedOption.dataset.uomId) {
                      uomIdInput.value = selectedOption.dataset.uomId;
                    }
                  }

                  if (balanceInput) {
                    setTimeout(() => {
                      updateBalance(this, balanceInput);
                    }, 10);
                  }
                } else {
                  // Clear event
                  if (balanceInput) {
                    balanceInput.value = '';
                    balanceInput.disabled = true;
                  }
                }
              }
            });
  }
});

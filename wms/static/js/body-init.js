document.addEventListener('alpine:init', () => {
  Alpine.data('bodyInit', () => ({
    init() {
      // Listener for the notification signal
      this.$el.addEventListener('showNotificationEvent', (event) => {
        if(event.detail && event.detail.message) {
          // Re-dispatch as an Alpine event ('notify')
          window.dispatchEvent(new CustomEvent('notify', { detail: event.detail }));
        } else {
          console.warn('showNotificationEvent received on body, but no details found.', event);
        }
      });

      // Listener for closing modals
      this.$el.addEventListener('closeModalEvent', (event) => {
        // Close the modal
        this.$dispatch('close-modal');
      });

      // Listener for page refresh
      this.$el.addEventListener('refreshPage', (event) => {
        // Refresh the current page
        window.location.reload();
      });

      // Listener for tab content refresh
      this.$el.addEventListener('refreshTabContent', (event) => {
        // If there's a URL in the event detail, load that URL into the tab content
        if (event.detail && event.detail.url) {
          // Find the tab content container
          const tabContent = document.getElementById('tab-content');
          if (tabContent) {
            // Use HTMX to load the content
            htmx.ajax('GET', event.detail.url, { target: tabContent, swap: 'innerHTML' });
          }
        }
      });

      // Listener for redirect event
      this.$el.addEventListener('redirectEvent', (event) => {
        // If the event contains a URL, redirect to that URL
        if (event.detail && event.detail.url) {
          window.location.href = event.detail.url;
        }
      });
    }
  }));
});

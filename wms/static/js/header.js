document.addEventListener('alpine:init', () => {
  Alpine.data('headerProfile', () => ({
    isOpen: false,
    closeDropdown() {
      this.isOpen = false;
    },
    toggleDropdown() {
      this.isOpen = !this.isOpen;
    },
    init() {
      const logoutForm = document.getElementById('logout-form');
      if (logoutForm) {
        logoutForm.addEventListener('submit', (e) => {
          e.preventDefault();
          fetch(logoutForm.action, {
            method: 'POST',
            body: new FormData(logoutForm),
            headers: {
              'X-CSRFToken': document.querySelector(
                  '[name=csrfmiddlewaretoken]').value,
            },
          }).then(() => {
            window.location.href = '/';
          });
        });
      }
    },
  }));

  // Add keyboard shortcuts to the existing sidebar store
  Alpine.store('sidebar', {
    isOpen: localStorage.getItem('sidebarOpen') !== 'false',
    isClicked: false,
    mouseEnterTimeout: null,
    mouseLeaveTimeout: null,
    showFooter: false,

    toggle() {
      this.isOpen = !this.isOpen;
      localStorage.setItem('sidebarOpen', this.isOpen);
      this.updateMainContentWidth();
    },

    handleMouseEnter() {
      clearTimeout(this.mouseLeaveTimeout);
      if (!this.isOpen) {
        this.mouseEnterTimeout = setTimeout(() => {
          this.isClicked = true;
        }, 300);
      }
    },

    handleMouseLeave() {
      clearTimeout(this.mouseEnterTimeout);
      if (!this.isOpen) {
        this.mouseLeaveTimeout = setTimeout(() => {
          this.isClicked = false;
        }, 300);
      }
    },

    updateMainContentWidth() {
      const mainContent = document.querySelector('main');
      if (mainContent) {
        mainContent.style.marginLeft = this.isOpen ? '16rem' : '0';
        mainContent.style.width = this.isOpen ? 'calc(100% - 16rem)' : '100%';
      }
    },

    init() {
      this.isOpen = localStorage.getItem('sidebarOpen') !== 'false';
      this.updateMainContentWidth();

      // Add keyboard shortcut listener
      document.addEventListener('keydown', (e) => {
        // Check for Ctrl + \ combination
        if (e.ctrlKey && e.key === '\\') {
          e.preventDefault();
          this.toggle();

          // Show feedback tooltip
          this.showShortcutFeedback();
        }
      });

      // Update layout on window resize
      window.addEventListener('resize', () => this.updateMainContentWidth());
    },

    showShortcutFeedback() {
      // Create feedback element if it doesn't exist
      let feedback = document.getElementById('shortcut-feedback');
      if (!feedback) {
        feedback = document.createElement('div');
        feedback.id = 'shortcut-feedback';
        feedback.className = 'fixed bottom-4 right-4 bg-gray-800 text-white px-4 py-1.5 rounded-lg shadow-lg z-50 opacity-0 transition-opacity duration-300';
        document.body.appendChild(feedback);
      }

      // Update and show feedback
      feedback.textContent = this.isOpen ?
          'Sidebar Opened (Ctrl+\\)' :
          'Sidebar Closed (Ctrl+\\)';
      feedback.style.opacity = '1';

      // Hide after 2 seconds
      setTimeout(() => {
        feedback.style.opacity = '0';
      }, 2000);
    },

    toggleFooter() {
      this.showFooter = !this.showFooter;
    },
  });
});

// Wrap in IIFE to avoid polluting global namespace but expose needed functions
(function(window) {
  const DEFAULT_PLACEHOLDER = '---------';
  const DEFAULT_OPTION = {
    id: '',
    text: DEFAULT_PLACEHOLDER,
  };

  // Function to determine the appropriate dropdown parent
  function getDropdownParent($element) {
    // First check for the specific modal structure in your application
    const $modalContent = $element.closest('#modal-form-content');
    if ($modalContent.length) {
      // Return the modal panel which is the proper container for the dropdown
      const $modalPanel = $modalContent.closest('.bg-theme-bg-primary');
      if ($modalPanel.length) {
        return $modalPanel;
      }
    }

    // Check if the element is inside a modal dialog (fallback to common selectors)
    const $modal = $element.closest('.modal-dialog, [x-data], [data-modal], .modal');
    if ($modal.length) {
      // Return the modal as the dropdown parent
      return $modal;
    }

    // Check if there's a specific dropdown parent attribute
    const dropdownParentSelector = $element.attr('data-dropdown-parent');
    if (dropdownParentSelector) {
      const $customParent = $(dropdownParentSelector);
      if ($customParent.length) {
        return $customParent;
      }
    }

    // Default to document.body for better positioning
    return $(document.body);
  }

  function initializeSelect2(element) {
    // If element is a string (selector), convert to DOM element
    if (typeof element === 'string') {
      element = document.querySelector(element);
    }

    // If element is a DOM element, convert to jQuery object
    if (element instanceof Element) {
      element = $(element);
    }

    // If no element or not a jQuery object, use all elements with django-select2 class
    if (!element || !element.jquery) {
      element = $('.django-select2');
    }

    element.each(function() {
      const $select = $(this);

      // Extract the last w-* class from the element
      const classes = $select.attr('class') ?
          $select.attr('class').split(' ') :
          [];
      const widthClasses = classes.filter(cls => /^w-/.test(cls));
      let size = widthClasses.length > 0 ?
          widthClasses[widthClasses.length - 1] :
          '';

      const errorClasses = classes.filter(
          cls => /^border-theme-status-error/.test(cls));
      let error = errorClasses.length > 0;

      // Check if the select is multiple
      const isMultiple = $select.prop('multiple');
      // Check if there's an API URL for AJAX loading
      const apiUrl = $select.attr('data-api-url');
      const isTags = $select.attr('tags');

      // Base configuration
      const config = {
        theme: 'default',
        width: size !== '' ? 'element' : '100%',
        dropdownAutoWidth: true,
        closeOnSelect: !isMultiple,
        dropdownParent: getDropdownParent($select),
        tags: isTags === 'true',
        selectionCssClass: isTags === 'true' ? 'select2-tags-enabled' : '',
      };

      // If API URL is provided, add AJAX configuration
      if (apiUrl) {
        config.ajax = {
          url: apiUrl,
          dataType: 'json',
          delay: 250,
          data: function(params) {
            return {
              search: params.term || '', // search term
              page: params.page || 1,
            };
          },
          processResults: function(data, params) {
            // Get current page number, default to 1 if not provided
            const page = params.page || 1;

            if (data.results && data.pagination) {
              const results = data.results.map(item => ({
                id: item.id,
                text: item.text || item.name || `${item.id}`,
                ...item,
              }));

              // Add DEFAULT_OPTION only on first page
              return {
                results: page === 1 ? [DEFAULT_OPTION, ...results] : results,
                pagination: {
                  more: data.pagination.more,
                },
              };
            }
            // Handle flat array response
            else if (Array.isArray(data)) {
              const results = data.map(item => ({
                id: item.id,
                text: item.text || item.name || `${item.id}`,
                ...item,
              }));

              // Add DEFAULT_OPTION only on first page
              return {
                results: page === 1 ? [DEFAULT_OPTION, ...results] : results,
              };
            }
            // Default case
            return {results: []};
          },
          cache: true,
        };

        // Set minimum input length for search
        config.minimumInputLength = $select.attr('data-min-input') || 0;

        // Add a placeholder if provided, otherwise use DEFAULT_PLACEHOLDER
        config.placeholder = $select.attr('data-placeholder');
      }

      // Initialize Select2 with configuration
      $select.select2(config);

      // For multiple select2, prevent dropdown from opening when removing individual items
      if (isMultiple) {
        $select.on('select2:unselecting', function(e) {
          $(this).data('unselecting', true);
        }).on('select2:opening', function(e) {
          if ($(this).data('unselecting')) {
            $(this).removeData('unselecting');
            e.preventDefault();
          }
        });
      }

      // Handle dropdown width when opened
      $select.on('select2:open', function() {
        // Focus the search input
        const searchField = document.querySelector(
            '.select2-container--open .select2-search__field');
        if (searchField) {
          searchField.focus();
        }
      });

      // Check for error state
      if (error) {
        $select.next('.select2-container').addClass('select2-container--error');
      }
    });
  }

  // Initial initialization
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize all Select2 elements
    initializeSelect2();

    // Register HTMX event listeners after DOM is loaded
    if (document.body) {
      // After any HTMX content swap
      document.body.addEventListener('htmx:afterSwap', function(evt) {
        setTimeout(function() {
          initializeSelect2();
        }, 50);
      });
    }
  });

  // Expose the initialization function to the global scope
  window.initializeSelect2 = initializeSelect2;
})(window);

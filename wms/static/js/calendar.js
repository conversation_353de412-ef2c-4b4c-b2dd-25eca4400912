// Make initializeCalendars available globally
function initializeCalendars(container = document) {
  // Initialize date pickers
  const dateInputs = container.querySelectorAll('[data-calendar-date="true"]');
  const datetimeInputs = container.querySelectorAll(
      '[data-calendar-datetime="true"]');

  const {Calendar} = window.VanillaCalendarPro;

  // Custom layout with Now button for date picker
  const dateLayout = {
    default: `
      <div class="vanilla-calendar-header" data-vc="header" role="toolbar" aria-label="Calendar navigation">
        <#ArrowPrev [month] />
        <div class="vanilla-calendar-header__content" data-vc-header="content">
          <#Month />
          <#Year />
        </div>
        <#ArrowNext [month] />
      </div>
      <div class="vanilla-calendar-wrapper" data-vc="wrapper">
        <#WeekNumbers />
        <div class="vanilla-calendar-content" data-vc="content">
          <#Week />
          <#Dates />
          <#DateRangeTooltip />
        </div>
      </div>
      <div class="vanilla-calendar-now-button flex justify-end px-0 pt-2">
        <button type="button" class="now-button px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 border border-gray-300 rounded cursor-pointer transition-colors">Now</button>
      </div>
      <#ControlTime />
    `
  };

  // Custom layout with Now button for datetime picker
  const datetimeLayout = {
    default: `
      <div class="vanilla-calendar-header" data-vc="header" role="toolbar" aria-label="Calendar navigation">
        <#ArrowPrev [month] />
        <div class="vanilla-calendar-header__content" data-vc-header="content">
          <#Month />
          <#Year />
        </div>
        <#ArrowNext [month] />
      </div>
      <div class="vanilla-calendar-wrapper" data-vc="wrapper">
        <#WeekNumbers />
        <div class="vanilla-calendar-content" data-vc="content">
          <#Week />
          <#Dates />
          <#DateRangeTooltip />
        </div>
      </div>
      <#ControlTime />
      <div class="vanilla-calendar-now-button flex justify-end px-0 pt-2">
        <button type="button" class="now-button px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 border border-gray-300 rounded cursor-pointer transition-colors">Now</button>
      </div>
    `
  };

  // Initialize date-only fields
  const localTimeString = new Date().toLocaleTimeString('en-US',
      {hour12: false, hour: '2-digit', minute: '2-digit'});
  dateInputs.forEach(input => {
    let initialSelectedDates = [];

    if (input.defaultValue) {
      // Attempt to parse the defaultValue
      const defaultDate = new Date(input.defaultValue);
      // Check if the parsed date is valid
      if (!isNaN(defaultDate)) {
        initialSelectedDates = [defaultDate.toLocaleDateString('sv-SE')];
      } else {
        // Log a warning if parsing fails
        console.warn(
            `Calendar: Could not parse defaultValue "${input.defaultValue}" for input:`,
            input);
      }
    }

    const calendar = new Calendar(input, {
      inputMode: true,
      selectedTheme: 'light',
      positionToInput: 'left',
      selectedDates: initialSelectedDates,
      layouts: dateLayout,
      enableJumpToSelectedDate: true,
      onChangeToInput(self) {
        if (!self.context.inputElement) return;

        if (self.context.selectedDates[0]) {
          self.context.inputElement.value = `${self.context.selectedDates[0]}`;
        } else {
          self.context.inputElement.value = '';
        }
        calendar.hide();
      },
      onShow(self) {
        self.context.mainElement.style.zIndex = '99';
        //* add Enter key handler
        self.context.mainElement.addEventListener('keydown', function(e) {
          if (e.key === 'Enter') {
            e.preventDefault();
            calendar.hide();
          }
        });

        // Add click handler for the Now button
        const nowButton = self.context.mainElement.querySelector('.now-button');
        if (nowButton) {
          nowButton.addEventListener('click', function() {
            const now = new Date();
            const formattedDate = now.toLocaleDateString('sv-SE');

            // Update the calendar
            self.context.selectedDates = [formattedDate];
            calendar.update();

            // Update the input field
            self.context.inputElement.value = formattedDate;

            // Hide the calendar
            calendar.hide();
          });
        }
      },
    });
    calendar.init();
  });

  // Initialize datetime fields
  datetimeInputs.forEach(input => {
    let initialSelectedDates = [];
    let defaultTime = localTimeString;
    if (input.defaultValue) {
      // Attempt to parse the defaultValue
      const defaultDate = new Date(input.defaultValue);
      // Check if the parsed date is valid
      if (!isNaN(defaultDate)) {
        // Format date as YYYY-MM-DD for selectedDates array
        initialSelectedDates = [defaultDate.toLocaleDateString('sv-SE')];
        // Format time as HH:MM A (12h) for selectedTime option
        defaultTime = defaultDate.toLocaleTimeString('en-US', {
          hour12: false,
          hour: '2-digit',
          minute: '2-digit',
        });
      } else {
        // Log a warning if parsing fails
        console.warn(
            `Calendar: Could not parse defaultValue "${input.defaultValue}" for input:`,
            input);
      }
    }

    const calendar = new Calendar(input, {
      inputMode: true,
      selectedTheme: 'light',
      positionToInput: 'left',
      selectionTimeMode: 24,
      selectedDates: initialSelectedDates,
      selectedTime: defaultTime,
      layouts: datetimeLayout,
      enableJumpToSelectedDate: true,
      onChangeToInput(self) {
        if (!self.context.inputElement) return;
        if (self.context.selectedDates[0] && self.context.selectedTime) {
          self.context.inputElement.value = `${self.context.selectedDates[0]} ${self.context.selectedTime}`;
        } else {
          // Clear input if no date is selected
          self.context.inputElement.value = '';
        }
      },
      onShow(self) {
        self.context.mainElement.style.zIndex = '99';
        //* add Enter key handler
        self.context.mainElement.addEventListener('keydown', function(e) {
          if (e.key === 'Enter') {
            e.preventDefault();
            calendar.hide();
          }
        });

        // Add click handler for the Now button
        const nowButton = self.context.mainElement.querySelector('.now-button');
        if (nowButton) {
          nowButton.addEventListener('click', function() {
            const now = new Date();
            const formattedDate = now.toLocaleDateString('sv-SE');
            const formattedTime = now.toLocaleTimeString('en-US', {
              hour12: false,
              hour: '2-digit',
              minute: '2-digit',
            });

            // Update the calendar
            self.context.selectedDates = [formattedDate];
            self.context.selectedTime = formattedTime;
            calendar.update();

            // Update the input field
            self.context.inputElement.value = `${formattedDate} ${formattedTime}`;

            // Hide the calendar
            calendar.hide();
          });
        }
      },
    });
    calendar.init();
  });
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
  // Initialize all calendars on the page
  initializeCalendars();

  // Initialize after HTMX content updates - moved inside DOMContentLoaded to ensure body exists
  document.body.addEventListener('htmx:afterSwap', function(evt) {
    // Check if the swapped content contains calendar inputs before re-initializing
    const hasCalendarDate = evt.detail.target.querySelector(
        '[data-calendar-date="true"]');
    const hasCalendarDateTime = evt.detail.target.querySelector(
        '[data-calendar-datetime="true"]');
    // Or check if the target itself is a calendar input (if replacing the input directly)
    const targetIsCalendarDate = evt.detail.target.matches(
        '[data-calendar-date="true"]');
    const targetIsCalendarDateTime = evt.detail.target.matches(
        '[data-calendar-datetime="true"]');

    if (hasCalendarDate || hasCalendarDateTime || targetIsCalendarDate ||
        targetIsCalendarDateTime) {
      // console.log('Re-initializing calendars after HTMX swap.');
      initializeCalendars(evt.detail.target);
    }
  });
});

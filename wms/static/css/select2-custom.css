/* Base select2 container styles */

.select2-container {
  /*min-width: 16.25rem;*/
}


.select2-container--default .select2-selection--single {
  background-color: var(--color-theme-default, #ffffff);
  border: 1px solid var(--color-theme-border-primary, #e5e7eb);
  border-radius: 0.125rem;
  height: 2.250rem;
  padding: 0.375rem 0.75rem;
  padding-right: 2.5rem;
  display: flex;
  align-items: center;
  transition: all 0.2s ease;
}


/* Selection text */
.select2-container--default .select2-selection--single .select2-selection__rendered {
  color: var(--color-theme-text-primary, #374151);
  font-size: 0.875rem;
  line-height: 17px;
  padding-left: 0;
}

/* Placeholder text */
.select2-container--default .select2-selection--single .select2-selection__placeholder {
  color: var(--color-theme-text-secondary, #6b7280);
  font-size: 0.875rem;
}

/* Custom arrow styling */
.select2-container--default .select2-selection--single .select2-selection__arrow {
  height: 100% !important;
  position: absolute;
  top: 0;
  right: 0.75rem;
  width: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Hide the default dropdown arrow */
.select2-tags-enabled .select2-selection__arrow b {
  display: none !important;
}
.select2-tags-enabled .select2-selection__arrow::before {
  content: '+';
  position: absolute;
  top: 50%;
  right: 5px;
  transform: translateY(-50%);
  font-size: 16px;
  font-weight: bold;
  color: var(--color-theme-text-secondary);
}

/* Arrow symbol with smooth transition */
.select2-container--default .select2-selection--single .select2-selection__arrow b {
  border-color: var(--color-theme-text-secondary) transparent transparent transparent;
  border-style: solid;
  border-width: 6px 6px 0 6px;
  height: 0;
  width: 0;
  position: absolute;
  transition: transform 0.2s ease;
}

/* Arrow symbol - when dropdown is open */
.select2-container--default.select2-container--open .select2-selection--single .select2-selection__arrow b {
  border-color: transparent transparent var(--color-theme-text-secondary) transparent;
  border-width: 0 6px 6px 6px;
}

/* Dropdown panel with improved shadow */
.select2-dropdown {
  background-color: var(--color-theme-bg-primary, #ffffff);
  border: 1px solid var(--color-theme-border-primary, #e5e7eb);
  --tw-shadow: 0 10px 15px -3px #05050629, 0 0 2px #05050629, 0 4px 6px -4px #05050629;
  --tw-shadow-colored: 0 0 1px 05050629, 0 0 2px 05050629, 0 2px 8px 05050629;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  overflow: hidden;
}

.select2-container--open .select2-dropdown--below {
  border-radius: 0.125rem;
  top: 0.5rem;
  border: 1px solid var(--color-theme-input-border-primary, #e5e7eb);
}

.select2-container--open .select2-dropdown--above {
  border-radius: 0.125rem;
  bottom: 0.5rem;
  border: 1px solid var(--color-theme-input-border-primary, #e5e7eb);
}

/* Enhanced search box with bottom border only */
.select2-container--default .select2-search--dropdown {
  margin: 0.5rem;
  position: relative; /* Added for icon positioning */
}

/* search input */
.select2-container--default .select2-search--dropdown .select2-search__field {
  border-bottom: 1px solid var(--color-theme-input-border-primary, #e5e7eb);
  padding: 0.375rem 2rem 0.375rem 0.75rem;
  width: 100% !important;
  color: var(--color-theme-text-primary, #374151);
  transition: border-color 0.2s ease;
  border-radius: 0.125rem;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236B7280' stroke-width='2'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' d='M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z'%3E%3C/path%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 1rem;
  background-color: var(--color-theme-bg-primary, #ffffff);
}

.select2-container--default .select2-search--dropdown .select2-search__field:focus {
  border-color: var(--color-theme-input-border-focus, #2563eb);
  outline-offset: 0.1125rem;
  outline-color: var(--color-theme-primary, #2563eb);
}

/* Multiple select container */
.select2-container--default .select2-selection--multiple {
  background-color: var(--color-theme-bg-primary, #ffffff);
  border: 1px solid var(--color-theme-border-primary, #e5e7eb);
  border-radius: 0.125rem;
  min-height: 2.375rem;
  padding: 0.25rem;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 0.25rem;
  transition: all 0.2s ease;
}

/* Selected choices/tags */
.select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: var(--color-theme-default);
  border: 1px solid var(--color-theme-border-primary);
  border-radius: 0.125rem;
  text-wrap: wrap;
  margin: 0.125rem;
  position: relative;
  color: var(--color-theme-text-primary);
  font-size: 0.875rem;
  padding: 0.25rem 0.5rem 0.25rem 1.5rem;
}

/* Remove button for selected choices */
.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  position: absolute;
  left: 0.25rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-theme-text-secondary);
  border: none;
  background: none;
  padding: 0 0.25rem;
  cursor: pointer;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.25rem; /* Fixed width for better alignment */
  height: 1.25rem; /* Fixed height for better alignment */
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: var(--color-theme-text-primary);
  background: none;
}

/* Search field in multiple select */
.select2-container--default .select2-selection--multiple .select2-search__field {
  color: var(--color-theme-text-primary);
  margin: 0 0 0.1rem;
}

/* Hover state for options with subtle background */
.select2-container--default .select2-results__option[aria-selected=true] {
  background-color: var(--color-theme-bg-primary, #fff) !important;
  color: var(--color-theme-text-primary, #374151);
  position: relative;
}

/* Checkmark for selected options in dropdown */
.select2-container--default .select2-results__option[aria-selected=true]::before {
  content: '';
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.select2-container--default .select2-results__option[aria-selected=true]::before {
  display: none;
}

/* Focus state for multiple select */
.select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: var(--color-theme-primary, #2563eb);
  outline-style: none;
  border-radius: 0.125rem;
}

/* Disabled state for multiple select */
.select2-container--default.select2-container--disabled .select2-selection--multiple {
  background-color: var(--color-theme-bg-secondary, #f3f4f6);
  cursor: not-allowed;
  opacity: 0.7;
}

/* Placeholder for multiple select */
.select2-container--default .select2-selection--multiple .select2-selection__placeholder {
  color: var(--color-theme-text-secondary);
}


/* Options container with better spacing */
.select2-results {
  padding: 0.375rem 0;
}

.select2-results__option {
  padding: 0.375rem 1rem 0.375rem 2.5rem !important;
  color: var(--color-theme-text-primary, #374151);
  cursor: pointer;
  transition: background-color 0.2s ease;
  position: relative;
}


/* Selected option with checkmark icon */
.select2-container--default .select2-results__option--selected {
  background-color: var(--color-theme-bg-primary, #fff) !important;
  color: var(--color-theme-text-primary, #374151);
  position: relative;
  padding-left: 2.5rem !important;
}

/* Add hover effect for all options including selected ones */
.select2-container--default .select2-results__option:hover {
  background-color: var(--color-theme-hover) !important;
}

.select2-container--default .select2-results__option--selected::before {
  content: '';
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  display: block !important;
}

/* Hover state - only change background color */
.select2-container--default .select2-results__option--selected:hover,
.select2-container--default .select2-results__option--highlighted[aria-selected] {
  background-color: var(--color-theme-hover) !important;
  color: var(--color-theme-text-primary, #374151) !important;
}

.select2-results__option {
  padding: 0.375rem 1rem 0.375rem 2.5rem !important;;
  color: var(--color-theme-text-primary, #374151);
  cursor: pointer;
  transition: background-color 0.2s ease;
  position: relative;
}


/* Focus states with smooth transition */
.select2-container--default.select2-container--focus .select2-selection--single,
.select2-container--default.select2-container--open .select2-selection--single {
  border-color: var(--color-theme-primary, #2563eb);
  outline: 0;
  border-radius: 0.125rem;
}

/* Empty results message */
.select2-results__message {
  padding: 0.5rem 1rem;
  color: var(--color-theme-text-secondary);
  font-style: italic;
  font-size: 0.875rem;
  text-align: center;
}

/* Disabled state */
.select2-container--default.select2-container--disabled .select2-selection--single {
  background-color: var(--color-theme-input-disabled, #f3f4f6);
  cursor: not-allowed;
}

/* Search results message */
.select2-results__message {
  padding: 0.5rem 1rem;
  color: var(--color-theme-text-secondary);
  font-style: italic;
}

/* Remove default browser styles */
select.select2-hidden-accessible {
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
}

.select2-selection {
  background-image: none !important;
}

/* Select2 Error State Styles */
.select2-container--error .select2-selection--single,
.select2-container--error .select2-selection--multiple {
  border-color: var(--color-theme-status-error, #dc3545) !important;
}

.select2-container--error.select2-container--focus .select2-selection--single,
.select2-container--error.select2-container--focus .select2-selection--multiple,
.select2-container--error.select2-container--open .select2-selection--single,
.select2-container--error.select2-container--open .select2-selection--multiple {
  border-color: var(--color-theme-status-error, #dc3545) !important;
  outline-color: var(--color-theme-status-error, #dc3545);
}

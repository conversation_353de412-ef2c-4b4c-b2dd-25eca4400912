/* Define the size and margins for A4 paper */
@page {
  @top-center {
    content: element(page_header);
  }
  size: A4;
  margin: 4cm 1cm 2cm 1cm; /* Top margin larger for header */
}

header {
  position: running(page_header);
  width: 18.6cm;
  padding-top: 1cm;
}
/* header > div:first-of-type {
  border: 0.1cm solid black;
} */
header .currentPage::after {
  content: counter(page) " of " counter(pages);
}

body {
  margin: 0;
  padding: 0 1cm;
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 68%;
}

/* Layout for main content */
.mainContent {
  width: calc(100% - 2cm); /* Adjust to fit inside the page margins */
  box-sizing: border-box;
  background: #fff; /* White background to mimic a printed page */
}

.fs-65rem {
  font-size: 0.65rem!important;
}
.fs-6rem {
  font-size: 0.6rem!important;
}
.fs-55rem {
  font-size: 0.55rem!important;
}
.fs-5rem {
  font-size: 0.5rem!important;
}

table {
  page-break-inside: auto; /* Allow page breaks inside the table */
}

tr {
  page-break-inside: avoid; /* Prevent row from splitting across pages */
}

th, td {
  page-break-inside: auto; /* Allow breaks within table cells */
}

.warehouses-list {
  padding-left: 0.5rem; /* Reduces padding for the list items */
}


.custom-border-table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid black; /* outer border */
}

.custom-border-table th,
.custom-border-table td {
  border: none; /* remove all inside borders */
}

/* (Optional) If you want a better-looking outer border even on th/td edges */
.custom-border-table tr:first-child th {
  border-top: none;
}
.custom-border-table tr:last-child td {
  border-bottom: none;
}
.custom-border-table tr td:first-child,
.custom-border-table tr th:first-child {
  border-left: none;
}
.custom-border-table tr td:last-child,
.custom-border-table tr th:last-child {
  border-right: none;
}

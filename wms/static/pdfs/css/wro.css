body {
  margin: 0;
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 78%;
}

header {
  position: running(page_header);
  width: 18.6cm;
  padding-top: 1cm;
}
/* header > div:first-of-type {
  border: 0.1cm solid black;
} */
header .currentPage::after {
  content: counter(page) " of " counter(pages);
}

.mainContent {
  width: 100%;
  box-decoration-break: clone;
  margin-bottom: 1.5cm;
}
/* .mainContent > div:first-of-type {
  border: 0.1cm solid red;
} */

.fs-65rem {
  font-size: 0.65rem!important;
}
.fs-6rem {
  font-size: 0.6rem!important;
}
.fs-55rem {
  font-size: 0.55rem!important;
}
.fs-5rem {
  font-size: 0.5rem!important;
}

footer {
  position: fixed;
  width: 100%;
  bottom: 0;
}
/* footer > div:first-of-type {
  border: 0.1cm solid white;
} */

@page {
  @top-center {
    content: element(page_header);
  }
  size: A4;
  margin: 6.7cm 1.2cm 0.4cm 1.2cm;
}

import logging
from decimal import Decimal
from typing import Any

from django.conf import settings

from pyas2.models import Message, Organization, Partner
from pyas2lib import Message as AS2Message
from xmltodict import unparse

from wms.cores.utils import localtime_now

from wms.apps.settings.utils import uom_converter

logger = logging.getLogger(__name__)


class FMCEDIConfirmation:
    """
    FMC Confirmation for EDI Interface 3 & Interface 5.

    Interface 2 (Inbound Delivery): FMC -> 3PL
    Interface 3 (Inbound Confirmation): 3PL -> FMC
    Interface 4 (Outbound Delivery): FMC -> 3PL
    Interface 5 (Outbound Confirmation): 3PL -> FMC
    """

    obj = None
    GTIN_NUMBER = 9999999999999
    log_interface = ""

    def __init__(self, obj: Any) -> None:
        self.obj = obj

    def obj_to_dict(self) -> dict[str, Any]:
        """
        Convert a valid GRN/WRO object into it's prepared dictionary form.
        """
        obj_type = self.obj.__class__.__name__

        if obj_type == "GoodsReceivedNote":
            grn_or_wro_items = self.obj.goodsreceivednoteitem_set.all()
            entity_identification = getattr(self.obj, "consignor_inbound_delivery_no", "")
            document_struct_version = "Goods Receipt"
            self.log_interface = "Interface 3 :: Inbound Confirmation"
        else:
            grn_or_wro_items = self.obj.get_ascending_sort_wro_items
            entity_identification = getattr(self.obj, "consignor_outbound_delivery_no", "")
            document_struct_version = "Goods Issued"
            self.log_interface = "Interface 5 :: Outbound Confirmation"

        final_item_list = {}
        # prepare a final list to be loop into the XML item sections
        # final_item_list = {
        #     "item_a": {
        #         "line_item_number": item.sort_order,
        #         "xml_item_qty": xml_item_qty,
        #         "uom": "PCE",
        #         "item_code": grn_or_wro_item.item.code,
        #         "batch_no": ["BN001", "BN002"],
        #         "is_serial_no": True/False,
        #     }
        #     "item_b": {
        #         "line_item_number": item.sort_order,
        #         "xml_item_qty": xml_item_qty,
        #         "uom": "PCE",
        #         "item_code": grn_or_wro_item.item.code,
        #         "batch_no": ["BN003"],
        #         "is_serial_no": True/False,
        #     }
        # }

        # final_item_list = {}
        # prepare a final list to be loop into the XML item sections
        # final_item_list = {
        #     "item_a": {
        #         "line_item_number": item.sort_order,
        #         "xml_item_qty": xml_item_qty,
        #         "uom": "PCE",
        #         "item_code": grn_or_wro_item.item.code,
        #         "batch_no": ["BN001", "BN002"],
        #         "is_serial_no": True/False,
        #     }
        #     "item_b": {
        #         "line_item_number": item.sort_order,
        #         "xml_item_qty": xml_item_qty,
        #         "uom": "PCE",
        #         "item_code": grn_or_wro_item.item.code,
        #         "batch_no": ["BN003"],
        #         "is_serial_no": True/False,
        #     }

        #     "item_c": {
        #         "is_serial_no": True/False,
        #         "item_code": grn_or_wro_item.item.code,
        #         "line_item_dict": {
        #             "000001": {
        #                 "line_item_number": "000001", // item.sort_order
        #                 "xml_item_qty": xml_item_qty,
        #                 "uom": "PCE",
        #                 "batch_no": ["BN003"],
        #             },
        #             "000002": {
        #                 "line_item_number": "000002", // item.sort_order
        #                 "xml_item_qty": xml_item_qty,
        #                 "uom": "PCE",
        #                 "batch_no": ["BN003"],
        #             }
        #         },
        #     }
        # }

        for item_level in grn_or_wro_items:
            if item_level.item.code not in final_item_list:
                final_item_list[item_level.item.code] = {
                    "is_serial_no": False,
                    "item_code": item_level.item.code,
                    "line_item_dict": {},
                }

            # to prepare is_serial_no boolean value
            final_item_list[item_level.item.code]["is_serial_no"] = item_level.is_serial_no

            # to prepare line_item_number value
            line_item_number = str(item_level.sort_order).rjust(6, "0")
            if line_item_number not in final_item_list[item_level.item.code]["line_item_dict"]:
                final_item_list[item_level.item.code]["line_item_dict"][line_item_number] = {
                    "xml_item_qty": 0,
                    "uom": "",
                    "item_code": item_level.item.code,
                    "batch_no": [],
                }

            # to prepare xml_item_qty value
            if obj_type == "GoodsReceivedNote":
                # interface 3
                item_qty = item_level.get_stockin_converted_received_quantity
            else:
                # interface 5
                quantity = item_level.total_picker_system_quantity
                item_qty = uom_converter(
                    origin_uom=item_level.item.uom,
                    target_uom=item_level.uom,
                    quantity=quantity,
                    skip_unit_precision=True,
                )
            decimal_places_exponent = item_qty.as_tuple().exponent
            final_decimal_places = abs(decimal_places_exponent)
            xml_qty = item_qty
            if Decimal(final_decimal_places) >= 4:
                xml_qty = round(item_qty, 3)

            if item_level.item.code not in final_item_list:
                final_item_list[item_level.item.code]["line_item_dict"][line_item_number]["xml_item_qty"] = xml_qty
                # final_item_list[item_level.item.code]["xml_item_qty"] = xml_item_qty
            else:
                final_item_list[item_level.item.code]["line_item_dict"][line_item_number]["xml_item_qty"] += xml_qty

            # to prepare UOM value
            if item_level.uom.symbol == "EA":
                measurement_unit_code = "EA"
            elif item_level.uom.symbol == "PCE":
                measurement_unit_code = "PCE"
            else:
                measurement_unit_code_split_list = item_level.uom.symbol.split("-")
                measurement_unit_code = measurement_unit_code_split_list[0]

            final_item_list[item_level.item.code]["line_item_dict"][line_item_number]["uom"] = measurement_unit_code

            # to update dict on whether item is using batchNumber or serialNumber
            if item_level.is_serial_no is False:
                final_item_list[item_level.item.code]["line_item_dict"][line_item_number]["batch_no"].append(
                    item_level.batch_no
                )
            elif item_level.is_serial_no is True:
                final_item_list[item_level.item.code]["line_item_dict"][line_item_number]["batch_no"].append(
                    item_level.batch_no
                )

        # for item_level in grn_or_wro_items:
        #     if item_level.item.code not in final_item_list:
        #         final_item_list[item_level.item.code] = {
        #             "line_item_number": "",
        #             "xml_item_qty": 0,
        #             "uom": "",
        #             "item_code": item_level.item.code,
        #             "batch_no": [],
        #         }

        #     # to prepare is_serial_no boolean value
        #     final_item_list[item_level.item.code]["is_serial_no"] = item_level.is_serial_no

        #     # to prepare line_item_number value
        #     final_item_list[item_level.item.code]["line_item_number"] = str(item_level.sort_order).rjust(6, "0")

        #     # to prepare line_item_number value
        #     if obj_type == "GoodsReceivedNote":
        #         # interface 3
        #         item_qty = item_level.get_stockin_converted_received_quantity
        #     else:
        #         # interface 5
        #         quantity = item_level.total_picker_system_quantity
        #         item_qty = uom_converter(
        #             origin_uom=item_level.item.uom,
        #             target_uom=item_level.uom,
        #             quantity=quantity,
        #             skip_unit_precision=True,
        #         )
        #     decimal_places_exponent = item_qty.as_tuple().exponent
        #     final_decimal_places = abs(decimal_places_exponent)
        #     xml_item_qty = item_qty
        #     if Decimal(final_decimal_places) >= 4:
        #         xml_item_qty = round(item_qty, 3)

        #     if item_level.item.code not in final_item_list:
        #         final_item_list[item_level.item.code]["xml_item_qty"] = xml_item_qty
        #     else:
        #         final_item_list[item_level.item.code]["xml_item_qty"] += xml_item_qty

        #     # to prepare UOM value
        #     if item_level.uom.symbol == "EA":
        #         measurement_unit_code = "EA"
        #     elif item_level.uom.symbol == "PCE":
        #         measurement_unit_code = "PCE"
        #     else:
        #         measurement_unit_code_split_list = item_level.uom.symbol.split("-")
        #         measurement_unit_code = measurement_unit_code_split_list[0]

        #     final_item_list[item_level.item.code]["uom"] = measurement_unit_code

        #     # to update dict on whether item is using batchNumber or serialNumber
        #     if item_level.is_serial_no is False:
        #         final_item_list[item_level.item.code]["batch_no"].append(item_level.batch_no)
        #     elif item_level.is_serial_no is True:
        #         final_item_list[item_level.item.code]["batch_no"].append(item_level.batch_no)

        print("final_item_list: ", final_item_list)

        # Prepare dict for every WRO or GRN's Items
        obj_line_items = []
        for item_code, item_full_info_dict in final_item_list.items():
            for item_line, item_line_info in item_full_info_dict["line_item_dict"].items():
                advice_line_item = {}

                advice_line_item = {
                    "lineItemNumber": item_line,
                    "quantityReceived": {
                        "@measurementUnitCode": item_line_info["uom"],
                        "#text": str(item_line_info["xml_item_qty"]),
                    },
                    "quantityAccepted": {
                        "@measurementUnitCode": item_line_info["uom"],
                        "#text": str(item_line_info["xml_item_qty"]),
                    },
                    "transactionalTradeItem": {
                        "gtin": self.GTIN_NUMBER,
                        "additionalTradeItemIdentification": {
                            "@additionalTradeItemIdentificationTypeCode": "SUPPLIER_ASSIGNED",
                            "#text": item_code,
                        },
                        "tradeItemQuantity": {
                            "@measurementUnitCode": item_line_info["uom"],
                            "#text": str(item_line_info["xml_item_qty"]),
                        },
                        "transactionalItemData": {
                            "tradeItemQuantity": {
                                "@measurementUnitCode": item_line_info["uom"],
                                "#text": str(item_line_info["xml_item_qty"]),
                            },
                        },
                    },
                }

                if len(item_line_info["batch_no"]) == 1:
                    if item_full_info_dict["is_serial_no"] is False:
                        transactional_item_data = advice_line_item["transactionalTradeItem"]["transactionalItemData"]
                        transactional_item_data["batchNumber"] = item_line_info["batch_no"][0]
                    elif item_full_info_dict["is_serial_no"] is True:
                        advice_line_item["transactionalTradeItem"]["transactionalItemData"][
                            "serialNumber"
                        ] = item_line_info["batch_no"][0]

                elif len(item_line_info["batch_no"]) > 1:
                    advice_line_item["transactionalTradeItem"]["transactionalItemData"]["serialNumber"] = []

                    for batch_no in item_line_info["batch_no"]:
                        tmp_dict = {
                            "#text": batch_no,
                        }
                        transactional_item_data = advice_line_item["transactionalTradeItem"]["transactionalItemData"]
                        transactional_item_data["serialNumber"].append(tmp_dict)

                obj_line_items.append(advice_line_item)

        prepared_dict = {
            "receiving_advice:receivingAdviceMessage": {
                "@xmlns:receiving_advice": "urn:gs1:ecom:receiving_advice:xsd:3",
                "@xmlns:sh": "http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader",
                "sh:StandardBusinessDocumentHeader": {
                    "sh:HeaderVersion": None,
                    "sh:DocumentIdentification": {
                        "sh:DocumentIdentification": {
                            "sh:Standard": "GS1",
                            "sh:TypeVersion": 3.1,
                            "sh:InstanceIdentifier": self.obj.system_number,
                            "sh:Type": "Receiving Advice Message",
                            "sh:CreationDateAndTime": localtime_now().strftime("%Y-%m-%dT%H:%M:%S"),
                        }
                    },
                },
                "receivingAdvice": {
                    "creationDateTime": self.obj.created.strftime("%Y-%m-%dT%H:%M:%S"),
                    "documentStatusCode": "ORIGINAL",
                    "documentActionCode": "ADD",
                    "documentStructureVersion": document_struct_version,
                    "receivingAdviceIdentification": {
                        "entityIdentification": entity_identification,
                    },
                    "receivingDateTime": localtime_now().strftime("%Y-%m-%dT%H:%M:%S"),
                    "reportingCode": "UNKOWN",
                    "shipper": {
                        "gln": self.GTIN_NUMBER,
                    },
                    "receiver": {
                        "gln": self.GTIN_NUMBER,
                    },
                    "despatchAdvice": {
                        "entityIdentification": entity_identification,
                    },
                    "receivingAdviceLogisticUnit": {
                        "receivingAdviceLineItem": obj_line_items,
                    },
                },
            },
        }
        return prepared_dict

    def send_xml_confirmation(self) -> None:
        """Send the XML payload to FMC EDI through pyas2 module."""

        # Requires 3 main data for send_xml_confirmation: organization, partner, payload
        organization = Organization.objects.first()
        fmc_partner = Partner.objects.get(as2_name=settings.FMC_AS2_IDENTIFIER)

        # Convert payload dict into XML format
        prepared_dict = self.obj_to_dict()
        payload = unparse(prepared_dict, pretty=True)
        payload = payload.replace('encoding="utf-8"', "")

        original_filename = f"ReceivingAdvice_{self.obj.system_number}.xml"
        as2message = AS2Message(sender=organization.as2org, receiver=fmc_partner.as2partner)

        # Build and send the AS2 message
        as2message.build(
            data=str.encode(payload),
            filename=original_filename,
            subject=fmc_partner.subject,
            content_type=fmc_partner.content_type,
            disposition_notification_to=organization.email_address or "<EMAIL>",
        )
        message, _ = Message.objects.create_from_as2message(
            as2message=as2message,
            payload=payload,
            filename=original_filename,
            direction="OUT",
            status="P",  # P stands for Pending status
        )
        message.send_message(as2message.headers, as2message.content)
        logger.info(f"({self.log_interface}): Sending {original_filename} from {organization} to {fmc_partner}.")

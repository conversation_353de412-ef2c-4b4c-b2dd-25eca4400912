import logging
from decimal import Decimal

from django.contrib.auth import get_user_model

import xmltodict

# from wms.bases.utils import send_notification

from wms.apps.consignors.models import Consignor
from wms.apps.inventories.models import Item
from wms.apps.settings.models import UnitConversion, UnitOfMeasure

logger = logging.getLogger("import_material_master_fmc")

UserModel = get_user_model()


class ImportMaterialMasterFMC:
    """
    Import Material Master class for FMC consignor.

    After self.cleaned(), it will create the following example:

    error_messages_dict = {
        "__all__": ["File PT0622SBH-02.txt uploaded in GRN-22-000001"],
        "3": ["Invalid number of column at line 3"],
        ...
    }
    error_messages_list = [
        "File PT0622SBH-02.txt uploaded in GRN-22-000001",
        "Invalid number of column at line 3",
        ...
    ]
    import_item_line_list = [
        {
            "line_number": "00001",
            "uom": "EA",
            "despatched_quantity": 100,
            "batch_no": "N/A"
            "expiry_date": "2023-07-28",
            "code": "12345",
            "object": ItemObject,
        },
        {
            "line_number": "00002",
            "uom": "EA",
            "despatched_quantity": 200,
            "batch_no": "N/A"
            "expiry_date": "2023-07-28",
            "code": "67890",
            "object": ItemObject,
        },
        ...
    ]

    """

    snapdec_user = None
    _valid = False
    consignor = None
    error_messages_dict = {}
    error_messages_list = []
    item_list_to_be_cleaned = []
    import_item_line_list = []
    consignor_code = "FMCSB"
    uom_symbol = "PCE"
    uom = None
    parent_item_code = None
    parent_item_name = None
    manage_type = None
    cleaned_material_master_dict = {}

    def __init__(self, file):
        self.snapdec_user = UserModel.objects.get(email="<EMAIL>")
        self.file = file
        self.consignor = Consignor.objects.get(code=self.consignor_code)
        self.error_messages_dict = {}
        self.error_messages_list = []
        self.item_list_to_be_cleaned = []
        self.import_item_line_list = []
        self.uom = UnitOfMeasure.objects.get(symbol=self.uom_symbol)
        self.parent_item_code = None
        self.parent_item_name = None
        self.manage_type = None
        self.cleaned_material_master_dict = {}

    def __str__(self):
        return str(self.file) or ""

    def _check_my_ordered_dict_main_keys(self, my_ordered_dict):
        """check my_ordered_dict's exist item_data_notification:itemDataNotificationMessage key or not"""
        if "item_data_notification:itemDataNotificationMessage" in my_ordered_dict:
            item_data_notification_message = my_ordered_dict["item_data_notification:itemDataNotificationMessage"]
            if "itemDataNotification" in item_data_notification_message:
                item_data_notification = item_data_notification_message["itemDataNotification"]
                if "itemDataNotificationIdentification" in item_data_notification:
                    item_data_notification_id = item_data_notification["itemDataNotificationIdentification"]

                    if "itemDataNotificationLineItem" in item_data_notification:
                        self.item_data_list = item_data_notification["itemDataNotificationLineItem"]

                        if "entityIdentification" in item_data_notification_id:
                            self.parent_item_code = item_data_notification_id["entityIdentification"]
                            return True
                        else:
                            error_message = "entityIdentification tag is missing"
                            self.error_messages_list.append(error_message)
                            return False
                    else:
                        error_message = "itemDataNotificationLineItem tag is missing"
                        self.error_messages_list.append(error_message)
                        return False
                else:
                    error_message = "itemDataNotificationIdentification tag is missing"
                    self.error_messages_list.append(error_message)
                    return False
            else:
                error_message = "itemDataNotification tag is missing"
                self.error_messages_list.append(error_message)
                return False
        else:
            error_message = "item_data_notification:itemDataNotificationMessage tag is missing"
            self.error_messages_list.append(error_message)
            return False

    def _check_item_data_uom_dict_keys(self, item_data_uom_dict, final_prepared_counter):
        if "tradeItemBaseUnitOfMeasure" in item_data_uom_dict:
            if "tradeItemClassificationCode" in item_data_uom_dict:
                trade_item_classification_code = item_data_uom_dict["tradeItemClassificationCode"]
                if (
                    "@additionalTradeItemClassificationCodeListCode" in trade_item_classification_code
                    and "#text" in trade_item_classification_code
                ):
                    return True
                else:
                    error_message = (
                        "@additionalTradeItemClassificationCodeListCode or #text tag missing at "
                        f"item line {final_prepared_counter}"
                    )
                    self.error_messages_list.append(error_message)
                    return False
            else:
                error_message = "item_data_notification:itemDataNotificationMessage tag is missing"
                self.error_messages_list.append(error_message)
                return False
        else:
            error_message = "item_data_notification:itemDataNotificationMessage tag is missing"
            self.error_messages_list.append(error_message)
            return False

    def _check_my_ordered_dict_item_list_keys(self, my_ordered_dict):
        """
        validate the whole all item data dictionaries and then prepare a ready to be clean list of dict
        Expected format:

        self.item_list_to_be_cleaned = [
            {
                'code':"MY655",
                'name': "AV-SET - FMAS DEHT-EB  (TX-JB-5)",
                'uom': "CS",
                'quantity': "24",
                'conversion_rate_to_pce': "1",
            },
            ...
        ]

        """

        # can be refactor to a reusable function, due to time constraint duplicate code first.
        if isinstance(self.item_data_list, list):
            for temp_item_data_dict in self.item_data_list:
                looped_item_code = None
                final_prepared_counter = str(int(temp_item_data_dict["lineItemNumber"]))

                if self.parent_item_name is None:
                    if isinstance(temp_item_data_dict["tradeItemDescription"], list):
                        for language_dict in temp_item_data_dict["tradeItemDescription"]:
                            if language_dict["@languageCode"] == "EN":
                                self.parent_item_name = language_dict["#text"]
                    elif isinstance(temp_item_data_dict["tradeItemDescription"], dict):
                        self.parent_item_name = temp_item_data_dict["tradeItemDescription"]["#text"]

                if (
                    "tradeItemIdentification" in temp_item_data_dict
                    and "itemDataTradingPartnerNeutral" in temp_item_data_dict
                ):
                    item_data_uom_dict = temp_item_data_dict["itemDataTradingPartnerNeutral"]
                    if (
                        "additionalTradeItemIdentification" in temp_item_data_dict["tradeItemIdentification"]
                        and "gtin" in temp_item_data_dict["tradeItemIdentification"]
                    ):
                        trade_item_id = temp_item_data_dict["tradeItemIdentification"]
                        additional_trade_item_id_list = trade_item_id["additionalTradeItemIdentification"]

                        if any(additional_trade_item_id_list) is True:

                            for additional_trade_item_dict in additional_trade_item_id_list:
                                if (
                                    "@additionalTradeItemIdentificationTypeCode"
                                    in additional_trade_item_dict
                                    # and "#text" in additional_trade_item_dict
                                ):
                                    id_type = additional_trade_item_dict["@additionalTradeItemIdentificationTypeCode"]
                                    id_text = ""
                                    if "#text" in additional_trade_item_dict:
                                        id_text = additional_trade_item_dict["#text"]

                                    # if id_type == "FOR_INTERNAL_USE_1" and id_text == "Finished product":
                                    #     internal_use_1_key_exist = True

                                    if id_type == "SUPPLIER_ASSIGNED":
                                        looped_item_code = id_text

                                    if id_type == "FOR_INTERNAL_USE_7":
                                        self.manage_type = id_text
                                else:
                                    error_message = (
                                        "@additionalTradeItemIdentificationTypeCode or #text tag missing at "
                                        f"item line {final_prepared_counter}"
                                    )
                                    self.error_messages_list.append(error_message)
                                    return False

                            if looped_item_code is None:
                                error_message = f"Missing item/material code at item line {final_prepared_counter}"
                                self.error_messages_list.append(error_message)
                                return False

                            # if internal_use_1_key_exist is True:
                            if looped_item_code == self.parent_item_code:

                                if (
                                    self._check_item_data_uom_dict_keys(item_data_uom_dict, final_prepared_counter)
                                    is False
                                ):
                                    return False
                                else:
                                    return True
                            else:
                                error_message = (
                                    "the elements in itemDataNotificationLineItem's Material/item code is different"
                                    " from itemDataNotificationIdentification tag at item line "
                                    f"{final_prepared_counter}"
                                )
                                self.error_messages_list.append(error_message)
                                return False

                            self.item_list_to_be_cleaned.append(temp_item_data_dict)
                            # else:
                            #     error_message = (
                            #         "No 'FOR_INTERNAL_USE_1' value in additionalTradeItemIdentificationTypeCode at"
                            #         f" item line {final_prepared_counter}"
                            #     )
                            #     self.error_messages_list.append(error_message)
                            #     return False
                        else:
                            error_message = (
                                "No elements in additionalTradeItemIdentification tag at "
                                f"item line {final_prepared_counter}"
                            )
                            self.error_messages_list.append(error_message)
                            return False
                    else:
                        error_message = f"No additionalTradeItemIdentification/gtin at line {final_prepared_counter}"
                        self.error_messages_list.append(error_message)
                        return False
                else:
                    error_message = (
                        "No tradeItemIdentification or itemDataTradingPartnerNeutral tag at "
                        f"item line {final_prepared_counter}"
                    )
                    self.error_messages_list.append(error_message)
                    return False

        elif isinstance(self.item_data_list, dict):
            temp_item_data_dict = self.item_data_list
            looped_item_code = None
            final_prepared_counter = str(int(temp_item_data_dict["lineItemNumber"]))

            if self.parent_item_name is None:
                if isinstance(temp_item_data_dict["tradeItemDescription"], list):
                    for language_dict in temp_item_data_dict["tradeItemDescription"]:
                        if language_dict["@languageCode"] == "EN":
                            self.parent_item_name = language_dict["#text"]
                elif isinstance(temp_item_data_dict["tradeItemDescription"], dict):
                    self.parent_item_name = temp_item_data_dict["tradeItemDescription"]["#text"]

            if (
                "tradeItemIdentification" in temp_item_data_dict
                and "itemDataTradingPartnerNeutral" in temp_item_data_dict
            ):
                item_data_uom_dict = temp_item_data_dict["itemDataTradingPartnerNeutral"]
                if (
                    "additionalTradeItemIdentification" in temp_item_data_dict["tradeItemIdentification"]
                    and "gtin" in temp_item_data_dict["tradeItemIdentification"]
                ):
                    trade_item_id = temp_item_data_dict["tradeItemIdentification"]
                    additional_trade_item_id_list = trade_item_id["additionalTradeItemIdentification"]

                    if any(additional_trade_item_id_list) is True:

                        for additional_trade_item_dict in additional_trade_item_id_list:
                            if (
                                "@additionalTradeItemIdentificationTypeCode"
                                in additional_trade_item_dict
                                # and "#text" in additional_trade_item_dict
                            ):
                                id_type = additional_trade_item_dict["@additionalTradeItemIdentificationTypeCode"]
                                id_text = ""
                                if "#text" in additional_trade_item_dict:
                                    id_text = additional_trade_item_dict["#text"]

                                # if id_type == "FOR_INTERNAL_USE_1" and id_text == "Finished product":
                                #     internal_use_1_key_exist = True

                                if id_type == "SUPPLIER_ASSIGNED":
                                    looped_item_code = id_text

                                if id_type == "FOR_INTERNAL_USE_7":
                                    self.manage_type = id_text
                            else:
                                error_message = (
                                    "@additionalTradeItemIdentificationTypeCode or #text tag missing at "
                                    f"item line {final_prepared_counter}"
                                )
                                self.error_messages_list.append(error_message)
                                return False

                        if looped_item_code is None:
                            error_message = f"Missing item/material code at item line {final_prepared_counter}"
                            self.error_messages_list.append(error_message)
                            return False

                        # if internal_use_1_key_exist is True:
                        if looped_item_code == self.parent_item_code:

                            if self._check_item_data_uom_dict_keys(item_data_uom_dict, final_prepared_counter) is False:
                                return False
                            else:
                                return True
                        else:
                            error_message = (
                                "the elements in itemDataNotificationLineItem's Material/item code is different"
                                " from itemDataNotificationIdentification tag at item line "
                                f"{final_prepared_counter}"
                            )
                            self.error_messages_list.append(error_message)
                            return False

                        self.item_list_to_be_cleaned.append(temp_item_data_dict)
                        # else:
                        #     error_message = (
                        #         "No 'FOR_INTERNAL_USE_1' value in @additionalTradeItemIdentificationTypeCode tag at"
                        #         f" item line {final_prepared_counter}"
                        #     )
                        #     self.error_messages_list.append(error_message)
                        #     return False
                    else:
                        error_message = (
                            "No elements in additionalTradeItemIdentification tag at "
                            f"item line {final_prepared_counter}"
                        )
                        self.error_messages_list.append(error_message)
                        return False
                else:
                    error_message = f"No additionalTradeItemIdentification/gtin at line {final_prepared_counter}"
                    self.error_messages_list.append(error_message)
                    return False
            else:
                error_message = (
                    "No tradeItemIdentification or itemDataTradingPartnerNeutral tag at "
                    f"item line {final_prepared_counter}"
                )
                self.error_messages_list.append(error_message)
                return False

    def is_valid(self):
        """Function to check if the uploaded file is valid."""

        valid = True

        # first checking to be done due to first most important thing is the XML file must be a valid file
        # if self._check_valid_xml_file() is False:
        #     error_message = f"The XML file is not valid due to mismatch tag"
        #     self.error_messages_list.append(error_message)
        #     self._valid = valid
        #     return False

        # read xml content from the file
        xml_content = self.file.read()

        my_ordered_dict = xmltodict.parse(xml_content)
        # print(my_ordered_dict)

        if self._check_my_ordered_dict_main_keys(my_ordered_dict) is False:
            valid = False
        elif self._check_my_ordered_dict_item_list_keys(my_ordered_dict) is False:
            valid = False

        self._valid = valid

        return self._valid

    def cleaned(self, mdn=None):
        """Function to build cleaned data from uploaded file."""

        if self._valid is True:

            self.cleaned_material_master_dict = {
                "as2_message_pk": mdn.message.pk,
                "filename": f"{self.file.name}",
                "code": f"{self.parent_item_code}",
                "name": f"{self.parent_item_name}",
                "uom": [],
            }

            if isinstance(self.item_data_list, list):
                for item_data_dict in self.item_data_list:
                    item_uom_dict = {}

                    item_uom_dict["line_number"] = item_data_dict["lineItemNumber"]
                    item_uom_dict["code"] = self.parent_item_code
                    item_uom_dict["name"] = self.parent_item_name
                    item_uom_dict["ean_number"] = item_data_dict["tradeItemIdentification"]["gtin"]

                    original_uom_dict = item_data_dict["itemDataTradingPartnerNeutral"]
                    item_uom_dict["uom"] = original_uom_dict["tradeItemBaseUnitOfMeasure"]
                    item_uom_dict["conversion_rate_to_pce"] = original_uom_dict["tradeItemClassificationCode"]["#text"]

                    self.cleaned_material_master_dict["uom"].append(item_uom_dict)

            elif isinstance(self.item_data_list, dict):
                item_data_dict = self.item_data_list
                item_uom_dict = {}

                item_uom_dict["line_number"] = item_data_dict["lineItemNumber"]
                item_uom_dict["code"] = self.parent_item_code
                item_uom_dict["name"] = self.parent_item_name
                item_uom_dict["ean_number"] = item_data_dict["tradeItemIdentification"]["gtin"]

                original_uom_dict = item_data_dict["itemDataTradingPartnerNeutral"]
                item_uom_dict["uom"] = original_uom_dict["tradeItemBaseUnitOfMeasure"]
                item_uom_dict["conversion_rate_to_pce"] = original_uom_dict["tradeItemClassificationCode"]["#text"]

                self.cleaned_material_master_dict["uom"].append(item_uom_dict)

    def process_internal(self, user):
        """To create WarehouseReleaseOrder and WarehouseReleaseOrderItem based on internal cleaned_material_master_dict.

        Example of cleaned_material_master_dict:

        cleaned_material_master_dict = {
            "as2_message_pk": 1,
            "filename": "MATMAS_DBSMY.xml"
            "code": "MY655",
            "name": "AV-SET - FMAS DEHT-EB  (TX-JB-5)",
            "uom": [
                {
                    "line_number": "00001",
                    "code": "MY655",
                    "name": "AV-SET - FMAS DEHT-EB  (TX-JB-5)",
                    "uom": "CS",
                    "conversion_rate_to_pce": "24",
                },
                {
                    "line_number": "00002",
                    "code": "MY655",
                    "name": "AV-SET - FMAS DEHT-EB  (TX-JB-5)",
                    "uom": "BOX",
                    "conversion_rate_to_pce": "24",
                },
                ...
            ]
        }

        """

        try:
            # prepare the manage_type value
            if self.manage_type is None or self.manage_type == "":
                self.manage_type = "NM"
            elif self.manage_type.lower() == ("serial managed").lower():
                self.manage_type = "SM"
            elif self.manage_type.lower() == ("batch managed").lower():
                self.manage_type = "BM"
            else:
                self.manage_type = "BM"

            code = self.cleaned_material_master_dict["code"]
            self.item_obj = Item.objects.get(code=code, consignor=self.consignor)

            if self.parent_item_name:
                self.item_obj.name = self.parent_item_name
            else:
                self.item_obj.name = code

            # 5/1/2024 special temporary condition to handle only item MY112 until liyao is back on 17th Jan 2024
            my_112 = Item.objects.get(code="MY112", consignor=self.consignor)
            if self.item_obj != my_112:
                self.item_obj.uom_json = self.cleaned_material_master_dict
            self.item_obj.manage_type = self.manage_type
            self.item_obj.modified_by = self.snapdec_user
            self.item_obj.save()

        except Item.DoesNotExist:

            # 6/1/2024 temporary fix for MM interface 1 XML don't have EN name pass to us, use code.
            prepared_temp_name = self.parent_item_name if self.parent_item_name else self.parent_item_code

            self.item_obj = Item.objects.create(
                created_by=user,
                code=self.parent_item_code,
                name=prepared_temp_name,
                uom=self.uom,
                manage_type=self.manage_type,
                default_stock_in_uom=self.uom,
                default_stock_out_uom=self.uom,
                consignor=self.consignor,
                uom_json=self.cleaned_material_master_dict,
            )

        for uom_dict in self.cleaned_material_master_dict["uom"]:
            uom_str = uom_dict["uom"] if uom_dict["uom"] != "PCE" else "EA"
            prepared_name = f"{uom_str} ({uom_dict['conversion_rate_to_pce']})"
            prepared_symbol = f"{uom_str}-{uom_dict['conversion_rate_to_pce']}"
            unit_precision = 0

            conversion_rate_pce_to_new_uom = 1 / Decimal(uom_dict["conversion_rate_to_pce"])
            decimal_places_exponent = conversion_rate_pce_to_new_uom.as_tuple().exponent
            final_decimal_places = abs(decimal_places_exponent)

            if Decimal(final_decimal_places) >= 4:
                unit_precision = 6
            else:
                unit_precision = final_decimal_places

            if uom_str != "EA":
                try:
                    UnitOfMeasure.objects.get(symbol=prepared_symbol)
                except UnitOfMeasure.DoesNotExist:
                    new_uom = UnitOfMeasure.objects.create(
                        created_by=user,
                        unit_name=prepared_name,
                        symbol=prepared_symbol,
                        unit_precision=unit_precision,
                    )
                    UnitConversion.objects.create(
                        created_by=user,
                        origin=new_uom,
                        target=self.uom,
                        rate=Decimal(f"{uom_dict['conversion_rate_to_pce']}"),
                    )
                    # send_notification(
                    #     instance=new_uom,
                    #     message=f"NEW UOM ({new_uom.unit_name}) had been added.",
                    #     user_role_list=["Superadmin", "Admin"],
                    #     level="info",
                    # )

        return self.item_obj

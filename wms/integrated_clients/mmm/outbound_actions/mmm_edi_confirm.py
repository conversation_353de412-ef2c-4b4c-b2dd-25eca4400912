import logging
import shutil
from typing import Any

from django.conf import settings

from wms.cores.utils import lf_to_crlf, localtime_now

from wms.apps.settings.models import UnitOfMeasure

logger = logging.getLogger(__name__)


class MMMEDIConfirmation:
    """
    MMM outbound Confirmation for EDI.

    Steps:
    1) MMM upload DO.txt into MMM_PICK_LIST folder.
    2) Sinoflex system interval 15 minutes check if there is any new file in MMM_PICK_LIST folder.
    3) IF Yes, Sinoflex System will auto create a DO and process the DO,
       If NO, the system will check the MMM_PICK_LIST folder again 15minutes later.
    4) The moment Sinoflex System auto created a DO,
       system WILL NOT IMMEDIATELY uploaded the RETURN_DO.txt into SINO_PACKINGSLIP folder.
    5) Instead, Sinoflex System will depend on the DO process on the Sinoflex's user to
       perform until the last action of DeliveryOrder status changed to Completed.
    6) Then. Sinoflex System will only upload the RETURN_DO.txt into SINO_PACKINGSLIP folder.
    """

    obj = None
    import_line_list = []
    new_items = {}
    existing_items = {}
    uom_symbol = "EA"

    def __init__(self, obj: Any) -> None:
        self.obj = obj
        self.new_items = {}
        self.existing_items = {}
        self.uom = UnitOfMeasure.objects.get(symbol=self.uom_symbol)

    def upload_return_do_txt(self) -> None:
        """upload the RETURN_DO.txt into SINO_PACKINGSLIP folder."""

        formatted_now = localtime_now().strftime("%Y%m%d%H%M%S")

        final_filename = f"DO_{self.obj.system_number}_{formatted_now}.txt"

        packing_slip_path = settings.MITSUBISHI_FTP_SINO_PACKINGSLIP_PATH

        with open(f"{packing_slip_path}/{final_filename}", "w") as new_file:

            for wro_item in self.obj.warehouse_release_order_items.all():
                packing_list_no = wro_item.release_order.consignor_picking_list_no
                system_delivery_date = wro_item.release_order.consignor_system_delivery_date.strftime("%Y-%m-%d")
                running_number = wro_item.running_number
                item_code = wro_item.item.code
                expected_quantity = str(round(wro_item.quantity, wro_item.item.uom.unit_precision))
                uom = wro_item.uom.symbol

                system_number = self.obj.system_number
                if self.obj.is_delivery_order is True:
                    system_number = self.obj.deliveryorder.system_number

                prepared_str = (
                    f"{packing_list_no}|{system_delivery_date}|{system_number}||"
                    f"{running_number}|{item_code}|{expected_quantity}|{uom}\n"
                )
                new_file.write(prepared_str)

        # TO support LF(Linux) -> CRLF(Windows)
        final_file_path = f"{packing_slip_path}/{final_filename}"
        lf_to_crlf(final_file_path)

        # To copy the generated *_DO.txt file to backup folder
        dest_backup_path = f"{settings.MITSUBISHI_FTP_MMM_BACKUP_PATH}/{final_filename}"
        shutil.copyfile(final_file_path, dest_backup_path)

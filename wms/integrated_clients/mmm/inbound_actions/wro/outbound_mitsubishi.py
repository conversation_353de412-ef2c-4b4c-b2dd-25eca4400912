import logging
from datetime import datetime, timedelta
from decimal import Decimal

from django.conf import settings
from django.utils.safestring import mark_safe
from django.utils.text import get_valid_filename

from wms.cores.utils import localtime_now  # , send_notification

from wms.apps.consignees.models import Consignee
from wms.apps.consignors.models import Consignor
from wms.apps.inventories.models import Item
from wms.apps.releases.models import UPLOAD_PATH, WarehouseReleaseOrder, WarehouseReleaseOrderItem
from wms.apps.settings.models import UnitOfMeasure, Warehouse

logger = logging.getLogger("import_outbound_mitsubishi")


class ImportOutboundMitsubishi:
    """
    Import class for Outbound Mitsubishi consignor.

    After self.cleaned(), it will create the following example:

    error_messages_dict = {
        "__all__": ["File PT0622SBH-02.txt uploaded in DO-22-000001"],
        "3": ["Invalid number of column at line 3"],
        ...
    }
    error_messages_list = [
        "File PT0622SBH-02.txt uploaded in DO-22-000001",
        "Invalid number of column at line 3",
        ...
    ]

    new_items = {
        "3": {
            "item_code": "1005XXXX",
            "item": Item,
            "quantity": "8",
            "uom": UOM,
            "running_num": "00001",
            "customer_document_no": "PPL00274146",
            "customer_reference": "",
            "consignee_code": "10342004",
            "remark": "",
        },
        ...
    }
    existing_items = {
        "1": {
            "item_code": "1005B373",
            "item": Item,
            "quantity": "8",
            "uom": UOM,
            "running_num": "00001",
            "customer_document_no": "PPL00274146",
            "customer_reference": "",
            "consignee_code": "10342004",
            "remark": "",
        },
        "2": {
            "item_code": "1005B373",
            "item": Item,
            "quantity": "8",
            "uom": UOM,
            "running_num": "00002",
            "customer_document_no": "PPL00274146",
            "customer_reference": "",
            "consignee_code": "10342004",
            "remark": "",
        },
        ...
    }
    import_line_list = [
        ["MITSUBISHI", "PPL00260806", "MMM", "10325777", "PSO00216617", "APSDPO22000070", "2022-02-18", "D/SLF",
        "2022-04-03", "", "00001", "4610A009", "2", "EA"],
        ...
    ]

    # Get from import form
    final_item_list = {
        "1": {
            "item_code": "1005B373",
            "item": Item,
            "batch_no": "N/A",
            "quantity": "20",
            "uom": UOM,
            "running_num": "00001",
            "customer_document_no": "PPL00274146",
            "customer_reference": "",
            "consignee_code": "10342004",
            "remark": "",
        },
        "2": {
            "item_code": "1011A392",
            "item": Item,
            "batch_no": "N/A",
            "quantity": "6",
            "uom": UOM,
            "running_num": "00002",
            "customer_document_no": "PPL00274146",
            "customer_reference": "",
            "consignee_code": "10342004",
            "remark": "",
        },
        "3": {
            "item_code": "1005XXXX",
            "item": Item or "ITEM NEW NAME",
            "batch_no": "N/A",
            "quantity": "10",
            "uom": UOM,
            "running_num": "00003",
            "customer_document_no": "PPL00274146",
            "customer_reference": "",
            "consignee_code": "10342004",
            "remark": "",
        },
        ...
    }

    """

    consignor = None
    consignee = None
    customer_document_no = None
    customer_reference = None
    warehouses = None
    error_messages_dict = {}
    error_messages_list = []
    import_line_list = []
    new_items = {}
    existing_items = {}
    uom_symbol = "EA"
    uom = None
    mode_of_delivery = None
    release_datetime = None
    consignor_picking_list_no = None
    consignor_sales_order_no = None
    consignor_customer_requisition_no = None
    consignor_ppl_date = None
    consignor_system_delivery_date = None
    # To tag PPL number to bool (determine whether to create WRO/DO if PPL already has valid/invalid rows)
    tag_ppl_to_bool = {}

    def __init__(self, file):
        self.file = file
        self.consignor = None
        self.consignee = None
        self.customer_document_no = None
        self.customer_reference = None
        self.warehouses = None
        self.error_messages_dict = {}
        self.error_messages_list = []
        self.import_line_list = []
        self.new_items = {}
        self.existing_items = {}
        self.uom = UnitOfMeasure.objects.get(symbol=self.uom_symbol)
        self.is_do = None
        self.release_datetime = None
        self.consignor_picking_list_no = None
        self.consignor_sales_order_no = None
        self.consignor_customer_requisition_no = None
        self.consignor_ppl_date = None
        self.consignor_system_delivery_date = None
        self.tag_ppl_to_bool = {}

    def __str__(self):
        return str(self.file) or ""

    def _check_num_of_column(self, word_item, num_of_column=14):
        """check word_item's length"""
        return len(word_item) == num_of_column

    def _check_file_exist(self):
        """check if file exist in db"""
        filename = get_valid_filename(self.file.name)
        return WarehouseReleaseOrder.objects.filter(imported_outbound_file=f"{UPLOAD_PATH}/{filename}")

    def _check_consignor(self, word_item, valid_string="MMM"):
        """check specific list's position equal string"""
        if self.consignor is None:
            try:
                self.consignor = Consignor.objects.get(code=valid_string)
            except Consignor.DoesNotExist:
                return False

        return word_item[2] == valid_string

    def _check_consignee(self, word_item):
        """check specific list's position equal string"""
        try:
            self.consignee = Consignee.objects.get(code=word_item[3])
        except Consignee.DoesNotExist:
            return False

        return True

    def _check_new_or_existing_item(self, word_item):
        """Check if item is new or existing. If item exist, append item object to the last position of the list."""
        code = word_item[11]
        try:
            item = Item.objects.get(code=code)
        except Item.DoesNotExist:
            return word_item, False
        else:
            word_item.append(item)
            return word_item, True

    def _check_mode_of_delivery(self, word_item):
        """Check if this WRO a DO or self collect.

        available mod option:
        D/SLF = self collect
        M/SF = delivery order
        D/POS = self collect (by courier service)
        """
        available_mod_list = ["D/SLF", "M/SF", "D/POS"]
        mod = word_item[7]

        if mod in available_mod_list:
            if mod in ["D/SLF", "D/POS"]:
                self.is_do = False
            elif mod == "M/SF":
                self.is_do = True
            else:
                False

            # this must check after _check_mode_of_delivery()
            if self.release_datetime is None and self.is_do is not None:
                if self.is_do is True:
                    self.release_datetime = localtime_now() + timedelta(
                        days=settings.MITSUBISHI_DEFAULT_DO_DUE_DATETIME
                    )
                elif self.is_do is False:
                    if localtime_now().time().hour < 14:
                        self.release_datetime = localtime_now() + timedelta(
                            hours=settings.MITSUBISHI_DEFAULT_WRO_DUE_DATETIME
                        )
                    else:
                        self.release_datetime = localtime_now() + timedelta(days=1)

            return True
        else:
            return False

    def _check_ppl_date_date_string_format(self, word_item):
        """check word_item's ppl_date date string format of YYYY-mm-dd"""
        date_string = word_item[6]

        if len(date_string) != 10 and date_string.count("-") != 2:
            return False
        else:
            self.consignor_ppl_date = datetime.strptime(date_string, "%Y-%m-%d").date()
            return True

    def _check_system_delivery_date_date_string_format(self, word_item):
        """check word_item's system delivery date string format of YYYY-mm-dd"""
        date_string = word_item[8]

        if len(date_string) != 10 and date_string.count("-") != 2:
            return False
        else:
            self.consignor_system_delivery_date = datetime.strptime(date_string, "%Y-%m-%d").date()
            return True

    def _check_item_exist(self, word_item):
        """Check if item is exist."""
        try:
            Item.objects.get(code=word_item[11])
        except IndexError:
            return False
        except Item.DoesNotExist:
            return False

    def is_valid(self) -> dict[str, bool]:
        """
        Function to check if the uploaded file is valid by keeping track on each line (based on PPL number).

        Example of input txt file:
            MITSUBISHI|PPL00297111|MMM|99999999|... (Invalid)
            MITSUBISHI|PPL00297111|MMM|10360907|... (Valid)
            MITSUBISHI|PPL00297112|MMM|10360907|... (Valid)
            MITSUBISHI|PPL00297113|MMM|10360907|... (Valid)

        Returns:
            {'PPL00297111': False, 'PPL00297112': True, 'PPL00297113': True}
        """

        warehouse_release_order_qs = self._check_file_exist()
        if warehouse_release_order_qs.exists():
            valid = False
            error_message = mark_safe(
                f"File {self.file.name} uploaded in "
                f"<a href='{warehouse_release_order_qs[0].get_absolute_url()}' "
                f"target='_blank'>{warehouse_release_order_qs[0].system_number}</a>"
            )
            self.error_messages_dict["__all__"] = [error_message]
            self.error_messages_list.append(error_message)

        line_counter = 1

        for line in self.file:
            valid = True
            decoded_line = line.decode("utf-8-sig")
            stripped_line = decoded_line.strip()
            word_item = stripped_line.split("|")

            if self.customer_document_no is None:
                self.customer_document_no = ""

            if self.customer_reference is None:
                self.customer_reference = ""

            if self.warehouses is None:
                self.warehouses = Warehouse.objects.filter(name__in=settings.MITSUBISHI_DEFAULT_WRO_WAREHOUSES)

            if self.consignor_picking_list_no is None:
                self.consignor_picking_list_no = word_item[1]

            if self.consignor_sales_order_no is None:
                self.consignor_sales_order_no = word_item[4]

            if self.consignor_customer_requisition_no is None:
                self.consignor_customer_requisition_no = word_item[5]

            if self._check_num_of_column(word_item) is False:
                valid = False
                error_message = f"Invalid number of column at line {line_counter}"
                self.error_messages_dict[str(int(line_counter))] = [error_message]
                self.error_messages_list.append(error_message)

            if self._check_consignor(word_item) is False:
                valid = False
                error_message = f"Invalid consignor at line {line_counter}"
                self.error_messages_list.append(error_message)
                if str(int(line_counter)) in self.error_messages_dict.keys():
                    self.error_messages_dict[str(int(line_counter))].append(error_message)
                else:
                    self.error_messages_dict[str(int(line_counter))] = [error_message]

            if self._check_consignee(word_item) is False:
                valid = False
                error_message = f"Invalid consignee/dealer at line {line_counter}"
                self.error_messages_list.append(error_message)
                if str(int(line_counter)) in self.error_messages_dict.keys():
                    self.error_messages_dict[str(int(line_counter))].append(error_message)
                else:
                    self.error_messages_dict[str(int(line_counter))] = [error_message]

            if self._check_mode_of_delivery(word_item) is False:
                valid = False
                error_message = f"Invalid mode of delivery at line {line_counter}"
                self.error_messages_list.append(error_message)
                if str(int(line_counter)) in self.error_messages_dict.keys():
                    self.error_messages_dict[str(int(line_counter))].append(error_message)
                else:
                    self.error_messages_dict[str(int(line_counter))] = [error_message]

            if self._check_ppl_date_date_string_format(word_item) is False:
                valid = False
                error_message = f"Invalid ppl date format at line {line_counter}"
                self.error_messages_list.append(error_message)
                if str(int(line_counter)) in self.error_messages_dict.keys():
                    self.error_messages_dict[str(int(line_counter))].append(error_message)
                else:
                    self.error_messages_dict[str(int(line_counter))] = [error_message]

            if self._check_system_delivery_date_date_string_format(word_item) is False:
                valid = False
                error_message = f"Invalid system delivery date format at line {line_counter}"
                self.error_messages_list.append(error_message)
                if str(int(line_counter)) in self.error_messages_dict.keys():
                    self.error_messages_dict[str(int(line_counter))].append(error_message)
                else:
                    self.error_messages_dict[str(int(line_counter))] = [error_message]

            if self._check_item_exist(word_item) is False:
                valid = False
                error_message = f"Invalid item code / part code at line {line_counter}"
                self.error_messages_list.append(error_message)
                if str(int(line_counter)) in self.error_messages_dict.keys():
                    self.error_messages_dict[str(int(line_counter))].append(error_message)
                else:
                    self.error_messages_dict[str(int(line_counter))] = [error_message]

            line_counter += 1
            ppl_number = word_item[1]
            if ppl_number not in self.tag_ppl_to_bool:
                self.tag_ppl_to_bool[ppl_number] = valid
            else:
                # If a line is invalid, we set that PPL flag as False, regardless
                # if that PPL already has a correct row earlier in the file.
                if not valid:
                    self.tag_ppl_to_bool[ppl_number] = valid

        return self.tag_ppl_to_bool

    def cleaned(self):
        """Function to build cleaned data from uploaded file (based on valid rows tagged to PPL number)."""

        for line in self.file:
            decoded_line = line.decode("utf-8-sig")
            stripped_line = decoded_line.strip()
            word_item = stripped_line.split("|")
            ppl_number = word_item[1]

            # Clean data if PPL number has valid rows
            if self.tag_ppl_to_bool[ppl_number]:
                word_item, valid_item_bool = self._check_new_or_existing_item(word_item)

                if valid_item_bool is False:
                    self.new_items[str(int(word_item[10]))] = {
                        "item_code": word_item[11],
                        "item": None,
                        "quantity": word_item[12],
                        "uom": self.uom,
                        "running_number": word_item[10],
                        "customer_document_no": "",
                        "customer_reference": "",
                        "warehouses": "",
                        "remark": "",
                    }
                else:
                    item = word_item[-1]
                    self.existing_items[str(int(word_item[10]))] = {
                        "item_code": word_item[11],
                        "item": item,
                        "quantity": word_item[12],
                        "uom": item.default_stock_in_uom,
                        "running_number": word_item[10],
                        "customer_document_no": "",
                        "customer_reference": "",
                        "warehouses": "",
                        "remark": "",
                    }

                self.import_line_list.append(word_item)

    def process_internal(
        self,
        user,
        customer_document_no,
        customer_reference,
    ):
        """To create WarehouseReleaseOrder and WarehouseReleaseOrderItem based on internal import_line_list.

        Example of import_line_list:

        import_line_list = [
            ["MITSUBISHI", "PPL00260806", "MMM", "10325777", "PSO00216617", "APSDPO22000070", "2022-02-18", "D/SLF",
            "2022-04-03", "", "00001", "4610A009", "2", "EA"],
            ...
        ]

        """
        warehouse_release_order = None
        ppl_number_dict = {}

        for item in self.import_line_list:

            if item[1] not in ppl_number_dict:

                self._check_consignee(item)
                self._check_mode_of_delivery(item)
                self.consignor_picking_list_no = item[1]
                self.consignor_sales_order_no = item[4]
                self.consignor_customer_requisition_no = item[5]
                self._check_ppl_date_date_string_format(item)
                self._check_system_delivery_date_date_string_format(item)

                warehouse_release_order = WarehouseReleaseOrder.objects.create(
                    created_by=user,
                    issued_by=user,
                    consignee=self.consignee,
                    release_datetime=self.release_datetime,
                    imported_outbound_file=self.file,
                    customer_document_no=customer_document_no,
                    customer_reference=customer_reference,
                    is_delivery_order=self.is_do,
                    consignor_picking_list_no=self.consignor_picking_list_no,
                    consignor_sales_order_no=self.consignor_sales_order_no,
                    consignor_customer_requisition_no=self.consignor_customer_requisition_no,
                    consignor_ppl_date=self.consignor_ppl_date,
                    consignor_system_delivery_date=self.consignor_system_delivery_date,
                    is_from_edi=True,
                )

                for warehouse in self.warehouses:
                    warehouse_release_order.warehouses.add(warehouse)

                ppl_number_dict[item[1]] = warehouse_release_order

                # send_notification(
                #     instance=warehouse_release_order,
                #     message=f"NEW WRO created ({warehouse_release_order.system_number}). Ready to pick!",
                #     user_role_list=["Picker"],
                #     warehouses=warehouse_release_order.warehouses.all(),
                #     level="info",
                # )

            WarehouseReleaseOrderItem.objects.create(
                sort_order=int(item[10]),
                release_order=ppl_number_dict[item[1]],
                item=item[14],
                quantity=Decimal(item[12]),
                uom=self.uom,
            )

    def process_external(
        self,
        user,
        customer_document_no,
        customer_reference,
        release_datetime,
        warehouses,
        final_item_list,
    ):
        """To create WarehouseReleaseOrder and WarehouseReleaseOrderItem based on given final_item_list.

        Example of final_item_list:

        final_item_list = {
            "1": {
                "item_code": "1005B373",
                "item": Item,
                "quantity": "20",
                "uom": UOM,
                "running_num": "00001",
                "customer_document_no": "PPL00274146",
                "customer_reference": "",
                "remark": "ITEM DIFFERENT FROM IMPORT",
            },
            "2": {
                "item_code": "1011A392",
                "item": Item,
                "quantity": "6",
                "uom": UOM,
                "running_num": "00002",
                "customer_document_no": "PPL00274146",
                "customer_reference": "",
                "remark": "",
            },
            "3": {
                "item_code": "1005XXXX",
                "item": Item or "ITEM NEW NAME",
                "quantity": "10",
                "uom": UOM,
                "running_num": "00003",
                "customer_document_no": "PPL00274146",
                "customer_reference": "",
                "remark": "ITEM CHOOSEN FROM EXISTING" or "ITEM CREATED FROM IMPORT",
            },
            ...
        }

        """
        warehouse_release_order = WarehouseReleaseOrder.objects.create(
            created_by=user,
            issued_by=user,
            consignee=self.consignee,
            release_datetime=self.release_datetime,
            imported_outbound_file=self.file,
            customer_document_no=customer_document_no,
            customer_reference=customer_reference,
            is_delivery_order=self.is_do,
            consignor_picking_list_no=self.consignor_picking_list_no,
            consignor_sales_order_no=self.consignor_sales_order_no,
            consignor_customer_requisition_no=self.consignor_customer_requisition_no,
            consignor_ppl_date=self.consignor_ppl_date,
            consignor_system_delivery_date=self.consignor_system_delivery_date,
        )

        for warehouse in self.warehouses:
            warehouse_release_order.warehouses.add(warehouse)

        for key, item in final_item_list.items():

            selected_item = None

            if item["remark"] == "ITEM CREATED FROM IMPORT":
                try:
                    selected_item = Item.objects.create(
                        code=item["item_code"], name=item["item"], uom=self.uom, consignor=self.consignor
                    )
                except Exception as error_message:
                    logger.error(f"CREATE ITEM FAILED DURING IMPORT: {error_message}")

            elif isinstance(item["item"], Item):
                selected_item = item["item"]

            if selected_item:
                WarehouseReleaseOrderItem.objects.create(
                    sort_order=int(key),
                    release_order=warehouse_release_order,
                    item=selected_item,
                    quantity=Decimal(item["quantity"]),
                    uom=item["uom"],
                    remark=item["remark"],
                )

        return warehouse_release_order

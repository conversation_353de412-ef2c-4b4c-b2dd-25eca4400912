import logging
from datetime import <PERSON><PERSON><PERSON>
from decimal import Decimal

from django.conf import settings
from django.utils.safestring import mark_safe
from django.utils.text import get_valid_filename

from wms.cores.utils import localtime_now

from wms.apps.consignors.models import Consignor
from wms.apps.inventories.models import Item
from wms.apps.receives.models import UPLOAD_PATH, GoodsReceivedNote, GoodsReceivedNoteItem
from wms.apps.settings.models import UnitOfMeasure, Warehouse

logger = logging.getLogger("import_inbound_mitsubishi")


class ImportInboundMitsubishi:
    """
    Import class for Mitsubishi consignor.

    After self.cleaned(), it will create the following example:

    error_messages_dict = {
        "__all__": ["File PT0622SBH-02.txt uploaded in GRN-22-000001"],
        "3": ["Invalid number of column at line 3"],
        ...
    }
    error_messages_list = [
        "File PT0622SBH-02.txt uploaded in GRN-22-000001",
        "Invalid number of column at line 3",
        ...
    ]
    new_items = {
        "3": {
            "item_code": "1005XXXX",
            "item": None,
            "quantity": "10",
            "uom": UOM,
            "running_number": "00003",
            "shipment_number": "PT0622SBH-02",
            "pallet_number": "BRZ1H00001",
            "remark": "",
        },
        ...
    }
    existing_items = {
        "1": {
            "item_code": "1005B373",
            "item": Item,
            "quantity": "8",
            "uom": UOM,
            "running_number": "00001",
            "shipment_number": "PT0622SBH-02",
            "pallet_number": "BRZ1H00002",
            "remark": "",
        },
        "2": {
            "item_code": "1011A392",
            "item": Item,
            "quantity": "6",
            "uom": UOM,
            "running_number": "00002",
            "shipment_number": "PT0622SBH-02",
            "pallet_number": "BRZ1H00001",
            "remark": "",
        },
        ...
    }
    import_line_list = [
        ["MITSUBISHI", "", "MMM", "", "IJ00302912", "00251", "MR590000HB", "4", "PT0622SBH-02^BRZ1H00001", Item],
        ...
    ]

    # Get from import form
    final_item_list = {
        "1": {
            "item_code": "1005B373",
            "item": Item,
            "quantity": "20",
            "uom": UOM,
            "running_number": "00001",
            "shipment_number": "PT0622SBH-02",
            "pallet_number": "BRZ1H00002",
            "remark": "ITEM DIFFERENT FROM IMPORT",
        },
        "2": {
            "item_code": "1011A392",
            "item": Item,
            "quantity": "6",
            "uom": UOM,
            "running_number": "00002",
            "shipment_number": "PT0622SBH-02",
            "pallet_number": "BRZ1H00001",
            "remark": "",
        },
        "3": {
            "item_code": "1005XXXX",
            "item": Item or "ITEM NEW NAME",
            "quantity": "10",
            "uom": UOM,
            "running_number": "00003",
            "shipment_number": "PT0622SBH-02",
            "pallet_number": "BRZ1H00001",
            "remark": "ITEM CHOOSEN FROM EXISTING" or "ITEM CREATED FROM IMPORT",
        },
        ...
    }

    """

    _valid = False
    consignor = None
    shipment_number = None
    error_messages_dict = {}
    error_messages_list = []
    import_line_list = []
    new_items = {}
    existing_items = {}
    uom_symbol = "EA"
    uom = None
    arrival_datetime = None
    deliver_to = None

    def __init__(self, file):
        self.file = file
        self.consignor = None
        self.shipment_number = None
        self.error_messages_dict = {}
        self.error_messages_list = []
        self.import_line_list = []
        self.new_items = {}
        self.existing_items = {}
        self.uom = UnitOfMeasure.objects.get(symbol=self.uom_symbol)
        self.arrival_datetime = None
        self.deliver_to = None

    def __str__(self):
        return str(self.file) or ""

    def _check_num_of_column(self, word_item, num_of_column=9):
        """check word_item's length"""
        return len(word_item) == num_of_column

    def _check_file_exist(self):
        """check if file exist in db"""
        filename = get_valid_filename(self.file.name)
        return GoodsReceivedNote.objects.filter(imported_file=f"{UPLOAD_PATH}/{filename}")

    def _check_consignor(self, word_item, valid_string="MMM"):
        """check specific list's position equal string"""
        if self.consignor is None:
            try:
                self.consignor = Consignor.objects.get(code=valid_string)
            except Consignor.DoesNotExist:
                return False

        return word_item[1] == valid_string

    def _check_new_or_existing_item(self, word_item):
        """Check if item is new or existing. If item exist, append item object to the last position of the list."""
        code = word_item[5]
        try:
            item = Item.objects.get(code=code)
        except Item.DoesNotExist:
            return word_item, False
        else:
            word_item.append(item)
            return word_item, True

    def _check_item_exist(self, word_item):
        """Check if item is exist."""
        code = word_item[5]
        try:
            Item.objects.get(code=code)
        except Item.DoesNotExist:
            Item.objects.create(code=code, name=code, uom=self.uom, consignor=self.consignor)
        except Exception:
            return False

    def is_valid(self):
        """Function to check if the uploaded file is valid."""

        valid = True

        goods_received_note_qs = self._check_file_exist()
        if goods_received_note_qs.exists():
            valid = False
            error_message = mark_safe(
                f"File {self.file.name} uploaded in "
                f"<a href='{goods_received_note_qs[0].get_absolute_url()}' "
                f"target='_blank'>{goods_received_note_qs[0].system_number}</a>"
            )
            self.error_messages_dict["__all__"] = [error_message]
            self.error_messages_list.append(error_message)

        line_counter = 1

        for line in self.file:
            decoded_line = line.decode("utf-8-sig")
            stripped_line = decoded_line.strip()
            word_item = stripped_line.split("|")

            if self.shipment_number is None:
                self.shipment_number = word_item[8].split("^")[0]

            if self.arrival_datetime is None:
                self.arrival_datetime = localtime_now() + timedelta(days=settings.MITSUBISHI_DEFAULT_GRN_DUE_DATETIME)

            if self.deliver_to is None:
                self.deliver_to = Warehouse.objects.filter(name__in=settings.MITSUBISHI_DEFAULT_GRN_WAREHOUSES).first()

            if self._check_num_of_column(word_item) is False:
                valid = False
                error_message = f"Invalid number of column at line {line_counter}"
                self.error_messages_dict[str(int(line_counter))] = [error_message]
                self.error_messages_list.append(error_message)

            if self._check_consignor(word_item) is False:
                valid = False
                error_message = f"Invalid consignor at line {line_counter}"
                self.error_messages_list.append(error_message)
                if str(int(line_counter)) in self.error_messages_dict.keys():
                    self.error_messages_dict[str(int(line_counter))].append(error_message)
                else:
                    self.error_messages_dict[str(int(line_counter))] = [error_message]

            if self._check_item_exist(word_item) is False:
                valid = False
                error_message = f"Invalid item code / part code at line {line_counter}"
                self.error_messages_list.append(error_message)
                if str(int(line_counter)) in self.error_messages_dict.keys():
                    self.error_messages_dict[str(int(line_counter))].append(error_message)
                else:
                    self.error_messages_dict[str(int(line_counter))] = [error_message]

            line_counter += 1

        self._valid = valid

        return self._valid

    def cleaned(self):
        """Function to build cleaned data from uploaded file."""

        if self._valid is True:
            for line in self.file:
                decoded_line = line.decode("utf-8-sig")
                stripped_line = decoded_line.strip()
                word_item = stripped_line.split("|")

                word_item, valid_item_bool = self._check_new_or_existing_item(word_item)
                shipment_number, pallet_number = word_item[8].split("^")

                if valid_item_bool is False:
                    self.new_items[str(int(word_item[4]))] = {
                        "item_code": word_item[5],
                        "item": None,
                        "quantity": word_item[6],
                        "uom": self.uom,
                        "running_number": word_item[4],
                        "shipment_number": shipment_number,
                        "pallet_number": pallet_number,
                        "remark": "",
                    }
                else:
                    item = word_item[-1]
                    self.existing_items[str(int(word_item[4]))] = {
                        "item_code": word_item[5],
                        "item": item,
                        "quantity": word_item[6],
                        "uom": item.default_stock_in_uom,
                        "running_number": word_item[4],
                        "shipment_number": shipment_number,
                        "pallet_number": pallet_number,
                        "remark": "",
                    }

                self.import_line_list.append(word_item)

    def process_internal(self, user):
        """To create GoodsReceivedNote and GoodsReceivedNoteItem based on internal import_line_list.

        Example of import_line_list:

        import_line_list = [
            ["MITSUBISHI", "", "MMM", "", "IJ00302912", "00251", "MR590000HB", "4", "PT0622SBH-02^BRZ1H00001", Item],
            ...
        ]

        """
        goods_received_note = GoodsReceivedNote.objects.create(
            created_by=user,
            issued_by=user,
            consignor=self.consignor,
            arrival_datetime=self.arrival_datetime,
            deliver_to=self.deliver_to,
            imported_file=self.file,
            customer_reference=self.shipment_number,
            remark=self.import_line_list[0][3],
        )

        for item in self.import_line_list:
            shipment_number, pallet_number = item[8].split("^")

            GoodsReceivedNoteItem.objects.create(
                sort_order=int(item[4]),
                goods_received_note=goods_received_note,
                item=item[9],
                quantity=Decimal(item[6]),
                uom=self.uom,
                shipment_number=shipment_number,
                pallet_number=pallet_number,
            )

        return goods_received_note

    def process_external(self, user, deliver_to, customer_reference, arrival_datetime, final_item_list):
        """To create GoodsReceivedNote and GoodsReceivedNoteItem based on given final_item_list.

        Example of final_item_list:

        final_item_list = {
            "1": {
                "item_code": "1005B373",
                "item": Item,
                "quantity": "20",
                "uom": UOM,
                "running_number": "00001",
                "shipment_number": "PT0622SBH-02",
                "pallet_number": "BRZ1H00002",
                "remark": "ITEM DIFFERENT FROM IMPORT",
            },
            "2": {
                "item_code": "1011A392",
                "item": Item,
                "quantity": "6",
                "uom": UOM,
                "running_number": "00002",
                "shipment_number": "PT0622SBH-02",
                "pallet_number": "BRZ1H00001",
                "remark": "",
            },
            "3": {
                "item_code": "1005XXXX",
                "item": Item or "ITEM NEW NAME",
                "quantity": "10",
                "uom": UOM,
                "running_number": "00003",
                "shipment_number": "PT0622SBH-02",
                "pallet_number": "BRZ1H00001",
                "remark": "ITEM CHOOSEN FROM EXISTING" or "ITEM CREATED FROM IMPORT",
            },
            ...
        }

        """
        goods_received_note = GoodsReceivedNote.objects.create(
            created_by=user,
            issued_by=user,
            consignor=self.consignor,
            arrival_datetime=arrival_datetime,
            deliver_to=deliver_to,
            imported_file=self.file,
            customer_reference=customer_reference,
            remark=self.import_line_list[0][3],
        )

        for key, item in final_item_list.items():

            selected_item = None

            if item["remark"] == "ITEM CREATED FROM IMPORT":
                try:
                    selected_item = Item.objects.create(
                        code=item["item_code"], name=item["item"], uom=self.uom, consignor=self.consignor
                    )
                except Exception as error_message:
                    logger.error(f"CREATE ITEM FAILED DURING IMPORT: {error_message}")

            elif isinstance(item["item"], Item):
                selected_item = item["item"]

            if selected_item:
                GoodsReceivedNoteItem.objects.create(
                    sort_order=int(key),
                    goods_received_note=goods_received_note,
                    item=selected_item,
                    quantity=Decimal(item["quantity"]),
                    uom=item["uom"],
                    shipment_number=item["shipment_number"],
                    pallet_number=item["pallet_number"],
                    remark=item["remark"],
                )

        return goods_received_note

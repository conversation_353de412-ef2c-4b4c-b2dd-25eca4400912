@import url("https://fonts.googleapis.com/css2?=Roboto+Slab&display=swap");
@import "tailwindcss";


/*1. Background colors:*/
/*- Main background: `white`*/
/*- Sidebar background: `#F5F6F8` (light gray)*/
/*- Hover states: `#EBEDF0` (slightly darker gray)*/

/*2. Text colors:*/
/*- Primary text: `#292D34` (dark gray)*/
/*- Secondary text: `#787D85` (medium gray)*/
/*- Accent/Active: `#7B68EE` (purple, kept from previous theme)*/

/*3. Border colors:*/
/*- All borders now use `#E8E8E8` (light gray)*/
@theme {
  --font-sans: "Arial", "Courier New", monospace;
  --font-serif: "Roboto Slab", serif;
  --color-theme-hover: rgba(235, 237, 240);
  --color-theme-primary: oklch(0.604 0.194 285.498);
  --color-theme-default: oklch(0.96 0.02 289.084);
  --color-theme-link: oklch(0.623 0.174 273.65);

  --color-theme-sidebar-primary: oklch(0.973 0.003 264.542);

  --color-theme-bg-primary: oklch(1 0 89.876);
  --color-theme-bg-secondary: oklch(0.967 0.003 264.542);

  --color-theme-text-primary: oklch(0 0 0);
  --color-theme-text-secondary: oklch(0.569 0.005 258.335);
  --color-theme-border-primary: oklch(0.674 0.004 286.282);
  --color-theme-border-secondary: oklch(0.876 0 89.876);

  --color-theme-input-border-primary: oklch(0.674 0.004 286.282);
  --color-theme-input-border-secondary: oklch(0.876 0 89.876);
  --color-theme-input-border-focus: oklch(0.318 0.004 286.198);
  --color-theme-input-disabled: oklch(0.874 0.001 17.181);

  --color-theme-table-primary: oklch(0.971 0.008 278.638);
  --color-theme-table-hover: oklch(0.604 0.194 285.498 / 0.3);
  --color-theme-table-checked: oklch(0.604 0.194 285.498 / 0.3);
  --color-theme-table-border: oklch(0.88 0.068 218.782);
  --color-theme-table-header: oklch(0.973 0.003 264.542);

  --color-theme-form-label: oklch(0.97 0 89.876);
  --color-theme-form-span: oklch(1 0 89.876);
  --color-theme-form-border: oklch(0.872 0.01 258.338);

  --color-theme-status-success: oklch(0.731 0.183 151.277);
  --color-theme-status-warning: oklch(0.813 0.165 75.044);
  --color-theme-status-error: oklch(0.655 0.231 26.401);
  --color-theme-status-info: oklch(0.623 0.188 259.815);

  /* Action button colors */
  --color-theme-action-create: oklch(0.758 0.144 159.822);
  --color-theme-action-create-hover: oklch(0.687 0.132 159.548);

  --color-theme-action-view: oklch(0.595 0.208 270.2);
  --color-theme-action-view-hover: oklch(0.536 0.204 269.694);

  --color-theme-action-update: oklch(0.784 0.159 66.131);
  --color-theme-action-update-hover: oklch(0.731 0.154 65.674);

  --color-theme-action-delete: oklch(0.648 0.223 26.049);
  --color-theme-action-delete-hover: oklch(0.596 0.207 26.199);

  --animate-progress: progress 2s cubic-bezier(0.4, 0, 0.2, 1) infinite;
  @keyframes progress {
    0% {
      transform: translateX(-100%) scaleX(0);
    }
    40% {
      transform: translateX(0%) scaleX(0.4);
    }
    100% {
      transform: translateX(100%) scaleX(0.5);
    }
  }
}

:where(button, [role="button"]):not([disabled]) {
  cursor: pointer;
}

.badge {
  display: inline-flex;
  align-items: center;
  border-radius: 0.25rem;
  padding: 0.125rem 0.5rem;
  font-weight: 500;
  color: white;
}

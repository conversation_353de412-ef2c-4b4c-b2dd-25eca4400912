{% extends "base.html" %}

{% load static %}
{% load crispy_forms_tags %}
{% load i18n %}

{% block content %}
  <!-- Form Section -->
  <form method="post" id="transfer_form">
    {% csrf_token %}
    <section class="max-w-max mx-auto py-6">
      <!-- Title Section -->
      <div class="mb-6">
        <h1 class="text-2xl font-semibold text-theme-text-primary">{{ section_title }}</h1>
        {% if section_desc %}<p class="mt-2 text-theme-text-secondary">{{ section_desc }}</p>{% endif %}
      </div>
      {% include "tailwind/errors.html" %}
      {% include '_components/color_bar.html' %}
      <!-- Basic Information Section -->
      <section class="mx-auto mb-6">
        {% include '_components/section_header.html' with header_title="Basic Information" %}
        <div class="bg-white border-1 border-gray-200 rounded-none overflow-hidden">
          <div class="p-0">
            <div class="space-y-6">
              <div class="bg-theme-bg-secondary/10 rounded-none">
                <div class="grid grid-cols-1 divide-y divide-gray-200 ">
                  {{ form.issued_by|as_crispy_field }}
                  {{ form.transfer_datetime|as_crispy_field }}
                  {{ form.transfer_from|as_crispy_field }}
                  {{ form.transfer_to|as_crispy_field }}
                  {% crispy formset formset.helper %}
                  {{ form.remark|as_crispy_field }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      <!-- Form Actions -->
      <div class="flex items-center justify-between mt-4">
        {% block form_buttons %}
          {% include "_components/button_cancel.html" with link=cancel_url text=cancel_text|default:"Cancel" %}
          {% include "_components/button_submit.html" with action_type=action_type text=submit_text|default:"Save" %}
        {% endblock form_buttons %}
      </div>
    </section>
  </form>
{% endblock content %}
{% block extra_js %}
  <script src="{% static 'js/transfers/transfer_form.js' %}"></script>
{% endblock extra_js %}

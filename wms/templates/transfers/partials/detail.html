{% load i18n %}

{% block content %}
  {% if object %}
    <section class="mx-auto border-1 [&:not(:first-child)]:border-t-0 border-theme-input-border-secondary">
      <div class="py-1.5 px-3 border-b-1 min-h-10 bg-theme-table-header border-theme-input-border-secondary font-semibold uppercase tracking-wider flex justify-between items-center">
        <span>{% trans "Basic Information" %}</span>
        <div class="flex gap-4">
          {% if transfer.status == "Draft" %}
            <a href="{% url 'transfers:notes:update' transfer.pk %}"
               class="inline-flex items-center px-3 py-1 text-sm bg-theme-action-update text-white rounded hover:bg-theme-action-update-hover transition-colors">
              {% trans "Update" %}
            </a>
          {% endif %}
        </div>
      </div>
      <div class="bg-white rounded-none overflow-hidden">
        <!-- Main Information Card -->
        <div class="p-0">
          <div class="space-y-6">
            <!-- Transfer Information Section -->
            <div class="bg-theme-bg-secondary/10 rounded-none">
              <div class="grid grid-cols-1">
                <!-- Left Content -->
                <div class="grid grid-cols-1 divide-y divide-gray-200 ">
                  {% include "_components/detail_field.html" with label="System Number" value=transfer.system_number %}
                  {% include "_components/detail_field.html" with label="Status" value=transfer.html_status_display %}
                  {% include "_components/detail_field.html" with label="Issued By" value=transfer.issued_by %}
                  {% include "_components/detail_field.html" with label="Transfer Date" value=transfer.transfer_datetime %}
                  {% include "_components/detail_field.html" with label="Transfer From" value=transfer.transfer_from %}
                  {% include "_components/detail_field.html" with label="Transfer To" value=transfer.transfer_to %}
                  {% include "_components/detail_field.html" with label="Remark" value=transfer.remark %}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <!-- Transfer Items Section -->
    <section class="mx-auto mt-4 border-0 border-theme-input-border-secondary">
      <div class="bg-white overflow-hidden">
        <!-- partial table content -->
        <div hx-get="{% url 'transfers:notes:item_list' transfer.pk %}?hxf=1"
             hx-trigger="load"
             hx-swap="outerHTML">{% include '_components/loading_spinner.html' %}</div>
      </div>
    </section>
  {% endif %}
{% endblock content %}

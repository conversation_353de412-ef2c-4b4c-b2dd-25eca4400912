{% load i18n %}

<div class="flex items-center gap-2 justify-center" id="transfer-item-actions-{{ record.pk }}">
  {% if record.status == "Draft" %}
    <button
      class="p-2 text-theme-status-success hover:text-theme-status-success/80 transition-colors duration-200"
      title="{% trans 'Approve' %}"
      hx-get="{% url 'transfers:notes:item_approve_form' record.pk %}"
      hx-target="#modal-form-content"
      hx-swap="innerHTML"
      @click="$dispatch('open-modal', { title: '{% trans 'Approve Transfer Item' %}' })">
      <svg xmlns="http://www.w3.org/2000/svg"
           class="h-5 w-5"
           viewBox="0 0 24 24"
           fill="none"
           stroke="currentColor"
           stroke-width="2"
           stroke-linecap="round"
           stroke-linejoin="round">
        <path d="M5 13l4 4L19 7"></path>
      </svg>
    </button>
    <button
      class="p-2 text-theme-status-error hover:text-theme-status-error/80 transition-colors duration-200"
      title="{% trans 'Reject' %}"
      hx-get="{% url 'transfers:notes:item_reject_form' record.pk %}"
      hx-target="#modal-form-content"
      hx-swap="innerHTML"
      @click="$dispatch('open-modal', { title: '{% trans 'Reject Transfer Item' %}' })">
      <svg xmlns="http://www.w3.org/2000/svg"
           class="h-5 w-5"
           viewBox="0 0 24 24"
           fill="none"
           stroke="currentColor"
           stroke-width="2"
           stroke-linecap="round"
           stroke-linejoin="round">
        <path d="M18 6L6 18"></path>
        <path d="M6 6l12 12"></path>
      </svg>
    </button>
  {% else %}
    <!-- Empty cell for non-draft items -->
    <span class="text-sm text-theme-text-secondary">-</span>
  {% endif %}
</div>

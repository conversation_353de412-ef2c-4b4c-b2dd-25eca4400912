{% load i18n %}

{% if filterset %}
  <div class="w-full mb-2 sm:mb-2 bg-theme-bg-secondary border border-theme-border-primary rounded-xs shadow-sm">
    <div class="p-4">
      <form method="get"
            class="w-full"
            id="filter-form"
            hx-get="{{ request.path }}"
            hx-target="{% if table.htmx_target %}#{{ table.htmx_target }}{% else %}#table-content{% endif %}"
            hx-swap="innerHTML"
            hx-include="{% if table.htmx_target %}#{{ table.htmx_target }}-partial-per-page, #{{ table.htmx_target }}-partial-query-search, #{{ table.htmx_target }}-partial-filter-form, #{{ table.htmx_target }}-partial-visible-columns{% else %}#per-page, #query-search, #filter-form, #visible-columns{% endif %}"
            hx-indicator="{% if table.htmx_target %}#{{ table.htmx_target }}-partial-progress-bar{% else %}#progress-bar{% endif %}">
        <div class="grid grid-cols-1 gap-2 lg:grid-cols-2">
          {% for field in filterset.form %}
            <div class="w-full form-group">
              <label for="{{ field.id_for_label }}"
                     class="block text-xs font-semibold tracking-wider text-theme-text-primary">
                {{ field.label }}
              </label>
              <div class="mt-1">
                {{ field }}
                {% if field.help_text %}<p class="mt-1 text-sm text-theme-text-secondary">{{ field.help_text }}</p>{% endif %}
                {% if field.errors %}
                  <div class="mt-1 text-sm text-theme-status-error">
                    {% for error in field.errors %}<p>{{ error }}</p>{% endfor %}
                  </div>
                {% endif %}
              </div>
            </div>
          {% endfor %}
        </div>
        <div class="mt-4 flex flex-col sm:flex-row justify-start gap-2 sm:gap-4">
          <div class="pt-2">
            <button type="button"
                    onclick="resetFilters(); return false;"
                    hx-get="{{ request.path }}"
                    hx-target="#table-wrapper"
                    hx-indicator="{% if table.htmx_target %}#{{ table.htmx_target }}-partial-progress-bar{% else %}#progress-bar{% endif %}"
                    class="py-1.5 px-4 cursor-pointer w-full flex whitespace-nowrap justify-center border border-theme-border-primary rounded-sm shadow-sm text-sm font-medium text-theme-text-primary bg-theme-bg-primary hover:bg-theme-bg-secondary focus:outline-none focus:ring-2 focus:ring-theme-border-focus transition-all duration-150">
              {% trans "Reset" %}
            </button>
          </div>
          {% include "_components/button_submit.html" with text="Filter" %}
        </div>
      </form>
    </div>
  </div>
{% endif %}

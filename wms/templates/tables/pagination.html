{% load django_tables2 %}
{% load i18n %}

{% if table.page and table.paginator.num_pages > 1 %}
  <div class="py-2 flex items-center justify-between">
    <!-- Mobile pagination -->
    <div class="flex-1 flex justify-between sm:hidden">
      {% if table.page.has_previous %}
        <button {% if table.htmx_base_url %} hx-get="{{ table.htmx_base_url }}?{{ table.prefixed_page_field }}={{ table.page.previous_page_number }}" {% else %} hx-get="{% querystring table.prefixed_page_field=table.page.previous_page_number %}" {% endif %}
                hx-target="{% if table.htmx_target %}#{{ table.htmx_target }}{% else %}#table-content{% endif %}"
                class="relative inline-flex items-center px-4 py-1.5 border-1 border-theme-border-secondary rounded-sm text-sm text-theme-text-primary bg-theme-bg-primary hover:bg-theme-hover">
          {% trans "Previous" %}
        </button>
      {% endif %}
      {% if table.page.has_next %}
        <button {% if table.htmx_base_url %} hx-get="{{ table.htmx_base_url }}?{{ table.prefixed_page_field }}={{ table.page.next_page_number }}" {% else %} hx-get="{% querystring table.prefixed_page_field=table.page.next_page_number %}" {% endif %}
                hx-target="{% if table.htmx_target %}#{{ table.htmx_target }}{% else %}#table-content{% endif %}"
                class="ml-3 relative inline-flex items-center px-4 py-1.5 border-1 border-theme-border-secondary rounded-sm text-sm text-theme-text-primary bg-theme-bg-primary hover:bg-theme-hover">
          {% trans "Next" %}
        </button>
      {% endif %}
    </div>
    <!-- Desktop pagination -->
    <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
      <div class="py-2 flex items-center justify-between">
        {% if table.selectable %}
          <!-- Selected Count -->
          <div id="selected-count" class="text-sm text-theme-text-secondary">
            {% if selected_count %}Selected {{ selected_count }}{% endif %}
          </div>
        {% endif %}
      </div>
      <div class="flex items-center justify-between gap-4">
        {% if not table.hide_paginator_count %}
          <p class="text-sm text-theme-text-secondary">
            {% blocktrans with start=table.page.start_index end=table.page.end_index total=table.paginator.count %}
              <span class="font-semibold">{{ start }}</span> -
              <span class="font-semibold">{{ end }}</span> of
              <span class="font-semibold">{{ total }}</span>
            {% endblocktrans %}
          </p>
        {% endif %}
        <nav class="inline-flex -space-x-px rounded-sm" aria-label="Pagination">
          {% include "tables/pagination_numbers.html" %}
        </nav>
      </div>
    </div>
  </div>
{% endif %}

<div class="relative flex-1">
  <input type="text"
         id="{% if table.htmx_target %}{{ table.htmx_target }}-partial-query-search{% else %}query-search{% endif %}"
         name="query"
         value="{{ request.GET.query }}"
         hx-trigger="keyup changed delay:500ms, {% if table.htmx_target %}refresh-table-partial{% else %}refresh-table{% endif %} from:body"
         hx-get="{{ request.path }}"
         hx-target="{% if table.htmx_target %}#{{ table.htmx_target }}{% else %}#table-content{% endif %}"
         hx-swap="innerHTML"
         hx-indicator="{% if table.htmx_target %}#{{ table.htmx_target }}-partial-progress-bar{% else %}#progress-bar{% endif %}"
         hx-include="{% if table.htmx_target %}#{{ table.htmx_target }}-partial-per-page, #{{ table.htmx_target }}-partial-query-search, #{{ table.htmx_target }}-partial-filter-form, #{{ table.htmx_target }}-partial-visible-columns{% else %}#per-page, #query-search, #filter-form, #visible-columns{% endif %}"
         class="group w-full min-w-26 pl-3 pr-10 py-1 border-1 border-theme-input-border-primary rounded-sm placeholder-theme-text/40 text-theme-text-primary focus:border-theme-input-border-focus focus:outline-theme-input-border-focus" />
  <label for="query-search"></label>
  <div class="absolute right-0 top-0 bottom-0 flex items-center pr-3 pointer-events-none search-icons">
    <svg class="h-4 w-4 text-theme-text-secondary"
         viewBox="0 0 20 20"
         fill="currentColor">
      <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
    </svg>
  </div>
</div>

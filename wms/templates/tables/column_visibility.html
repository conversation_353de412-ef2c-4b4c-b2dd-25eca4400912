{% load i18n %}

{% if all_columns %}
  <div class="relative inline-block text-left"
       x-data="{ visibleColumns: new Set({{ visible_columns|safe|default:'[]' }}), allColumns: {{ all_columns|safe|default:'[]' }}, toggleColumnVisibility(columnName, isChecked) { if (isChecked) { this.visibleColumns.add(columnName); } else { this.visibleColumns.delete(columnName); } if (this.visibleColumns.size === 0) { this.visibleColumns.add(columnName); document.querySelector(`#column-${columnName}`).checked = true; alert('At least one column must remain visible.'); return false; } return true; },  isColumnVisible(columnName) { return this.visibleColumns.has(columnName); }, selectAllColumns() { document.querySelectorAll('.column-toggle').forEach(checkbox => { checkbox.checked = true; this.visibleColumns.add(checkbox.value); }); },  deselectAllColumns() { const checkboxes = document.querySelectorAll('.column-toggle'); const firstCheckbox = checkboxes[0]; if (firstCheckbox) { firstCheckbox.checked = true; this.visibleColumns.clear(); this.visibleColumns.add(firstCheckbox.value); }  checkboxes.forEach((checkbox, index) => { if (index !== 0) { checkbox.checked = false; } }); },  initializeColumns() { if (this.visibleColumns.size === 0 && this.allColumns && this.allColumns.length > 0) { this.allColumns.forEach((column, index) => { if (index === 0) this.visibleColumns.add(column); }); } } }"
       x-init="initializeColumns()">
    <button type="button"
            id="columnVisibilityDropdown"
            onclick="this.nextElementSibling.classList.toggle('hidden')"
            class="inline-flex items-center gap-2 px-2 py-1.25 bg-theme-bg-primary hover:bg-theme-bg-secondary border border-theme-border-primary rounded-md text-theme-text-primary text-sm focus:outline-none focus:ring-2 focus:ring-theme-border-focus">
      <svg class="h-4 w-4"
           xmlns="http://www.w3.org/2000/svg"
           viewBox="0 0 20 20"
           fill="currentColor">
        <path
          d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 6a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 6a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"/>
      </svg>
    </button>
    <div
      class="hidden absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-theme-bg-primary border border-theme-border-primary divide-y divide-theme-border-primary z-50"
      id="column-visibility-menu">
      <div class="py-1.5 px-3 max-h-64 overflow-y-auto">
        {% for key, column_name in all_columns.items %}
          <div class="relative flex items-center py-2">
            <input type="checkbox"
                   @change="if(toggleColumnVisibility('{{ key }}', $event.target.checked)) $event.target.dispatchEvent(new Event('column-changed'))"
                   class="h-4 w-4 rounded border-theme-border-primary column-toggle text-theme-primary focus:ring-theme-primary bg-theme-input hover:bg-theme-input-hover"
                   value="{{ key }}"
                   id="column-{{ key }}"
                   :checked="isColumnVisible('{{ key }}')"
                   hx-get="{{ request.path }}"
                   hx-include="{% if table.htmx_target %}#{{ table.htmx_target }}-partial-per-page, #{{ table.htmx_target }}-partial-query-search, #{{ table.htmx_target }}-partial-filter-form, #{{ table.htmx_target }}-partial-visible-columns{% else %}#per-page, #query-search, #filter-form, #visible-columns{% endif %}"
                   hx-target="{% if table.htmx_target %}#{{ table.htmx_target }}{% else %}#table-content{% endif %}"
                   hx-indicator="{% if table.htmx_target %}#{{ table.htmx_target }}-partial-progress-bar{% else %}#progress-bar{% endif %}"
                   hx-swap="innerHTML"
                   hx-trigger="column-changed delay:700ms"/>
            <label class="ml-3 text-sm text-theme-text-primary cursor-pointer select-none"
                   for="column-{{ key }}">{{ column_name|title }}</label>
          </div>
        {% endfor %}
      </div>
      <div class="py-1.5 px-3 flex justify-between gap-2">
        <button type="button"
                name="columnVisibilityChanged"
                class="flex-1 px-3 py-1.5 text-xs bg-theme-bg-primary hover:bg-theme-bg-secondary border border-theme-border-primary rounded text-theme-text-primary focus:outline-none focus:ring-2 focus:ring-theme-border-focus"
                @click="selectAllColumns();"
                hx-get="{{ request.path }}"
                hx-include="{% if table.htmx_target %}#{{ table.htmx_target }}-partial-per-page, #{{ table.htmx_target }}-partial-query-search, #{{ table.htmx_target }}-partial-filter-form, #{{ table.htmx_target }}-partial-visible-columns{% else %}#per-page, #query-search, #filter-form, #visible-columns{% endif %}"
                hx-target="{% if table.htmx_target %}#{{ table.htmx_target }}{% else %}#table-content{% endif %}"
                hx-indicator="{% if table.htmx_target %}#{{ table.htmx_target }}-partial-progress-bar{% else %}#progress-bar{% endif %}">
          {% translate "Select All" %}
        </button>
        <button type="button"
                name="columnVisibilityChanged"
                class="flex-1 px-3 py-1.5 text-xs bg-theme-bg-primary hover:bg-theme-bg-secondary border border-theme-border-primary rounded text-theme-text-primary focus:outline-none focus:ring-2 focus:ring-theme-border-focus"
                @click="deselectAllColumns();"
                hx-get="{{ request.path }}"
                hx-include="{% if table.htmx_target %}#{{ table.htmx_target }}-partial-per-page, #{{ table.htmx_target }}-partial-query-search, #{{ table.htmx_target }}-partial-filter-form, #{{ table.htmx_target }}-partial-visible-columns{% else %}#per-page, #query-search, #filter-form, #visible-columns{% endif %}"
                hx-target="{% if table.htmx_target %}#{{ table.htmx_target }}{% else %}#table-content{% endif %}"
                hx-indicator="{% if table.htmx_target %}#{{ table.htmx_target }}-partial-progress-bar{% else %}#progress-bar{% endif %}">
          {% translate "Deselect All" %}
        </button>
      </div>
      <!-- Hidden input to store visible columns for HTMX requests -->
      <input type="hidden"
             id="{% if table.htmx_target %}{{ table.htmx_target }}-partial-visible-columns{% else %}visible-columns{% endif %}"
             name="visible_columns"
             :value="JSON.stringify(Array.from(visibleColumns))"/>
    </div>
  </div>
{% endif %}

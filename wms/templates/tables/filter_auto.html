{% load i18n %}

{% if filterset %}
  <div class="w-full mb-2 sm:mb-2">
    <div class="p-0">
      <form method="get"
            class="w-full"
            id="{% if table.htmx_target %}{{ table.htmx_target }}-partial-filter-form{% else %}filter-form{% endif %}"
            hx-get="{{ request.path }}"
            hx-target="{% if table.htmx_target %}#{{ table.htmx_target }}{% else %}#table-content{% endif %}"
            hx-swap="innerHTML"
            hx-include="{% if table.htmx_target %}#{{ table.htmx_target }}-partial-per-page, #{{ table.htmx_target }}-partial-query-search, #{{ table.htmx_target }}-partial-filter-form, #{{ table.htmx_target }}-partial-visible-columns{% else %}#per-page, #query-search, #filter-form, #visible-columns{% endif %}"
            hx-indicator="{% if table.htmx_target %}#{{ table.htmx_target }}-partial-progress-bar{% else %}#progress-bar{% endif %}"
            x-data="filterForm('{% if table.htmx_target %}{{ table.htmx_target }}-partial-filter-form{% else %}filter-form{% endif %}')"
            x-init="initializeForm">
        <div class="grid grid-cols-1">
          {% for field in filterset.form %}
            <div class="w-full form-group">
              <label for="{{ field.id_for_label }}"
                     class="block text-xs font-semibold tracking-wider text-theme-text-primary">
                {{ field.label }}
              </label>
              <div class="mt-1">
                {{ field }}
                {% if field.help_text %}<p class="mt-1 text-sm text-theme-text-secondary">{{ field.help_text }}</p>{% endif %}
                {% if field.errors %}
                  <div class="mt-1 text-sm text-theme-status-error">
                    {% for error in field.errors %}<p>{{ error }}</p>{% endfor %}
                  </div>
                {% endif %}
              </div>
            </div>
          {% endfor %}
        </div>
      </form>
    </div>
  </div>
{% endif %}

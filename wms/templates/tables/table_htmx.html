{% extends "django_tables2/table.html" %}

{% load static %}
{% load django_tables2 %}
{% load i18n %}

{% block table-wrapper %}
  <div id="table-wrapper"
       class="relative flex flex-col {{ table.table_height|default:'max-h-[calc(75svh)]' }}">
    <!-- Table Content -->
    <div id="table-container"
         class="overflow-x-auto overflow-y-auto flex-1"
         hx-indicator="{% if table.htmx_target %}#{{ table.htmx_target }}-partial-progress-bar{% else %}#progress-bar{% endif %}">
      {% block table %}
        <!-- Progress Bar -->
        <div id="{% if table.htmx_target %}{{ table.htmx_target }}-partial-progress-bar{% else %}progress-bar{% endif %}"
             class="group htmx-indicator sticky h-0.5 bg-gray-200 top-0 left-0 z-20">
          <div class="h-full bg-theme-action-view origin-left group-[.htmx-request]:animate-progress"></div>
        </div>
        <table class="border-1 border-theme-table-border rounded-xs min-w-full divide-y divide-table-border relative">
          {% block table.thead %}
            <thead class="bg-theme-table-header border-1 border-gray-300 sticky z-10 top-0">
              <tr>
                {% if table.selectable %}
                  <th class="w-4 px-6 py-3 sticky left-0 z-20 bg-theme-table-header">
                    <div class="flex items-center">
                      <input type="checkbox"
                             id="select-all"
                             onchange="handleSelectAll(this)"
                             class="w-4 h-4 rounded-sm border-theme-input-border-primary text-theme-primary" />
                    </div>
                  </th>
                {% endif %}
                {% for column in table.columns %}
                  {% if column.name == 'actions' %}
                    <th scope="col"
                        class="w-[120px] max-w-[120px] text-left px-6 py-2 text-xs font-semibold text-theme-text-primary uppercase tracking-wider bg-theme-table-header {{ column.attrs.th.class|default:'' }}">
                      <span class="text-theme-text-primary">{% trans column.header %}</span>
                    </th>
                  {% else %}
                    <th scope="col"
                        class="px-4 py-1.5 text-left text-xs font-semibold text-theme-text-primary uppercase tracking-wider {{ column.attrs.th.class|default:'' }}">
                      {% if column.orderable %}
                        <a class="group inline-flex items-center gap-1 text-theme-text-primary hover:text-theme-primary/80"
                           {% if table.htmx_base_url %} hx-get="{{ table.htmx_base_url }}?{{ table.prefixed_order_by_field }}={{ column.order_by_alias.next }}" href="{{ table.htmx_base_url }}?{{ table.prefixed_order_by_field }}={{ column.order_by_alias.next }}" {% else %} hx-get="{% querystring table.prefixed_order_by_field=column.order_by_alias.next %}" href="{% querystring table.prefixed_order_by_field=column.order_by_alias.next %}" {% endif %}
                           hx-target="{% if table.htmx_target %}#{{ table.htmx_target }}{% else %}#table-content{% endif %}">
                          {% trans column.header %}
                          <span class="inline-flex flex-col items-center">
                            {% if column.is_ordered %}
                              {% if column.order_by_alias.is_ascending %}
                                <svg class="w-5 h-5 text-theme-primary"
                                     viewBox="0 0 24 24"
                                     fill="none"
                                     stroke="currentColor"
                                     stroke-width="2">
                                  <path d="M8 12L12 8L16 12"></path>
                                </svg>
                              {% else %}
                                <svg class="w-5 h-5 text-theme-primary"
                                     viewBox="0 0 24 24"
                                     fill="none"
                                     stroke="currentColor"
                                     stroke-width="2">
                                  <path d="M8 12L12 16L16 12"></path>
                                </svg>
                              {% endif %}
                            {% else %}
                              <svg class="w-5 h-5 text-theme-text-secondary/80 group-hover:text-theme-primary/50"
                                   viewBox="0 0 24 24"
                                   fill="none"
                                   stroke="currentColor"
                                   stroke-width="2">
                                <path d="M8 10L12 6L16 10M8 14L12 18L16 14"></path>
                              </svg>
                            {% endif %}
                          </span>
                        </a>
                      {% else %}
                        <span class="text-theme-text-primary">{% trans column.header %}</span>
                      {% endif %}
                    </th>
                  {% endif %}
                {% endfor %}
              </tr>
            </thead>
          {% endblock table.thead %}
          {% block table.tbody %}
            <tbody class="bg-theme-bg-primary divide-y divide-theme-table-border">
              {% if table.page and table.page.object_list|length > 0 %}
                {% for row in table.page.object_list|default:table.rows %}
                  <tr id="{{ row.attrs.id|default:'' }}" class="{{ row.attrs.class|default:'' }} odd:bg-theme-table-primary even:bg-theme-bg-primary hover:bg-theme-table-hover has-checked:bg-theme-table-checked">
                    {% if table.selectable %}
                      <td class="w-4 px-6 py-3 sticky left-0 bg-inherit">
                        <div class="flex items-center">
                          <input type="checkbox"
                                 class="row-checkbox w-4 h-4 rounded-sm border-theme-input-border-secondary text-theme-primary"
                                 value="{{ row.record.pk }}"
                                 onchange="handleRowCheckbox(this)" />
                        </div>
                      </td>
                    {% endif %}
                    {% for column, cell in row.items %}
                      {% if column.name == 'actions' %}
                        <td class="w-[120px] max-w-[120px] px-4 py-1.5 text-center">
                          <span class="text-theme-link hover:text-theme-link font-semibold">{{ cell }}</span>
                        </td>
                      {% else %}
                        {% if 'hx-get' in cell|stringformat:'s' %}
                          <td class="px-4 py-1.5 text-theme-link font-semibold {{ column.attrs.td.class|default:'' }}"
                              {% if column.attrs.td.title %}title="{{ column.attrs.td.title }}"{% endif %}>
                            {{ cell }}
                          </td>
                        {% elif cell|stringformat:'s'|slice:':2' == '<a' %}
                          <td class="px-4 py-1.5 text-theme-link font-semibold {{ column.attrs.td.class|default:'' }}"
                              {% if column.attrs.td.title %}title="{{ column.attrs.td.title }}"{% endif %}>
                            {{ cell }}
                          </td>
                        {% else %}
                          <td class="px-4 py-1.5 text-theme-text-primary {{ column.attrs.td.class|default:'' }}"
                              {% if column.attrs.td.title %}title="{{ column.attrs.td.title }}"{% endif %}>
                            {{ cell }}
                          </td>
                        {% endif %}
                      {% endif %}
                    {% endfor %}
                  </tr>
                {% endfor %}
              {% else %}
                <tr>
                  {% with total_columns=table.columns|length %}
                    <td colspan=" {% if table.selectable %}{{ total_columns|add:'1' }}{% else %}{{ total_columns }}{% endif %}"
                        class="px-6 py-4 text-center text-theme-text-secondary font-medium">{% trans "No data" %}</td>
                  {% endwith %}
                </tr>
              {% endif %}
            </tbody>
          {% endblock table.tbody %}
        </table>
      {% endblock table %}
    </div>
    {% include "tables/pagination.html" %}
  </div>
  <style>
    /* Hide loading indicator by default */
    .htmx-indicator {
      opacity: 1;
    }
  </style>
{% endblock table-wrapper %}

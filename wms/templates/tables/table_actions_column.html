{% load static %}

<div class="flex items-center gap-2 justify-start">
  {% if view_url %}
    <a href="{% url view_url record.pk %}"
       class="p-2 group-[.group]:text-white text-theme-action-view hover:text-theme-action-view-hover transition-colors duration-200"
       title="View">
      <svg xmlns="http://www.w3.org/2000/svg"
           width="16"
           height="16"
           viewBox="0 0 24 24"
           fill="none"
           stroke="currentColor"
           stroke-width="2"
           stroke-linecap="round"
           stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" />
        <circle cx="12" cy="12" r="3" />
      </svg>
    </a>
  {% endif %}
  {% if edit_url %}
    <a href="{% url edit_url record.pk %}"
       class="p-2 group-[.group]:text-white text-theme-action-update hover:text-theme-action-update-hover transition-colors duration-200"
       title="Edit">
      <svg xmlns="http://www.w3.org/2000/svg"
           width="16"
           height="16"
           viewBox="0 0 24 24"
           fill="none"
           stroke="currentColor"
           stroke-width="2"
           stroke-linecap="round"
           stroke-linejoin="round">
        <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" />
        <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" />
      </svg>
    </a>
  {% endif %}
  {% if delete_url %}
    <a href="{% url delete_url record.pk %}"  {# Delete URL #}
      class="p-2 group-[.group]:text-white text-theme-action-delete hover:text-theme-action-delete-hover transition-colors duration-200"
      title="Delete">
      <svg xmlns="http://www.w3.org/2000/svg"
           width="16"
           height="16"
           viewBox="0 0 24 24"
           fill="none"
           stroke="currentColor"
           stroke-width="2"
           stroke-linecap="round"
           stroke-linejoin="round">
        <path d="M3 6h18" />
        <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6" />
        <path d="M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2" />
      </svg>
    </a>
  {% endif %}
</div>

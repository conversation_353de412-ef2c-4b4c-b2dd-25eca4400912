<div class="flex items-center gap-2">
  <span class="text-sm text-theme-text-secondary">Rows:</span>
  <div class="relative inline-block">
    <select id="{% if table.htmx_target %}{{ table.htmx_target }}-partial-per-page{% else %}per-page{% endif %}"
            aria-label="per-page"
            name="per_page"
            title="title"
            class="appearance-none w-16 pl-3 pr-6 py-1 border border-theme-input-border-primary rounded-sm text-theme-text-primary bg-theme-bg-primary focus:border-theme-input-border-focus focus:outline-theme-input-border-focus"
            hx-get="{{ request.path }}"
            hx-target="{% if table.htmx_target %}#{{ table.htmx_target }}{% else %}#table-content{% endif %}"
            hx-trigger="change"
            hx-indicator="{% if table.htmx_target %}#{{ table.htmx_target }}-partial-progress-bar{% else %}#progress-bar{% endif %}"
            hx-include="{% if table.htmx_target %}#{{ table.htmx_target }}-partial-per-page, #{{ table.htmx_target }}-partial-query-search, #{{ table.htmx_target }}-partial-filter-form, #{{ table.htmx_target }}-partial-visible-columns{% else %}#per-page, #query-search, #filter-form, #visible-columns{% endif %}">
      <option value="25" {% if table.paginator.per_page == 25 %}selected{% endif %}>25</option>
      <option value="50" {% if table.paginator.per_page == 50 %}selected{% endif %}>50</option>
      <option value="100"
              {% if table.paginator.per_page == 100 %}selected{% endif %}>100</option>
    </select>
    <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-theme-text-secondary">
      <svg class="h-4 w-4"
           xmlns="http://www.w3.org/2000/svg"
           viewBox="0 0 20 20"
           fill="currentColor">
        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
      </svg>
    </div>
  </div>
</div>

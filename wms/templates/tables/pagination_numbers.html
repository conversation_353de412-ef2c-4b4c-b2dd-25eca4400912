{% load django_tables2 %}
{% load i18n %}

<div class="flex flex-wrap items-center gap-1 max-w-full">
  <div class="flex flex-wrap gap-1">
    {% if table.page.has_previous %}
      <button {% if table.htmx_base_url %} hx-get="{{ table.htmx_base_url }}?{{ table.prefixed_page_field }}={{ table.page.previous_page_number }}" {% else %} hx-get="{% querystring table.prefixed_page_field=table.page.previous_page_number %}" {% endif %}
              hx-target="{% if table.htmx_target %}#{{ table.htmx_target }}{% else %}#table-content{% endif %}"
              hx-swap="innerHTML"
              hx-include="{% if table.htmx_target %}#{{ table.htmx_target }}-partial-per-page, #{{ table.htmx_target }}-partial-query-search, #{{ table.htmx_target }}-partial-filter-form, #{{ table.htmx_target }}-partial-visible-columns{% else %}#per-page, #query-search, #filter-form, #visible-columns{% endif %}"
              class="relative cursor-pointer inline-flex items-center px-2 py-1.5 rounded-l-sm border-1 border-theme-input-border-secondary bg-theme-bg-primary text-sm text-theme-text-primary hover:bg-theme-hover">
        <span class="sr-only">Previous</span>
        <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
        </svg>
      </button>
    {% endif %}
    {% with ''|center:table.paginator.num_pages as range %}
      {% for _ in range %}
        {% with forloop.counter as p %}
          {% if p == 1 or p == table.paginator.num_pages or p|add:"-2" <= table.page.number and p|add:"2" >= table.page.number %}
            <button {% if table.htmx_base_url %} hx-get="{{ table.htmx_base_url }}?{{ table.prefixed_page_field }}={{ p }}" {% else %} hx-get="{% querystring table.prefixed_page_field=p %}" {% endif %}
                    hx-target="{% if table.htmx_target %}#{{ table.htmx_target }}{% else %}#table-content{% endif %}"
                    hx-indicator="{% if table.htmx_target %}#{{ table.htmx_target }}-partial-progress-bar{% else %}#progress-bar{% endif %}"
                    hx-include="{% if table.htmx_target %}#{{ table.htmx_target }}-partial-per-page, #{{ table.htmx_target }}-partial-query-search, #{{ table.htmx_target }}-partial-filter-form, #{{ table.htmx_target }}-partial-visible-columns{% else %}#per-page, #query-search, #filter-form, #visible-columns{% endif %}"
                    class="relative cursor-pointer inline-flex items-center px-4 py-1.5 border-1 border-theme-input-border-secondary {% if table.page.number == p %}bg-theme-primary text-white{% else %}bg-theme-bg-primary text-theme-text-primary hover:bg-theme-hover{% endif %} text-sm">
              {{ p }}
            </button>
          {% elif p == 2 and table.page.number > 4 or p == table.paginator.num_pages|add:"-1" and table.page.number < table.paginator.num_pages|add:"-2" %}
            <span class="relative inline-flex items-center px-4 py-1.5 border-1 border-theme-input-border-secondary bg-theme-bg-primary text-sm text-theme-text-primary">
              ...
            </span>
          {% endif %}
        {% endwith %}
      {% endfor %}
    {% endwith %}
    {% if table.page.has_next %}
      <button {% if table.htmx_base_url %} hx-get="{{ table.htmx_base_url }}?{{ table.prefixed_page_field }}={{ table.page.next_page_number }}" {% else %} hx-get="{% querystring table.prefixed_page_field=table.page.next_page_number %}" {% endif %}
              hx-target="{% if table.htmx_target %}#{{ table.htmx_target }}{% else %}#table-content{% endif %}"
              hx-indicator="{% if table.htmx_target %}#{{ table.htmx_target }}-partial-progress-bar{% else %}#progress-bar{% endif %}"
              hx-include="{% if table.htmx_target %}#{{ table.htmx_target }}-partial-per-page, #{{ table.htmx_target }}-partial-query-search, #{{ table.htmx_target }}-partial-filter-form, #{{ table.htmx_target }}-partial-visible-columns{% else %}#per-page, #query-search, #filter-form, #visible-columns{% endif %}"
              class="relative cursor-pointer inline-flex items-center px-2 py-1.5 rounded-r-sm border-1 border-theme-input-border-secondary bg-theme-bg-primary text-sm text-theme-text-primary hover:bg-theme-hover">
        <span class="sr-only">Next</span>
        <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
        </svg>
      </button>
    {% endif %}
  </div>
</div>

{% load django_tables2 %}
{% load i18n %}
{% load custom_tags %}
{% load static %}
{% load crispy_forms_tags %}

<!-- Table Search Header -->
<header class="flex flex-col md:flex-row justify-between items-center bg-theme-bg-primary pt-2 pb-4 gap-4">
  <div class="w-full sm:w-106 flex items-center gap-4">
    {% include "tables/search.html" %}
    {% include "tables/column_visibility.html" %}
  </div>
  <!-- Actions -->
  <div class="w-full justify-end flex items-center gap-4">
    <!-- Per page dropdown -->
    {% include "tables/per_page.html" %}
    {% if table.isExportable %}
      <!-- Export dropdown -->
      <div class="relative">
        <button type="button"
                id="export-menu"
                aria-expanded="false"
                aria-haspopup="true"
                onclick="toggleExportMenu()"
                class="px-2 py-1 border-1 border-theme-input-border-primary rounded-sm text-theme-text-primary bg-theme-bg-primary focus:border-theme-input-border-focus focus:outline-theme-input-border-focus focus:outline-1 inline-flex items-center gap-2">
          <svg class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
          </svg>
          <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
          </svg>
        </button>
        <div id="export-dropdown"
             class="hidden absolute z-30 right-0 mt-2 w-48 rounded-sm shadow-lg bg-theme-bg-primary border-1 border-theme-border-secondary">
          <div class="py-1">
            {% if table.selectable %}
              <button onclick="handleExportSelected()"
                      class="w-full text-left px-4 py-1.5 text-sm text-theme-text-primary hover:bg-theme-hover">
                Export Selected
              </button>
            {% endif %}
            <a href="{{ request.path }}?export=csv{% if request.GET.query %}&query={{ request.GET.query }}{% endif %}"
               target="_blank"
               class="block w-full text-left px-4 py-1.5 text-sm text-theme-text-primary hover:bg-theme-hover">
              Export All
            </a>
            <a href="{{ request.path }}?export=csv"
               class="block w-full text-left px-4 py-1.5 text-sm text-theme-text-primary hover:bg-theme-hover"
               onclick="return handleExportFiltered(this, '{{ table.htmx_target|default:'#table-content' }}')"
               target="_blank">
              <div class="flex items-center justify-between">
                <span>Export Filtered Data</span>
              </div>
            </a>
          </div>
        </div>
      </div>
    {% endif %}
  </div>
</header>

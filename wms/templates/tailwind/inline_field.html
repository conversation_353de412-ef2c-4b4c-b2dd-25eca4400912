{% load tailwind_field %}

{% if field.is_hidden %}
  {{ field }}
{% else %}
  {% with element_tag=tag|default:'div' %}
    <{{ element_tag }}
    id="div_{{ field.auto_id }}"
    class="
    {% if wrapper_class %}{{ wrapper_class }}{% endif %}
    {% if field_class %}
      {{ field_class }}
    {% else %}
      mb-0
    {% endif %}
    ">
    <div class="flex flex-col h-full">
      {# Changed to flex for better td compatibility #}
      <div class="mx-2 mt-2">
        <div class="flex-grow">
          {% if field|is_select %}
            <div class="{% if field_class %}{{ field_class }}{% else %}{% endif %}"
                 {% if flat_attrs %}{{ flat_attrs|safe }}{% endif %}>
              {% include 'tailwind/layout/select.html' %}
            </div>
          {% elif field|is_checkboxselectmultiple %}
            <div class="{% if field_class %}{{ field_class }}{% else %}mb-3{% endif %}"
                 {% if flat_attrs %}{{ flat_attrs|safe }}{% endif %}>
              {% include 'tailwind/layout/checkboxselectmultiple.html' %}
            </div>
          {% elif field|is_radioselect %}
            <div class="{% if field_class %}{{ field_class }}{% else %}mb-3{% endif %}"
                 {% if flat_attrs %}{{ flat_attrs|safe }}{% endif %}>
              {% include 'tailwind/layout/radioselect.html' %}
            </div>
          {% else %}
            {# otherwise use django rendering with additional classes added #}
            {% tailwind_field field field_class="border border-theme-input-border-primary px-4 py-1.5 w-full" %}
          {% endif %}
        </div>
      </div>
      <div class="mx-2 h-6 mt-0 text-xs">
        {# Fixed height for error message #}
        {% if field.help_text or form_show_errors and field.errors %}
          {% if form_show_labels %}
            {% include 'tailwind/layout/help_text_and_errors.html' %}
          {% else %}
            {% if error_text_inline %}
              {% include "tailwind/layout/field_errors.html" %}
            {% else %}
              {% include "tailwind/layout/field_errors_block.html" %}
            {% endif %}
          {% endif %}
        {% endif %}
      </div>
    </div>
    </{{ element_tag }}>
  {% endwith %}
{% endif %}

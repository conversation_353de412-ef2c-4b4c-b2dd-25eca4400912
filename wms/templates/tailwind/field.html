{% load tailwind_field %}

{% if field.is_hidden %}
  {{ field }}
{% else %}
  {% with element_tag=tag|default:'div' %}
    <{{ element_tag }} id="div_{{ field.auto_id }}" class="{% if wrapper_class %} {{ wrapper_class }} {% endif %}
  {% if field_class %} {{ field_class }} {% else %} mb-0 {% endif %}">
    <div class="{% if form_show_labels %}grid grid-cols-[minmax(9rem,auto)_1fr] gap-0{% else %}grid gap-0{% endif %}">
      {% if field.label and form_show_labels %}
        <label for="{{ field.id_for_label }}"
               class="{% if label_class %}{{ label_class }}{% else %} flex items-baseline p-3 pt-4 justify-end text-end text-theme-text-primary text-sm min-w-38 max-w-38 font-medium bg-theme-form-label {% endif %}">
          {% if field.field.required %}<span class="pr-1 text-red-500">*</span>{% endif %}
          {{ field.label|safe }}
        </label>
      {% else %}
        <div class="min-w-38"></div>
      {% endif %}
      <div class="flex flex-col mx-2 my-1.5">
        {% if field|is_select %}
          <div class="{% if field_class %}{{ field_class }}{% else %}{% endif %}"
            {% if flat_attrs %}{{ flat_attrs|safe }}{% endif %}>
            {% include 'tailwind/layout/select.html' %}
          </div>
        {% elif field|is_checkboxselectmultiple %}
          <div class="{% if field_class %}{{ field_class }}{% else %}{% endif %}"
            {% if flat_attrs %}{{ flat_attrs|safe }}{% endif %}>
            {% tailwind_field field %}
          </div>
        {% elif field|is_radioselect %}
          <div class="{% if field_class %}{{ field_class }}{% else %}{% endif %}"
            {% if flat_attrs %}{{ flat_attrs|safe }}{% endif %}>
            {% include 'tailwind/layout/radioselect.html' %}
          </div>
        {% else %}
          {# otherwise use django rendering with additional classes added #}
          {% tailwind_field field field_class="border border-theme-input-border-primary px-4 py-1.5" %}
        {% endif %}
        {% if field.help_text or form_show_errors and field.errors %}
          <!-- Help text and errors directly under the input field -->
          {% if form_show_labels %}
            {% include 'tailwind/layout/help_text_and_errors.html' %}
          {% else %}
            {% if error_text_inline %}
              {% include "tailwind/layout/field_errors.html" %}
            {% else %}
              {% include "tailwind/layout/field_errors_block.html" %}
            {% endif %}
          {% endif %}
        {% endif %}
      </div>
    </div>
    </{{ element_tag }}>
  {% endwith %}
{% endif %}

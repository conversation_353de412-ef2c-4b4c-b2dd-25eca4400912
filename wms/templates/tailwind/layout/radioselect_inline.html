{% if field.is_hidden %}
  {{ field }}
{% else %}
  <
  {% if tag %}
    {{ tag }}
  {% else %}
    div
  {% endif %}
  id="div_{{ field.auto_id }}" class="
  {% if wrapper_class %}{{ wrapper_class }}{% endif %}
  {% if field_class %}
    {{ field_class }}
  {% else %}
    mb-3
  {% endif %}
  ">
  {% if field.label %}
    <label for="{{ field.id_for_label }}"
           class="{% if label_class %}{{ label_class }}{% else %}block text-gray-700 text-sm font-bold mb-2 {% endif %}{% if not inline_class %} col-form-label{% endif %}{% if field.field.required %} requiredField{% endif %}">
      {{ field.label|safe }}
      {% if field.field.required %}<span class="asteriskField">*</span>{% endif %}
    </label>
  {% endif %}
  <div id="div_{{ field.auto_id }}"
       class="flex flex-row{% if wrapper_class %} {{ wrapper_class }}{% endif %}{% if field.css_classes %} {{ field.css_classes }}{% endif %}">
    {% include "tailwind/layout/radioselect.html" %}
  </div>
{% endif %}

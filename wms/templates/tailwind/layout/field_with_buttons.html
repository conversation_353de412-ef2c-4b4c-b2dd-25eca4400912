{% load tailwind_field %}

<div {% if div.css_id %}id="{{ div.css_id }}"{% endif %}
     class="{% if wrapper_class %} {{ wrapper_class }}{% endif %}{% if field.css_classes %} {{ field.css_classes }}{% endif %}{% if div.css_class %} {{ div.css_class }}{% endif %}"
     {{ div.flat_attrs|safe }}>
  {% if field.label and form_show_labels %}
    <label for="{{ field.id_for_label }}"
           class="{% if 'form-horizontal' in form_class %}col-form-label {% endif %}{{ label_class }}{% if field.field.required %} requiredField{% endif %}">
      {{ field.label|safe }}
      {% if field.field.required %}<span class="asteriskField">*</span>{% endif %}
    </label>
  {% endif %}
  <div class="flex{% if field_class %} {{ field_class }}{% endif %}">
    {% tailwind_field field "class" "border border-gray-300 rounded-xs rounded-r-none px-4 py-1.5 w-full focus:outline-none text-gray-700 border-r-0 leading-normal" %}
    <span class="{% if field.errors %}border-red-500 border-r-0 {% else %}border-gray-300 {% endif %}border rounded-xs rounded-l-none bg-gray-200 text-gray-800 flex items-center">{{ buttons|safe }}</span>
    {% include "tailwind/layout/help_text_and_errors.html" %}
  </div>
</div>

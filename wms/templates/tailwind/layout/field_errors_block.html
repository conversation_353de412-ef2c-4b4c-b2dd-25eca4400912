{% if form_show_errors and field.errors %}
  {% for error in field.errors %}
    <div class="mt-1 flex items-start">
      <div class="flex-shrink-0">
        <svg class="h-3.5 w-3.5 text-theme-status-error"
             viewBox="0 0 20 20"
             fill="currentColor">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
        </svg>
      </div>
      <div class="ml-1">
        <p id="error_{{ forloop.counter }}_{{ field.auto_id }}"
           class="text-xs font-medium text-theme-status-error">{{ error|striptags }}</p>
      </div>
    </div>
  {% endfor %}
{% endif %}

{% load crispy_forms_filters %}
{% load tailwind_filters %}
{% load l10n %}

<div class="relative {% if field.errors %}has-error{% endif %}">
  <div class="relative inline-block w-full">
    <select class="{{ field.field.widget.attrs.class|default:'border rounded-sm py-2 pl-4 pr-10 block appearance-none leading-normal text-theme-text-primary' }} {% if field.errors %}border-theme-status-error{% else %}border-theme-border-primary{% endif %}"
            name="{{ field.html_name }}"
            {% if field.help_text %}aria-describedby="hint_{{ field.auto_id }}"{% endif %}
            {% if field.field.required %}required{% endif %}
            {{ field|build_attrs }}>
      {% if field.field.choices %}
        {% for choice in field.field.choices %}
          <option value="{{ choice.0|stringformat:'s' }}"
                  {% if choice.0|stringformat:"s" == field.value|stringformat:"s" %}selected{% endif %}
                  {% if choice.2 %}disabled{% endif %}>{{ choice.1 }}</option>
        {% endfor %}
      {% else %}
        {% for group, options, index in field.optgroups %}
          {% if group %}<optgroup label="{{ group }}">{% endif %}
            {% for option in options %}
              {% include "tailwind/layout/select_option.html" %}
            {% endfor %}
            {% if group %}</optgroup>{% endif %}
        {% endfor %}
      {% endif %}
    </select>
    {#    {% if field.help_text %}#}
    {#      <div id="hint_{{ field.auto_id }}"#}
    {#           class="mt-1 text-sm text-theme-text-secondary">{{ field.help_text }}</div>#}
    {#    {% endif %}#}
  </div>
</div>

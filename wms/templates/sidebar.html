<div class="relative h-screen"
     @mouseleave="$store.sidebar.handleMouseLeave()"
     @mouseenter="$store.sidebar.handleMouseEnter()">
  <!-- Sidebar Container -->
  <div class="fixed top-0 left-0 h-screen bg-theme-sidebar-primary border-r border-theme-border-secondary transition-all duration-300 ease-in-out z-30 overflow-hidden"
       :class="{ 'w-64': $store.sidebar.isOpen || $store.sidebar.isHovered, 'w-0': !$store.sidebar.isOpen && !$store.sidebar.isHovered {# Hidden Sidebar State #} }">
    <!-- Inner Container with fixed width to prevent content squishing -->
    <div class="h-full w-64 flex flex-col">
      <!-- Profile Section -->
      <div class="flex-shrink-0">{% include "profile_dropdown.html" %}</div>
      <!-- Navigation Menu -->
      <div class="flex-1 overflow-y-auto">{% include "navigation.html" %}</div>
      <!-- Version Footer -->
      <div class="flex-shrink-0 border-t border-theme-border-secondary p-1">
        <div class="flex items-center justify-center text-xs text-theme-text-secondary">
          <span>Version 1.0.0</span>
          <button @click="$store.sidebar.toggleFooter()" class="ml-2 text-theme-text-secondary hover:text-theme-text-primary transition-colors" title="Show footer information">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
{# Sidebar Container End #}

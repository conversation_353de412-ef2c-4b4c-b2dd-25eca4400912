<div class="relative flex items-center justify-between px-4 h-12 border-b border-theme-border-secondary">
  <div class="relative flex-1" x-data="headerProfile">
    <button @click="toggleDropdown"
            title="Profile"
            class="w-full flex items-center gap-3 px-2 py-1 rounded-xs hover:bg-theme-hover transition-all duration-200">
      <span class="w-8 h-8 rounded-full bg-indigo-50 flex items-center justify-center">
        {% if request.user.avatar %}
          <img src="{{ request.user.avatar.url }}"
               alt="{{ request.user.get_full_name }}"
               class="w-6 h-6 rounded-full" />
        {% else %}
          <svg class="w-5 h-5 text-indigo-600"
               fill="none"
               stroke="currentColor"
               viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
        {% endif %}
      </span>
      <span class="flex flex-col text-left">
        <span class="text-xs font-medium text-theme-text-primary">
          {{ request.user.get_full_name|default:request.user.username }}
        </span>
        <span class="text-xs text-theme-text-secondary">@{{ request.user.username }}</span>
      </span>
    </button>
    <!-- Dropdown Menu -->
    <div x-show="isOpen"
         x-cloak
         @click.away="closeDropdown"
         x-transition:enter="transition ease-out duration-100"
         x-transition:enter-start="transform opacity-0 scale-95"
         x-transition:enter-end="transform opacity-100 scale-100"
         x-transition:leave="transition ease-in duration-75"
         x-transition:leave-start="transform opacity-100 scale-100"
         x-transition:leave-end="transform opacity-0 scale-95"
         class="fixed left-0 ml-1 mt-2 w-[280px] rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-[100] divide-y divide-gray-100">
      <!-- User Info -->
      <div class="px-4 py-3">
        <p class="text-sm font-medium text-theme-text-primary">Signed in as</p>
        <p class="text-sm text-theme-text-secondary truncate">{{ request.user.email }}</p>
      </div>
      <!-- Actions -->
      <div class="py-1">
        <a href="{% url 'users:detail' request.user.id %}"
           class="group flex items-center px-4 py-1.5 text-sm text-theme-text-primary hover:bg-theme-hover">
          <svg class="mr-3 h-5 w-5 text-gray-600 group-hover:text-gray-500"
               fill="none"
               viewBox="0 0 24 24"
               stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
          Profile
        </a>
      </div>
      <!-- Logout -->
      <div class="py-1">
        <form id="logout-form" method="post" action="{% url 'account_logout' %}">
          {% csrf_token %}
          <button type="submit" {# Sign out button #}
            class="group flex w-full items-center px-4 py-1.5 text-sm text-theme-status-error hover:bg-theme-status-error/10">
            <svg class="mr-3 h-5 w-5 text-red-400 group-hover:text-red-500"
                 fill="none"
                 viewBox="0 0 24 24"
                 stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
            </svg>
            Sign out
          </button>
        </form>
      </div>
    </div>
  </div>
  <!-- Sidebar Toggle -->
  <button title="title"
          @click="$store.sidebar.toggle()"
          class="p-2 rounded-lg hover:bg-theme-hover transition-colors duration-200">
    <svg class="w-5 h-5 text-gray-500"
         fill="none"
         stroke="currentColor"
         viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
    </svg>
    <!-- Keyboard Shortcut Tooltip -->
    <span class="absolute bottom-0 right-0 transform translate-y-full mb-1 hidden group-hover:block bg-gray-800 text-white text-xs px-2 py-1 rounded whitespace-nowrap">
      Ctrl+\
    </span>
  </button>
</div>

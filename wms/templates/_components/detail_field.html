{% load i18n %}

<div class="flex border-b-1 [&:last-child]:border-b-0 [&:only-child]:border-b-1 rounded-none border-gray-200">
  <label class="flex items-center p-3 justify-end text-end text-theme-text-primary text-sm w-36 font-medium bg-theme-form-label">
    {% if translate_label|default:True %}
      {% trans label %}
    {% else %}
      {{ label }}
    {% endif %}
  </label>
  <span class="flex items-center p-3 text-theme-text-primary bg-theme-form-span flex-1">{{ value }}</span>
</div>

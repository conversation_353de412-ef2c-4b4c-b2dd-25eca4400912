{% load i18n %}

<!-- Modal Component (supports closing with Escape key) -->
<div x-data="{
       open: false,
       title: '',
       eventHandlers: [],
       setupEventHandlers() {
         // Find all scripts in the modal content and execute them
         const scripts = document.querySelectorAll('#modal-form-content script');
         scripts.forEach(script => {
           // Create a new script element to ensure it executes
           const newScript = document.createElement('script');
           newScript.textContent = script.textContent;
           // Store a reference to any event handlers created by this script
           if (newScript.textContent.includes('addEventListener')) {
             this.eventHandlers.push(newScript);
           }
           // Replace the original script with the new one
           script.parentNode.replaceChild(newScript, script);
         });
       },
       cleanup() {
         // Immediately hide the content (but keep the structure for animation)
         const modalContent = document.getElementById('modal-form-content');
         if (modalContent) modalContent.style.opacity = '0';

         // Delay full cleanup until after the animation completes
         setTimeout(() => {
           // Clear the modal content to prevent duplicate event handlers
           if (modalContent) {
             modalContent.innerHTML = '';
             modalContent.style.opacity = '1'; // Reset opacity for next open
           }
           // Remove any event handlers that were added by the modal content
           this.eventHandlers = [];
         }, 300); // Match the duration of the leave animation (200ms) plus a small buffer
       }
     }"
     x-show="open"
     x-cloak
     @open-modal.window="open = true; title = $event.detail.title; $nextTick(() => { const modalContent = document.getElementById('modal-form-content'); if (modalContent) modalContent.style.visibility = 'visible'; $refs.closeButton.focus(); setupEventHandlers(); })"
     @close-modal.window="open = false; cleanup()"
     @keydown.escape.window="if(open) { open = false; cleanup() }"
     x-trap.noscroll.inert="open"
     class="fixed inset-0 z-50 overflow-y-auto"
     aria-labelledby="modal-title"
     role="dialog"
     aria-modal="true">

  <!-- Background overlay - semi-transparent -->
  <div class="fixed inset-0 bg-gray-100/80 transition-opacity"
       x-show="open"
       x-transition:enter="ease-out duration-300"
       x-transition:enter-start="opacity-0"
       x-transition:enter-end="opacity-100"
       x-transition:leave="ease-in duration-200"
       x-transition:leave-start="opacity-100"
       x-transition:leave-end="opacity-0"
       @click="open = false; cleanup()"></div>

  <!-- Modal panel -->
  <div class="flex items-center justify-center min-h-screen p-4 text-center sm:p-0">
    <div class="relative bg-theme-bg-primary rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:min-w-lg sm:w-fit border border-theme-border-primary"
         x-show="open"
         x-transition:enter="ease-out duration-300"
         x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
         x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
         x-transition:leave="ease-in duration-200"
         x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
         x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
         @transitionend.leave="if (!open) { document.getElementById('modal-form-content').style.visibility = 'hidden'; }">

      <!-- Modal header -->
      <div class="px-4 pt-2 pb-2 sm:p-6 sm:pb-2 relative">
        <!-- Close button at top right -->
        <button type="button"
                x-ref="closeButton"
                @click="open = false; cleanup()"
                class="absolute top-3 right-3 text-theme-text-secondary hover:text-theme-text-primary focus:outline-none focus:ring-2 focus:ring-theme-primary focus:ring-offset-2 rounded-sm">
          <span class="sr-only">Close</span>
          <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
          </svg>
        </button>

        <div class="flex items-center">
          <div class="mt-3 text-center tracking-wide sm:mt-0 sm:text-left">
            <h3 class="text-lg leading-6 font-medium text-theme-text-primary"
                id="modal-title"
                x-text="title"></h3>
          </div>
        </div>
      </div>

      <!-- Modal content -->
      <div class="px-4 pb-4 sm:p-6 sm:pt-0">
        <div id="modal-form-content" class="transition-opacity duration-200">
          <!-- Form content will be loaded here -->
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  [x-cloak] { display: none !important; }
</style>

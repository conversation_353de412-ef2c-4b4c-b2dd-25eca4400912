{% load i18n %}

<!-- Cancel Button -->
<div class="pt-2 mx-1">
  <button type="button"
          onclick="goBackOrRedirect('{% if ':' in link %} {% if link_args %} {% url link link_args %} {% else %} {% url link %} {% endif %} {% else %} {{ link }} {% endif %}')"
          class="w-full flex whitespace-nowrap justify-center py-1.5 px-4 border border-theme-border-primary rounded-sm shadow-sm text-sm font-medium text-theme-text-primary bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-theme-link transition-all duration-150">
    {% trans text %}
  </button>
</div>
<script>
  function goBackOrRedirect(fallbackUrl) {
    // Check if there's history and the previous page is from the same domain
    if (window.history.length > 1 && document.referrer && document.referrer.startsWith(window.location.origin)) {
      window.history.back();
    } else {
      // Clean up the URL (remove extra spaces)
      fallbackUrl = fallbackUrl.trim();
      window.location.href = fallbackUrl;
    }
  }
</script>

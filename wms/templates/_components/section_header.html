{% load i18n %}

<!-- Section Header -->
<div class="py-1.5 px-3 border-1 min-h-10 bg-theme-table-header border-gray-300 font-semibold uppercase tracking-wider flex justify-between items-center">
  <div class="flex gap-2 items-center">
    <span>{% trans header_title %}</span>
    {% if header_key %}
      <div class="inline-flex items-center px-3 py-1 text-sm text-theme-action-view rounded">{{ header_key }}</div>
    {% endif %}
  </div>
</div>

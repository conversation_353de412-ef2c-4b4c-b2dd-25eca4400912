{% load i18n %}

{% if messages %}
  <div class="messages">
    {% for message in messages %}
      <div class="flex items-center p-2 mt-2 rounded-xs {% if message.tags %} {% if message.tags == 'success' %}bg-theme-status-success/20 text-theme-status-success border border-theme-status-success/30{% endif %} {% if message.tags == 'error' %}bg-theme-status-error/20 text-theme-status-error border border-theme-status-error/30{% endif %} {% if message.tags == 'warning' %}bg-theme-status-warning/20 text-theme-status-warning border border-theme-status-warning/30{% endif %} {% if message.tags == 'info' %}bg-theme-status-info/20 text-theme-status-info border border-theme-status-info/30{% endif %} {% else %}bg-theme-status-info/20 text-theme-status-info border border-theme-status-info/30{% endif %}"
           role="alert">
        <div class="ml-3 text-sm font-medium">{{ message }}</div>
        <button type="button"
                class="ml-auto -mx-1.5 -my-1.5 rounded-lg focus:ring-2 p-1.5 inline-flex items-center justify-center h-8 w-8 {% if message.tags == 'success' %}hover:bg-theme-status-success/20 focus:ring-theme-status-success/30{% endif %} {% if message.tags == 'error' %}hover:bg-theme-status-error/20 focus:ring-theme-status-error/30{% endif %} {% if message.tags == 'warning' %}hover:bg-theme-status-warning/20 focus:ring-theme-status-warning/30{% endif %} {% if message.tags == 'info' %}hover:bg-theme-status-info/20 focus:ring-theme-status-info/30{% endif %}"
                aria-label="Close"
                onclick="this.parentElement.remove()">
          <span class="sr-only">Close</span>
          <svg class="w-3 h-3"
               aria-hidden="true"
               xmlns="http://www.w3.org/2000/svg"
               fill="none"
               viewBox="0 0 14 14">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
          </svg>
        </button>
      </div>
    {% endfor %}
  </div>
{% endif %}

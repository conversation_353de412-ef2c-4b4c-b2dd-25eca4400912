{% load static %}

<!DOCTYPE html>
<html lang="en" class="h-full">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>
    {% block head_title %}Crate Pilot{% endblock %}
  </title>
  <!-- Favicons -->
  <link rel="icon" href="{% static 'images/favicons/favicon.ico' %}"/>
  {% block css %}
    <link href="{% static 'css/vendor/vanilla-calendar-pro.css' %}"
          rel="stylesheet"/>
    <link href="{% static 'css/vendor/select2.min.css' %}" rel="stylesheet"/>
    <link href="{% static 'css/output.css' %}" rel="stylesheet"/>
    <link href="{% static 'css/select2-custom.css' %}" rel="stylesheet"/>
    <style>
      [x-cloak] {
        display: none !important;
      }
    </style>
  {% endblock css %}
  {% block js %}
    <!-- Load Alpine.js first -->
    <script defer src="{% static 'js/vendor/alpinejs-focus.min.js' %}"></script>
    <script defer src="{% static 'js/vendor/alpinejs.min.js' %}"></script>

    <!-- Then load your dependent scripts -->
    <script defer src="{% static 'js/vendor/vanilla-calendar-pro.js' %}"></script>
    <script src="{% static 'js/vendor/jquery.min.js' %}"></script>
    <!-- jQuery is required for Select2 -->
    <script src="{% static 'js/vendor/select2.min.js' %}"></script>
    <script src="{% static 'js/toast.js' %}"></script>
    <script src="{% static 'js/body-init.js' %}"></script>
    {% if request.user.is_authenticated %}
      <script src="{% static 'js/vendor/htmx.min.js' %}" defer></script>
      <script src="{% static 'js/header.js' %}"></script>
      <script src="{% static 'js/calendar.js' %}"></script>
      <script src="{% static 'js/select2.js' %}"></script>
      <script src="{% static 'js/table.js' %}"></script>
      <script src="{% static 'js/formset_handler.js' %}"></script>
      <script src="{% static 'js/breadcrumb_updater.js' %}"></script>
    {% endif %}
  {% endblock js %}
</head>
<body
  class="text-sm"
  x-data="bodyInit"
>
{% include 'toast.html' %}
{% include '_components/modal.html' %}
{% block layout %}
  {% if request.user.is_authenticated %}
    <div class="flex h-screen bg-theme-sidebar-primary"
         x-data="{ isMobileMenuOpen: false }">
      <!-- Sidebar with Navigation -->
      {% include "sidebar.html" %}
      <!-- Main Content Area -->
      <main class="flex-1 flex flex-col overflow-hidden bg-theme-bg-primary transition-all duration-300 ease-in-out"
            :class="$store.sidebar.isOpen ? 'ml-64' : 'ml-0'">
        {% include "header.html" %}
        <div class="flex-1 overflow-auto">
          <div class="mx-auto px-6 max-w-screen min-w-md">
            <div class="flex items-center">
              <!-- messages -->
              <div class="w-full mx-auto">{% include '_components/messages.html' %}</div>
            </div>
            {% block content %}{% endblock %}
          </div>
        </div>
        <div x-show="$store.sidebar.showFooter" x-cloak>
          {% include "footer.html" %}
        </div>
      </main>
    </div>
  {% else %}
    <div class="min-h-screen min-w-md flex flex-col justify-center bg-linear-to-t from-gray-50 to-gray-100 px-6">
      <div class="w-full rounded-xl max-w-md mx-auto space-y-2 pt-12">
        {% block auth_content %}{% endblock %}
      </div>
    </div>
  {% endif %}
  {% block extra_js %}{% endblock %}
{% endblock %}
</body>
</html>

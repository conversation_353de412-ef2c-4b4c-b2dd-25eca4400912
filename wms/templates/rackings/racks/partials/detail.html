{% load i18n %}

{% block content %}
  {% if object %}
    <section class="mx-auto border-1 [&:not(:first-child)]:border-t-0 border-theme-input-border-secondary">
      <div class="py-1.5 px-3 border-b-1 min-h-10 bg-theme-table-header border-theme-input-border-secondary font-semibold uppercase tracking-wider flex justify-between items-center">
        <span>{% trans "Basic Information" %}</span>
        <div class="flex gap-4">
          <a href="{% url 'rackings:racks:update' rack.pk %}"
             class="inline-flex items-center px-3 py-1 text-sm bg-theme-action-update text-white rounded hover:bg-theme-action-update-hover transition-colors">
            {% trans "Update" %}
          </a>
          <a href="{% url 'rackings:racks:update' rack.pk %}"
             class="inline-flex items-center px-3 py-1 text-sm bg-theme-action-delete text-white rounded hover:bg-theme-action-delete-hover transition-colors">
            {% trans "Delete" %}
          </a>
        </div>
      </div>
      <div class="bg-white rounded-none overflow-hidden">
        <!-- Main Information Card -->
        <div class="p-0">
          <div class="space-y-6">
            <!-- Item Information Section -->
            <div class="bg-theme-bg-secondary/10 rounded-none">
              <div class="grid grid-cols-1">
                <!-- Left Content -->
                <div class="grid grid-cols-1 divide-y divide-gray-200 ">
                  {% include "_components/detail_field.html" with label="Name" value=rack.name %}
                  {% include "_components/detail_field.html" with label="Full Name" value=rack.full_name %}
                  {% include "_components/detail_field.html" with label="Warehouse" value=rack.warehouse %}
                  {% include "_components/detail_field.html" with label="Rack Type" value=rack.rack_type %}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  {% endif %}
{% endblock content %}

{% load i18n %}

{% if object %}
  <!-- Tab Navigation -->
  <div class="mb-2">
    <!-- Desktop Tabs -->
    <div class="block overflow-x-auto">
      <div class="mb-2">
        <nav class="-mb-px flex space-x-4"
             aria-label="Tabs"
             x-data="{ activeTab: window.location.hash || '#detail' }">
          {% include '_components/hide_table_button.html' %}
          <a href="#detail"
             class="group inline-flex items-center rounded-md px-8 py-2 font-medium transition-colors border-1"
             :class="activeTab === '#detail' ? 'bg-theme-primary/30 text-indigo-700' : 'bg-theme-bg-primary text-theme-text-secondary hover:border-gray-299 hover:text-theme-text-primary'"
             hx-get="{% url 'adjustments:adjustment:detail' adjustment.pk %}"
             hx-target="#tab-content"
             hx-indicator="#tab-loading"
             hx-trigger="click, load[window.location.hash === '' || window.location.hash === '#detail']"
             hx-swap="innerHTML"
             hx-push-url="false"
             @click="activeTab = '#detail'">
            <span>Detail</span>
          </a>
          {#          <a href="#event"#}
          {#             class="group inline-flex items-center rounded-md px-8 py-2 font-medium transition-colors border-1"#}
          {#             :class="activeTab === '#event' ? 'bg-theme-primary/50 text-indigo-700' : 'bg-theme-bg-primary text-theme-text-secondary hover:border-gray-299 hover:text-theme-text-primary'"#}
          {#             hx-get="{% url 'adjustments:adjustment:event' adjustment.pk %}"#}
          {#             hx-target="#tab-content"#}
          {#             hx-indicator="#tab-loading"#}
          {#             hx-trigger="click, load[window.location.hash === '#event']"#}
          {#             hx-swap="innerHTML"#}
          {#             hx-push-url="false"#}
          {#             @click="activeTab = '#event'">#}
          {#            <span>Event</span>#}
          {#          </a>#}
        </nav>
      </div>
    </div>
  </div>
  <!-- Progress Bar -->
  <div id="tab-loading"
       class="group htmx-indicator sticky h-0.5 bg-gray-200 top-0 left-0 z-20 overflow-hidden">
    <div class="h-full bg-theme-action-view origin-left group-[.htmx-request]:animate-progress"></div>
  </div>
  <div id="tab-content" class="bg-white rounded-none overflow-hidden mb-6">
    <!-- Tab Content -->
  </div>
{% endif %}

{% load humanize %}
{% load i18n %}
{% load crispy_forms_tags %}

<form method="post" action="{% url 'adjustments:adjustment:item_reject' adjustment_item.pk %}"
      hx-post="{% url 'adjustments:adjustment:item_reject' adjustment_item.pk %}"
      hx-trigger="submit"
      @htmx:after-request="
        if(event.detail.successful) {
          $dispatch('close-modal');
          $dispatch('notify', {message: '{% trans "Adjustment item rejected successfully" %}', type: 'success'});

          const response = JSON.parse(event.detail.xhr.response);

          // Update the actions column
          if (response.actions) {
            const actionsEl = document.querySelector('#adjustment-item-actions-{{ adjustment_item.pk }}');
            if (actionsEl) actionsEl.outerHTML = response.actions;
          }

          // Update the status column
          if (response.status) {
            const statusEl = document.querySelector('#adjustment-item-status-{{ adjustment_item.pk }}');
            if (statusEl) statusEl.outerHTML = response.status;
          }

          // Update the action_by column
          if (response.action_by) {
            const actionByEl = document.querySelector('#adjustment-item-action-by-{{ adjustment_item.pk }}');
            if (actionByEl) actionByEl.outerHTML = response.action_by;
          }
        }
      ">
  {% csrf_token %}

  <!-- Item Details Section -->
  <div class="mb-4 border border-theme-border-primary rounded-xs overflow-hidden">
    <div class="bg-theme-bg-secondary px-4 py-1.5 border-b border-theme-border-primary">
      <h4 class="text-sm font-medium text-theme-text-primary">{% trans "Item Details" %}
        #{{ adjustment_item.get_position }}
      </h4>
    </div>
    <div class="p-4 grid grid-cols-3 gap-3 text-sm">
      <div class="col-span-3">
        <p class="font-medium text-theme-text-secondary">{% trans "Item Code" %}</p>
        <p class="text-theme-text-primary">{{ adjustment_item.item.code }}</p>
      </div>
      <div class="col-span-3">
        <p class="font-medium text-theme-text-secondary">{% trans "Item Name" %}</p>
        <p class="text-theme-text-primary">{{ adjustment_item.item.name }}</p>
      </div>
      <div>
        <p class="font-medium text-theme-text-secondary">{% trans "Batch No" %}</p>
        <p class="text-theme-text-primary font-bold">{{ adjustment_item.batch_no }}</p>
      </div>
      <div>
        <p class="font-medium text-theme-text-secondary">{% trans "Expiry Date" %}</p>
        <p class="text-theme-text-primary">{{ adjustment_item.expiry_date|date:"Y-m-d" }}</p>
      </div>
      <div>
        <p class="font-medium text-theme-text-secondary">{% trans "Quantity" %}</p>
        <p class="text-theme-text-primary">{{ adjustment_item.quantity|floatformat|intcomma }} {{ adjustment_item.uom.symbol }}</p>
      </div>
    </div>
  </div>

  <div class="mb-4">
    <label for="action_by"
           class="block text-sm font-bold text-theme-text-primary mb-1">{% trans "Rejected By" %}</label>
    <div class="px-3 text-sm text-theme-text-primary">{{ request.user.get_display_name }}</div>
    <input type="hidden" name="action_by" value="{{ request.user.pk }}">
  </div>

  <div class="mb-4">
    <label for="remark" class="block text-sm font-bold text-theme-text-primary mb-1">{% trans "Remarks" %}</label>
    <textarea id="remark" name="remark" rows="3"
              class="w-full px-3 py-2 border border-theme-border-primary rounded-xs shadow-sm focus:outline-none focus:ring-theme-primary focus:border-theme-primary"
              required></textarea>
    <p class="mt-1 text-xs text-theme-text-secondary">{% trans "Optional: Provide a reason for rejection" %}</p>
  </div>

  <div class="flex justify-between">
    <button type="button" @click="$dispatch('close-modal')"
            class="px-4 py-1.5 border border-theme-border-primary rounded-sm shadow-sm text-sm font-medium text-theme-text-primary bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-theme-link">
      {% trans "Cancel" %}
    </button>
    <button type="submit"
            class="px-4 py-1.5 border border-transparent rounded-sm shadow-sm text-sm font-medium text-white bg-theme-status-error hover:bg-theme-status-error/80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-theme-status-error">
      {% trans "Reject" %}
    </button>
  </div>
</form>



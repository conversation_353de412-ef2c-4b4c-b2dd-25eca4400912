{% load i18n %}
{% load humanize %}

<form method="post"
      hx-post="{% url 'receives:goods_received_notes:obsolete' goods_received_note.pk %}"
      hx-trigger="submit">
  {% csrf_token %}
  <div class="p-4">
    <!-- Goods Received Note Information Section -->
    <div class="mb-4 border border-theme-border-primary rounded-xs overflow-hidden">
      <div class="bg-theme-bg-secondary px-4 py-2 border-b border-theme-border-primary">
        <h4 class="text-sm font-medium text-theme-text-primary">{% trans "Goods Received Note Details" %}</h4>
      </div>
      <div class="p-4">
        <!-- Basic Goods Received Note Information -->
        <div class="grid grid-cols-2 gap-3 sm:grid-cols-2 mb-4">
          <div class="col-span-2">
            <label class="block text-sm font-medium text-gray-700">{% trans "System Number" %}</label>
            <div class="mt-1 text-sm text-gray-900 font-semibold">{{ goods_received_note.system_number }}</div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">{% trans "Status" %}</label>
            <div class="mt-1 text-sm text-gray-900">{{ goods_received_note.get_status_display }}</div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">{% trans "Consignor" %}</label>
            <div class="mt-1 text-sm text-gray-900">{{ goods_received_note.consignor.name }}</div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">{% trans "Arrival Date" %}</label>
            <div class="mt-1 text-sm text-gray-900">{{ goods_received_note.arrival_datetime|date:"d M Y" }}</div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">{% trans "Issued By" %}</label>
            <div class="mt-1 text-sm text-gray-900">{{ goods_received_note.issued_by.get_full_name|default:goods_received_note.issued_by.username }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Obsolete Confirmation -->
    <div class="mb-4 text-center">
      <h3 class="text-lg font-medium text-red-600">
        {% translate "Are you sure you want to mark this GRN as obsolete?" %}
      </h3>
      <p class="mt-2 text-sm text-gray-500">
        {% translate "This action cannot be undone." %}
      </p>
    </div>

    <!-- Action Buttons -->
    <div class="flex justify-between space-x-3">
      <button
        type="button"
        class="py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400 cursor-pointer"
        @click="$dispatch('close-modal')"
      >
        {% translate "Cancel" %}
      </button>
      <button
        type="submit"
        class="py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 cursor-pointer"
      >
        {% translate "Mark as Obsolete" %}
      </button>
    </div>
  </div>
</form>

{% load i18n %}

<div class="flex items-center gap-2 justify-center" id="goods-received-note-item-actions-{{ record.pk }}">
  {% if record.goods_received_note.status == 'New' or record.goods_received_note.status == 'Partially Received' %}
    {% if record.status == 'Open' or record.status == 'Received (Partially)' %}
      <button
        hx-get="{% url 'receives:goods_received_notes:item_receive_form' record.pk %}"
        hx-target="#modal-form-content"
        hx-swap="innerHTML"
        class="p-2 text-theme-status-success hover:text-theme-status-success/80 transition-colors duration-200 cursor-pointer"
        title="{% translate 'Receive Item' %}"
        @click="$dispatch('open-modal', { title: '{% trans 'Pick Item' %}' })">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor"
             stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M14 3v4a1 1 0 0 0 1 1h4"/>
          <path d="M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z"/>
          <path d="M9 17h6"/>
          <path d="M9 13h6"/>
        </svg>
      </button>
    {% endif %}
    {% if record.get_received_quantity == 0 %}
      {% with item_count=record.goods_received_note.goodsreceivednoteitem_set.count %}
        {% if item_count > 1 %}
          {# Delete Button - Only show when there's more than one item #}
          <button
            hx-get="{% url 'receives:goods_received_notes:item_delete_form' record.pk %}"
            hx-target="#modal-form-content"
            hx-swap="innerHTML"
            class="p-2 text-red-600 hover:text-red-800 cursor-pointer"
            title="{% translate 'Delete Item' %}"
            @click="$dispatch('open-modal', { title: '{% trans 'Delete GRN Item' %}' })">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none"
                 stroke="currentColor"
                 stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M18 6L6 18"></path>
              <path d="M6 6l12 12"></path>
            </svg>
          </button>
        {% endif %}
      {% endwith %}
    {% endif %}
  {% endif %}
</div>

{% load i18n %}
{% load crispy_forms_tags %}
{% load humanize %}

<form
  method="post"
  hx-post="{% url 'receives:goods_received_notes:item_receive_form' goods_received_note_item.pk %}"
  hx-trigger="submit"
>
  {% csrf_token %}
  <div class="p-0">
    <!-- Item Information Section -->
    <div class="mb-4 border border-theme-border-primary rounded-xs overflow-hidden">
      <div class="bg-theme-bg-secondary px-4 py-2 border-b border-theme-border-primary">
        <h4 class="text-sm font-medium text-theme-text-primary">{% trans "Item Details" %}</h4>
      </div>
      <div class="p-4">
        <!-- Basic Item Information -->
        <div class="grid grid-cols-2 gap-3 sm:grid-cols-2 mb-4">
          <div class="col-span-2">
            <label class="block text-sm font-medium text-gray-700">{% trans "Item Code" %}</label>
            <div class="mt-1 text-sm text-gray-900 font-semibold">{{ goods_received_note_item.item.code }}</div>
          </div>
          <div class="col-span-2">
            <label class="block text-sm font-medium text-gray-700">{% trans "Item Name" %}</label>
            <div class="mt-1 text-sm text-gray-900">{{ goods_received_note_item.item.name }}</div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">{% trans "Batch No" %}</label>
            <div class="mt-1 text-sm text-gray-900">{{ goods_received_note_item.batch_no|default:"-" }}</div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">{% trans "Expiry Date" %}</label>
            <div class="mt-1 text-sm text-gray-900">{{ goods_received_note_item.expiry_date|date:"d M Y"|default:"-" }}</div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">{% trans "Quantity" %}</label>
            <div class="mt-1 text-sm text-gray-900">{{ goods_received_note_item.quantity|floatformat:0 }}</div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">{% trans "UOM" %}</label>
            <div class="mt-1 text-sm text-gray-900">{{ goods_received_note_item.uom }}</div>
          </div>
        </div>
      </div>
    </div>

    <div class="mb-2">
      {{ form.quantity.label_tag }}
      <div class="mt-1">
        {{ form.quantity }}
      </div>
      {% if form.quantity.errors %}
        <div class="text-red-500 text-xs mt-1">{{ form.quantity.errors }}</div>
      {% else %}
        <p class="mt-1 text-xs text-gray-500">{% trans "Enter the quantity to pick" %}</p>
      {% endif %}
    </div>

    <!-- Action Buttons -->
    <div class="flex justify-between space-x-3">
      <button
        type="button"
        class="py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400 cursor-pointer"
        @click="$dispatch('close-modal')"
      >
        {% translate "Cancel" %}
      </button>
      <button
        type="submit"
        class="py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-theme-action-create hover:bg-theme-action-create-hover focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 cursor-pointer"
      >
        {% translate "Pick Item" %}
      </button>
    </div>
  </div>
</form>

<script>
  (function() {
    // Function to position cursor at the end of the quantity field
    function positionCursorAtEnd() {
      const quantityField = document.getElementById('id_quantity');
      if (quantityField) {
        quantityField.focus();
        const valueLength = quantityField.value.length;
        quantityField.type = 'text';
        quantityField.setSelectionRange(valueLength, valueLength);
        quantityField.type = 'number';
      }
    }

    // Initialize and set up event listeners
    document.addEventListener('htmx:afterSettle', function() {
      const quantityField = document.getElementById('id_quantity');
      if (quantityField) {
        positionCursorAtEnd();
      }
    });
  })();
</script>

{% load i18n %}

{% block content %}
  {% if object %}
    <section class="mx-auto border-1 border-t-0 border-theme-input-border-secondary">
      <div class="py-1.5 px-3 border-b-1 min-h-10 bg-theme-table-header border-theme-input-border-secondary font-semibold uppercase tracking-wider flex justify-between items-center">
        <span>{% trans "Basic Information" %}</span>
        <div class="flex gap-4">
          <a href="{% url 'inventories:items:update' item.pk %}"
             class="inline-flex items-center px-3 py-1 text-sm bg-theme-action-update text-white rounded hover:bg-theme-action-update-hover transition-colors">
            {% trans "Update" %}
          </a>
          <a href="{% url 'inventories:items:update' item.pk %}"
             class="inline-flex items-center px-3 py-1 text-sm bg-theme-action-delete text-white rounded hover:bg-theme-action-delete-hover transition-colors">
            {% trans "Delete" %}
          </a>
        </div>
      </div>
      <div class="bg-white rounded-none overflow-hidden">
        <!-- Main Information Card -->
        <div class="p-0">
          <div class="space-y-6">
            <!-- Item Information Section -->
            <div class="bg-theme-bg-secondary/10 rounded-none">
              <div class="grid grid-cols-1">
                <!-- Left Content -->
                <div class="grid grid-cols-1 divide-y divide-gray-200 ">
                  {% include "_components/detail_field.html" with label="Consignor" value=item.consignor %}
                  {% include "_components/detail_field.html" with label="Code" value=item.code %}
                  {% include "_components/detail_field.html" with label="Name" value=item.name %}
                  {% include "_components/detail_field.html" with label="Brand" value=item.brand %}
                  {% include "_components/detail_field.html" with label="Type" value=item.item_type %}
                  {% include "_components/detail_field.html" with label="Manage Type" value=item.manage_type %}
                  {% include "_components/detail_field.html" with label="UOM" value=item.uom %}
                  {% include "_components/detail_field.html" with label="Dimensions" value=item.html_dimensions_display %}
                  {% include "_components/detail_field.html" with label="Weight" value=item.html_weight_display %}
                  {% include "_components/detail_field.html" with label="Outbound UOM display" value=item.html_outbound_uom_display_conversions_display %}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <section class="mx-auto">
      <!-- Two column grid for the entire content -->
      <div class="grid grid-cols-1 md:grid-cols-2">
        <div>
          <!-- Left Header -->
          {% include '_components/section_header.html' with header_title="Settings" %}
          <!-- Left Column -->
          <div class="border-1 border-t-0 border-theme-input-border-secondary">
            <!-- Left Content -->
            <div class="bg-white rounded-none overflow-hidden">
              <div class="p-0 space-y-6">
                <div class="bg-theme-bg-secondary/10 rounded-none">
                  <div class="grid grid-cols-1 divide-y divide-gray-200 ">
                    {% include "_components/detail_field.html" with label="Default stock in UOM" value=item.consignor %}
                    {% include "_components/detail_field.html" with label="Default stock out UOM" value=item.code %}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- Right Column -->
        <div>
          <!-- Right Header -->
          {% include '_components/section_header.html' with header_title="Barcode" %}
          <!-- Left Column -->
          <div class="border-1 border-t-0 border-theme-input-border-secondary">
            <!-- Right Content -->
            <div class="bg-white rounded-none overflow-hidden">
              <div class="p-0 space-y-6">
                <div class="bg-theme-bg-secondary/10 rounded-none">
                  <div class="space-y-0">{% include "_components/detail_field.html" with label="Barcode" value=item.code %}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  {% endif %}
{% endblock content %}

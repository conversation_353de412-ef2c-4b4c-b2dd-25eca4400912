{% load i18n %}
{% load humanize %}

{% block content %}
  {% if object %}
    <section class="mx-auto">
      {% include '_components/section_header.html' with header_title="Stock" %}
      <div class="bg-white overflow-hidden">
        <!-- Main Information Card -->
        <div class="p-0">
          <div class="space-y-6">
            <!-- Item Information Section -->
            <div class="bg-theme-bg-secondary/10 rounded-none">
              <div class="py-4 space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div class="group border-gray-100 border-1 rounded-xs shadow-md overflow-hidden bg-white">
                    <div class="p-3">
                      <div class="flex items-center justify-center">
                        <span class="text-theme-text-primary font-bold text-xl">{{ stocks_total.total|floatformat|intcomma }} {{ stocks_total.uom_symbol }}</span>
                      </div>
                    </div>
                  </div>
                  <div class="group border-gray-100 border-1 rounded-xs shadow-md overflow-hidden bg-white">
                    <div class="p-3">
                      <div class="flex items-center justify-center">
                        <span class="text-green-500 font-bold text-xl">{{ stocks_total.total_non_defect|floatformat|intcomma }} {{ stocks_total.uom_symbol }}</span>
                      </div>
                    </div>
                  </div>
                  <div class="group border-gray-100 border-1 rounded-xs shadow-md overflow-hidden bg-white">
                    <div class="p-3">
                      <div class="flex items-center justify-center">
                        <span class="text-red-500  font-bold text-xl">{{ stocks_total.total_defect|floatformat|intcomma }} {{ stocks_total.uom_symbol }}</span>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- Group By Warehouse -->
                {% regroup warehouses_stock by warehouse_group as warehouse_groups %}
                {% for group in warehouse_groups %}
                  <div class="mb-4">
                    <!-- Warehouse Group Header -->
                    <div class="bg-theme-tables-header p-3 pl-0 border-0 border-gray-200 text-base font-bold uppercase tracking-wider rounded-xs ">
                      {{ group.grouper }}
                    </div>
                    <!-- Cards for this warehouse group - 3 per row max -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {% for stock_on_hand in group.list %}
                        <div class="group flex border-gray-100 border-1 rounded-xs shadow-md overflow-hidden bg-white hover:bg-theme-table-hover/50 cursor-pointer">
                          <!-- Warehouse Icon - Full Height Column -->
                          <div class=" flex items-center justify-center px-4 {% if 'defect' in stock_on_hand.warehouse_name|lower %}bg-red-100{% else %}bg-green-100 {% endif %}">
                            <svg xmlns="http://www.w3.org/2000/svg"
                                 class="h-10 w-10 {% if 'defect' in stock_on_hand.warehouse_name|lower %} text-red-500 {% else %} text-green-500 {% endif %}"
                                 fill="none"
                                 viewBox="0 0 24 24"
                                 stroke="currentColor">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                            </svg>
                          </div>
                          <!-- Content Column - Two Rows -->
                          <div class="flex-1">
                            <!-- Warehouse Path - Top Row -->
                            <div class="px-4 pt-2">
                              <span class="text-blue-500 font-medium text-sm">
                                {% for path_item in stock_on_hand.full_warehouse_path %}
                                  {{ path_item }}
                                  {% if not forloop.last %}>{% endif %}
                                {% endfor %}
                              </span>
                            </div>
                            <!-- Quantity with UOM - Bottom Row -->
                            <div class="px-4 pt-1 pb-1">
                              <div class="flex items-center">
                                <span class="text-blue-500 font-bold text-xl">{{ stock_on_hand.total_quantity|floatformat|intcomma }} {{ stock_on_hand.uom_symbol }}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      {% endfor %}
                    </div>
                  </div>
                {% endfor %}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="bg-white overflow-hidden">
        <!-- partial table content -->
        <div hx-get="{% url 'inventories:items:stock_list' item.pk %}?hxf=1"
             hx-trigger="load"
             hx-swap="outerHTML">{% include '_components/loading_spinner.html' %}</div>
      </div>
    </section>
  {% endif %}
{% endblock content %}

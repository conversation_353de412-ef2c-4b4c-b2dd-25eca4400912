{% load i18n %}
{% load humanize %}

{% block content %}
  {% if object %}
    <section class="mx-auto">
      {% include '_components/section_header.html' with header_title="Rack Transaction" %}
      <div class="bg-white overflow-hidden">
        <!-- Main Information Card -->
        <div class="p-0">
          <div class="space-y-6">
            <!-- Item Information Section -->
            <div class="bg-theme-bg-secondary/10 rounded-none">
              <div class="py-4 space-y-4">{{ rackstorage }}</div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <section class="mx-auto pt-4">
      <div class="bg-white overflow-hidden">
        <!-- partial table content -->
        <div hx-get="{% url 'inventories:rackstorages:rack_transaction_list' rackstorage.pk %}?hxf=1"
             hx-trigger="load"
             hx-swap="outerHTML"></div>
      </div>
    </section>
  {% endif %}
{% endblock content %}

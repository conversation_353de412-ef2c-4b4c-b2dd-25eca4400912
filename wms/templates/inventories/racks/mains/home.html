{% load i18n %}

{% if object %}
  <!-- Tab Navigation -->
  <div class="mb-2">
    <!-- Desktop Tabs -->
    <div class="block overflow-x-auto">
      <div class="mb-4">
        <nav class="-mb-px flex space-x-4"
             aria-label="Tabs"
             x-data="{ activeTab: window.location.hash || '#detail' }">
          {% include '_components/hide_table_button.html' %}
          <a href="#detail"
             class="group inline-flex items-center rounded-md px-8 py-2 font-medium transition-colors border-1"
             :class="activeTab === '#detail' ? 'bg-theme-primary/30 text-indigo-700' : 'bg-theme-bg-primary text-theme-text-secondary hover:border-gray-299 hover:text-theme-text-primary'"
             hx-get="{% url 'inventories:racks:detail' rack.pk %}"
             hx-target="#tab-content"
             hx-indicator="#tab-loading"
             hx-trigger="click, load[window.location.hash === '' || window.location.hash === '#detail']"
             hx-swap="innerHTML"
             hx-push-url="false"
             @click="activeTab = '#detail'">
            <span>Detail</span>
          </a>
          <a href="{% url 'inventories:racks:outer_rack_transaction_adjustment' rack.pk %}"
             class="px-4 py-1.5 rounded-sm text-white font-semibold bg-theme-action-create hover:bg-theme-action-create-hover focus:ring-2 focus:ring-theme-action-create focus:ring-offset-2 inline-flex items-center gap-1">
            <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" />
            </svg>
            {% trans "Adjustment" %}
          </a>
        </nav>
      </div>
    </div>
  </div>
  <!-- Progress Bar -->
  <div id="tab-loading"
       class="group htmx-indicator sticky h-0.5 bg-gray-200 top-0 left-0 z-20">
    <div class="h-full bg-theme-action-view origin-left group-[.htmx-request]:animate-progress"></div>
  </div>
  <div id="tab-content" class="bg-white rounded-none overflow-hidden mb-6">
    <!-- Tab Content -->
  </div>
{% endif %}

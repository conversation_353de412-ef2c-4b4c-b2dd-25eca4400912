{% load i18n %}

{% block content %}
  {% if object %}
    <section class="mx-auto border-1 [&:not(:first-child)]:border-t-0 border-theme-input-border-secondary">
      <div class="bg-white overflow-hidden">
        <!-- partial table content -->
        <div hx-get="{% url 'inventories:warehouses:item_list' warehouse.pk %}?hxf=1"
             hx-trigger="load"
             hx-swap="outerHTML">{% include '_components/loading_spinner.html' %}</div>
      </div>
    </section>
  {% endif %}
{% endblock content %}

{% extends "account/base_manage_password.html" %}

{% load i18n %}

{% block manage_title %}
  {% trans "Change Password" %}
{% endblock manage_title %}
{% block password_content %}
  <form method="post"
        action="{% url 'account_change_password' %}"
        class="space-y-6">
    {% csrf_token %}
    {{ form.as_p }} {# Password Change Form Fields #}
    <button type="submit"
            class="w-full flex justify-center py-1.5 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-theme-primary hover:bg-theme-primary/80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-theme-primary">
      {% trans "Change Password" %}
    </button>
  </form>
{% endblock password_content %}

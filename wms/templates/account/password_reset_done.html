{% extends "base.html" %}

{% load i18n %}

{% block title %}
  {% trans "Password Reset Email Sent" %} - WMS
{% endblock title %}
{% block auth_content %}
  <div class="sm:mx-auto sm:w-full sm:max-w-md">
    <div class="text-center">
      <h2 class="mt-6 text-3xl font-extrabold text-theme-text-primary">{% trans "Check Your Email" %}</h2>
      <p class="mt-2 text-sm text-theme-text-primary/60">
        {% trans "We've sent you instructions for resetting your password." %}
      </p>
    </div>
  </div>
  <div class="bg-theme-background py-8 px-10 shadow-2xl rounded-lg">
    <div class="rounded-md bg-theme-status-success/10 p-4 mb-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-theme-status-success"
               viewBox="0 0 20 20"
               fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <p class="text-sm font-medium text-theme-status-success">
            {% trans "If an account exists with the email you entered, you will receive password reset instructions." %}
          </p>
        </div>
      </div>
    </div>
    <!-- Back to Log in -->
    {% include "_components/link.html" with link="account_login" text="Back to login" %}
  </div>
{% endblock auth_content %}

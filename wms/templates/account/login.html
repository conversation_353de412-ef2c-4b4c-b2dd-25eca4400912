{% extends "base.html" %}

{% load static %}
{% load i18n %}

{% block title %}
  Login - WMS
{% endblock title %}
{% block auth_content %}
  <!-- Logo and Header Section with better spacing -->
  <div class="mx-auto text-center space-y-4">
    <img class="h-34 w-auto"
         src="{% static 'images/logo.png' %}"
         alt="WMS Logo" />
  </div>
  <!-- Form Container -->
  <div class="bg-theme-bg-primary py-8 px-10 shadow-2xl rounded-xl">
    <!-- Form Level Errors with better visibility -->
    {% if form.non_field_errors %}
      <div class="mb-6 rounded-md bg-theme-status-error/10 p-4">
        <div class="flex items-start">
          <div class="flex-shrink-0 mt-0.5">
            <svg class="h-5 w-5 text-theme-status-error"
                 viewBox="0 0 20 20"
                 fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-theme-status-error">
              {% for error in form.non_field_errors %}{{ error }}{% endfor %}
            </h3>
          </div>
        </div>
      </div>
    {% endif %}
    <!-- Login Form -->
    <form class="space-y-6" method="post" action="{% url 'account_login' %}">
      {% csrf_token %}
      {% if redirect_field_value %}
        <input type="hidden"
               name="{{ redirect_field_name }}"
               value="{{ redirect_field_value }}" />
      {% endif %}
      <!-- Username/Email Field -->
      <div class="space-y-1">
        {# Reduced space-y to 1 for tighter spacing #}
        <label for="{{ form.login.id_for_label }}"
               class="block text-sm font-medium text-theme-text-primary {% if form.login.errors %}text-theme-status-error{% endif %}">
          {% trans "Username or Email" %}
        </label>
        <div class="relative rounded-md shadow-sm">
          <input type="text"
                 name="{{ form.login.html_name }}"
                 id="{{ form.login.id_for_label }}"
                 {% if form.login.value %}value="{{ form.login.value }}"{% endif %}
                 class="appearance-none block w-full px-3 py-2 bg-theme-input hover:bg-theme-input-hover border {% if form.login.errors %}border-theme-status-error{% else %}border-theme-border-primary{% endif %} rounded-md placeholder-theme-text/40 text-theme-text-primary text-sm focus:outline-none {% if form.password1.errors %} focus:border-status-error {% else %}focus:border-theme-border-primary{% endif %} transition-colors duration-150"
                 required
                 autocomplete="username" />
          {% if form.login.errors %}
            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
              <svg class="h-5 w-5 text-theme-status-error"
                   fill="currentColor"
                   viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
              </svg>
            </div>
          {% endif %}
        </div>
        {% if form.login.errors %}
          <p class="mt-1 text-sm text-theme-status-error">{{ form.login.errors|striptags }}</p>
        {% endif %}
      </div>
      <!-- Password Field with better touch targets -->
      <div class="space-y-1">
        {# Reduced space-y to 1 for tighter spacing #}
        <label for="{{ form.password.id_for_label }}"
               class="block text-sm font-medium text-theme-text-primary {% if form.password.errors %}text-theme-status-error{% endif %}">
          {% trans "Password" %}
        </label>
        <div class="relative rounded-md shadow-sm">
          <input type="password"
                 name="{{ form.password.html_name }}"
                 id="{{ form.password.id_for_label }}"
                 class="appearance-none block w-full px-3 py-2 bg-theme-input hover:bg-theme-input-hover border {% if form.password.errors %}border-theme-status-error{% else %}border-theme-border-primary{% endif %} rounded-md placeholder-theme-secondary/40 text-theme-text-primary text-sm focus:outline-none {% if form.password.errors %} focus:border-status-error {% else %}focus:border-theme-border-primary{% endif %} transition-colors duration-150"
                 required
                 autocomplete="current-password" />
          {% if form.password.errors %}
            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
              <svg class="h-5 w-5 text-theme-status-error"
                   fill="currentColor"
                   viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
              </svg>
            </div>
          {% endif %}
        </div>
        {% if form.password.errors %}
          <p class="mt-1 text-sm text-theme-status-error">{{ form.password.errors|striptags }}</p>
        {% endif %}
      </div>
      <!-- Remember Me & Forgot Password with improved mobile layout -->
      <div class="flex flex-row items-center justify-between space-y-0">
        <div class="flex items-center">
          <input id="remember"
                 name="remember"
                 type="checkbox"
                 class="h-4 w-4 text-theme-primary focus:ring-theme-primary border-theme-input-border-primary rounded" />
          <label for="remember" class="ml-2 block text-sm text-theme-text-secondary">{% trans "Remember me" %}</label>
        </div>
        {% include "_components/link.html" with link="account_reset_password" text='Forgot password?' %}
      </div>
      <!-- Submit Button -->
      {% include "_components/button_submit.html" with text='Sign In' %}
    </form>
  </div>
{% endblock auth_content %}

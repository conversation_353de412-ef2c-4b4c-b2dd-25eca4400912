{% extends "base.html" %}

{% load i18n %}

{% block title %}
  {% trans "Confirm Email" %} - WMS
{% endblock title %}
{% block auth_content %}
  <div class="min-h-screen flex flex-col justify-center py-12 bg-theme-sidebar-primary sm:px-6 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
      <!-- Header Section -->
      <div class="text-center">
        <h2 class="mt-6 text-3xl font-extrabold text-theme-text-primary">{% trans "Confirm Email" %}</h2>
        <p class="mt-2 text-sm text-theme-text-primary/60">
          {% trans "Please confirm your email address to complete the registration" %}
        </p>
      </div>
    </div>
    <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
      <div class="bg-theme-background py-8 px-4 shadow-2xl rounded-lg sm:px-10">
        {% if confirmation %}
          <form method="post"
                action="{% url 'account_confirm_email' confirmation.key %}">
            {% csrf_token %}
            <!-- Form Level Messages -->
            {% if form.non_field_errors %}
              <div class="mb-4 rounded-md bg-theme-status-error/10 p-4">
                <div class="flex">
                  <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-theme-status-error"
                         viewBox="0 0 20 20"
                         fill="currentColor">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                    </svg>
                  </div>
                  <div class="ml-3">
                    <h3 class="text-sm font-medium text-theme-status-error">
                      {% for error in form.non_field_errors %}{{ error }}{% endfor %}
                    </h3>
                  </div>
                </div>
              </div>
            {% endif %}
            <!-- Confirmation Message -->
            <div class="rounded-md bg-theme-status-success/10 p-4 mb-6">
              <div class="flex">
                <div class="flex-shrink-0">
                  <svg class="h-5 w-5 text-theme-status-success"
                       viewBox="0 0 20 20"
                       fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                  </svg>
                </div>
                <div class="ml-3">
                  <p class="text-sm font-medium text-theme-status-success">
                    {% trans "Please confirm that" %} <span class="font-semibold">{{ confirmation.email_address.email }}</span> {% trans "is your email address." %}
                  </p>
                </div>
              </div>
            </div>
            <!-- Submit Button -->
            {% include "_components/button_submit.html" with text="Confirm Email" %}
          </form>
        {% else %}
          <!-- Invalid Link Message -->
          <div class="rounded-md bg-theme-status-error/10 p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-theme-status-error"
                     viewBox="0 0 20 20"
                     fill="currentColor">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-theme-status-error">
                  {% trans "This confirmation link is invalid or has expired." %}
                </h3>
              </div>
            </div>
          </div>
        {% endif %}
      </div>
    </div>
  </div>
{% endblock auth_content %}

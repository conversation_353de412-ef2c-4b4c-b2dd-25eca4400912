{% extends "base.html" %}

{% load i18n %}
{% load account socialaccount %}

{% block title %}
  {% trans "Sign Up Disabled" %} - WMS
{% endblock title %}
{% block auth_content %}
  <!-- Header -->
  <div class="text-center space-y-3 sm:space-y-4">
    <h2 class="mt-6 text-3xl font-extrabold text-theme-text-primary">{% trans "Sign Up Disabled" %}</h2>
    <p class="mt-2 text-sm text-theme-text-primary/60">{% trans "New user registration is currently disabled" %}</p>
  </div>
  <div class="bg-theme-background py-8 px-4 shadow-2xl rounded-lg sm:px-10">
    <!-- Notice Message -->
    <div class="rounded-md bg-theme-status-info/10 p-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-theme-status-info"
               viewBox="0 0 20 20"
               fill="currentColor">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <p class="text-sm text-theme-text-primary">
            {% trans "Sign up is currently disabled. Please contact the administrator for access to the system." %}
          </p>
        </div>
      </div>
    </div>
    <!-- Contact Info -->
    <div class="mt-6 text-center">
      <p class="text-sm text-theme-text-primary/60">
        {% trans "For assistance, please contact:" %}
        <a href="mailto:<EMAIL>"
           class="text-theme-link hover:text-theme-link/80"><EMAIL></a>
      </p>
    </div>
    <!-- Back to Login Link -->
    <div class="mt-6">{% include "_components/link.html" with link="account_login" text="Back to login" %}</div>
  </div>
{% endblock auth_content %}

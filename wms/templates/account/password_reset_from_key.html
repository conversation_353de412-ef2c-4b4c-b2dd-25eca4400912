{% extends "base.html" %}

{% load i18n %}

{% block title %}
  {% trans "Set New Password" %} - WMS
{% endblock title %}
{% block auth_content %}
  <div class="text-center space-y-3 sm:space-y-4">
    <h2 class="mt-6 text-3xl font-extrabold text-theme-text-primary">{% trans "Set New Password" %}</h2>
    <p class="mt-2 text-sm text-theme-text-primary/60">
      {% if token_fail %}
        {% url 'account_reset_password' as passwd_reset_url %}
        {% blocktrans %}The password reset link was invalid. Please <a href="{{ passwd_reset_url }}" class="text-theme-link hover:text-theme-link/80">request a new password reset</a>.
        {% endblocktrans %}
      {% else %}
        {% trans "Please enter your new password." %}
      {% endif %}
    </p>
  </div>
  <div class="bg-theme-background py-8 px-10 shadow-2xl rounded-lg">
    {% if not token_fail %}
      <form method="post" action="{{ action_url }}" class="space-y-6">
        {% csrf_token %}
        <!-- Password Fields -->
        <div class="space-y-2">
          <label for="{{ form.password1.id_for_label }}"
                 class="block font-medium text-theme-text-primary {% if form.password1.errors %}text-theme-status-error{% endif %}">
            {% trans "New Password" %}
          </label>
          <div class="mt-1 relative rounded-md shadow-sm">
            <input type="password"
                   name="{{ form.password1.html_name }}"
                   id="{{ form.password1.id_for_label }}"
                   class="appearance-none block w-full px-3 py-2 border {% if form.password1.errors %}border-theme-status-error{% else %}border-theme-border-primary{% endif %} rounded-md {# New Password Input Style #} placeholder-theme-text/40 text-theme-text-primary focus:outline-none {% if form.password1.errors %} focus:border-status-error {% else %}focus:border-theme-border-primary{% endif %} transition-colors duration-150"
                   required />
            {% if form.password1.errors %}
              <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                <svg class="h-5 w-5 text-theme-status-error"
                     fill="currentColor"
                     viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
              </div>
            {% endif %}
          </div>
          {% if form.password1.errors %}
            <p class="mt-2 text-sm text-theme-status-error">{{ form.password1.errors|striptags }}</p>
          {% endif %}
        </div>
        <div class="space-y-2">
          <label for="{{ form.password2.id_for_label }}"
                 class="block font-medium text-theme-text-primary {% if form.password2.errors %}text-theme-status-error{% endif %}">
            {% trans "Confirm New Password" %}
          </label>
          <div class="mt-1 relative rounded-md shadow-sm">
            <input type="password"
                   name="{{ form.password2.html_name }}"
                   id="{{ form.password2.id_for_label }}"
                   class="appearance-none block w-full px-3 py-2 border {% if form.password2.errors %}border-theme-status-error{% else %}border-theme-border-primary{% endif %} rounded-md {# Confirm Password Input Style #} placeholder-theme-text/40 text-theme-text-primary focus:outline-none {% if form.password1.errors %} focus:border-status-error {% else %}focus:border-theme-border-primary{% endif %} transition-colors duration-150"
                   required />
            {% if form.password2.errors %}
              <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                <svg class="h-5 w-5 text-theme-status-error"
                     fill="currentColor"
                     viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
              </div>
            {% endif %}
          </div>
          {% if form.password2.errors %}
            <p class="mt-2 text-sm text-theme-status-error">{{ form.password2.errors|striptags }}</p>
          {% endif %}
        </div>
        <!-- Submit Button -->
        {% include "_components/button_submit.html" with text="Change Password" %}
      </form>
    {% endif %}
    <!-- Back to Log in -->
    <div class="{% if not token_fail %}mt-6{% endif %}">
      {% include "_components/link.html" with link="account_login" text="Back to login" %}
    </div>
  </div>
{% endblock auth_content %}

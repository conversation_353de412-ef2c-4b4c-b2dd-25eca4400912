{% extends "account/base_manage.html" %}

{% load crispy_forms_tags %}
{% load i18n %}

{% block manage_content %}
  <section class="mx-auto mb-6 pt-6 max-w-4xl [&:not(:first-child)]:border-t-1 border-theme-input-border-primary">
    <div class="py-2">
      <h3 class="font-bold text-2xl tracking-normal text-theme-text-primary mb-2">{% trans "Email Addresses" %}</h3>
      <p class="text-sm text-theme-text-secondary mb-2">
        {% trans 'The following email addresses are associated with your account:' %}
      </p>
    </div>
    {# Email List Section #}
    <div class="py-2">
      <section class="border-1 border-theme-border-secondary rounded-sm">
        <div class="">
          <div class="mx-0 my-0 overflow-x-auto">
            <table class="min-w-full divide-y divide-theme-border-secondary">
              {# Email Table #}
              <thead class="hidden md:table-header-group bg-gray-100">
                <tr>
                  <th scope="col"
                      class="px-4 py-4 text-left text-xs font-semibold text-theme-text-primary uppercase tracking-wider w-2/5">
                    {% trans "Email" %}
                  </th>
                  <th scope="col"
                      class="px-4 py-4 text-left text-xs font-semibold text-theme-text-primary uppercase tracking-wider w-2/5">
                    {% trans "Status" %}
                  </th>
                  <th scope="col"
                      class="px-6 py-4 text-right text-xs font-semibold text-theme-text-primary uppercase tracking-wider">
                    {% trans "Actions" %}
                  </th>
                </tr>
              </thead>
              <tbody class="divide-y divide-theme-border-secondary">
                {% for emailaddress in user.emailaddress_set.all %}
                  <tr class="hover:bg-theme-hover">
                    {# Email Row #}
                    <td class="px-4 py-4 whitespace-nowrap text-sm">
                      <span class="text-theme-text-primary">{{ emailaddress.email }}</span>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap">
                      <div class="flex items-center">
                        <span class="px-2 py-1 rounded-xs inline-flex text-xs leading-5 font-medium {% if emailaddress.verified %} bg-green-100 text-green-800 {% else %} bg-yellow-100 text-yellow-800 {% endif %}">
                          {% if emailaddress.verified %}
                            {% trans "Verified" %}
                          {% else %}
                            {% trans "Unverified" %}
                          {% endif %}
                        </span>
                        {% if emailaddress.primary %}
                          <span class="ml-2 px-2 py-1 inline-flex text-xs leading-5 font-medium rounded-xs bg-blue-100 text-blue-800">
                            {% trans "Primary" %}
                          </span>
                        {% endif %}
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm">
                      <div class="flex items-center justify-end space-x-2">
                        {% if not emailaddress.primary %}
                          <form method="post" action="{% url 'account_email' %}" class="inline">
                            {# Added method="post" #}
                            {% csrf_token %}
                            <input type="hidden" name="email" value="{{ emailaddress.email }}" />
                            <input type="hidden" name="action" value="primary" />
                            <button type="submit"
                              name="action_primary"
                              value="{{ emailaddress.email }}"
                              class="px-3 py-1 text-sm bg-theme-action-update text-white rounded hover:bg-theme-action-update-hover transition-colors duration-200 flex items-center" {# Added button classes and flex #}
                              title="{% trans 'Make Primary' %}">
                              <svg xmlns="http://www.w3.org/2000/svg"
                                   viewBox="0 0 24 24"
                                   fill="none"
                                   stroke="currentColor"
                                   class="hidden w-5 h-5 text-blue-600 hover:text-blue-700 transition-colors duration-200 md:inline-block mr-1">
                                {# Hide svg on smaller screens and show on medium and larger and add margin right #}
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                              </svg>
                              <span class="whitespace-nowrap">{% trans "Make Primary" %}</span> {# Hide text on smaller screens and show on medium and larger #}
                            </button>
                          </form>
                        {% endif %}
                        {% if not emailaddress.verified %}
                          <form method="post" action="{% url 'account_email' %}" class="inline">
                            {# Added method="post" #}
                            {% csrf_token %}
                            <input type="hidden" name="email" value="{{ emailaddress.email }}" />
                            <input type="hidden" name="action" value="resend" />
                            <button type="submit"
                              name="action_send"
                              value="{{ emailaddress.email }}"
                              class="px-3 py-1 text-sm bg-theme-action-create text-white rounded hover:bg-theme-action-create-hover transition-colors duration-200 flex items-center" {# Added button classes and flex #}
                              title="{% trans 'Re-send Verification' %}">
                              <svg xmlns="http://www.w3.org/2000/svg"
                                   viewBox="0 0 24 24"
                                   fill="none"
                                   stroke="currentColor"
                                   class="hidden w-5 h-5 text-emerald-600 hover:text-emerald-700 transition-colors duration-200 md:inline-block mr-1">
                                {# Hide svg on smaller screens and show on medium and larger and add margin right #}
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                              </svg>
                              <span class="whitespace-nowrap">{% trans "Re-send Verification" %}</span> {# Hide text on smaller screens and show on medium and larger #}
                            </button>
                          </form>
                        {% endif %}
                        {% if not emailaddress.primary %}
                          <form method="post" action="{% url 'account_email' %}" class="inline">
                            {# Added method="post" #}
                            {% csrf_token %}
                            <input type="hidden" name="email" value="{{ emailaddress.email }}" />
                            <input type="hidden" name="action" value="remove" />
                            <button type="submit"
                              name="action_remove"
                              value="{{ emailaddress.email }}"
                              class="px-3 py-1 text-sm bg-theme-action-delete text-white rounded hover:bg-theme-action-delete-hover transition-colors duration-200 flex items-center" {# Added button classes and flex #}
                              title="{% trans 'Remove' %}"
                              onclick="return confirm('{% trans 'Do you really want to remove this email address?' %}')">
                              <svg xmlns="http://www.w3.org/2000/svg"
                                   viewBox="0 0 24 24"
                                   fill="none"
                                   stroke="currentColor"
                                   class="hidden w-5 h-5 text-red-600 hover:text-red-700 transition-colors duration-200 md:inline-block mr-1">
                                {# Hide svg on smaller screens and show on medium and larger and add margin right #}
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                              </svg>
                              <span class="whitespace-nowrap">{% trans "Remove" %}</span> {# Hide text on smaller screens and show on medium and larger #}
                            </button>
                          </form>
                        {% endif %}
                      </div>
                    </td>
                  </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </section>
    </div>
  </section>
  {# Add Email Section - Unchanged, but added method="post" #}
  <section class="mx-auto mb-6 pt-6 max-w-4xl [&:not(:first-child)]:border-t-1 border-theme-border-secondary">
    <div class="py-2">
      <h3 class="font-bold text-2xl tracking-normal text-theme-text-primary mb-2">{% trans "Add Email" %}</h3>
    </div>
    <form method="post" action="{% url 'account_email' %}">
      {# Added method="post" #}
      {% csrf_token %}
      <div class="max-w-xl mb-4">
        <label for="{{ form.email.id_for_label }}"
               class="mb-2 block font-semibold text-theme-text-primary {% if form.email.errors %}text-theme-status-error{% endif %}">
          {% trans "Email Address" %}
        </label>
        <div class="relative rounded-md shadow-sm">
          <input type="email"
                 name="{{ form.email.html_name }}"
                 id="{{ form.email.id_for_label }}"
                 {% if form.email.value %}value="{{ form.email.value }}"{% endif %}
                 class="appearance-none block w-full px-3 py-2 border {% if form.email.errors %}border-theme-status-error{% else %}border-theme-border-primary{% endif %} rounded-sm placeholder-theme-text/40 text-theme-text-primary focus:outline-none {% if form.password1.errors %}focus:border-status-error{% else %}focus:border-theme-border-primary{% endif %} transition-colors duration-150"
                 required
                 autocomplete="email" />
          {% if form.email.errors %}
            <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
              <svg class="h-5 w-5 text-theme-status-error"
                   fill="currentColor"
                   viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
              </svg>
            </div>
          {% endif %}
        </div>
        {% if form.email.errors %}
          <p class="mt-1 text-sm text-theme-status-error">{{ form.email.errors|striptags }}</p>
        {% endif %}
      </div>
      <div class="my-3">
        <button type="submit"
                name="action_add"
                class="px-4 py-1.5 bg-theme-primary text-white rounded-sm hover:bg-theme-primary/80 focus:outline-none focus:ring-2 focus:ring-theme-primary focus:ring-offset-2">
          {% trans "Add Email" %}
        </button>
      </div>
    </form>
  </section>
{% endblock %}

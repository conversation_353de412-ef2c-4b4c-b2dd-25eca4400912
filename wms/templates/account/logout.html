{% extends "base.html" %}

{% load i18n %}

{% block title %}
  {% trans "Sign Out" %} - WMS
{% endblock %}
{% block content %}
  <!-- Main Container with improved mobile spacing -->
  <div class="col-start-2 col-span-4 space-y-4">
    <!-- Header Section -->
    <div class="text-center space-y-3 sm:space-y-4">
      {#        <img class="mx-auto h-12 sm:h-14 w-auto" src="" alt="WMS Logo" />#}
      <h2 class="text-2xl sm:text-3xl lg:text-4xl font-extrabold text-theme-text-primary tracking-tight">
        {% trans "Sign Out" %}
      </h2>
      <p class="text-base sm:text-lg text-theme-text-primary/60">{% trans "Are you sure you want to sign out?" %}</p>
    </div>
    <!-- Logout Confirmation Container -->
    <div class="bg-theme-background py-8 px-6 px-10 shadow-2xl rounded-xl border border-theme-border-primary">
      <form method="post" action="{% url 'account_logout' %}" class="space-y-6">
        {% csrf_token %}
        {% if redirect_field_value %}
          <input type="hidden"
                 name="{{ redirect_field_name }}"
                 value="{{ redirect_field_value }}" />
        {% endif %}
        <!-- Warning Message -->
        <div class="rounded-lg bg-theme-status-warning/10 p-4">
          <div class="flex items-start">
            <div class="flex-shrink-0 mt-0.5">
              <svg class="h-5 w-5 text-theme-status-warning"
                   viewBox="0 0 20 20"
                   fill="currentColor">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm text-theme-warning">
                {% trans "You will be signed out of your account and will need to sign in again to access your data." %}
              </p>
            </div>
          </div>
        </div>
        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-4">
          <!-- Cancel Button -->
          {% include "_components/button_redirect.html" with link="home" text="Cancel" %}
          <!-- Submit Button -->
          {% include "_components/button_submit.html" with text="Sign Out" %}
        </div>
      </form>
    </div>
  </div>
{% endblock %}

{% extends "base.html" %}

{% load i18n %}

{% block title %}
  {% trans "Reset Password" %} - WMS
{% endblock title %}
{% block auth_content %}
  <!-- Header Section -->
  <div class="text-center space-y-3 sm:space-y-4">
    <h2 class="mt-6 text-3xl font-extrabold text-theme-text-primary">{% trans "Reset Password" %}</h2>
    <p class="mt-2 text-sm text-theme-text-primary/60">
      {% trans "For security purposes, enter your email address below. If an account exists, we'll send password reset instructions to this email." %}
    </p>
    <p class="mt-1 text-xs text-theme-text-primary/50">
      {% trans "For your protection, the reset link will expire after 24 hours." %}
    </p>
  </div>
  <div class="bg-theme-background py-8 px-10 shadow-2xl rounded-lg">
    <!-- Form Level Errors -->
    {% if form.non_field_errors %}
      <div class="mb-4 rounded-md bg-theme-status-error/10 p-4">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-theme-status-error"
                 viewBox="0 0 20 20"
                 fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-theme-status-error">
              {% for error in form.non_field_errors %}{{ error }}{% endfor %}
            </h3>
          </div>
        </div>
      </div>
    {% endif %}
    <form method="post"
          action="{% url 'account_reset_password' %}"
          class="space-y-6">
      {% csrf_token %}
      <!-- Email Field -->
      <div class="space-y-2">
        <label for="{{ form.email.id_for_label }}"
               class="block font-medium text-theme-text-primary {% if form.email.errors %}text-theme-status-error{% endif %}">
          {% trans "Email Address" %}
        </label>
        <div class="mt-1 relative rounded-md shadow-sm">
          <input type="email"
                 name="{{ form.email.html_name }}"
                 id="{{ form.email.id_for_label }}"
                 {% if form.email.value %}value="{{ form.email.value }}"{% endif %}
                 class="appearance-none block w-full px-3 py-2 border {% if form.email.errors %}border-theme-status-error{% else %}border-gray-400{% endif %} rounded-md placeholder-theme-text/40 text-theme-text-primary focus:outline-none {% if form.password1.errors %} focus:border-status-error {% else %}focus:border-gray-600{% endif %} transition-colors duration-150"
                 required
                 autocomplete="email" />
          {% if form.email.errors %}
            <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
              <svg class="h-5 w-5 text-theme-status-error"
                   fill="currentColor"
                   viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
              </svg>
            </div>
          {% endif %}
        </div>
        {% if form.email.errors %}
          <p class="mt-2 text-sm text-theme-status-error">{{ form.email.errors|striptags }}</p>
        {% endif %}
      </div>
      <!-- Submit Button -->
      {% include "_components/button_submit.html" with text="Send Reset Link" %}
      <!-- Back to Login -->
      {% include "_components/link.html" with link="account_login" text="Back to login" %}
      <!-- Help Text -->
      <div class="mt-4 text-xs text-theme-text-primary/50 text-center">
        <p>
          {% trans "If you don't receive an email, please check your spam folder or verify the email address is correct." %}
        </p>
      </div>
    </form>
  </div>
{% endblock auth_content %}

{% extends "account/base_manage.html" %}

{% load i18n %}

{% block title %}
  {% trans "Email Address Removed" %} - WMS
{% endblock title %}
{% block manage_title %}
  {% trans "Email Address Removed" %}
{% endblock manage_title %}
{% block manage_content %}
  <div class="bg-theme-background py-8 px-4 shadow-2xl rounded-lg sm:px-10">
    <!-- Success Message -->
    <div class="rounded-md bg-theme-status-success/10 p-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-theme-status-success"
               viewBox="0 0 20 20"
               fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <p class="text-sm font-medium text-theme-status-success">
            {% trans "Email address has been removed successfully." %}
          </p>
        </div>
      </div>
    </div>
    <!-- Warning for Primary Email -->
    {% if is_primary %}
      <div class="mt-4 rounded-md bg-theme-status-warning/10 p-4">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-theme-status-warning"
                 viewBox="0 0 20 20"
                 fill="currentColor">
              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <p class="text-sm text-theme-status-warning">
              {% trans "You have removed your primary email address. Please set another email as primary." %}
            </p>
          </div>
        </div>
      </div>
    {% endif %}
    <!-- Back to Email Management -->
    <div class="mt-6">{% include "_components/link.html" with link="account_email" text="Back to email settings" %}</div>
  </div>
{% endblock manage_content %}

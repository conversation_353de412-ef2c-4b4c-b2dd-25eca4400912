{% load i18n %}

{% if object %}
  <section class="mx-auto border-1 [&:not(:first-child)]:border-t-0 border-gray-200">
    <div class="py-1.5 px-3 border-b-1 bg-theme-table-header border-gray-200 font-semibold uppercase tracking-wider flex justify-between items-center">
      <span>{% trans "Basic Information" %}</span>
      <div class="flex gap-4">
        <a href="{% url 'consignees:update' consignee.pk %}"
           class="inline-flex items-center px-3 py-1 text-sm bg-theme-action-update text-white rounded hover:bg-theme-action-update-hover transition-colors">
          {% trans "Update" %}
        </a>
        <a href="{% url 'consignees:update' consignee.pk %}"
           class="inline-flex items-center px-3 py-1 text-sm bg-theme-action-delete text-white rounded hover:bg-theme-action-delete-hover transition-colors">
          {% trans "Delete" %}
        </a>
      </div>
    </div>
    <div class="bg-white rounded-none overflow-hidden">
      <!-- Main Information Card -->
      <div class="p-0">
        <div class="space-y-6">
          <!-- Item Information Section -->
          <div class="bg-theme-bg-secondary/10 rounded-none">
            <div class="grid grid-cols-1">
              <!-- Left Content -->
              <div class="grid grid-cols-1 divide-y divide-gray-200 ">
                {% include "_components/detail_field.html" with label="Code" value=consignee.code %}
                {% include "_components/detail_field.html" with label="Company Name" value=consignee.company_name %}
                {% include "_components/detail_field.html" with label="Display Name" value=consignee.display_name %}
                {% include "_components/detail_field.html" with label="Consignor" value=consignee.consignor %}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
  <section class="mx-auto border-1 [&:not(:first-child)]:border-t-0 border-gray-200">
    <!-- Two column grid for the entire content -->
    <div class="grid grid-cols-1 md:grid-cols-2">
      <!-- Left Column -->
      <div class="border-r-0 md:border-r border-theme-border-secondary">
        <!-- Left Header -->
        <div class="py-1.5 px-3 border-b-1 bg-theme-table-header border-gray-300 font-semibold uppercase tracking-wider flex justify-between items-center">
          {% trans "Address" %}
        </div>
        <!-- Left Content -->
        <div class="bg-white rounded-none overflow-hidden">
          <div class="p-0">
            <div class="space-y-6">
              <div class="bg-theme-bg-secondary/10 rounded-none">
                <div class="">
                  {% include "_components/detail_field.html" with label="Billing Address" value=consignee.billing_address.get_address %}
                  {% include "_components/detail_field.html" with label="Shipping Address" value=consignee.billing_address.get_address %}
                  <!-- Add more left side items as needed -->
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- Right Column -->
      <div>
        <!-- Right Header -->
        <div class="py-1.5 px-3 border-b-1 bg-theme-table-header border-gray-300 font-semibold uppercase tracking-wider flex justify-between items-center">
          <span>{% trans "Primary Contact" %}</span>
        </div>
        <!-- Right Content -->
        <div class="bg-white rounded-none overflow-hidden">
          <div class="p-0">
            <div class="space-y-6">
              <div class="bg-theme-bg-secondary/10 rounded-none">
                <div class="">
                  {% include "_components/detail_field.html" with label="Name" value=consignee.get_primary_contact_formal_full_name %}
                  {% include "_components/detail_field.html" with label="Email" value=consignee.primary_contact.email %}
                  {% include "_components/detail_field.html" with label="Phone" value=consignee.primary_contact.phone %}
                  {% include "_components/detail_field.html" with label="Mobile" value=consignee.primary_contact.mobile %}
                  {% include "_components/detail_field.html" with label="Designation" value=consignee.primary_contact.designation %}
                  {% include "_components/detail_field.html" with label="Department" value=consignee.primary_contact.department %}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
  <section class="mx-auto border-1 [&:not(:first-child)]:border-t-0 border-gray-200">
    <div class="py-1.5 px-3 border-b-1 bg-theme-table-header border-gray-300 font-semibold uppercase tracking-wider flex justify-between items-center">
      <span>{% trans "Item Stock" %}</span>
    </div>
    <div class="bg-white rounded-none overflow-hidden">
      <!-- Main Information Card -->
      <div class="p-0">
        <div class="space-y-6">
          <!-- Item Information Section -->
          <div class="bg-theme-bg-secondary/10 rounded-none">
            <div class="grid grid-cols-1 gap-4">
              <!-- Left Content -->
              <div class="">
                {% include "_components/detail_field.html" with label="Warehouse" value="Need to show input and datatable here" translate_label=false %}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
{% endif %}

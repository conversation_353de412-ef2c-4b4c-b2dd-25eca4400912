{% load i18n %}

{% if object %}
  <!-- Tab Navigation -->
  <div class="mb-2">
    <!-- Mobile Tab Selector -->
    <div class="sm:hidden">
      <div class="relative">
        <select class="w-full rounded-lg border-theme-border-secondary py-2 pl-3 pr-10 text-base focus:border-theme-primary focus:outline-none focus:ring-theme-primary"
                hx-get="{% url 'consignees:detail' consignee.pk %}?tab=detail' %}"
                hx-target="#tab-content"
                hx-trigger="change"
                hx-swap="innerHTML"
                hx-push-url="false"
                _="on change set window.location.hash to me.value">
          <option value="#detail" selected>Detail</option>
          <option value="#event">Event</option>
        </select>
        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2">
          <svg class="h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
          </svg>
        </div>
      </div>
    </div>
    <!-- Desktop Tabs -->
    <div class="hidden sm:block">
      <div class="mb-4">
        <nav class="-mb-px flex space-x-4"
             aria-label="Tabs"
             x-data="{ activeTab: window.location.hash || '#detail' }">
          <a href="#detail"
             class="group inline-flex items-center rounded-md px-6 py-2 font-medium transition-colors border-1"
             :class="activeTab === '#detail' ? 'bg-theme-primary/30 text-indigo-700' : 'bg-theme-bg-primary text-theme-text-secondary hover:border-gray-299 hover:text-theme-text-primary'"
             hx-get="{% url 'consignees:detail' consignee.pk %}"
             hx-target="#tab-content"
             hx-indicator="#tab-loading"
             hx-trigger="click, load[window.location.hash === '' || window.location.hash === '#detail']"
             hx-swap="innerHTML"
             hx-push-url="false"
             @click="activeTab = '#detail'">
            <span>Detail</span>
          </a>
          <a href="#event"
             class="group inline-flex items-center rounded-md px-4 py-1 text-sm font-medium transition-colors border-1"
             :class="activeTab === '#event' ? 'bg-theme-primary/50 text-indigo-700' : 'bg-theme-bg-primary text-theme-text-secondary hover:border-gray-299 hover:text-theme-text-primary'"
             hx-get="{% url 'consignees:event' consignee.pk %}"
             hx-target="#tab-content"
             hx-indicator="#tab-loading"
             hx-trigger="click, load[window.location.hash === '#event']"
             hx-swap="innerHTML"
             hx-push-url="false"
             @click="activeTab = '#event'">
            <span>Event</span>
          </a>
        </nav>
      </div>
    </div>
  </div>
  <!-- Progress Bar -->
  <div id="tab-loading"
       class="group htmx-indicator sticky h-0.5 bg-gray-200 top-0 left-0 z-20">
    <div class="h-full bg-blue-600 origin-left group-[.htmx-request]:animate-progress"></div>
  </div>
  <div id="tab-content"
       class="bg-white rounded-none shadow-sm overflow-hidden mb-6">
    <!-- Tab Content -->
  </div>
{% endif %}

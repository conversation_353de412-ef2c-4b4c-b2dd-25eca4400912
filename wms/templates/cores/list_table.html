{% extends 'base.html' %}

{% load crispy_forms_tags %}
{% load render_table from django_tables2 %}

{% block content %}
  <section class="lg:col-span-6 mb-6 pt-2">
    {% include 'tables/title_section.html' %}
    <!-- Filters Section -->
    {% include "tables/filter.html" %}
    {% if customize_export_excel_url %}
      <div class="flex justify-end gap-4">
      {% comment %}
      # NOTE: tyler, please inject the request url's PARAM here.
      <a href="{% url 'reports:export_grn_report_xlsx' %}?date_range_after=2025-03-19&date_range_before=&status=New&status=Partially+Received&status=Completed&keyword_search=&consignor=5"
      {% endcomment %}
      <a href="#"
         target="_blank"
         class="inline-flex items-center justify-center px-2 py-1 bg-theme-status-success text-white rounded hover:bg-theme-status-success transition">
        <svg xmlns="http://www.w3.org/2000/svg"
             fill="none"
             viewBox="0 0 24 24"
             stroke-width="1.5"
             stroke="currentColor"
             class="h-5 w-5 mr-2"
        >
          <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m.75 12 3 3m0 0 3-3m-3 3v-6m-1.5-9H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z" />
        </svg>
        Export Customized Excel
      </a>
    </div>
    {% endif %}
    {% include 'tables/control_section.html' %}
    <div id="table-content">{% render_table table %}</div>
  </section>
{% endblock content %}

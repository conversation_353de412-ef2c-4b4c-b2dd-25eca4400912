{% spaceless %}
  <div class="flex flex-col">
    <!-- Labels row -->
    {#  <div class="flex flex-row space-x-4 mb-1">#}
    {#    <label class="text-xs font-semibold tracking-wider text-theme-text-primary w-1/2">{{ widget.attrs.start_label|default:"From" }}</label>#}
    {#    <label class="text-xs font-semibold tracking-wider text-theme-text-primary w-1/2">{{ widget.attrs.end_label|default:"To" }}</label>#}
    {#  </div>#}
    <!-- Inputs row -->
    <div class="flex flex-row items-center w-full xl:w-[80%] justify-between">
      <!-- Start date input -->
      <div class="relative-">
        <div class="relative inline-block w-full">
          <input type="{{ widget.attrs.type|default:'text' }}"
                 name="{{ widget.name }}_after"
                 id="{{ widget.attrs.id }}_0"
                 {% if widget.value.0 != None and widget.value.0 != "[" %}value="{{ widget.value.0|stringformat:'s' }}"{% endif %}
                 {% include "django/forms/widgets/attrs.html" with widget=widget.subwidgets.0 %}
                 class="{{ widget.attrs.class }} pr-10 w-full" />
          <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
            <svg class="w-4 h-4 text-theme-text-secondary"
                 fill="currentColor"
                 viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
            </svg>
          </div>
        </div>
        {% if widget.subwidgets.0.errors %}
          <p class="mt-1 text-sm text-theme-status-error">{{ widget.subwidgets.0.errors.0 }}</p>
        {% endif %}
      </div>
      <!-- Dash separator -->
      <div class="flex-shrink-0 text-center">
        <span class="text-lg font-medium text-theme-text-secondary">-</span>
      </div>
      <!-- End date input -->
      <div class="relative w-[50%]">
        <div class="relative inline-block w-full">
          <input type="{{ widget.attrs.type|default:'text' }}"
                 name="{{ widget.name }}_before"
                 id="{{ widget.attrs.id }}_1"
                 {% if widget.value.1 != None and widget.value.1 != "N" %}value="{{ widget.value.1|stringformat:'s' }}"{% endif %}
                 {% include "django/forms/widgets/attrs.html" with widget=widget.subwidgets.1 %}
                 class="{{ widget.attrs.class }} pr-10 w-full" />
          <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
            <svg class="w-4 h-4 text-theme-text-secondary"
                 fill="currentColor"
                 viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
            </svg>
          </div>
        </div>
        {% if widget.subwidgets.1.errors %}
          <p class="mt-1 text-sm text-theme-status-error">{{ widget.subwidgets.1.errors.0 }}</p>
        {% endif %}
      </div>
    </div>
  </div>
{% endspaceless %}

{% spaceless %}
  <div class="relative" x-data="{ open: false }">
    <div class="relative inline-block" style="width: auto;">
      <input type="{{ widget.type }}"
             name="{{ widget.name }}"
             id="{{ widget.attrs.id }}"
             {% if widget.value != None %}value="{{ widget.value|stringformat:'s' }}"{% endif %}
             {% include "django/forms/widgets/attrs.html" %}
             @click="open = true"
             @click.away="open = false"
             class="{{ widget.attrs.class }} pr-10" />
      <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
        <svg class="w-4 h-4 text-theme-text-secondary"
             fill="currentColor"
             viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
        </svg>
      </div>
    </div>
    {% if widget.attrs.help_text %}
      <p class="mt-1 text-sm text-theme-text-secondary">{{ widget.attrs.help_text }}</p>
    {% endif %}
    {% if widget.errors %}<p class="mt-1 text-sm text-theme-status-error">{{ widget.errors.0 }}</p>{% endif %}
  </div>
{% endspaceless %}

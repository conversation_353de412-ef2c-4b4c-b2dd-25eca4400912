{% load l10n %}

<script>
  // Define the Alpine.js component function
  document.addEventListener('alpine:init', () => {
    Alpine.data('treeCheckboxComponent', () => ({
      expanded: {},
      selectedItems: [],

      init() {
        this.initializeExpanded();
      },

      toggleExpand(id) {
        this.expanded[id] = !this.expanded[id];
        this.updateChildrenVisibility(id);
      },

      updateChildrenVisibility(parentId) {
        const container = this.$el.closest('.tree-checkbox-container');
        const children = container.querySelectorAll(`[data-parent-id="${parentId}"]`);
        children.forEach(child => {
          const container = child.closest('.tree-item');
          if (this.expanded[parentId]) {
            container.classList.remove('hidden');
          } else {
            container.classList.add('hidden');
            if (child.dataset.value) {
              this.expanded[child.dataset.value] = false;
              this.updateChildrenVisibility(child.dataset.value);
            }
          }
        });
      },

      updateSelectedItems() {
        this.selectedItems = [];
        const container = this.$el.closest('.tree-checkbox-container');
        container.querySelectorAll('input[type="checkbox"]:checked').forEach(checkbox => {
          const label = container.querySelector(`label[for="${checkbox.id}"]`);
          if (label) {
            this.selectedItems.push({
              value: checkbox.value,
              label: label.innerText.trim(),
            });
          }
        });
      },

      initializeExpanded() {
        const container = this.$el.closest('.tree-checkbox-container');
        // First, collapse all nodes by default
        container.querySelectorAll('[data-value]').forEach(item => {
          if (item.dataset.value) {
            this.expanded[item.dataset.value] = false;
          }
        });

        // Then, find all checked checkboxes
        const checkedBoxes = container.querySelectorAll('input[type="checkbox"]:checked');

        // For each checked box, expand all its parent nodes
        checkedBoxes.forEach(checkbox => {
          let currentElement = checkbox;
          while (currentElement && currentElement.dataset && currentElement.dataset.parentId) {
            const parentId = currentElement.dataset.parentId;
            if (parentId && parentId !== 'None') {
              this.expanded[parentId] = true;
              // Find the parent element
              currentElement = container.querySelector(`[data-value="${parentId}"]`);
            } else {
              break;
            }
          }
        });

        // Update the visibility based on expanded state
        Object.keys(this.expanded).forEach(id => {
          this.updateChildrenVisibility(id);
        });

        this.updateSelectedItems();
      },

      expandAll() {
        const container = this.$el.closest('.tree-checkbox-container');
        // Set all expandable items to expanded
        container.querySelectorAll('[data-value]').forEach(item => {
          if (item.dataset.value) {
            this.expanded[item.dataset.value] = true;
          }
        });

        // Show all tree items
        container.querySelectorAll('.tree-item').forEach(item => {
          item.classList.remove('hidden');
        });
      },

      collapseAll() {
        const container = this.$el.closest('.tree-checkbox-container');
        // Set all expandable items to collapsed
        container.querySelectorAll('[data-value]').forEach(item => {
          if (item.dataset.value) {
            this.expanded[item.dataset.value] = false;
          }
        });

        // Hide all items with a parent, except top-level nodes
        container.querySelectorAll('[data-parent-id]').forEach(item => {
          // Skip nodes with parent_id of None or empty (top-level nodes)
          if (item.dataset.parentId && item.dataset.parentId !== 'None') {
            const itemContainer = item.closest('.tree-item');
            itemContainer.classList.add('hidden');
          }
        });
      },
    }));
  });
</script>

<div class="px-2 py-1 tree-checkbox-container" x-data="treeCheckboxComponent()" @change="updateSelectedItems()">
  {% for group, options, index in widget.optgroups %}
    {% for option in options %}
      {% with widget_option=option %}
        {% with level=widget_option.attrs.level|default:1 %}
          <div
            class="tree-item flex items-center py-1 {% if level == 2 %}ml-6{% elif level == 3 %}ml-12{% elif level == 4 %}ml-18{% elif level == 5 %}ml-24{% endif %}">
            <div class="flex items-center min-w-0">
              <input type="checkbox"
                     name="{{ widget_option.name }}"
                     value="{{ widget_option.value|unlocalize }}"
                     {% if widget_option.attrs.checked %}checked{% endif %}
                     id="{{ widget_option.attrs.id }}"
                     class="{{ widget_option.attrs.class|default:'h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500' }}"
                     data-parent-id="{{ widget_option.attrs.parent_id }}"
                     data-value="{{ widget_option.value|unlocalize }}"
                     data-level="{{ widget_option.attrs.level }}"
                     {% if widget_option.attrs.disabled %}disabled{% endif %}>

              <label for="{{ widget_option.attrs.id }}"
                     class="ml-2 truncate text-sm {% if widget_option.attrs.is_parent %}font-medium{% endif %}
                                          {% if widget_option.attrs.disabled %}text-gray-500{% else %}text-gray-900{% endif %}">
                {{ widget_option.label }}
              </label>

              {% if not widget_option.attrs.is_leaf %}
                <button type="button"
                        class="ml-1 w-4 h-4 flex items-center justify-center  text-theme-primary transition-transform duration-200"
                        :class="{ 'rotate-90': expanded['{{ widget_option.value|unlocalize }}'] }"
                        @click="toggleExpand('{{ widget_option.value|unlocalize }}')">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                  </svg>
                </button>
              {% endif %}
            </div>
          </div>
        {% endwith %}
      {% endwith %}
    {% endfor %}
  {% endfor %}

  <!-- Expand/Collapse All Buttons -->
  <div class="flex space-x-2 mt-2 mb-2">
    <button type="button"
            @click="expandAll()"
            class="px-3 py-1 text-xs font-medium text-gray-600 bg-gray-100 rounded hover:bg-gray-200 transition-colors">
      Expand
    </button>
    <button type="button"
            @click="collapseAll()"
            class="px-3 py-1 text-xs font-medium text-gray-600 bg-gray-100 rounded hover:bg-gray-200 transition-colors">
      Collapse
    </button>
  </div>

  <!-- Selected Items Badges -->
  <div x-show="selectedItems.length > 0" class="mt-2 pt-2 border-0 border-gray-200">
    <div class="flex flex-wrap gap-2">
      <template x-for="item in selectedItems" :key="item.value">
        <span
          class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium border-1 border-theme-text-primary text-theme-text-primary">
          <span x-text="item.label"></span>
        </span>
      </template>
    </div>
  </div>
</div>

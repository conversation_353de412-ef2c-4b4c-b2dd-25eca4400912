{% load l10n %}

<div class="flex items-center">
  <input type="{{ widget.type }}"
         name="{{ widget.name }}"
         {% if widget.value != None %}value="{{ widget.value|unlocalize }}"{% endif %}
         class="{{ widget.attrs.class|default:'h-4 w-4 rounded border-theme-input-border-primary text-theme-primary focus:ring-theme-primary' }}"
         {% if widget.attrs.disabled %}disabled{% endif %}
         {% if widget.required %}required{% endif %}
         {% if widget.attrs.id %}id="{{ widget.attrs.id }}"{% endif %}
         {% if widget.checked %}checked{% endif %}>
  {% if widget.attrs.label %}
  <label class="ml-2 block text-sm text-theme-text-primary"
         for="{{ widget.attrs.id }}">{{ widget.attrs.label }}</label>
  {% endif %}
</div>

{% spaceless %}
  <div class="relative" x-data="{ open: false }">
    <div class="relative inline-block w-auto">
      <input type="{{ widget.type }}"
             name="{{ widget.name }}"
             id="{{ widget.attrs.id }}"
             {% if widget.value != None %}value="{{ widget.value|stringformat:'s' }}"{% endif %}
             {% include "django/forms/widgets/attrs.html" %}
             @click="open = true"
             @click.away="open = false"
             class="{{ widget.attrs.class }} pr-10" />
      <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
        <svg class="w-4 h-4 text-theme-text-secondary"
             fill="currentColor"
             viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
        </svg>
      </div>
    </div>
    {% if widget.attrs.help_text %}
      <p class="mt-1 text-sm text-theme-text-secondary">{{ widget.attrs.help_text }}</p>
    {% endif %}
    {% if widget.errors %}<p class="mt-1 text-sm text-theme-status-error">{{ widget.errors.0 }}</p>{% endif %}
  </div>
{% endspaceless %}

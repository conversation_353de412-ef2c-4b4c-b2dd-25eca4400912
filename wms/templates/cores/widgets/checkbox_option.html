{% load l10n %}

<div class="flex items-center mb-2">
  <input type="checkbox"
         name="{{ widget.name }}"
         value="{{ option.value|unlocalize }}"
         class="{{ widget.attrs.class|default:'h-4 w-4 rounded border-theme-input-border-primary text-theme-primary focus:ring-theme-primary' }}"
         {% if option.attrs %}{% for name, value in option.attrs.items %}{{ name }}{% if value is not False %} = "{{ value }}"{% endif %}
         {% endfor %}
         {% endif %}
         {% if option.checked %}checked{% endif %}
         id="{{ widget.attrs.id }}_{{ option.index }}"
         {% if widget.required %}required{% endif %}
         {% if widget.attrs.disabled %}disabled{% endif %} />
  <label class="ml-2 block text-sm text-theme-text-primary"
         for="{{ widget.attrs.id }}_{{ option.index }}">{{ option.label }}</label>
</div>

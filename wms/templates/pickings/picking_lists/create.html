{% extends "base.html" %}
{% load i18n static django_tables2 crispy_forms_tags %}

{% block title %}{% trans "Create Picking List" %}{% endblock %}

{% block content %}
  <section id="main-content" class="mx-auto w-[90%] py-6">
    <div class="mb-6">
      <h1 class="text-2xl font-semibold text-theme-text-primary">{{ section_title }}</h1>
    </div>

    <div class="bg-white rounded-xs overflow-hidden">
      <!-- Step indicators -->
      <div class="flex">
        <div id="step1-indicator" class="flex-1 px-6 py-4 bg-theme-action-create text-white font-medium">
          {% trans "Basic Information" %}
        </div>
        <div id="step2-indicator" class="flex-1 px-6 py-4 bg-theme-bg-secondary text-theme-text-secondary font-medium">
          {% trans "Select Release Orders" %}
        </div>
      </div>

      <!-- Step 1: Basic Information Form -->
      <div id="step1-content" class="pt-4">
        <section class="mx-auto mb-6">
          {% include "tailwind/errors.html" %}

          <div id="basic-form-container">
            {% include "pickings/picking_lists/partials/basic_info_form.html" %}
          </div>
        </section>
      </div>

      <!-- Step 2: Release Order Selection (initially empty) -->
      <div id="step2-content" class="hidden">
        <!-- Will be populated by HTMX -->
        <div class="p-6 text-center text-theme-text-secondary">
          {% trans "Please complete Step 1 first" %}
        </div>
      </div>
    </div>
  </section>

{% endblock %}

{% block extra_js %}
  <script>
    // Function to initialize all form components
    function initializeFormComponents() {
      // Initialize date pickers
        initializeCalendars();

      // Initialize Select2 dropdowns
      if (typeof $ !== 'undefined' && typeof $.fn.select2 !== 'undefined') {
        // Initialize release_from dropdown
        const releaseFromSelect = $('#id_release_from');
        if (releaseFromSelect.length) {
          try {
            // Try to destroy first in case it's already initialized
            releaseFromSelect.select2('destroy');
          } catch (e) {
            // Ignore errors if it wasn't initialized
          }
          initializeSelect2(releaseFromSelect);
        }

        // Initialize any other Select2 dropdowns
        $('.select2-widget').each(function() {
          const $this = $(this);
          try {
            // Try to destroy first in case it's already initialized
            $this.select2('destroy');
          } catch (e) {
            // Ignore errors if it wasn't initialized
          }
            initializeSelect2($this);
        });
      }
    }

    // Initialize components when the page loads
    document.addEventListener('DOMContentLoaded', function() {
      initializeFormComponents();
    });

    // Prevent HTMX from showing default error messages
    document.addEventListener('htmx:beforeSwap', function(event) {
      // If the response is a validation error (422 status), we want to handle it specially
      if (event.detail.xhr.status === 422) {
        // This is a form validation error - the response contains the form with errors
        // We'll swap this into the form container instead of the step2-content
        const formHtml = event.detail.xhr.responseText;
        const formContainer = document.getElementById('basic-form-container');
        formContainer.innerHTML = formHtml;

        // Reinitialize all form components
        setTimeout(function() {
          initializeFormComponents();
        }, 50); // Small delay to ensure DOM is updated

        // Scroll to the top to show errors
        formContainer.scrollIntoView({ behavior: 'smooth' });

        // Prevent the default swap
        event.detail.shouldSwap = false;
      }

      // Handle validation errors from the submit view (step 2)
      if (event.detail.xhr.status === 422 && event.detail.target.id === 'step2-content') {
        // This is a form validation error for step 2
        // The response contains the re-rendered form with validation errors
        // We'll let the default swap happen to update the form with errors

        // After the swap, reinitialize any JavaScript components
        document.addEventListener('htmx:afterSwap', function(afterEvent) {
          if (afterEvent.detail.target.id === 'step2-content') {
            // Scroll to the top to show errors
            document.getElementById('step2-content').scrollIntoView({ behavior: 'smooth' });

            // This is a one-time event handler
            this.removeEventListener(afterEvent.type, arguments.callee);
          }
        });
      }

      // Handle other errors from the submit view
      else if (event.detail.xhr.status >= 400 && event.detail.xhr.status !== 422) {
        // Show the error message
        const errorHtml = event.detail.xhr.responseText;

        // Create an error message container if it doesn't exist
        let errorContainer = document.getElementById('step2-error-container');
        if (!errorContainer) {
          errorContainer = document.createElement('div');
          errorContainer.id = 'step2-error-container';
          const formContainer = document.querySelector('#step2-content form');
          if (formContainer) {
            formContainer.insertBefore(errorContainer, formContainer.firstChild);
          }
        }

        // Set the error message
        errorContainer.innerHTML = errorHtml;
        errorContainer.scrollIntoView({ behavior: 'smooth' });

        // Prevent the default swap
        event.detail.shouldSwap = false;
      }
    });

    // Function to handle the response from step 1 form submission
    function handleStep1Response(event) {
      // Check if the response status indicates a validation error (422 Unprocessable Entity)
      const hasErrors = event.detail.xhr.status === 422;

      if (!hasErrors) {
        // If no errors, proceed to step 2
        showStep2();
      }
      // If there are errors, they're already handled by the htmx:beforeSwap event
    }

    // Function to show Step 2 and hide Step 1
    function showStep2() {
      // Show step 2 content
      document.getElementById('step2-content').classList.remove('hidden');
      document.getElementById('step1-content').classList.add('hidden');

      // Update step indicators
      document.getElementById('step1-indicator').classList.remove('bg-theme-action-create', 'text-white');
      document.getElementById('step1-indicator').classList.add('bg-theme-bg-secondary', 'text-theme-text-secondary');

      document.getElementById('step2-indicator').classList.remove('bg-theme-bg-secondary', 'text-theme-text-secondary');
      document.getElementById('step2-indicator').classList.add('bg-theme-action-create', 'text-white');

      // Make step 1 form read-only
      const form = document.getElementById('picking-list-basic-form');
      const inputs = form.querySelectorAll('input, select, textarea');
      inputs.forEach(input => {
        input.setAttribute('disabled', 'disabled');
      });

      // Initialize the table if needed
      if (typeof window.updateSelectedCount === 'function') {
        window.updateSelectedCount();
      }
    }

    // Function to show Step 1 and hide Step 2
    function showStep1() {
      // Show step 1 content
      document.getElementById('step1-content').classList.remove('hidden');
      document.getElementById('step2-content').classList.add('hidden');

      // Update step indicators
      document.getElementById('step1-indicator').classList.remove('bg-theme-bg-secondary', 'text-theme-text-secondary');
      document.getElementById('step1-indicator').classList.add('bg-theme-action-create', 'text-white');

      document.getElementById('step2-indicator').classList.remove('bg-theme-action-create', 'text-white');
      document.getElementById('step2-indicator').classList.add('bg-theme-bg-secondary', 'text-theme-text-secondary');

      // Enable step 1 form
      const form = document.getElementById('picking-list-basic-form');
      const inputs = form.querySelectorAll('input, select, textarea');
      inputs.forEach(input => {
        if (input.name !== 'issued_by') { // Keep issued_by disabled
          input.removeAttribute('disabled');
        }
      });
    }
  </script>
{% endblock %}

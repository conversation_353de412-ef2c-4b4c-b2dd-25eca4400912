{% load i18n %}

<div class="flex items-center">
  {{ value }}
  {% if picking_list.status == "New" or picking_list.status == "Processing" %}
    <form method="post"
          hx-post="{% url 'pickings:picking_lists:complete' picking_list.pk %}"
          class="ml-2">
      {% csrf_token %}
      <button type="submit"
              class="flex text-theme-success hover:text-theme-success-hover transition-colors"
              title="{% trans 'Complete Picking List' %}">
        <svg xmlns="http://www.w3.org/2000/svg"
             class="h-6 w-6"
             viewBox="0 0 24 24"
             fill="none"
             stroke="currentColor"
             stroke-width="2"
             stroke-linecap="round"
             stroke-linejoin="round">
          <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
          <polyline points="22 4 12 14.01 9 11.01"></polyline>
        </svg>
      </button>
    </form>
  {% endif %}
</div>

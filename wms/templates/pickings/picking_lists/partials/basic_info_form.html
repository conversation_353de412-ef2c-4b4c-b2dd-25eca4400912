{% load i18n crispy_forms_tags %}

<div class="form-container">
  <form id="picking-list-basic-form"
        hx-post="{% url 'pickings:picking_lists:load_release_orders' %}"
        hx-target="#step2-content"
        hx-swap="innerHTML"
        hx-trigger="submit"
        hx-on::after-request="handleStep1Response(event)">
    {% csrf_token %}

    <div class="bg-white border-1 border-gray-200 rounded-none overflow-hidden cursor-pointer">
      <div class="p-0">
        <div class="space-y-6">
          <div class="bg-theme-bg-secondary/10 rounded-none">
            <div class="grid grid-cols-1 divide-y divide-gray-200">
              {{ form.name|as_crispy_field }}
              {{ form.issued_by|as_crispy_field }}
              {{ form.expected_completion_date|as_crispy_field }}
              {{ form.release_from|as_crispy_field }}
              {{ form.remark|as_crispy_field }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="flex items-center justify-between mt-4">
      {% include "_components/button_cancel.html" with link=cancel_url text=cancel_text|default:"Cancel" %}
      {% include "_components/button_submit.html" with action_type='create' text=submit_text|default:"Next" %}
    </div>
  </form>
</div>

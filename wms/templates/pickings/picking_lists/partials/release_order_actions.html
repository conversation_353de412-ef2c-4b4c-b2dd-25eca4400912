{% load i18n %}

<div class="flex space-x-2">
  <button
    class="inline-flex items-center px-2 py-1 text-sm bg-theme-status-error text-white rounded hover:bg-theme-status-error/80 transition-colors"
    title="{% translate 'Remove from Picking List' %}"
    hx-get="{% url 'pickings:picking_lists:release_order_delete_form' record.pk %}"
    hx-target="#modal-form-content"
    hx-swap="innerHTML"
    @click="$dispatch('open-modal', { title: '{% trans 'Remove Release Order' %}' })">
    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
      <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
    </svg>
  </button>
</div>

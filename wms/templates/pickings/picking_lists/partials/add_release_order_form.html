{% load i18n %}
{% load render_table from django_tables2 %}

<form
  method="post"
  hx-post="{% url 'pickings:picking_lists:add_release_order_submit' picking_list.pk %}"
  hx-trigger="submit"
>
  {% csrf_token %}
  <div class="p-4">
    {% if error %}
      <div class="mb-4 p-3 bg-red-50 border border-red-300 rounded-md">
        <p class="text-red-700 text-sm">{{ error }}</p>
      </div>
    {% else %}
      <!-- Display non-field errors if any -->
      {% if form.non_field_errors %}
        <div class="mb-4 p-3 bg-red-50 border border-red-300 rounded-md">
          <ul class="text-red-700 text-sm">
            {% for error in form.non_field_errors %}
              <li>{{ error }}</li>
            {% endfor %}
          </ul>
        </div>
      {% endif %}

      <!-- Release Order Selection Section -->
      <div class="mb-4 overflow-hidden">
        <div class="p-0">
          {% include 'tables/control_section.html' with table=table %}
          <div id="release-order-table-container">
              <!-- Release Order Table -->
              <div class="overflow-x-auto">
                {% render_table table %}
              </div>
          </div>

          <!-- Hidden field for selected release orders -->
          {{ form.selected_release_orders }}
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex justify-between space-x-3">
        <button
          type="button"
          class="py-1.5 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400"
          @click="$dispatch('close-modal')"
        >
          {% translate "Cancel" %}
        </button>
        <button
          type="submit"
          class="py-1.5 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-theme-action-create hover:bg-theme-action-create-hover focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-theme-action-create disabled:opacity-50"
          disabled>
          {% translate "Add Release Orders" %}
        </button>
      </div>
    {% endif %}
  </div>
</form>

<script>
  // This script runs when the modal is loaded
  (function() {
    // Initialize the hidden input for selected release orders
    const selectedReleaseOrdersInput = document.querySelector('input[name="selected_release_orders"]');
    if (!selectedReleaseOrdersInput) return; // Exit if not found (safety check)

    // Function to update the selected release orders input
    function updateSelectedReleaseOrders() {
      const checkboxes = document.querySelectorAll('.row-checkbox:checked');
      const selectedIds = Array.from(checkboxes).map(checkbox => checkbox.value);
      selectedReleaseOrdersInput.value = selectedIds.join(',');

      // Enable/disable submit button based on selection
      const submitButton = document.querySelector('button[type="submit"]');
      if (submitButton) {
        submitButton.disabled = selectedIds.length === 0;
      }
    }

    // Add event listeners to all checkboxes
    document.querySelectorAll('.row-checkbox').forEach(checkbox => {
      // Add change event listener
      checkbox.addEventListener('change', updateSelectedReleaseOrders);
    });

    // Initialize the selected release orders input
    updateSelectedReleaseOrders();

    // Add a global event listener for HTMX after-swap events
    document.body.addEventListener('htmx:afterSwap', function(event) {
      // Only process if the swap target is our table container
      if (event.detail.target.id === 'release-order-table-container') {
        // Re-initialize the checkboxes after the table is updated
        setTimeout(function() {
          // Get the current selected IDs
          const currentSelectedIds = selectedReleaseOrdersInput.value.split(',').filter(id => id.trim());

          // Add event listeners to all checkboxes and restore selections
          document.querySelectorAll('.row-checkbox').forEach(checkbox => {
            // Check if this checkbox should be checked (was previously selected)
            if (currentSelectedIds.includes(checkbox.value)) {
              checkbox.checked = true;
            }

            // Add change event listener (remove existing ones first to avoid duplicates)
            checkbox.removeEventListener('change', updateSelectedReleaseOrders);
            checkbox.addEventListener('change', updateSelectedReleaseOrders);
          });

          // Update the submit button state after restoring selections
          updateSelectedReleaseOrders();
        }, 50); // Small delay to ensure DOM is fully updated
      }
    });
  })();
</script>

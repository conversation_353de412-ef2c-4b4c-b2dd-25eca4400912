{% load render_table from django_tables2 %}

<!-- Release Order Table -->
<div class="overflow-x-auto">
  {% render_table table %}
</div>

<script>
  // This script runs immediately when loaded via HTMX
  (function() {
    // Initialize the hidden input for selected release orders
    const selectedReleaseOrdersInput = document.querySelector('input[name="selected_release_orders"]');
    if (!selectedReleaseOrdersInput) return; // Exit if not found (safety check)

    // Get all currently checked checkboxes
    const checkedBoxes = document.querySelectorAll('.row-checkbox:checked');

    // Store the currently selected IDs
    const currentSelectedIds = selectedReleaseOrdersInput.value.split(',').filter(id => id.trim());

    // Function to update the selected release orders input
    function updateSelectedReleaseOrders() {
      const checkboxes = document.querySelectorAll('.row-checkbox:checked');
      const selectedIds = Array.from(checkboxes).map(checkbox => checkbox.value);
      selectedReleaseOrdersInput.value = selectedIds.join(',');
    }

    // Add event listeners to all checkboxes
    document.querySelectorAll('.row-checkbox').forEach(checkbox => {
      // Check if this checkbox should be checked (was previously selected)
      if (currentSelectedIds.includes(checkbox.value)) {
        checkbox.checked = true;
      }

      // Add change event listener
      checkbox.addEventListener('change', updateSelectedReleaseOrders);
    });

    // Update the hidden input with the current selections
    updateSelectedReleaseOrders();
  })();
</script>

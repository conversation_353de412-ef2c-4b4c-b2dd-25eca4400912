{% load i18n django_tables2 crispy_forms_tags %}

<section class="mx-auto mb-6">
  {% include "tailwind/errors.html" %}

  <form id="picking-list-selection-form"
        hx-post="{% url 'pickings:picking_lists:submit' %}"
        hx-trigger="submit"
        data-hx-path="{% url 'pickings:picking_lists:load_release_orders' %}">
    {% csrf_token %}

    <!-- Hidden field to store selected release order IDs -->
    {{ selection_form.selected_release_orders }}

    {% include 'tables/control_section.html' with table=table %}

    <!-- Release Orders Table -->
    <div class="mb-6 cursor-pointer">
      {% render_table table %}
    </div>

    <div>
      <span id="selected-count" class="text-sm text-theme-text-secondary">0 items selected</span>
    </div>
    <div class="flex items-center justify-between mt-4">
      <button type="button"
              class="px-4 py-1.5 border border-theme-border-primary rounded-md text-theme-text-primary"
              onclick="showStep1()">
        {% trans "Back" %}
      </button>
      <button type="submit"
              id="submit-button"
              class="px-4 py-1.5 bg-theme-action-create text-white rounded-md disabled:opacity-50"
              disabled>
        {% trans "Create Picking List" %}
      </button>
    </div>
  </form>
</section>

<script>
  // Use window.selectedRows to avoid redeclaration issues
  window.selectedRows = window.selectedRows || new Set();

  // Function to update the selected count display
  window.updateSelectedCount = function() {
    const countElement = document.getElementById('selected-count');
    if (countElement) {
      const count = window.selectedRows.size;
      countElement.textContent = count + (count === 1 ? ' item selected' : ' items selected');
    }
  };

  // Define updateSelectAllCheckbox function
  window.updateSelectAllCheckbox = function() {
    const selectAllCheckbox = document.querySelector('.select-all-checkbox');
    if (selectAllCheckbox) {
      const rowCheckboxes = document.querySelectorAll('.row-checkbox');
      const checkedRowCheckboxes = document.querySelectorAll('.row-checkbox:checked');

      // If all row checkboxes are checked, check the select-all checkbox
      // If some row checkboxes are checked, make the select-all checkbox indeterminate
      // If no row checkboxes are checked, uncheck the select-all checkbox
      if (checkedRowCheckboxes.length === rowCheckboxes.length && rowCheckboxes.length > 0) {
        selectAllCheckbox.checked = true;
        selectAllCheckbox.indeterminate = false;
      } else if (checkedRowCheckboxes.length > 0) {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = true;
      } else {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = false;
      }
    }
  };

  // Override the handleRowCheckbox function to update our hidden field
  window.handleRowCheckbox = function(checkbox) {
    const rowId = checkbox.value;

    if (checkbox.checked) {
      window.selectedRows.add(rowId);
    } else {
      window.selectedRows.delete(rowId);
    }

    // Update the hidden field with selected IDs
    document.querySelector('input[name="selected_release_orders"]').value =
      Array.from(window.selectedRows).join(',');

    // Update the selected count display
    window.updateSelectedCount();

    // Enable/disable submit button based on selection
    const submitButton = document.getElementById('submit-button');
    submitButton.disabled = window.selectedRows.size === 0;

    // Update the "select all" checkbox state
    window.updateSelectAllCheckbox();
  };

  // Call updateSelectedCount after initialization
  window.updateSelectedCount();

  // Override the handleSelectAll function to update our hidden field
  window.handleSelectAll = function(checkbox) {
    const rowCheckboxes = document.querySelectorAll('.row-checkbox');

    rowCheckboxes.forEach(rowCheckbox => {
      rowCheckbox.checked = checkbox.checked;

      const rowId = rowCheckbox.value;
      if (checkbox.checked) {
        window.selectedRows.add(rowId);
      } else {
        window.selectedRows.delete(rowId);
      }
    });

    // Update the hidden field with selected IDs
    document.querySelector('input[name="selected_release_orders"]').value =
      Array.from(window.selectedRows).join(',');

    // Update the selected count display
    window.updateSelectedCount();

    // Enable/disable submit button based on selection
    const submitButton = document.getElementById('submit-button');
    submitButton.disabled = window.selectedRows.size === 0;
  };
</script>

{% extends "base.html" %}

{% load i18n %}
{% load static %}
{% load custom_tags %}

{% block title %}
  {% trans "User" %}: {{ object.name }}
{% endblock title %}
{% block content %}
  <section class="mx-auto max-w-4xl py-8">
    <div class="py-1.5 px-3 border-1 min-h-10 bg-theme-table-header border-gray-300 font-semibold uppercase tracking-wider flex justify-between items-center">
      <span>{% trans "User Information" %}</span>
      {% if object == request.user %}
        <div class="flex gap-4">
          <a href="{% url 'account_email' %}"
             class="inline-flex items-center px-3 py-1 text-sm bg-theme-action-update text-white rounded hover:bg-theme-action-update-hover transition-colors">
            {% trans "Manage E-Mail" %}
          </a>
          {#          <a href="{% url 'users:update' user.id %}"#}
          {#             class="inline-flex items-center px-3 py-1 text-sm bg-theme-action-update text-white rounded hover:bg-theme-action-update-hover transition-colors">#}
          {#            {% trans "Update Profile" %}#}
          {#          </a>#}
          {#          <a href="{% url 'mfa_index' %}"#}
          {#             class="inline-flex items-center px-3 py-1 text-sm bg-theme-action-update text-white rounded hover:bg-theme-action-update-hover transition-colors">#}
          {#            {% trans "Configure MFA" %}#}
          {#          </a>#}
        </div>
      {% endif %}
    </div>
    <div class="bg-white border-1 border-gray-200 rounded-none overflow-hidden">
      <!-- Main Information Card -->
      <div class="space-y-6">
        <div class="bg-theme-bg-secondary/10 rounded-none">
          <div class="grid grid-cols-1 divide-y divide-gray-200 ">
            {% if object.profile_image %}
              {% include "_components/detail_field.html" with label="Profile Image" value="<img src='{{ object.profile_image.url }}' class='w-16 h-16 rounded-full' />"|safe %}
            {% endif %}
            {% include "_components/detail_field.html" with label="Email" value=object.email %}
            {% include "_components/detail_field.html" with label="Username" value=object.username %}
            {% if object.is_active %}
              {% include "_components/detail_field.html" with label="Active Status" value='<span class="py-2 px-6 inline-flex text-xs leading-5 font-semibold rounded-md bg-green-100 text-green-800">Yes</span>'|safe %}
            {% else %}
              {% include "_components/detail_field.html" with label="Active Status" value='<span class="py-2 px-6 inline-flex text-xs leading-5 font-semibold rounded-md bg-red-100 text-red-800">No</span>'|safe %}
            {% endif %}
            {% include "_components/detail_field.html" with label="Last Login" value=object.last_login %}
            {% include "_components/detail_field.html" with label="Gender" value=object.get_gender_display %}
          </div>
        </div>
      </div>
    </section>
  {% endblock content %}

{% load i18n %}

{% if object %}
  <section class="mx-auto border-1 [&:not(:first-child)]:border-t-0 border-theme-border-secondary">
    <div class="py-1.5 px-3 border-b-1 bg-theme-table-header border-gray-300 font-semibold uppercase tracking-wider flex justify-between items-center">
      <span>{% trans "Event & Historical Data" %}</span>
    </div>
    <div class="bg-white rounded-none overflow-hidden">
      <!-- Main Information Card -->
      <div class="p-0">
        <div class="space-y-6">
          <!-- Item Information Section -->
          <div class="bg-theme-bg-secondary/10 rounded-none p-6">
            <div class="grid grid-cols-1 gap-4">
              <!-- Left Content -->
              <div class="space-y-2">
                <div class="flex items-center">
                  <span class="text-theme-text-primary w-36 font-bold">{% trans "ToDo" %}</span>
                  <span class="text-theme-text-primary">{{ consignor.code }}</span>
                </div>
                <div class="flex items-center">
                  <span class="text-theme-text-primary w-36 font-bold">{% trans "Code" %}</span>
                  <span class="text-theme-text-primary">{{ consignor.display_name }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
{% endif %}

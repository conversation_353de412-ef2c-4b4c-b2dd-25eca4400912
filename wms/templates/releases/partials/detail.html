{% load i18n %}

{% block content %}
  {% if object %}
    <section class="mx-auto border-1 [&:not(:first-child)]:border-t-0 border-theme-input-border-secondary">
      <div
        class="py-1.5 px-3 border-b-1 min-h-10 bg-theme-table-header border-theme-input-border-secondary font-semibold uppercase tracking-wider flex justify-between items-center">
        <span>{% trans "Basic Information" %}</span>
        <div class="flex gap-4">
          {% if release_order.status == "New" and release_order.all_items_in_new_status %}
            <a href="{% url 'releases:orders:update' release_order.pk %}"
               class="inline-flex items-center px-3 py-1 text-sm bg-theme-action-update text-white rounded hover:bg-theme-action-update-hover transition-colors">
              {% trans "Update" %}
            </a>
          {% endif %}

          {% if release_order.status == "New" or release_order.status == "Processing" or release_order.status == "Ready To Print" %}
            <button
              hx-get="{% url 'releases:orders:obsolete_form' release_order.pk %}"
              hx-target="#modal-form-content"
              hx-swap="innerHTML"
              class="inline-flex items-center px-3 py-1 text-sm bg-theme-status-error text-white rounded hover:bg-theme-status-error/80 transition-colors cursor-pointer"
              title="{% translate 'Mark as Obsolete' %}"
              @click="$dispatch('open-modal', { title: '{% trans 'Mark as Obsolete' %}' })">
              {% trans "Obsolete" %}
            </button>
            <button
              hx-get="{% url 'releases:orders:delete_form' release_order.pk %}"
              hx-target="#modal-form-content"
              hx-swap="innerHTML"
              class="inline-flex items-center px-3 py-1 text-sm bg-theme-status-error text-white rounded hover:bg-theme-status-error/80 transition-colors"
              title="{% translate 'Delete Release Order' %}"
              @click="$dispatch('open-modal', { title: '{% trans 'Delete Release Order' %}' })">
              {% trans "Delete" %}
            </button>
          {% endif %}
          <a href="{% url 'pdfs:releases:print_info' release_order.pk %}"
             target="_blank"
             class="inline-flex items-center justify-center px-2 py-1 bg-gray-500 text-white rounded hover:bg-gray-600 transition">
            <svg xmlns="http://www.w3.org/2000/svg"
                 class="h-5 w-5 mr-2"
                 viewBox="0 0 24 24"
                 fill="none"
                 stroke="currentColor"
                 stroke-width="2"
                 stroke-linecap="round"
                 stroke-linejoin="round">
              <polyline points="6 9 6 2 18 2 18 9"></polyline>
              <path d="M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2"></path>
              <rect x="6" y="14" width="12" height="8"></rect>
            </svg>
            Print Info
          </a>
          {% if release_order.status == "Ready To Release" or release_order.status == "Completed" %}
            <a href="{% url 'pdfs:releases:wro_pdf' release_order.pk %}"
               target="_blank"
               class="inline-flex items-center justify-center px-2 py-1 bg-gray-500 text-white rounded hover:bg-gray-600 transition">
              <svg xmlns="http://www.w3.org/2000/svg"
                   class="h-5 w-5 mr-2"
                   viewBox="0 0 24 24"
                   fill="none"
                   stroke="currentColor"
                   stroke-width="2"
                   stroke-linecap="round"
                   stroke-linejoin="round">
                <polyline points="6 9 6 2 18 2 18 9"></polyline>
                <path d="M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2"></path>
                <rect x="6" y="14" width="12" height="8"></rect>
              </svg>
              Print WRO
            </a>
          {% endif %}
          {% if release_order.status == "Ready To Release" or release_order.status == "Completed" %}
            <a href="{% url 'pdfs:releases:do_pdf' release_order.pk %}"
               target="_blank"
               class="inline-flex items-center justify-center px-2 py-1 bg-gray-500 text-white rounded hover:bg-gray-600 transition">
              <svg xmlns="http://www.w3.org/2000/svg"
                   class="h-5 w-5 mr-2"
                   viewBox="0 0 24 24"
                   fill="none"
                   stroke="currentColor"
                   stroke-width="2"
                   stroke-linecap="round"
                   stroke-linejoin="round">
                <polyline points="6 9 6 2 18 2 18 9"></polyline>
                <path d="M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2"></path>
                <rect x="6" y="14" width="12" height="8"></rect>
              </svg>
              Print DO
            </a>
          {% endif %}
        </div>
      </div>
      <div class="bg-white rounded-none overflow-hidden">
        <!-- Main Information Card -->
        <div class="p-0">
          <div class="space-y-6">
            <!-- Release Order Information Section -->
            <div class="bg-theme-bg-secondary/10 rounded-none">
              <div class="grid grid-cols-2">
                <!-- Left Content -->
                <div class="grid grid-cols-1 divide-y divide-gray-200 ">
                  {% include "_components/detail_field.html" with label="System Number" value=release_order.system_number %}
                  {% include "_components/detail_field.html" with label="Issued By" value=release_order.issued_by %}
                  {% include "_components/detail_field.html" with label="Consignor" value=release_order.consignee.consignor %}
                  {% include "_components/detail_field.html" with label="Consignee" value=release_order.consignee %}
                  <div
                    class="flex border-b-1 [&:last-child]:border-b-0 [&:only-child]:border-b-1 rounded-none border-gray-200">
                    <label
                      class="flex items-center p-3 justify-end text-end text-theme-text-primary text-sm w-36 font-medium bg-theme-form-label">
                      {% trans "Status" %}
                    </label>
                    <span class="flex items-center p-3 text-theme-text-primary bg-theme-form-span flex-1">
                      {% include "releases/partials/status_with_print_icon.html" with value=release_order.html_status_display %}
                    </span>
                  </div>
                  {% include "_components/detail_field.html" with label="Release Date" value=release_order.release_datetime|date:"Y-m-d H:i" %}
                  {% include "_components/detail_field.html" with label="Document No" value=release_order.document_no|default:"-" %}
                  {% include "_components/detail_field.html" with label="Reference" value=release_order.customer_reference|default:"-" %}
                  {% include "_components/detail_field.html" with label="Tag" value=release_order.tag|default:"-" %}
                  {% with warehouses=release_order.warehouses.all %}
                    <div
                      class="flex border-b-1 [&:last-child]:border-b-0 [&:only-child]:border-b-1 rounded-none border-gray-200">
                      <label
                        class="flex items-center p-3 justify-end text-end text-theme-text-primary text-sm w-36 font-medium bg-theme-form-label">
                        {% trans "Warehouses" %}
                      </label>
                      <span class="flex items-center p-3 text-theme-text-primary bg-theme-form-span flex-1">
                        <span class="flex flex-wrap gap-2">
                      {% for warehouse in warehouses %}
                        <span
                          title="{{ warehouse.full_name }}"
                          class="badge bg-theme-link">
                          {{ warehouse.name }}
                        </span>
                      {% empty %}
                        -
                      {% endfor %}
                        </span>
                      </span>
                    </div>
                  {% endwith %}
                  <div
                    class="flex border-b-1 [&:last-child]:border-b-0 [&:only-child]:border-b-1 rounded-none border-gray-200">
                    <label
                      class="flex items-center p-3 justify-end text-end text-theme-text-primary text-sm w-36 font-medium bg-theme-form-label">
                      {% trans "Is Delivery Order" %}
                    </label>
                    <span class="flex items-center p-3 text-theme-text-primary bg-theme-form-span flex-1">
                      {% if release_order.is_delivery_order %}
                        <span class="inline-flex items-center text-theme-status-success">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20"
                               fill="currentColor">
                            <path fill-rule="evenodd"
                                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                  clip-rule="evenodd"/>
                          </svg>
                          <span class="ml-2">Yes</span>
                        </span>
                      {% else %}
                        <span class="inline-flex items-center text-theme-status-error">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20"
                               fill="currentColor">
                            <path fill-rule="evenodd"
                                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                                  clip-rule="evenodd"/>
                          </svg>
                          <span class="ml-2">No</span>
                        </span>
                      {% endif %}
                    </span>
                  </div>
                  <div
                    class="flex border-b-1 [&:last-child]:border-b-0 [&:only-child]:border-b-1 rounded-none border-gray-200">
                    <label
                      class="flex items-center p-3 justify-end text-end text-theme-text-primary text-sm w-36 font-medium bg-theme-form-label">
                      {% trans "Is Reverse PGI" %}
                    </label>
                    <span class="flex items-center p-3 text-theme-text-primary bg-theme-form-span flex-1">
                      {% if release_order.is_reverse_pgi %}
                        <span class="inline-flex items-center text-theme-status-success">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20"
                               fill="currentColor">
                            <path fill-rule="evenodd"
                                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                  clip-rule="evenodd"/>
                          </svg>
                          <span class="ml-2">Yes</span>
                        </span>
                      {% else %}
                        <span class="inline-flex items-center text-theme-status-error">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20"
                               fill="currentColor">
                            <path fill-rule="evenodd"
                                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                                  clip-rule="evenodd"/>
                          </svg>
                          <span class="ml-2">No</span>
                        </span>
                      {% endif %}
                    </span>
                  </div>
                  <div
                    class="flex border-b-1 [&:last-child]:border-b-0 [&:only-child]:border-b-1 rounded-none border-gray-200">
                    <label
                      class="flex items-center p-3 justify-end text-end text-theme-text-primary text-sm w-36 font-medium bg-theme-form-label">
                      {% trans "Is Return" %}
                    </label>
                    <span class="flex items-center p-3 text-theme-text-primary bg-theme-form-span flex-1">
                      {% if release_order.is_return %}
                        <span class="inline-flex items-center text-theme-status-success">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20"
                               fill="currentColor">
                            <path fill-rule="evenodd"
                                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                  clip-rule="evenodd"/>
                          </svg>
                          <span class="ml-2">Yes</span>
                        </span>
                      {% else %}
                        <span class="inline-flex items-center text-theme-status-error">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20"
                               fill="currentColor">
                            <path fill-rule="evenodd"
                                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                                  clip-rule="evenodd"/>
                          </svg>
                          <span class="ml-2">No</span>
                        </span>
                      {% endif %}
                    </span>
                  </div>
                  {% include "_components/detail_field.html" with label="Outbound Delivery No" value=release_order.outbound_delivery_no|default:"-" %}
                  {% include "_components/detail_field.html" with label="Remark" value=release_order.remark|default:"-" %}
                </div>
                <!-- Right Content -->
                <div class="grid grid-cols-1 divide-y divide-gray-200 ">
                  {% include "_components/detail_field.html" with label="Show Shipper Info" value=release_order.show_shipper_info|yesno:"Yes,No" %}
                  {% include "_components/detail_field.html" with label="Shipper Address" value=release_order.shipper_address|default:"-" %}
                  {% include "_components/detail_field.html" with label="Shipper Tel" value=release_order.shipper_phone|default:"-" %}
                  {% include "_components/detail_field.html" with label="Shipper Mobile" value=release_order.shipper_mobile|default:"-" %}
                  {% include "_components/detail_field.html" with label="Shipper Attn" value=release_order.shipper_attn|default:"-" %}
                  {% include "_components/detail_field.html" with label="Shipper Address" value=release_order.shipping_address|default:"-" %}
                  {% include "_components/detail_field.html" with label="Shipping Tel" value=release_order.shipping_phone|default:"-" %}
                  {% include "_components/detail_field.html" with label="Shipping Mobile" value=release_order.shipping_mobile|default:"-" %}
                  {% include "_components/detail_field.html" with label="Shipping Attn" value=release_order.shipping_attn|default:"-" %}
                  {% include "_components/detail_field.html" with label="Total Cartons" value=release_order.total_cartons|default:"-" %}
                  {% include "_components/detail_field.html" with label="Total Weight" value=release_order.total_weight|default:"-" %}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <!-- Release Order Items Section -->
    <section class="mx-auto mt-4 border-0 border-theme-input-border-secondary">
      <div class="flex gap-4">
        {% if release_order.status == "Completed" and release_order.is_reverse_pgi is False %}
          <a href="{% url 'adjustments:adjustment:create' %}?wro_pk={{ release_order.pk }}"
             target="_blank"
             class="inline-flex items-center justify-center px-2 py-1 bg-gray-500 text-white rounded hover:bg-gray-600 transition">

            <svg xmlns="http://www.w3.org/2000/svg"
                 fill="none"
                 viewBox="0 0 24 24"
                 stroke-width="1.5"
                 stroke="currentColor"
                 class="h-5 w-5 mr-2"
            >
              <path stroke-linecap="round" stroke-linejoin="round" d="m15 15-6 6m0 0-6-6m6 6V9a6 6 0 0 1 12 0v3" />
            </svg>
            Reverse PGI
          </a>
        {% endif %}
        {% if release_order.status == "Completed" and release_order.is_return is False %}
          <a href="{% url 'receives:goods_received_notes:create' %}?wro_pk={{ release_order.pk }}"
             target="_blank"
             class="inline-flex items-center justify-center px-2 py-1 bg-gray-500 text-white rounded hover:bg-gray-600 transition">
            <svg xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                class="h-5 w-5 mr-2"
              >
              <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 9V5.25A2.25 2.25 0 0 1 10.5 3h6a2.25 2.25 0 0 1 2.25 2.25v13.5A2.25 2.25 0 0 1 16.5 21h-6a2.25 2.25 0 0 1-2.25-2.25V15M12 9l3 3m0 0-3 3m3-3H2.25" />
            </svg>
            Return Goods
          </a>
        {% endif %}
      </div>
      <div class="flex justify-end gap-4">
        {% if release_order.status == "New" or release_order.status == "Processing" %}
          <button
            hx-get="{% url 'releases:orders:item_add_form' release_order.pk %}"
            hx-target="#modal-form-content"
            hx-swap="innerHTML"
            class="inline-flex items-center px-3 py-1 text-sm bg-theme-primary text-white rounded hover:bg-theme-primary-hover transition-colors"
            title="{% translate 'Add New Item' %}"
            @click="$dispatch('open-modal', { title: '{% trans 'Add New Item' %}' })">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd"
                    d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
                    clip-rule="evenodd"/>
            </svg>
            {% trans "Add Item" %}
          </button>
        {% endif %}

      </div>
      <div class="bg-white overflow-hidden">
        <!-- partial table content -->
        <div hx-get="{% url 'releases:orders:item_list' release_order.pk %}?hxf=1"
             hx-trigger="load"
             hx-swap="outerHTML">{% include '_components/loading_spinner.html' %}</div>
      </div>
    </section>
  {% endif %}
{% endblock content %}

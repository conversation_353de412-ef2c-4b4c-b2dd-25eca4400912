{% load i18n %}
{% load humanize %}

<form method="post"
      hx-post="{% url 'releases:orders:delete' release_order.pk %}"
      hx-trigger="submit">
  {% csrf_token %}
  <div class="p-4">
    <!-- Release Order Information Section -->
    <div class="mb-4 border border-theme-border-primary rounded-xs overflow-hidden">
      <div class="bg-theme-bg-secondary px-4 py-2 border-b border-theme-border-primary">
        <h4 class="text-sm font-medium text-theme-text-primary">{% trans "Release Order Details" %}</h4>
      </div>
      <div class="p-4">
        <!-- Basic Release Order Information -->
        <div class="grid grid-cols-2 gap-3 sm:grid-cols-2 mb-4">
          <div class="col-span-2">
            <label class="block text-sm font-medium text-gray-700">{% trans "System Number" %}</label>
            <div class="mt-1 text-sm text-gray-900 font-semibold">{{ release_order.system_number }}</div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">{% trans "Status" %}</label>
            <div class="mt-1 text-sm text-gray-900">{{ release_order.get_status_display }}</div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">{% trans "Consignee" %}</label>
            <div class="mt-1 text-sm text-gray-900">{{ release_order.consignee.name }}</div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">{% trans "Release Date" %}</label>
            <div class="mt-1 text-sm text-gray-900">{{ release_order.release_datetime|date:"d M Y" }}</div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">{% trans "Issued By" %}</label>
            <div class="mt-1 text-sm text-gray-900">{{ release_order.issued_by.get_full_name|default:release_order.issued_by.username }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation -->
    <div class="mb-4 text-center">
      <h3 class="text-lg font-medium text-red-600">
        {% translate "Are you sure you want to delete this release order?" %}
      </h3>
      <p class="mt-2 text-sm text-gray-500">
        {% translate "This action cannot be undone." %}
      </p>
    </div>

    <!-- Action Buttons -->
    <div class="flex justify-between space-x-3">
      <button
        type="button"
        class="py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400"
        @click="$dispatch('close-modal')"
      >
        {% translate "Cancel" %}
      </button>
      <button
        type="submit"
        class="py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
      >
        {% translate "Delete" %}
      </button>
    </div>
  </div>
</form>

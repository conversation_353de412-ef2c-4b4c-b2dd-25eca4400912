{% load humanize %}
{% load i18n %}
{% load crispy_forms_tags %}

<form
  method="post"
  hx-post="{% url 'releases:orders:item_expected_quantity_update' release_order_item.pk %}"
  hx-trigger="submit"
>
  {% csrf_token %}
  <div class="p-4">
    <!-- Item Information Section -->
    <div class="mb-4 border border-theme-border-primary rounded-xs overflow-hidden">
      <div class="bg-theme-bg-secondary px-4 py-1.5 border-b border-theme-border-primary">
        <h4 class="text-sm font-medium text-theme-text-primary">{% trans "Item Details" %}</h4>
      </div>
      <div class="p-4">
        <!-- Basic Item Information -->
        <div class="grid grid-cols-2 gap-3 sm:grid-cols-2 mb-4">
          <div class="col-span-2">
            <label class="block text-sm font-medium text-gray-700">{% trans "Item Code" %}</label>
            <div class="mt-1 text-sm text-gray-900 font-semibold">{{ release_order_item.item.code }}</div>
          </div>
          <div class="col-span-2">
            <label class="block text-sm font-medium text-gray-700">{% trans "Item Name" %}</label>
            <div class="mt-1 text-sm text-gray-900">{{ release_order_item.item.name }}</div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">{% trans "Batch No" %}</label>
            <div class="mt-1 text-sm text-gray-900">{{ release_order_item.batch_no|default:"-" }}</div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">{% trans "Expiry Date" %}</label>
            <div class="mt-1 text-sm text-gray-900">{{ release_order_item.expiry_date|date:"d M Y"|default:"-" }}</div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">{% trans "Current Expected Quantity" %}</label>
            <div class="mt-1 text-sm text-gray-900">{{ release_order_item.expected_quantity|floatformat:0|intcomma }} {{ release_order_item.uom.symbol }}</div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">{% trans "Picked Quantity" %}</label>
            <div class="mt-1 text-sm text-gray-900">{{ release_order_item.total_picker_system_quantity|floatformat:0|intcomma }} {{ release_order_item.uom.symbol }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Expected Quantity Input -->
    <div class="mb-4">
      <label for="expected_quantity" class="block text-sm font-bold text-theme-text-primary mb-1">{% trans "New Expected Quantity" %}</label>
      <div class="flex items-center">
        <input
          type="number"
          id="expected_quantity"
          name="expected_quantity"
          min="1"
          step="1"
          value="{{ release_order_item.quantity|floatformat:0 }}"
          class="w-full px-3 py-2 border border-theme-border-primary rounded-xs shadow-sm focus:outline-none focus:ring-theme-primary focus:border-theme-primary"
          onClick="this.select();"
        >
      </div>
      <p class="mt-1 text-xs text-theme-text-secondary">{% trans "Enter the new expected quantity (must be greater than 0)" %}</p>
      {% if form.expected_quantity.errors %}
        <div class="text-red-500 text-xs mt-1">
          {% for error in form.expected_quantity.errors %}
            {{ error }}
          {% endfor %}
        </div>
      {% endif %}
    </div>

    <!-- Action Buttons -->
    <div class="flex justify-between space-x-3">
      <button
        type="button"
        class="py-1.5 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400"
        @click="$dispatch('close-modal')"
      >
        {% translate "Cancel" %}
      </button>
      <button
        type="submit"
        class="py-1.5 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-theme-action-create hover:bg-theme-action-create-hover focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
      >
        {% translate "Update" %}
      </button>
    </div>
  </div>
</form>

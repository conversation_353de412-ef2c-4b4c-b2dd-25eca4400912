{% load humanize %}
{% load i18n %}
{% load crispy_forms_tags %}
{% load static %}

<script src="{% static 'js/releases/release_order_item_add.js' %}" defer></script>

<form
  method="post"
  hx-post="{% url 'releases:orders:item_add' release_order.pk %}"
  hx-trigger="submit"
>
  {% csrf_token %}
  <div class="p-4">
    <!-- Release Order Information Section -->
    <div class="mb-4 border border-theme-border-primary rounded-xs overflow-hidden">
      <div class="bg-theme-bg-secondary px-4 py-1.5 border-b border-theme-border-primary">
        <h4 class="text-sm font-medium text-theme-text-primary">{% trans "Release Order Details" %}</h4>
      </div>
      <div class="p-4">
        <!-- Basic Release Order Information -->
        <div class="grid grid-cols-2 gap-3 sm:grid-cols-2 mb-2">
          <div class="col-span-2">
            <label class="block text-sm font-medium text-gray-700">{% trans "Consignor" %}</label>
            <div class="mt-1 text-sm text-gray-900">{{ release_order.consignee.consignor.display_name }}</div>
          </div>
          <div class="col-span-2">
            <label class="block text-sm font-medium text-gray-700">{% trans "Consignee" %}</label>
            <div class="mt-1 text-sm text-gray-900">{% if release_order.consignee %}{{ release_order.consignee.display_name }}{% else %}{% trans "Not specified" %}{% endif %}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Display non-field errors if any -->
    {% if form.non_field_errors or non_field_errors %}
      <div class="mb-4 p-3 bg-red-50 border border-red-300 rounded-md">
        <ul class="text-red-700 text-sm">
          {% for error in form.non_field_errors %}
            <li>{{ error }}</li>
          {% endfor %}
          {% for error in non_field_errors %}
            <li>{{ error }}</li>
          {% endfor %}
        </ul>
      </div>
    {% endif %}

    <!-- Item Form Section -->
    <div class="mb-4 border border-theme-border-primary rounded-xs overflow-hidden">
      <div class="flex bg-theme-bg-secondary px-4 py-1.5 border-b border-theme-border-primary gap-2">
        <h4 class="text-sm font-medium text-theme-text-primary">{{ release_order.system_number }}</h4>
      </div>
      <div class="p-4">
        <div class="space-y-4">
          <!-- Item Selection -->
          <div>
            <label for="id_item" class="block text-sm font-medium text-gray-700">{% trans "Item" %} <span class="text-red-500">*</span></label>
            {{ form.item }}
            {% if form.item.errors %}
              <div class="text-red-500 text-xs mt-1">{{ form.item.errors }}</div>
            {% endif %}
            {{ form.item_id }}
          </div>

          <!-- Batch Number -->
          <div>
            <label for="id_batch_no" class="block text-sm font-medium text-gray-700">{% trans "Batch Number" %}</label>
            {{ form.batch_no }}
            {% if form.batch_no.errors %}
              <div class="text-red-500 text-xs mt-1">{{ form.batch_no.errors }}</div>
            {% endif %}
            {{ form.batch_no_hidden }}
          </div>

          <!-- Expiry Date -->
          <div>
            <label for="id_expiry_date" class="block text-sm font-medium text-gray-700">{% trans "Expiry Date" %}</label>
            {{ form.expiry_date }}
            {% if form.expiry_date.errors %}
              <div class="text-red-500 text-xs mt-1">{{ form.expiry_date.errors }}</div>
            {% endif %}
            {{ form.expiry_date_hidden }}
          </div>

          <!-- Quantity and UOM -->
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label for="id_quantity" class="block text-sm font-medium text-gray-700">{% trans "Quantity" %} <span class="text-red-500">*</span></label>
              {{ form.quantity }}
              {% if form.quantity.errors %}
                <div class="text-red-500 text-xs mt-1">{{ form.quantity.errors }}</div>
              {% endif %}
            </div>
            <div>
              <label for="id_uom" class="block text-sm font-medium text-gray-700">{% trans "UOM" %}</label>
              {{ form.uom }}
              {% if form.uom.errors %}
                <div class="text-red-500 text-xs mt-1">{{ form.uom.errors }}</div>
              {% endif %}
              {{ form.uom_id }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex justify-between space-x-3">
      <button
        type="button"
        class="py-1.5 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400"
        @click="$dispatch('close-modal')"
      >
        {% translate "Cancel" %}
      </button>
      <button
        type="submit"
        class="py-1.5 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-theme-primary hover:bg-theme-primary-hover focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-theme-primary"
      >
        {% translate "Add Item" %}
      </button>
    </div>
  </div>
</form>


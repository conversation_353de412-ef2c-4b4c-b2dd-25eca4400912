{% load humanize %}
{% load i18n %}
{% load crispy_forms_tags %}

<form
  method="post"
  hx-post="{% url 'releases:orders:item_approve' release_order_item.pk %}"
  hx-trigger="submit"
>
  {% csrf_token %}
  <div class="p-4">
    <!-- Item Information Section -->
    <div class="mb-4 border border-theme-border-primary rounded-xs overflow-hidden">
      <div class="bg-theme-bg-secondary px-4 py-1.5 border-b border-theme-border-primary">
        <h4 class="text-sm font-medium text-theme-text-primary">{% trans "Item Details" %}</h4>
      </div>
      <div class="p-4">
        <!-- Basic Item Information -->
        <div class="grid grid-cols-2 gap-3 sm:grid-cols-2 mb-4">
          <div class="col-span-2">
            <label class="block text-sm font-medium text-gray-700">{% trans "Item Code" %}</label>
            <div class="mt-1 text-sm text-gray-900 font-semibold">{{ release_order_item.item.code }}</div>
          </div>
          <div class="col-span-2">
            <label class="block text-sm font-medium text-gray-700">{% trans "Item Name" %}</label>
            <div class="mt-1 text-sm text-gray-900">{{ release_order_item.item.name }}</div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">{% trans "Batch No" %}</label>
            <div class="mt-1 text-sm text-gray-900">{{ release_order_item.batch_no|default:"-" }}</div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">{% trans "Expiry Date" %}</label>
            <div class="mt-1 text-sm text-gray-900">{{ release_order_item.expiry_date|date:"d M Y"|default:"-" }}</div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">{% trans "Expected Quantity" %}</label>
            <div class="mt-1 text-sm text-gray-900">{{ release_order_item.quantity|floatformat:0|intcomma }} {{ release_order_item.uom.symbol }}</div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">{% trans "Picked Quantity" %}</label>
            <div class="mt-1 text-sm text-gray-900">{{ release_order_item.total_picker_system_quantity }} {{ release_order_item.uom.symbol }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Approval Information -->
    <div class="mb-4">
      <label for="approved_by" class="block text-sm font-bold text-theme-text-primary mb-1">{% trans "Approved By" %}</label>
      <div class="px-3 text-sm text-theme-text-primary">{{ request.user.get_display_name }}</div>
      <input type="hidden" name="approved_by" value="{{ request.user.pk }}">
    </div>

    <div class="mb-4">
      <label for="remark" class="block text-sm font-bold text-theme-text-primary mb-1">{% trans "Remarks" %}</label>
      <textarea id="remark" name="remark" rows="3"
                class="w-full px-3 py-2 border border-theme-border-primary rounded-xs shadow-sm focus:outline-none focus:ring-theme-primary focus:border-theme-primary"></textarea>
      <p class="mt-1 text-xs text-theme-text-secondary">{% trans "Optional: Add any notes about this approval" %}</p>
    </div>

    <!-- Action Buttons -->
    <div class="flex justify-between space-x-3">
      <button
        type="button"
        class="py-1.5 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400"
        @click="$dispatch('close-modal')"
      >
        {% translate "Cancel" %}
      </button>
      <button
        type="submit"
        class="py-1.5 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
      >
        {% translate "Approve" %}
      </button>
    </div>
  </div>
</form>

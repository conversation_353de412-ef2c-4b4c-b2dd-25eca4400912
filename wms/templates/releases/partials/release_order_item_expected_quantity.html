{% load i18n %}
{% load humanize %}

<div id="release-order-item-expected-quantity-{{ record.pk }}">
  {% if record.status == 'New' or record.status == 'Processing' %}
    <button
      hx-get="{% url 'releases:orders:item_expected_quantity_form' record.pk %}"
      hx-target="#modal-form-content"
      hx-swap="innerHTML"
      class="text-blue-600 hover:text-blue-800 font-medium"
      title="{% translate 'Update Expected Quantity' %}"
      @click="$dispatch('open-modal', { title: '{% trans 'Update Expected Quantity' %}' })">
      {{ record.total_picker_system_quantity|floatformat:0|intcomma }} / {{ record.expected_quantity|floatformat:0|intcomma }}
    </button>
  {% else %}
    <span class="text-gray-700">
      {{ record.total_picker_system_quantity|floatformat:0|intcomma }} / {{ record.expected_quantity|floatformat:0|intcomma }}
    </span>
  {% endif %}
</div>

{% load i18n %}

<div class="flex items-center">
  {{ value }}
  {% if release_order.status == "Ready To Print" %}
    <form method="post"
          hx-post="{% url 'releases:orders:proceed_print' release_order.pk %}"
          class="ml-2">
      {% csrf_token %}
      <button type="submit"
              class="flex text-theme-primary hover:text-theme-primary-hover transition-colors cursor-pointer"
              title="{% trans 'Proceed with Printing' %}">
        <svg xmlns="http://www.w3.org/2000/svg"
             class="h-6 w-6"
             viewBox="0 0 24 24"
             fill="none"
             stroke="currentColor"
             stroke-width="2"
             stroke-linecap="round"
             stroke-linejoin="round">
          <polyline points="6 9 6 2 18 2 18 9"></polyline>
          <path d="M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2"></path>
          <rect x="6" y="14" width="12" height="8"></rect>
        </svg>
      </button>
    </form>
  {% endif %}

  {% if release_order.status == "Ready To Release" %}
    <form method="post"
          hx-post="{% url 'releases:orders:proceed_release' release_order.pk %}"
          class="ml-2">
      {% csrf_token %}
      <button type="submit"
              class="flex text-theme-success hover:text-theme-success-hover transition-colors cursor-pointer"
              title="{% trans 'Complete Release Order' %}">
        <svg xmlns="http://www.w3.org/2000/svg"
             class="h-6 w-6"
             viewBox="0 0 24 24"
             fill="none"
             stroke="currentColor"
             stroke-width="2"
             stroke-linecap="round"
             stroke-linejoin="round">
          <rect x="3" y="8" width="18" height="12" rx="2"></rect>
          <path d="M16 8V5c0-1.1-.9-2-2-2H7a2 2 0 0 0-2 2v3"></path>
          <path d="M12 19v-7m0 0l-3 3m3-3l3 3"></path>
        </svg>
      </button>
    </form>
  {% endif %}
</div>

{% load i18n %}

<div class="flex items-center gap-2 justify-center" id="release-order-item-actions-{{ record.pk }}">
  {% if record.release_order.status == 'New' or record.release_order.status == 'Processing' %}
    {% if record.status == 'New' or record.status == 'Processing' %}
      <button
        hx-get="{% url 'releases:orders:item_pick_form' record.pk %}"
        hx-target="#modal-form-content"
        hx-swap="innerHTML"
        class="p-2 text-theme-status-success hover:text-theme-status-success/80 transition-colors duration-200 cursor-pointer"
        title="{% translate 'Pick Item' %}"
        @click="$dispatch('open-modal', { title: '{% trans 'Pick Item' %}' })">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor"
             stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M14 3v4a1 1 0 0 0 1 1h4"/>
          <path d="M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z"/>
          <path d="M9 17h6"/>
          <path d="M9 13h6"/>
        </svg>
      </button>

      {# Check if picked quantity equals expected quantity #}
      {% if record.total_picker_system_quantity == record.expected_quantity %}
        <button
          hx-get="{% url 'releases:orders:item_approve_form' record.pk %}"
          hx-target="#modal-form-content"
          hx-swap="innerHTML"
          class="p-2 text-theme-status-success hover:text-theme-status-success/80 transition-colors duration-200 cursor-pointer"
          title="{% translate 'Approve Picked Item' %}"
          @click="$dispatch('open-modal', { title: '{% trans 'Approve Picked Item' %}' })">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor"
               stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M20 6L9 17l-5-5"></path>
          </svg>
        </button>
      {% endif %}
    {% endif %}
    {% if record.total_picker_system_quantity == 0 %}
      {% with item_count=record.release_order.warehouse_release_order_items.count %}
        {% if item_count > 1 %}
          {# Delete Button - Only show when there's more than one item #}
          <button
            hx-get="{% url 'releases:orders:item_delete_form' record.pk %}"
            hx-target="#modal-form-content"
            hx-swap="innerHTML"
            class="p-2 text-red-600 hover:text-red-800 cursor-pointer"
            title="{% translate 'Delete Item' %}"
            @click="$dispatch('open-modal', { title: '{% trans 'Delete Release Item' %}' })">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none"
                 stroke="currentColor"
                 stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M18 6L6 18"></path>
              <path d="M6 6l12 12"></path>
            </svg>
          </button>
        {% endif %}
      {% endwith %}
    {% endif %}
  {% endif %}
</div>

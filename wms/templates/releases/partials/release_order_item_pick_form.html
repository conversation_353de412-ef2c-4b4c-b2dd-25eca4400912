{% load i18n %}
{% load crispy_forms_tags %}
{% load humanize %}

<form
  method="post"
  hx-post="{% url 'releases:orders:item_pick_form' release_order_item.pk %}"
  hx-trigger="submit"
>
  {% csrf_token %}
  <div class="p-0">
    <!-- Item Information Section -->
    <div class="mb-4 border border-theme-border-primary rounded-xs overflow-hidden">
      <div class="bg-theme-bg-secondary px-4 py-1.5 border-b border-theme-border-primary">
        <h4 class="text-sm font-medium text-theme-text-primary">{% trans "Item Details" %}</h4>
      </div>
      <div class="p-4">
        <!-- Basic Item Information -->
        <div class="grid grid-cols-2 gap-3 sm:grid-cols-2 mb-4">
          <div class="col-span-2">
            <label class="block text-sm font-medium text-gray-700">{% trans "Item Code" %}</label>
            <div class="mt-1 text-sm text-gray-900 font-semibold">{{ release_order_item.item.code }}</div>
          </div>
          <div class="col-span-2">
            <label class="block text-sm font-medium text-gray-700">{% trans "Item Name" %}</label>
            <div class="mt-1 text-sm text-gray-900">{{ release_order_item.item.name }}</div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">{% trans "Batch No" %}</label>
            <div class="mt-1 text-sm text-gray-900">{{ release_order_item.batch_no|default:"-" }}</div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">{% trans "Expiry Date" %}</label>
            <div class="mt-1 text-sm text-gray-900">{{ release_order_item.expiry_date|date:"d M Y"|default:"-" }}</div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">{% trans "Quantity" %}</label>
            <div class="mt-1 text-sm text-gray-900">{{ release_order_item.quantity|floatformat:0 }}</div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">{% trans "UOM" %}</label>
            <div class="mt-1 text-sm text-gray-900">{{ release_order_item.uom }}</div>
          </div>
        </div>

        <!-- Stock Balance Table -->
        <div class="mt-4">
          {% if warehouses_stock %}
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200 border border-gray-200 rounded-sm">
                <thead class="bg-gray-50">
                <tr>
                  <th scope="col"
                      class="px-4 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{% trans "Warehouse" %}</th>
                  <th scope="col"
                      class="px-4 py-1.5 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">{% trans "Available Qty" %}</th>
                </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                {% for stock_on_hand in warehouses_stock %}
                  <tr
                    class="{% if stock_on_hand.total_quantity < release_order_item.quantity %}bg-gray-50{% endif %} hover:bg-gray-100">
                    <td class="px-4 py-1.5 whitespace-nowrap text-sm text-gray-900">
                      <div
                        class="font-medium {% if 'defect' in stock_on_hand.warehouse_name|lower %}text-red-500{% else %}text-gray-900{% endif %}">
                        {{ stock_on_hand.warehouse_name }}
                        {% if stock_on_hand.full_warehouse_path %}
                          <div class="text-xs text-gray-500 mt-1">
                            {% for path_item in stock_on_hand.full_warehouse_path %}
                              {{ path_item }}{% if not forloop.last %} > {% endif %}
                            {% endfor %}
                          </div>
                        {% endif %}
                      </div>
                    </td>
                    <td class="px-4 py-1.5 whitespace-nowrap text-sm text-right">
                        <span
                          class="font-bold {% if 'defect' in stock_on_hand.warehouse_name|lower %}text-red-500{% else %}text-green-600{% endif %}">
                          {{ stock_on_hand.total_quantity|floatformat|intcomma }} {{ stock_on_hand.uom_symbol }}
                        </span>
                    </td>
                  </tr>
                {% endfor %}
                </tbody>
              </table>
            </div>
          {% else %}
            <div class="text-sm text-gray-500 text-center p-4 border border-gray-200 rounded-sm bg-gray-50">
              {% trans "No stock available with matching batch and expiry date" %}
            </div>
          {% endif %}
        </div>
      </div>
    </div>


    <!-- Form Fields -->
    <div class="mb-2">
      {{ form.warehouse.label_tag }}
      <div class="mt-1">
        {{ form.warehouse }}
      </div>
      {% if form.warehouse.errors %}
        <div class="text-red-500 text-xs mt-1">{{ form.warehouse.errors }}</div>
      {% else %}
        <p class="mt-1 text-xs text-gray-500">{% trans "Select the warehouse to pick from" %}</p>
      {% endif %}
    </div>

    <div class="mb-2">
      {{ form.pick_quantity.label_tag }}
      <div class="mt-1">
        {{ form.pick_quantity }}
      </div>
      {% if form.pick_quantity.errors %}
        <div class="text-red-500 text-xs mt-1">{{ form.pick_quantity.errors }}</div>
      {% else %}
        <p class="mt-1 text-xs text-gray-500">{% trans "Enter the quantity to pick" %}</p>
      {% endif %}
    </div>

    <!-- Action Buttons -->
    <div class="flex justify-between space-x-3">
      <button
        type="button"
        class="py-1.5 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400"
        @click="$dispatch('close-modal')"
      >
        {% translate "Cancel" %}
      </button>
      <button
        type="submit"
        class="py-1.5 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-theme-action-create hover:bg-theme-action-create-hover focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
      >
        {% translate "Pick Item" %}
      </button>
    </div>
  </div>
</form>

<script>
  (function() {
    // Function to position cursor at the end of the quantity field
    function positionCursorAtEnd() {
      const quantityField = document.getElementById('id_pick_quantity');
      if (quantityField) {
        quantityField.focus();
        const valueLength = quantityField.value.length;
        quantityField.type = 'text';
        quantityField.setSelectionRange(valueLength, valueLength);
        quantityField.type = 'number';
      }
    }

    // Initialize and set up event listeners
    document.addEventListener('htmx:afterSettle', function() {
      const quantityField = document.getElementById('id_pick_quantity');
      if (quantityField) {
        positionCursorAtEnd();
      }
    });

    // Handle Select2 cleanup
    document.addEventListener('htmx:beforeSwap', function(event) {
      const warehouseSelect = document.getElementById('id_warehouse');
      if (warehouseSelect && $(warehouseSelect).data('select2')) {
        try {
          $(warehouseSelect).select2('destroy');
        } catch (e) {
          console.log('Select2 not initialized yet or already destroyed');
        }
      }
    });
  })();
</script>

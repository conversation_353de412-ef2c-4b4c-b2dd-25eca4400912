{% extends "base.html" %}

{% load static %}
{% load crispy_forms_tags %}
{% load i18n %}

{% block content %}
  <!-- Form Section -->
  <form class="form-horizontal" method="post" action="." enctype="multipart/form-data" autocomplete="off">
    {{ form.media }}

    {% csrf_token %}
    <section class="max-w-max mx-auto py-6">
      <!-- Title Section -->
      <div class="mb-6">
        <h1 class="text-2xl font-semibold text-theme-text-primary">Import Bulk Update</h1>
        {% if section_desc %}<p class="mt-2 text-theme-text-secondary">{{ section_desc }}</p>{% endif %}
      </div>
      {% include "tailwind/errors.html" %}
      {% include '_components/color_bar.html' %}
      <!-- Basic Information Section -->
      <section class="mx-auto mb-6">
        {% include '_components/section_header.html' with header_title="Basic Information" %}
        <div class="bg-white border-1 border-gray-200 rounded-none overflow-hidden">
          <div class="p-0">
            <div class="space-y-6">
              <div class="bg-theme-bg-secondary/10 rounded-none">
                <div class="grid grid-cols-1 divide-y divide-gray-200 ">
                  <label for="id_download_example" class="col-sm-4 col-form-label">Download Example Excel</label>
                  <div class="col-sm-8">
                    <div class="form-control-plaintext"><a href="{% static 'download/EXAMPLE_WRO_BULK_UPDATE.xlsx' %}">EXAMPLE_WRO_BULK_UPDATE.xlsx</a></div>
                  </div>
                  {{ form.upload_excel_file|as_crispy_field }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      <!-- Form Actions -->
      <div class="flex items-center justify-between mt-4">
        {% block form_buttons %}
          {% include "_components/button_cancel.html" with link=cancel_url text=cancel_text|default:"Cancel" %}
          {% include "_components/button_submit.html" with action_type=action_type text=submit_text|default:"Save" %}
        {% endblock form_buttons %}
      </div>
    </section>
  </form>
{% endblock content %}
{% comment %}
{% block extra_js %}
  <script src="{% static 'js/releases/release_order_form.js' %}"></script>
{% endblock extra_js %}
{% endcomment %}

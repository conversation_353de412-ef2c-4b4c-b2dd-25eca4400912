{% load static %}
{% load thumbnail %}

{% block content %}
  <div class="card card-maroon card-outline shadow-lg rounded-lg">
    <div class="card-body text-center">
      <p class="text-xl bg-dark text-white py-5 rounded-top">
        Thank You for Importing the WRO Excel.<br />
        <small class="text-muted">{% now 'SHORT_DATETIME_FORMAT' %} 😀</small>
      </p>

      <div class="text-left pl-3 text-lg bg-dark text-white py-4 rounded-bottom">
        <div class="row mb-3">
          <div class="col-6">
            <strong>Total WRO updated:</strong>
          </div>
          <div class="col-6">
            {{ summary.delivery_number_count|default:0 }}
          </div>
        </div>

        <hr class="border-light">

        <div class="row mb-3">
          <div class="col-6">
            <details>
              <summary class="text-lg cursor-pointer"><strong>Skipped total number of lines with invalid Request Delivery Date values:</strong> </summary>
              <div class="pl-4 py-3">
                {% for info in summary.skip_delivery_number_invalid_request_delivery_date_info %}
                  {% for line_number, info in info.items %}
                    <p>{{ line_number }}: {{ info }}</p>
                  {% endfor %}
                {% endfor %}
              </div>
            </details>
          </div>
          <div class="col-6">
            {{ summary.skip_delivery_number_invalid_request_delivery_date|default:0 }}
          </div>
        </div>

        <div class="row mb-3">
          <div class="col-6">
            <details>
              <summary class="text-lg cursor-pointer"><strong>Skipped total number of blank lines from column A (blank WRO):</strong> </summary>
              <div class="pl-4 py-3">
                {% for info in summary.skip_rows_with_empty_delivery_number_info %}
                  {% for line_number, info in info.items %}
                    <p>{{ line_number }}</p>
                  {% endfor %}
                {% endfor %}
              </div>
            </details>
          </div>
          <div class="col-6">
            {{ summary.skip_rows_with_empty_delivery_number|default:0 }}
          </div>
        </div>

        <div class="row mb-3">
          <div class="col-6">
            <details>
              <summary class="text-lg cursor-pointer"><strong>Skipped total number of invalid WRO value from column A:</strong> </summary>
              <div class="pl-4 py-3">
                {% for info in summary.skip_rows_with_invalid_delivery_number_info %}
                  {% for line_number, info in info.items %}
                    <p>{{ line_number }}: {{ info }}</p>
                  {% endfor %}
                {% endfor %}
              </div>
            </details>
          </div>
          <div class="col-6">
            {{ summary.skip_rows_with_invalid_delivery_number|default:0 }}
          </div>
        </div>

        <div class="row mb-3">
          <div class="col-6">
            <details>
              <summary class="text-lg cursor-pointer"><strong>Skipped total number of duplicated WRO's DN from column A:</strong> </summary>
              <div class="pl-4 py-3">
                {% for info in summary.skip_delivery_number_duplicated_info %}
                  {% for line_number, info in info.items %}
                    <p>{{ line_number }}: {{ info }}</p>
                  {% endfor %}
                {% endfor %}
              </div>
            </details>
          </div>
          <div class="col-6">
            {{ summary.skip_delivery_number_duplicated|default:0 }}
          </div>
        </div>

      </div>
      <div class="row">
        <div class="col text-left">
          <a href="{% url 'releases:orders:import_bulk_update' %}" class="btn mt-3 btn-success btn-lg"><i class="fas fa-plus fa-fw"></i> Import Bulk Update</a>
        </div>
        <div class="col text-right">
          <a href="{% url 'releases:orders:list' %}" class="ml-3 btn mt-3 btn-primary btn-lg"> Warehouse Release Orders</a>
        </div>
      </div>
    </div>
  </div>
{% endblock content %}

{% block server_side_buttons %} buttons: [], {% endblock server_side_buttons %}


{% load static %}
<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <!-- Bootstrap v5.2.3 -->
    <link href="{% static 'pdfs/css/bootstrap.min.css' %}" media="print" rel="stylesheet">
    <link href="{% static 'pdfs/css/arrival_notice.css' %}" media="print" rel="stylesheet">
    <title>Warehouse Arrival Notice - {{ object.system_number }}</title>
    <meta name="description" content="Warehouse Arrival Notice - {{ object.system_number }}">
  </head>

  <body>
    <header id="header" class="header">
      <div>
        <div class="row">
          <div class="col-10 fs-65rem">
            <h2>SINOFLEX LOGISTICS SDN BHD <small class="fs-6rem">(974297-A)</small></h2>
            <p class="lh-sm fs-65rem">
              No 38, Jalan 15/22, <PERSON><PERSON>, Section 15,<br />
              40200, Shah <PERSON>, Se<PERSON>, Malaysia.<br />
              Tel: +603 5524 3793
            </p>
          </div>
          <div class="col-2">
            <img src="{% static 'images/sinoflex-logo.png' %}" class="img-fluid" alt="Sinoflex">
          </div>
        </div>

        <div class="row mt-2">
          <div class="col text-center">
            <h2>WAREHOUSE ARRIVAL NOTICE</h2>
          </div>
        </div>
        <div class="row justify-content-end fs-6rem">
          <div class="col" style="padding-right: 12px;">

            <div class="row">
              <div class="col-3 p-1" style="border: 1px solid #dee2e6;">Delivered By</div>
              <div class="col p-1" style="
                border-top: 1px solid #dee2e6;
                border-right: 1px solid #dee2e6;
                border-bottom: 1px solid #dee2e6;
              ">{{ object.consignor.company_name }}</div>
            </div>
            <div class="row">
              <div class="col-3 p-1" style="
                border-left: 1px solid #dee2e6;
                border-bottom: 1px solid #dee2e6;
                border-right: 1px solid #dee2e6;
              ">Attention</div>
              <div class="col p-1" style="
                border-bottom: 1px solid #dee2e6;
                border-right: 1px solid #dee2e6;
              ">{{ object.consignor.primary_contact.get_formal_full_name }}</div>
            </div>
            <div class="row">
              <div class="col-3 p-1" style="
                border-left: 1px solid #dee2e6;
                border-bottom: 1px solid #dee2e6;
                border-right: 1px solid #dee2e6;
              ">Contact No.</div>
              <div class="col p-1" style="
                border-bottom: 1px solid #dee2e6;
                border-right: 1px solid #dee2e6;
              ">{{ object.consignor.primary_contact.phone }}</div>
            </div>

            <div class="row mt-3 fs-5rem">
              <div class="col-2 p-1" style="
                border: 1px solid #dee2e6;
              ">Container No.</div>
              <div class="col p-1" style="
                border-top: 1px solid #dee2e6;
                border-right: 1px solid #dee2e6;
                border-bottom: 1px solid #dee2e6;
              ">{{ object.container_number }}</div>
              <div class="col-2 p-1" style="
                border-top: 1px solid #dee2e6;
                border-bottom: 1px solid #dee2e6;
                border-right: 1px solid #dee2e6;
              ">Seal No.</div>
              <div class="col p-1" style="
                border-top: 1px solid #dee2e6;
                border-bottom: 1px solid #dee2e6;
                border-right: 1px solid #dee2e6;
              ">{{ object.container_seal_number }}</div>
            </div>
            <div class="row fs-5rem">
              <div class="col-2 p-1" style="
                border-left: 1px solid #dee2e6;
                border-bottom: 1px solid #dee2e6;
                border-right: 1px solid #dee2e6;
              ">Remark</div>
              <div class="col p-1" style="
                border-bottom: 1px solid #dee2e6;
                border-right: 1px solid #dee2e6;
              ">{{ object.remark }}</div>
            </div>

          </div>
          <div class="col" style="padding-left: 12px;">

            <div class="row">
              <div class="col-6 text-start p-1" style="border: 1px solid #dee2e6;">Warehouse Arrival Notice No.</div>
              <div class="col text-start p-1" style="
                border-top: 1px solid #dee2e6;
                border-right: 1px solid #dee2e6;
                border-bottom: 1px solid #dee2e6;
              "><strong>{{ object.system_number }}</strong></div>
            </div>
            <div class="row">
              <div class="col-6 text-start p-1" style="
                border-left: 1px solid #dee2e6;
                border-bottom: 1px solid #dee2e6;
                border-right: 1px solid #dee2e6;
              ">Issue Date</div>
              <div class="col text-start p-1" style="
                border-bottom: 1px solid #dee2e6;
                border-right: 1px solid #dee2e6;
              ">{{ object.created|date:"SHORT_DATE_FORMAT" }}</div>
            </div>
            <div class="row">
              <div class="col-6 text-start p-1" style="
                border-left: 1px solid #dee2e6;
                border-bottom: 1px solid #dee2e6;
                border-right: 1px solid #dee2e6;
              ">Issue By</div>
              <div class="col text-start p-1" style="
                border-bottom: 1px solid #dee2e6;
                border-right: 1px solid #dee2e6;
              ">{{ object.created_by.get_display_name }}</div>
            </div>
            <div class="row">
              <div class="col-6 text-start p-1" style="
                border-left: 1px solid #dee2e6;
                border-bottom: 1px solid #dee2e6;
                border-right: 1px solid #dee2e6;
              ">Customer Reference</div>
              <div class="col text-start p-1" style="
                border-bottom: 1px solid #dee2e6;
                border-right: 1px solid #dee2e6;
              ">{{ object.customer_reference }}</div>
            </div>
            {% if object.consignor.code == "FMCSB" %}
            <div class="row">
              <div class="col-6 text-start p-1" style="
                border-left: 1px solid #dee2e6;
                border-bottom: 1px solid #dee2e6;
                border-right: 1px solid #dee2e6;
              ">Inbound Delivery No</div>
              <div class="col text-start p-1" style="
                border-bottom: 1px solid #dee2e6;
                border-right: 1px solid #dee2e6;
              ">{{ object.consignor_inbound_delivery_no }}</div>
            </div>
            {% endif %}
            <div class="row">
              <div class="col-6 text-start p-1" style="
                border-left: 1px solid #dee2e6;
                border-bottom: 1px solid #dee2e6;
                border-right: 1px solid #dee2e6;
              ">Arrival Date</div>
              <div class="col text-start p-1" style="
                border-bottom: 1px solid #dee2e6;
                border-right: 1px solid #dee2e6;
              ">{{ object.arrival_datetime|date:"SHORT_DATE_FORMAT" }}</div>
            </div>
            <div class="row">
              <div class="col-6 text-start p-1" style="
                border-left: 1px solid #dee2e6;
                border-bottom: 1px solid #dee2e6;
                border-right: 1px solid #dee2e6;
              ">Page</div>
              <div class="col text-start p-1" style="
                border-bottom: 1px solid #dee2e6;
                border-right: 1px solid #dee2e6;
              "><span class="currentPage"></span></div>
            </div>

          </div>
        </div>
      </div>
    </header>

    <div class="mainContent">
      <div>
        <div class="row mb-3">
          <div class="col-12">
            <table class="table table-bordered table-sm mb-4 fs-65rem">
              <thead class="">
                <tr>
                  <th class="text-center" style="width: 10px">NO.</th>
                  <th class="text-center">PRODUCT CODE</th>
                  <th class="text-center">PRODUCT DESCRIPTION</th>
                  <th class="text-center">BATCH NO.</th>
                  <th class="text-center">REASON</th>
                  <th class="text-center">QUANTITY</th>
                  <th class="text-center">UNIT</th>
                </tr>
              </thead>
              <tbody>
                {% for item in object.goodsreceivednoteitem_set.all %}
                  <tr>
                    <td class="font-weight-bold">{{ counter|default:item.get_position }}.</td>
                    <td>{{ item.item.code }}</td>
                    <td>{{ item.item.name }}</td>
                    <td></td>
                    <td></td>
                    <td class="text-end">TOTAL: {{ item.get_total_received_rejected_quantity }}</td>
                    <td>{{ item.uom.symbol }}</td>
                  </tr>
                    {% for distinct_item_batch_no in item.get_distinct_item_batch_no_list %}
                      <tr>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td>{{ distinct_item_batch_no.batch_no }}</td>
                        <td></td>
                        <td class="text-end">{{ distinct_item_batch_no.total_quantity }}</td>
                        <td>{{ item.uom.symbol }}</td>
                      </tr>
                    {% endfor %}
                    {% for distinct_defect_item_batch_no in item.get_distinct_defect_item_batch_no_list %}
                      <tr>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td>{{ distinct_defect_item_batch_no.batch_no }}</td>
                        <td>{{ distinct_defect_item_batch_no.reason }}</td>
                        <td class="text-end">{{ distinct_defect_item_batch_no.total_quantity }}</td>
                        <td>{{ item.uom.symbol }}</td>
                      </tr>
                    {% endfor %}
                {% endfor %}
                  <tr>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td class="text-end">Total: </td>
                    <td class="text-end">{{ object.total_all_stockin_system_quantity }}</td>
                    <td>EA</td>
                  </tr>
              </tbody>
            </table>
          </div>
        </div>

      </div>
    </div>

    {% comment "no footer for now" %}
      <footer>
        <div>Any complaint on goods delivered will have to be recorded on this D/O. No claim will be entertained if this D/O is signed with good received in goodorder and condition.</div>
      </footer>
    {% endcomment %}
  </body>
</html>

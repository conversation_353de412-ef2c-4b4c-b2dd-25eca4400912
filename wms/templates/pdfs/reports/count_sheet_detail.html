{% load static %}
{% load custom_tags %}

{% block layout_style %}
  <style>
    /* Switch to Landscape orientation */
    @page {
      size: letter landscape;
      margin: 2cm;

      @top-left {
        content: "DocID: {{ object.doc_id }} \A \A Date: ______________";
        font-size: 12px;
        white-space: pre; /* Allows line breaks (\A) */
        font-weight: bold;
      }

      @top-right {
        content: "Counter: ______________ \A \A Checker: ______________";
        font-size: 12px;
        white-space: pre; /* Allows line breaks (\A) */
        font-weight: bold;
      }
    }

    body {
      margin-top: 30px; /* Leave room for the header */
    }

    table {
      border-collapse: collapse;
      width: 100%;
      font-size: 14px; /* Smaller font size */
    }
    th, td {
      border: 1px solid black; /* Explicit solid border */
      padding: 2px 4px; /* Reduce padding */
      text-align: center; /* Optional: align text */
      line-height: 1.2; /* Compact line height */
    }
  </style>
{% endblock %}

<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <title>Count Sheet - {{ object.doc_id }}</title>
    <meta name="description" content="Count Sheet - {{ object.doc_id }}">
  </head>

  <body>
    <div>
      <div class="col-12">
        <table class="table table-bordered mb-2">
          <thead class="">
            <tr>
              <th class="text-center">Location</th>
              <th class="text-center">ProductCode</th>
              <th class="text-center" style="width: 35%">Description</th>
              <th class="text-center">Batch</th>
              <th class="text-center">Expiry Date</th>
              <th class="text-center" style="width: 8%">Qty</th>
              <th class="text-center" style="width: 20%">Remarks</th>
            </tr>
          </thead>
          <tbody>
            {% for row in object.get_all_rackstorages_order_by_levels %}
              {% get_count_sheet_pdf_data row as data %}
              <tr>
                <td>{{ data.location }}</td>
                <td>{{ data.product_code }}</td>
                <td>{{ data.description }}</td>
                <td>{{ data.batch_no }}</td>
                <td>{{ data.expiry_date }}</td>
                <td></td>
                <td></td>
              </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
  </body>
</html>

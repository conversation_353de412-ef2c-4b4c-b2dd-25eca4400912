{% load static %}

{% block layout_style %}
  <style>
    @page {
      @top-left {
        content: "Date:";
        font-weight: bold;
      }
    }

    body {
      margin-top: 30px; /* Leave room for the header */
    }

    table {
      border-collapse: collapse;
      width: 100%;
      font-size: 14px; /* Smaller font size */
    }

    th, td {
      border: 1px solid black; /* Explicit solid border */
      padding: 2px 4px; /* Reduce padding */
      text-align: center; /* Optional: align text */
      line-height: 1.2; /* Compact line height */
    }
  </style>
{% endblock %}

<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <title>Count Sheet Index</title>
    <meta name="description" content="Count Sheet Index">
  </head>

  <body>
    <div>
      <div class="col-12">
        <table class="table table-bordered mb-2">
          <thead>
            <tr>
              <th class="text-center" style="width: 5%">No</th>
              <th class="text-center">DocID</th>
              <th class="text-center">Team</th>
              <th class="text-center">Status</th>
              <th class="text-center" style="width: 30%">Recount</th>
            </tr>
          </thead>
          <tbody>
            {% for row in object_list %}
              <tr>
                <td>{{ forloop.counter }}</td>
                <td>{{ row.doc_id }}</td>
                <td></td>
                <td></td>
                <td>
                  <div style="display: flex; flex-direction: row; justify-content: space-between; gap: 10px;">
                    <input type="checkbox" name="remark1" disabled style="transform: scale(1.0);margin-right: 20%;">
                    <input type="checkbox" name="remark2" disabled style="transform: scale(1.0);margin-right: 20%;">
                    <input type="checkbox" name="remark3" disabled style="transform: scale(1.0);margin-right: 20%;">
                  </div>
                </td>
              </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
  </body>
</html>

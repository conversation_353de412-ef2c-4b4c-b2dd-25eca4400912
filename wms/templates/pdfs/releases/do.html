{% load static %}
<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <!-- Bootstrap v5.2.3 -->
    <link href="{% static 'pdfs/css/bootstrap.min.css' %}" media="print" rel="stylesheet">
    <link href="{% static 'pdfs/css/do.css' %}" media="print" rel="stylesheet">
    <title>Delivery Order - {{ object.system_number }}</title>
    <meta name="description" content="Delivery Order - {{ object.system_number }}">
  </head>

  <body>
    <header id="header" class="header">
      <div>
        <div class="row">
          <div class="col-10 fs-65rem">
            <h2>SINOFLEX LOGISTICS SDN BHD <small class="fs-6rem">(974297-A)</small></h2>
            <p class="lh-sm fs-65rem">
              No 38, Jalan 15/22, <PERSON><PERSON>, Section 15,<br />
              40200, <PERSON>, Selangor, Malaysia.<br />
              Tel: +603 5524 3793
            </p>
          </div>
          <div class="col-2">
            <img src="{% static 'images/sinoflex-logo.png' %}" class="img-fluid" alt="Sinoflex">
          </div>
        </div>

        <div class="row mt-1">
          <div class="col text-center">
            <h2>DELIVERY ORDER</h2>
          </div>
        </div>
        <div class="row justify-content-end fs-6rem">
          <div class="col-5">
            <div class="row">
              <div class="col text-end p-1" style="
                border: 1px solid #dee2e6;
              ">Warehouse Release Order No.</div>
              <div class="col text-center p-1" style="
                border-top: 1px solid #dee2e6;
                border-right: 1px solid #dee2e6;
                border-bottom: 1px solid #dee2e6;
              "><strong>{{ object.system_number }}</strong></div>
            </div>
            <div class="row">
              <div class="col text-end p-1" style="
                border-left: 1px solid #dee2e6;
                border-right: 1px solid #dee2e6;
                border-bottom: 1px solid #dee2e6;
              ">Delivery Order No.</div>
              <div class="col text-center p-1" style="
                border-right: 1px solid #dee2e6;
                border-bottom: 1px solid #dee2e6;
              "><strong>{{ object.deliveryorder.system_number }}</strong></div>
            </div>
            <div class="row">
              <div class="col text-end p-1" style="
                border-left: 1px solid #dee2e6;
                border-bottom: 1px solid #dee2e6;
                border-right: 1px solid #dee2e6;
              ">Page</div>
              <div class="col text-center p-1" style="
                border-bottom: 1px solid #dee2e6;
                border-right: 1px solid #dee2e6;
              "><span class="currentPage"></span></div>
            </div>
          </div>
        </div>
      </div>
    </header>

    <div class="mainContent">
      <div>
        <div class="row mb-2">
          <div class="col">
            <div class="row">
              <div class="col">
                SHIPPER ADDRESS
              </div>
            </div>
            <div class="row">
              <div class="col">
                <table class="table table-sm table-bordered mt-1 mb-0 fs-65rem">
                  {% if object.show_shipper_info is True%}
                    <tbody>
                      <tr>
                        <td style="height: 70px;" colspan="2">
                          {{ object.shipper_address|upper|default:DISPLAY_EMPTY_VALUE }}
                        </td>
                      </tr>
                      <tr>
                        <td colspan="2">
                          ATTN: {{ object.shipper_attn|default:DISPLAY_EMPTY_VALUE }}
                        </td>
                      </tr>
                      <tr>
                        <td class="col-6">
                          TEL: {{ object.shipper_phone|default:DISPLAY_EMPTY_VALUE }}
                        </td>
                        <td class="col-6">
                          HP: {{ object.shipper_mobile|default:DISPLAY_EMPTY_VALUE }}
                        </td>
                      </tr>
                    </tbody>
                  {% else %}
                    <tbody>
                      <tr>
                        <td style="height: 70px;" colspan="2">
                          ---
                        </td>
                      </tr>
                      <tr>
                        <td colspan="2">
                          ATTN: ---
                        </td>
                      </tr>
                      <tr>
                        <td class="col-6">
                          TEL: ---
                        </td>
                        <td class="col-6">
                          HP: ---
                        </td>
                      </tr>
                    </tbody>
                  {% endif %}
                </table>
              </div>
            </div>
          </div>
          <div class="col-1" style="width: 20px;"></div>
          <div class="col">
            <div class="row">
              <div class="col">
                DELIVERY INSTRUCTION & CONSIGNEE ADDRESS
              </div>
            </div>
            <div class="row">
              <div class="col">
                <table class="table table-sm table-bordered mt-1 mb-0 fs-65rem">
                  <tbody>
                    <tr>
                      <td style="height: 70px;" colspan="2">
                        {{ object.consignee.display_name }} <br / >
                        {{ object.shipping_address|upper|default:DISPLAY_EMPTY_VALUE }}
                      </td>
                    </tr>
                    <tr>
                      <td colspan="2">
                        ATTN: {{ object.shipping_attn|default:DISPLAY_EMPTY_VALUE }}
                      </td>
                    </tr>
                    <tr>
                      <td class="col-6">
                        TEL: {{ object.shipping_phone|default:DISPLAY_EMPTY_VALUE }}
                      </td>
                      <td class="col-6">
                        HP: {{ object.shipping_mobile|default:DISPLAY_EMPTY_VALUE }}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-12">
            <table class="table table-sm table-bordered mb-2 fs-65rem">
              <tbody>
                <tr>
                  {% if object.consignee.consignor.code == 'FMCSB' %}
                    <td class="col-3 text-center">CUSTOMER DOCUMENT NO.</td>
                    <td class="col-3 text-center">CUSTOMER INBOUND DELIVERY NO.</td>
                    <td class="col-3 text-center">CUSTOMER REFERENCE</td>
                    <td class="col-3 text-center">DELIVERY DATE</td>
                  {% else %}
                    <td class="col-4 text-center">CUSTOMER DOCUMENT NO.</td>
                    <td class="col-4 text-center">CUSTOMER REFERENCE</td>
                    <td class="col-4 text-center">DELIVERY DATE</td>
                  {% endif %}
                </tr>
                <tr>
                  <td class="text-center">{{ object.customer_document_no|default:DISPLAY_EMPTY_VALUE }}</td>
                  {% if object.consignee.consignor.code == 'FMCSB' %}
                    <td class="text-center">{{ object.consignor_inbound_delivery_no|default:DISPLAY_EMPTY_VALUE }}</td>
                  {% endif %}
                  <td class="text-center">{{ object.customer_reference|default:DISPLAY_EMPTY_VALUE }}</td>
                  <td class="text-center">{{ object.warehouse_release_order_items.first.warehouse_release_order_stockouts.first.transaction.transaction_datetime|default:DISPLAY_EMPTY_VALUE|date:"SHORT_DATE_FORMAT" }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {% if object.consignee.consignor.code == 'MMM' %}
          <div class="row">
            <div class="col-12">
              <table class="table table-sm table-bordered mb-2 fs-65rem">
                <tbody>
                  <tr>
                    <td class="col-4 text-center">CUST. PPL NO.</td>
                    <td class="col-4 text-center">CUST. SALES ORDER NO.</td>
                    <td class="col-4 text-center">CUST. REQUISITION NO.</td>
                  </tr>
                  <tr>
                    <td class="text-center">{{ object.consignor_picking_list_no|default:DISPLAY_EMPTY_VALUE }}</td>
                    <td class="text-center">{{ object.consignor_sales_order_no|default:DISPLAY_EMPTY_VALUE }}</td>
                    <td class="text-center">{{ object.consignor_customer_requisition_no|default:DISPLAY_EMPTY_VALUE }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        {% endif %}

        <div class="col-12">
          <table class="table table-bordered mb-2">
            <thead class="">
              <tr>
                <th class="text-center" style="width: 10px">NO.</th>
                <th class="text-center">CODE</th>
                <th class="text-center">PRODUCT DESCRIPTION</th>
                <th class="text-center">Batch</th>
                <th class="text-center">QTY</th>
                <th class="text-center">UNIT</th>
              </tr>
            </thead>
            <tbody>
              {% for delivery_order_item in object.get_ascending_sort_wro_items %}
                <tr>
                  <td class="font-weight-bold">{{ counter|default:delivery_order_item.get_position }}.</td>
                  <td>{{ delivery_order_item.item.code }}</td>
                  <td>{{ delivery_order_item.item.name }}</td>
                  <td style="text-align: right;">{{ delivery_order_item.batch_no }}</td>
                  <td style="text-align: right;">{{ delivery_order_item.total_picker_system_quantity }}</td>
                  <td>{{ delivery_order_item.uom.symbol }}</td>
                </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>

        <div class="row">
          <div class="col-6">
            <table class="table table-sm table-bordered mb-2 fs-65rem">
              <tbody>
                <tr>
                  <td style="height: 84.6px;">
                    REMARK (IF ANY):<br />
                    {{ object.remark|default:DISPLAY_EMPTY_VALUE }}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div class="col-1">&nbsp;</div>
          <div class="col-5 justify-content-end">
            <table class="table table-sm table-bordered mb-2 fs-65rem">
              <tbody>
                <tr>
                  <td class="text-end">TOTAL QUANTITY</td>
                  <td style="text-align: right;">{{ object.total_all_picker_system_quantity}}</td>
                </tr>
                <tr>
                  <td class="text-end">TOTAL CARTONS</td>
                  <td style="text-align: right;">{{ object.total_cartons}}</td>
                </tr>
                <tr>
                  <td class="text-end">TOTAL WEIGHT</td>
                  <td style="text-align: right;">{{ object.total_weight}}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <div class="row">
          <div class="col-12 mb-1 fs-55rem">
              Any complaint on goods delivered will have to be recorded on this D/O. No claim will be entertained if this D/O is signed with good received in good order and condition.
          </div>
        </div>

        <div class="row">
          <div class="col-8">
            <table class="table table-sm table-bordered mt-1 mb-0">
              <tbody>
                <tr>
                  <td class="border-top-0">
                    <table class="table table-sm table-borderless mt-1 mb-0 fs-65rem">
                      <tr>
                        <td class="border-bottom-0" colspan="2" style="height: 70px;">
                          Acknowledge receipt of the above products in good order and conditions
                        </td>
                      </tr>
                      <tr>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                      </tr>
                      <tr>
                        <td> ________________________________ </td>
                        <td> ________________________________ </td>
                      </tr>
                      <tr>
                        <td class="py-0">Received by Name & SIGN</td>
                        <td class="py-0">COMPANY RUBBER STAMP</td>
                      </tr>
                      <tr>
                        <td class="pt-0">Received date:</td>
                        <td class="pt-0">&nbsp;</td>
                      </tr>
                    </table>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div class="col-1" style="width: 16px;">&nbsp;</div>
          <div class="col-3">
            <table class="table table-sm table-borderless ml-3 mt-1 mb-0 fs-65rem">
              <tbody>
                <tr>
                  <td class="border-top-0">
                    <table class="table table-sm table-borderless mt-1 mb-0">
                      <tr>
                        <td class="border-bottom-0" style="height: 98px;">&nbsp;</td>
                      </tr>
                      <tr>
                        <td> ________________________________ </td>
                      </tr>
                      <tr>
                        <td class="py-0 text-start">Issue by Name & Sign</td>
                      </tr>
                      <tr>
                        <td class="pt-0 text-start">E. & O.E.</td>
                      </tr>
                    </table>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

      </div>
    </div>

    {% comment "no footer for now" %}
      <footer>
        <div>Any complaint on goods delivered will have to be recorded on this D/O. No claim will be entertained if this D/O is signed with good received in goodorder and condition.</div>
      </footer>
    {% endcomment %}
  </body>
</html>

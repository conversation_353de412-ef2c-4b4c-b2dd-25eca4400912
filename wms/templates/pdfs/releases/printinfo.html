{% load static %}
<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <!-- Bootstrap v5.2.3 -->
    <link href="{% static 'pdfs/css/bootstrap.min.css' %}" media="print" rel="stylesheet">
    <link href="{% static 'pdfs/css/printinfo.css' %}" media="print" rel="stylesheet">
  </head>

  <body>
    <header id="header" class="header">
      <div>
        <br/>
        <div class="row">
          <div class="col-10 fs-65rem">
            <h2 class="mb-3">{{ object.system_number }}
              {% if object.deliveryorder.system_number %}
               | {{ object.deliveryorder.system_number|default:DISPLAY_EMPTY_VALUE }}
              {% endif%}
              {% if object.consignor_outbound_delivery_no %}
               | {{ object.consignor_outbound_delivery_no|default:DISPLAY_EMPTY_VALUE }}
              {% endif%}
            </h2>
            Page: <span class="currentPage"></span>
          </div>
          <div class="col-2">
            {% if object.status == "Completed" %}
              <img src="{% static 'images/copy_box.png' %}" class="img-fluid" alt="Sinoflex">
            {% endif %}
          </div>
        </div>
      </div>
    </header>

    <div class="mainContent">
      <div class="row">
        <div class="col">
          <h3>Basic Information</h3>

          <table class="table table-bordered table-sm mb-1">
            <tr>
              <th>Issued By</th>
              <td>{{ object.issued_by }}</td>
              <th>Consignee</th>
              <td>{{ object.consignee }}</td>
            </tr>
            <tr>
              <th>Status</th>
              <td>{{ object.status }}</td>
              <th>Release Date Time</th>
              <td>{{ object.release_datetime }}</td>
            </tr>
            <tr>
              <th>Customer Document No.</th>
              <td>{{ object.customer_document_no|default:DISPLAY_EMPTY_VALUE }}</td>
              <th>Customer Reference</th>
              <td>{{ object.customer_reference|default:DISPLAY_EMPTY_VALUE }}</td>
            </tr>
            <tr>
              <th>Warehouses</th>
              <td>
                <ul class="warehouses-list mb-0">
                  {% for warehouse in object.warehouses.all %}
                    <li>{{ warehouse.name }}</li>
                  {% endfor %}
                </ul>
              </td>
              <th>Outbound Delivery No.</th>
              <td>{{ object.consignor_outbound_delivery_no|default:DISPLAY_EMPTY_VALUE }}</td>
            </tr>
          </table>

          <table class="table table-bordered table-sm">
            <tr>
              <th>Remark:<br />{{ object.remark|default:DISPLAY_EMPTY_VALUE|linebreaksbr }}</th>
            </tr>
          </table>

          <h3>WRO Items</h3>

          <table class="table table-bordered table-sm">
            <thead>
              <tr>
                <th class="text-center" style="width: 10px">#</th>
                <th class="text-center" style="width: 10px">CODE</th>
                <th class="text-center">NAME</th>
                <th class="text-center">BATCH NO</th>
                <th class="text-center" style="width: 10px">EXPIRY DATE</th>
                <th class="text-center">EXP QTY</th>
                <th class="text-center">UOM</th>
                <th class="text-center">CARTON</th>
                <th class="text-center" style="width: 15%">PALLETS</th>
              </tr>
            </thead>
            <tbody>
              {% for wro_item in wro_items %}
                <tr>
                  <td class="text-center">{{ forloop.counter }}</td>
                  <td class="text-center">{{ wro_item.item.code }}</td>
                  <td class="text-center">{{ wro_item.item.name }}</td>
                  <td class="text-center">{{ wro_item.batch_no|default:DISPLAY_EMPTY_VALUE }}</td>
                  <td class="text-center">{{ wro_item.expiry_date|default:DISPLAY_EMPTY_VALUE }}</td>
                  <td class="text-center">{{ wro_item.expected_quantity }}</td>
                  <td class="text-center">{{ wro_item.uom.symbol }}</td>
                  <td class="text-center">
                    {{ wro_item.get_outbound_uom_display_conversion.Carton.converted_quantity }} {{ wro_item.get_outbound_uom_display_conversion.Carton.converted_uom }}
                    {% if wro_item.get_outbound_uom_display_conversion.Carton.base_quantity > 0 %}
                      <br/>
                      {{ wro_item.get_outbound_uom_display_conversion.Carton.base_quantity }} PCE
                    {% endif %}
                  </td>
                  <td class="text-center">{{ wro_item.html_rack_display }}</td>
                </tr>
              {% endfor %}
            </tbody>
          </table>

          <h3>OUTGOING CHECK</h3>

          <table class="table table-bordered table-sm">
            <tr>
              <th>
                <table class="table-sm mb-1 custom-border-table">
                  <tr>
                    <th style="width: 20%;">Inspection</th>
                    <th style="width: 10%;">OK</th>
                    <th style="width: 10%;">NOT OK</th>
                    <th colspan="4"></th>
                  </tr>
                  <tr>
                    <th>Container Condition</th>
                    <th><input type="checkbox" disabled style="transform: scale(1.5); vertical-align: middle;"></th>
                    <th><input type="checkbox" disabled style="transform: scale(1.5); vertical-align: middle;"></th>
                    <th style="width: 20%;">Container Seal:</th>
                    <th colspan="3">________________________________</th>
                  </tr>
                  <tr>
                    <th>Cargo Condition</th>
                    <th><input type="checkbox" disabled style="transform: scale(1.5); vertical-align: middle;"></th>
                    <th><input type="checkbox" disabled style="transform: scale(1.5); vertical-align: middle;"></th>
                    <th>Container No.:</th>
                    <th colspan="3">________________________________</th>
                  </tr>
                  <tr>
                    <th>Batch/Lot/Serial No.</th>
                    <th><input type="checkbox" disabled style="transform: scale(1.5); vertical-align: middle;"></th>
                    <th><input type="checkbox" disabled style="transform: scale(1.5); vertical-align: middle;"></th>
                    <th>Driver Name:</th>
                    <th colspan="3">________________________________</th>
                  </tr>
                  <tr>
                    <th>Quantity</th>
                    <th><input type="checkbox" disabled style="transform: scale(1.5); vertical-align: middle;"></th>
                    <th><input type="checkbox" disabled style="transform: scale(1.5); vertical-align: middle;"></th>
                    <th>Truck No.:</th>
                    <th colspan="3">________________________________</th>
                  </tr>
                  <tr>
                    <th style="width: 20%;">Exp Date(if applicable)</th>
                    <th style="width: 10%;"><input type="checkbox" disabled style="transform: scale(1.5); vertical-align: middle;"></th>
                    <th style="width: 10%;"><input type="checkbox" disabled style="transform: scale(1.5); vertical-align: middle;"></th>
                    <th style="width: 20%;">Picker:</th>
                    <th style="width: 15%;">_______________</th>
                    <th style="width: 10%;">Date:</th>
                    <th style="width: 15%;">_______________</th>
                  </tr>
                  <tr>
                    <th></th>
                    <th></th>
                    <th></th>
                    <th>Checker:</th>
                    <th>_______________</th>
                    <th>Date:</th>
                    <th>_______________</th>
                  </tr>
                  <tr>
                    <th></th>
                    <th></th>
                    <th></th>
                    <th>Driver:</th>
                    <th>_______________</th>
                    <th>Date:</th>
                    <th>_______________</th>
                  </tr>
                  <tr>
                    <th></th>
                    <th></th>
                    <th></th>
                    <th>Status:</th>
                    <th colspan="3">
                      <label style="margin-right: 1rem;">
                        Accept <input type="checkbox" disabled style="transform: scale(1.5); vertical-align: middle;">
                      </label>
                      <label>
                        Reject <input type="checkbox" disabled style="transform: scale(1.5); vertical-align: middle;">
                      </label>
                    </th>
                  </tr>
                  <tr>
                    <th></th>
                    <th></th>
                    <th></th>
                    <th>Remark:</th>
                    <th colspan="3"></th>
                  </tr>
                </table>
              </th>
            </tr>
          </table>
        </div>
      </div>
    </div>
  </body>
</html>

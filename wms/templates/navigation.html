{% load static %}
{% load custom_tags %}

{% get_user_menus as menu_data %}
<!-- Main Navigation -->
<nav class="flex-1 overflow-y-auto py-4"
     x-data="{ activeMenu: '{{ menu_data.active_module }}', activeSubmenu: '{{ menu_data.active_submenu }}', openMenus: [], initializeMenus() { const storedMenus = localStorage.getItem('openMenus'); this.openMenus = storedMenus ? JSON.parse(storedMenus) : ['{{ menu_data.active_module }}']; }, toggleMenu(menuLabel) { if (!this.openMenus.includes(menuLabel)) { this.openMenus.push(menuLabel); } else { this.openMenus = this.openMenus.filter(m => m !== menuLabel); } localStorage.setItem('openMenus', JSON.stringify(this.openMenus)); } }"
     x-init="initializeMenus()">
  {% for module in menu_data.menus %}
    <div class="px-2 mb-2">
      <a href="{% if not module.submenus %}{{ module.url }}{% else %}#{% endif %}"
         {% if module.submenus %}@click.prevent="toggleMenu('{{ module.label }}')"{% endif %}
         class="flex items-center justify-between px-3 py-1 rounded-md hover:bg-theme-hover transition-colors duration-200 group"
         :class="{ 'bg-theme-primary/30 hover:bg-theme-default': activeMenu === '{{ module.label }}' }">
        <div class="flex items-center gap-3">
          <!-- Icon -->
          <div class="w-5 h-5">
            <svg xmlns="http://www.w3.org/2000/svg"
                 class="w-5 h-5 transition-colors duration-200"
                 :class="activeMenu === '{{ module.label }}' ? 'text-theme-primary' : 'text-theme-text-secondary group-hover:text-theme-text-primary'"
                 fill="none"
                 viewBox="0 0 24 24"
                 stroke="currentColor"
                 stroke-width="2"
                 stroke-linecap="round"
                 stroke-linejoin="round">
              {{ module.icon|safe }}
            </svg>
          </div>
          <!-- Label -->
          <span class="text-sm font-medium">{{ module.label }}</span>
        </div>
        <!-- Collapse Icon (show only if has submenus) -->
        {% if module.submenus %}
          <div class="w-5 h-5">
            <svg xmlns="http://www.w3.org/2000/svg"
                 class="w-4 h-4 transition-transform duration-200"
                 :class="{ 'rotate-180 text-theme-text-secondary': openMenus.includes('{{ module.label }}'), 'text-theme-text-secondary group-hover:text-theme-text-secondary': !openMenus.includes('{{ module.label }}') }"
                 fill="none"
                 viewBox="0 0 24 24"
                 stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
            </svg>
          </div>
        {% endif %}
      </a>
      <!-- Submenu with enhanced transitions -->
      {% if module.submenus %}
        <div class="ml-8 mt-1 space-y-1 overflow-hidden"
             x-show="openMenus.includes('{{ module.label }}')"
             x-transition:enter="transition ease-out duration-200"
             x-transition:enter-start="opacity-0 transform -translate-y-2"
             x-transition:enter-end="opacity-100 transform translate-y-0"
             x-transition:leave="transition ease-in duration-150"
             x-transition:leave-start="opacity-100 transform translate-y-0"
             x-transition:leave-end="opacity-0 transform -translate-y-2">
          {% for submenu in module.submenus %}
            <a href="{{ submenu.url }}"
               @click="activeSubmenu = '{{ submenu.label }}'"
               class="block px-3 py-1 text-sm rounded-md transition-colors duration-200 group"
               :class="{ 'bg-theme-primary/30': activeSubmenu === '{{ submenu.label }}', 'hover:bg-theme-hover hover:text-theme-text-primary': activeSubmenu !== '{{ submenu.label }}' }">
              {{ submenu.label }}
            </a>
          {% endfor %}
        </div>
      {% endif %}
    </div>
    <br/>
  {% endfor %}
</nav>

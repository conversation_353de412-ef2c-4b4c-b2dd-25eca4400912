from django.apps import AppConfig
from django.utils.translation import gettext_lazy as _


class PickingsConfig(AppConfig):
    """Config class for Pickings app."""

    name = "wms.apps.pickings"
    verbose_name = _("Pickings")

    def ready(self):
        """Enabled signals and actstream."""

        try:
            import wms.apps.pickings.signals  # noqa F401
        except ImportError:
            pass

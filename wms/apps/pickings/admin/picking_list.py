from django.contrib import admin

from wms.cores.admin import BaseModelAdmin, BaseTabularInline

from ..models import Picking<PERSON>ist, PickingListItem


class PickingListItemInline(BaseTabularInline):
    model = PickingListItem
    extra = 1


@admin.register(PickingList)
class PickingListAdmin(BaseModelAdmin):
    """Django admin for PickingList."""

    list_display = [
        "system_number",
        "name",
        "issued_by",
        "expected_completion_date",
        "status",
        "remark",
    ]
    fieldsets = (
        (
            None,
            {
                "fields": (
                    "name",
                    "issued_by",
                    "completion_datetime",
                    "expected_completion_date",
                    "status",
                    "release_from",
                    "remark",
                )
            },
        ),
    )
    inlines = [PickingListItemInline]
    search_fields = ["system_number"]
    list_filter = ["status"]


@admin.register(PickingListItem)
class PickingListItemAdmin(BaseModelAdmin):
    """Django admin for PickingListItem."""

    list_display = [
        "picking_list",
        "remark",
        "status",
    ]
    search_fields = [
        "picking_list__system_number",
    ]
    list_filter = ["status"]

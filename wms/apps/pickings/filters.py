from django.db.models import Q
from django.utils.translation import gettext_lazy as _
from django_filters import filters, FilterSet, CharFilter

from wms.apps.pickings.models import PickingList
from wms.apps.releases.models import WarehouseReleaseOrder
from wms.cores.forms.widget import CoreSelectWidget, CoreSelectMultipleWidget, CoreDateRangeWidget, CoreTextWidget
from wms.cores.utils import get_user_warehouse_choices


class PickingListFilter(FilterSet):
    """
    Filter class for the Picking List list view.
    Provides filtering capabilities for Picking List attributes.
    """
    date_range = filters.DateFromToRangeFilter(
        label=_("Expected Completion Date Range"),
        field_name="expected_completion_date",
        widget=CoreDateRangeWidget()
    )

    status = filters.MultipleChoiceFilter(
        choices=PickingList.Status.choices,
        label=_("Status"),
        widget=CoreSelectMultipleWidget()
    )

    release_from = filters.ModelChoiceFilter(
        queryset=None,  # Will be set in __init__
        label=_("Release From"),
        widget=CoreSelectWidget()
    )

    class Meta:
        model = PickingList
        fields = ['date_range', 'status', 'release_from']

    def __init__(self, data=None, queryset=None, *, request=None, prefix=None):
        super().__init__(data=data, queryset=queryset, request=request, prefix=prefix)

        # Set up dynamic filter choices
        from wms.apps.settings.models import Warehouse

        # Filter out the warehouses based on User Role
        user_warehouses = get_user_warehouse_choices(request.user)
        warehouse_ids = [int(wh_id) for wh_id, _ in user_warehouses]
        self.filters['release_from'].queryset = Warehouse.objects.filter(pk__in=warehouse_ids)

    def filter_queryset(self, queryset):
        """
        Apply custom filtering logic beyond the standard field filtering.
        """
        queryset = super().filter_queryset(queryset)
        return queryset


class PickingListReleaseOrderFilter(FilterSet):
    """
    Filter class for the Release Orders selection in Picking List creation.
    Provides filtering capabilities for WarehouseReleaseOrder attributes.
    """
    tag = CharFilter(
        label=_('Tag'),
        lookup_expr='icontains',
        widget=CoreTextWidget()
    )

    search = CharFilter(
        label=_('Search'),
        method='search_filter',
        widget=CoreTextWidget()
    )

    class Meta:
        model = WarehouseReleaseOrder
        fields = ['tag', 'search']

    def search_filter(self, queryset, name, value):
        """
        Custom filter method to search across multiple fields.
        """
        if not value:
            return queryset

        return queryset.filter(
            Q(system_number__icontains=value) |
            Q(customer_reference__icontains=value) |
            Q(consignee__display_name__icontains=value) |
            Q(deliveryorder__system_number__icontains=value)
        ).distinct()

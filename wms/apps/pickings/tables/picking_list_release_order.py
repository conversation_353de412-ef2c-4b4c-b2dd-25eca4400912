import django_tables2 as tables
from django.utils.translation import gettext_lazy as _

from wms.apps.releases.models import WarehouseReleaseOrder


class PickingListReleaseOrderTable(tables.Table):
    """Table for displaying Release Orders associated with a Picking List."""
    
    system_number = tables.Column(
        verbose_name=_("Numbering"),
        accessor="system_number"
    )

    deliveryorder__system_number = tables.Column(
        verbose_name=_("DO"),
        accessor="deliveryorder__system_number",
        empty_values=('-',)
    )

    status = tables.Column(
        verbose_name=_("Status")
    )

    customer_reference = tables.Column(
        verbose_name=_("Customer Reference")
    )

    consignee = tables.Column(
        verbose_name=_("Consignee")
    )

    actions = tables.TemplateColumn(
        verbose_name=_("Actions"),
        template_name="pickings/picking_lists/partials/release_order_actions.html",
        orderable=False
    )

    class Meta:
        model = WarehouseReleaseOrder
        order_by = '-system_number'
        template_name = "tables/table_htmx.html"
        fields = (
            "system_number",
            "deliveryorder__system_number",
            "status",
            "customer_reference",
            "consignee",
            "actions"
        )

    def render_status(self, value, record):
        """Return nice html label display for status."""
        return record.html_status_display

    def render_deliveryorder__system_number(self, value, record):
        """Handle empty delivery order numbers."""
        if hasattr(record, 'deliveryorder') and record.deliveryorder:
            return record.deliveryorder.system_number
        return "-"

import django_tables2 as tables
from django.conf import settings
from django.urls import reverse, NoReverseMatch
from django.utils.translation import gettext_lazy as _

from wms.apps.pickings.models import PickingList
from wms.apps.releases.models import WarehouseReleaseOrder
from wms.cores.columns import HTMXColumn


class PickingListTable(tables.Table):
    section_title = "Picking Lists"
    section_name = "Picking List"
    selectable = False

    system_number = tables.LinkColumn(
        "pickings:picking_lists:panel",
        args=[tables.utils.A("pk")],
        verbose_name=_("Numbering")
    )

    name = tables.Column(verbose_name=_("Name"))
    status = tables.Column(verbose_name=_("Status"))
    issued_by = tables.Column(verbose_name=_("Issued By"))

    expected_completion_date = tables.DateColumn(
        verbose_name=_("Expected Completion Date"),
        format=settings.DATE_FORMAT,
        attrs={
            "td": {
                "class": "whitespace-nowrap",
            },
        }
    )

    completion_datetime = tables.DateTimeColumn(
        verbose_name=_("Completion Date"),
        format=settings.DATETIME_FORMAT,
        attrs={
            "td": {
                "class": "whitespace-nowrap",
            },
        }
    )

    created = tables.DateTimeColumn(
        verbose_name=_("Created Date"),
        format=settings.DATETIME_FORMAT,
        attrs={
            "td": {
                "class": "whitespace-nowrap",
            },
        }
    )

    actions = tables.TemplateColumn(
        verbose_name=_("Actions"),
        template_name="tables/table_actions_column.html",
        orderable=False
    )

    @property
    def create_url(self):
        try:
            return reverse('pickings:picking_lists:create')
        except NoReverseMatch:
            return None

    class Meta:
        model = PickingList
        order_by = '-system_number'
        template_name = "tables/table_htmx.html"
        fields = (
            "system_number",
            "name",
            "status",
            "issued_by",
            "expected_completion_date",
            "completion_datetime",
            "created",
        )

    def render_status(self, value, record):
        """Return nice html label display for status."""
        return record.html_status_display

    def render_actions(self, value, record):
        """Actions column to show action buttons."""
        from django.template.loader import get_template

        # Create a context dictionary for the template
        context = {'view_url': "pickings:picking_lists:panel"}
        context['record'] = record

        # Render the template with our custom context
        template = get_template("tables/table_actions_column.html")
        return template.render(context)


class PickingListDetailTable(tables.Table):
    """Table used on picking list detail page."""
    selectable = False

    system_number = HTMXColumn(
        url_name="pickings:picking_lists:detail_home",
        target_id="detail-panel",
        verbose_name=_("Number"),
        push_url=True,
        push_url_name="pickings:picking_lists:panel",
    )

    status = tables.Column()

    class Meta:
        model = PickingList
        order_by = '-system_number'
        template_name = "tables/table_htmx.html"
        fields = (
            "system_number",
            "status",
        )

    def __init__(self, *args, object_pk=None, **kwargs):
        super().__init__(*args, **kwargs)
        self.object_pk = object_pk

    def render_status(self, value, record):
        """Return nice html label display for status."""
        return record.html_status_display


class PickingListSelectReleaseOrderTable(tables.Table):
    """
    Table for selecting release orders to include in a picking list.
    Includes a checkbox column for selection.
    """
    section_title = "Available Release Orders"
    section_name = "Release Orders"
    selectable = True  # Enable checkbox selection
    htmx_target = "picking-list-selection-form"  # Target for HTMX requests

    system_number = tables.Column(
        verbose_name=_("Numbering"),
        accessor="system_number"
    )

    deliveryorder__system_number = tables.Column(
        verbose_name=_("DO"),
        accessor="deliveryorder__system_number",
        empty_values=('-',)
    )

    status = tables.Column(
        verbose_name=_("Status")
    )

    customer_reference = tables.Column(
        verbose_name=_("Customer Reference")
    )

    tag = tables.Column(
        verbose_name=_("Tag")
    )

    consignee = tables.Column(
        verbose_name=_("Consignee")
    )

    class Meta:
        model = WarehouseReleaseOrder
        order_by = '-system_number'
        template_name = "tables/table_htmx.html"
        fields = (
            "system_number",
            "deliveryorder__system_number",
            "status",
            "customer_reference",
            "tag",
            "consignee",
        )

    def render_status(self, value, record):
        """Return nice html label display for status."""
        return record.html_status_display

    def render_deliveryorder__system_number(self, value, record):
        """Handle empty delivery order numbers."""
        if hasattr(record, 'deliveryorder') and record.deliveryorder:
            return record.deliveryorder.system_number
        return "-"

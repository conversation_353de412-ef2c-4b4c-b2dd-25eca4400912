# Generated by Django 5.1 on 2025-03-15 13:12

import django.db.models.deletion
import django.utils.timezone
import model_utils.fields
import wms.cores.models
import wms.cores.utils
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('cores', '0001_create_extensions'),
        ('inventories', '0001_initial'),
        ('settings', '0002_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='PickingList',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('system_number', models.CharField(blank=True, max_length=32, verbose_name='System Number')),
                ('name', models.CharField(blank=True, max_length=255, null=True, verbose_name='Name')),
                ('expected_completion_date', models.DateField(default=wms.cores.utils.localtime_now, verbose_name='Expected Completion Date')),
                ('completion_datetime', models.DateTimeField(blank=True, default=None, null=True, verbose_name='Completion Date Time')),
                ('status', models.CharField(choices=[('New', 'New'), ('Processing', 'Processing'), ('Completed', 'Completed')], default='New', max_length=32, verbose_name='Status')),
                ('remark', models.TextField(blank=True, max_length=512, verbose_name='Remark')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('issued_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Modified by')),
                ('release_from', models.ForeignKey(limit_choices_to={'is_storage': True}, on_delete=django.db.models.deletion.PROTECT, related_name='release_from', to='settings.warehouse')),
            ],
            options={
                'ordering': ['-system_number'],
                'get_latest_by': 'created',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='PickingListItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('sort_order', models.PositiveIntegerField(default=0, verbose_name='Ordering')),
                ('status', models.CharField(choices=[('New', 'New'), ('Approved', 'Approved')], default='New', max_length=32, verbose_name='Status')),
                ('consolidated_quantity', models.DecimalField(decimal_places=6, default=Decimal('0'), max_digits=19, validators=[wms.cores.models.greater_than_zero], verbose_name='Quantity')),
                ('remark', models.TextField(blank=True, max_length=512, verbose_name='Remark')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('modified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Modified by')),
                ('picking_list', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='picking_list_items', to='pickings.pickinglist')),
                ('stock', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventories.stock')),
            ],
            options={
                'ordering': ['sort_order'],
                'get_latest_by': 'created',
                'abstract': False,
            },
        ),
    ]

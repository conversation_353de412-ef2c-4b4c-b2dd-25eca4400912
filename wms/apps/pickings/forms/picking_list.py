from django import forms
from django.utils.translation import gettext_lazy as _

from wms.cores.forms.fields import (
    CoreModelForm, CoreCharField, CoreDateField, CoreModelChoiceField,
    CoreTextField, FormFieldSize
)
from wms.apps.pickings.models import PickingList
from wms.apps.settings.models import Warehouse
from wms.cores.forms.widget import CoreTextWidget, CoreDateWidget, CoreTextAreaWidget


class PickingListCreateForm(CoreModelForm):
    """
    Form for creating a new Picking List (Step 1).
    Collects basic information about the picking list.
    """
    name = CoreCharField(
        label=_("Name"),
        required=True,
        widget=CoreTextWidget(
            attrs={
                'class': 'w-xs',
            }
        ),
    )

    expected_completion_date = CoreDateField(
        label=_("Expected Date"),
        required=True,
        widget=CoreDateWidget(
            attrs={
                'class': 'w-xs',
            }
        ),
    )

    release_from = CoreModelChoiceField(
        queryset=None,  # Will be set in __init__
        label=_("Release From"),
        required=True,
    )

    remark = CoreTextField(
        label=_("Remarks"),
        required=False,
        widget=CoreTextAreaWidget(
            attrs={
                'cols': '70',
                'rows': '4',
            }
        ),
    )

    class Meta:
        model = PickingList
        fields = [
            "name",
            "issued_by",
            "expected_completion_date",
            "release_from",
            "remark",
        ]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Set the current user as the issued_by
        if hasattr(self, 'request') and self.request.user:
            self.fields['issued_by'].initial = self.request.user
            self.fields['issued_by'].disabled = True

        # Filter warehouses based on user permissions
        if hasattr(self, 'request') and self.request.user:
            if self.request.user.is_superuser:
                self.fields['release_from'].queryset = Warehouse.objects.filter(is_storage=True)
            else:
                user_warehouses = list(self.request.user.warehouses.filter(is_storage=True).values_list("pk", flat=True))
                self.fields['release_from'].queryset = Warehouse.objects.filter(pk__in=user_warehouses, is_storage=True)


class PickingListReleaseOrderSelectionForm(forms.Form):
    """
    Form for selecting release orders to include in the picking list (Step 2).
    """
    selected_release_orders = forms.CharField(
        widget=forms.HiddenInput(),
        required=True
    )

    def __init__(self, *args, **kwargs):
        # Extract request from kwargs if present
        self.request = kwargs.pop('request', None)
        super().__init__(*args, **kwargs)

    def clean_selected_release_orders(self):
        data = self.cleaned_data['selected_release_orders']
        if not data:
            raise forms.ValidationError(_("Please select at least one release order."))

        # Convert comma-separated string to list of integers
        try:
            release_order_ids = [int(id.strip()) for id in data.split(',') if id.strip()]
            if not release_order_ids:
                raise forms.ValidationError(_("Please select at least one release order."))
            return release_order_ids
        except ValueError:
            raise forms.ValidationError(_("Invalid release order selection."))

from django import forms
from django.utils.translation import gettext_lazy as _

from wms.cores.forms.fields import CoreModelForm, CoreTextField
from wms.cores.forms.widget import CoreTextAreaWidget


class PickingListAddReleaseOrderForm(forms.Form):
    """
    Form for selecting release orders to add to an existing picking list.
    """
    selected_release_orders = forms.CharField(
        widget=forms.HiddenInput(),
        required=False
    )

    def __init__(self, *args, **kwargs):
        # Extract request from kwargs if present
        self.request = kwargs.pop('request', None)
        super().__init__(*args, **kwargs)

    def clean_selected_release_orders(self):
        data = self.cleaned_data['selected_release_orders']
        if not data:
            raise forms.ValidationError(_("Please select at least one release order."))

        # Convert comma-separated string to list of integers
        try:
            release_order_ids = [int(id.strip()) for id in data.split(',') if id.strip()]
            if not release_order_ids:
                raise forms.ValidationError(_("Please select at least one release order."))
            return release_order_ids
        except ValueError:
            raise forms.ValidationError(_("Invalid release order selection."))

from django.urls import include, path

from wms.apps.pickings.views.picking_list import (
    picking_list_list_view,
    picking_list_detail_home_view,
    picking_list_detail_view,
    picking_list_data_table_detail_view,
    picking_list_item_list_view,
    picking_list_release_order_list_view,
    picking_list_release_order_delete_form,
    picking_list_release_order_delete,
    picking_list_complete,
)
from wms.apps.pickings.views.picking_list_add_wro import (
    picking_list_add_release_order_view,
    picking_list_add_release_order_submit_view,
)
from wms.apps.pickings.views.picking_list_assign_rack import (
    picking_list_assign_rack,
)
from wms.apps.pickings.views.picking_list_create import (
    picking_list_create_view,
    picking_list_load_release_orders_view,
    picking_list_submit_view,
)


app_name = "pickings"

picking_lists_urlpatterns = [
    path("", view=picking_list_list_view, name="list"),
    path("create/", view=picking_list_create_view, name="create"),
    path("load-release-orders/", view=picking_list_load_release_orders_view, name="load_release_orders"),
    path("submit/", view=picking_list_submit_view, name="submit"),
    path("<int:pk>/", view=picking_list_data_table_detail_view, name="panel"),
    path("<int:pk>/detail/", view=picking_list_detail_view, name="detail"),
    path("<int:pk>/detail-home/", view=picking_list_detail_home_view, name="detail_home"),
    path("<int:pk>/item/list/", view=picking_list_item_list_view, name="item_list"),
    path("<int:pk>/release-order/list/", view=picking_list_release_order_list_view, name="release_order_list"),
    # Release order actions
    path("release-order/<int:pk>/delete-form/", view=picking_list_release_order_delete_form, name="release_order_delete_form"),
    path("release-order/<int:pk>/delete/", view=picking_list_release_order_delete, name="release_order_delete"),
    # Picking list actions
    path("<int:pk>/complete/", view=picking_list_complete, name="complete"),
    path("<int:pk>/add-release-order/", view=picking_list_add_release_order_view, name="add_release_order"),
    path("<int:pk>/add-release-order-submit/", view=picking_list_add_release_order_submit_view, name="add_release_order_submit"),
    path("<int:pk>/assign-rack/", view=picking_list_assign_rack, name="picking_list_assign_rack"),
]

urlpatterns = [
    path(
        "picking-lists/",
        include(
            (picking_lists_urlpatterns, "pickings.picking_lists"),
            namespace="picking_lists",
        ),
    ),
]

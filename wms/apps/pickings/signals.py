import logging

from django.db.models.signals import post_save
from django.dispatch import receiver

from wms.cores.actstream import register_stream

from .models import PickingList, PickingListItem

logger = logging.getLogger(__name__)


#################
# FOR ACTSTREAM #
#################

register_stream(PickingList, logger=logger)
register_stream(PickingListItem, logger=logger, parent_field="picking_list")


###################
# FOR APP SIGNALS #
###################


@receiver(post_save, sender=PickingList)
def generate_system_number(sender, instance, created=False, **kwargs):
    """To generate system number on instance."""
    if created:
        instance.generate_system_number()
        logger.info(f"SIGNAL: {sender.__name__}: generated system number for {instance}")

# from typing import Any

# from django import forms
# from django.core.exceptions import Validation<PERSON>rror

# from wss.cores.forms import CoreHtmxModelForm, CoreModelForm
# from wss.cores.forms.base import CoreForm

# from wss.apps.releases.models import WarehouseReleaseOrder
# from wss.apps.settings.models import Warehouse

# from .models import PickingList


# class PickingListStep1Form(CoreModelForm):
#     """Form to create PickingList's basic info in 1st step."""

#     class Meta:
#         model = PickingList
#         fields = [
#             "name",
#             "status",
#             "issued_by",
#             "expected_completion_date",
#             "release_from",
#             "remark",
#         ]
#         widgets = {
#             "status": forms.HiddenInput(),
#             "remark": forms.Textarea(attrs={"cols": 80, "rows": 5}),
#         }

#     def __init__(self, *args, **kwargs):
#         super().__init__(*args, **kwargs)
#         self.request = kwargs.pop("request", None)

#         self.fields["status"].disabled = True
#         self.fields["issued_by"].disabled = True
#         self.fields["issued_by"].initial = self.request.user

#         if self.request.user.is_superuser is True:
#             self.fields["release_from"].queryset = Warehouse.objects.all()
#         else:
#             user_warehouse = list(self.request.user.warehouses.all().values_list("pk", flat=True))
#             self.fields["release_from"].queryset = Warehouse.objects.filter(pk__in=user_warehouse)


# class PickingListStep2Form(CoreForm):
#     """Form to link selected WRO/DO checkboxes from datatables in Step 2, in order to create PickingListItem."""

#     release_orders_pk_list = forms.CharField(
#         widget=forms.HiddenInput(),
#         # widget=forms.TextInput(attrs={'placeholder': 'Enter PKs separated by commas'}),
#         help_text="Enter a comma-separated list of primary keys.",
#         required=False,  # Makes the field optional
#     )

#     def clean_release_orders_pk_list(self):
#         # Get the raw data entered in release_orders_pk_list
#         data = self.cleaned_data["release_orders_pk_list"]

#         # Split the input by commas and convert each to an integer
#         try:
#             release_orders_pk_list = [int(pk.strip()) for pk in data.split(",") if pk.strip()]
#         except ValueError:
#             raise forms.ValidationError("Please select a valid WRO checkbox for picking list")

#         return release_orders_pk_list

#     def clean(self) -> dict[str, Any]:
#         cleaned_data = super().clean()

#         release_orders_pk_list = cleaned_data["release_orders_pk_list"]

#         if not release_orders_pk_list:
#             raise ValidationError("Please check at least 1 WRO checkbox for picking list")

#         return cleaned_data


# ############
# # FOR HTMX #
# ############


# class PickingListInfoUpdateForm(CoreHtmxModelForm):
#     """Form to update PickingList's info."""

#     class Meta:
#         model = PickingList
#         fields = [
#             "name",
#             "expected_completion_date",
#             "remark",
#         ]
#         widgets = {
#             "remark": forms.Textarea(attrs={"cols": 80, "rows": 5}),
#         }


# class PickingListWROItemCreateForm(CoreForm):
#     """Form to create PickingListItem based on select WRO only."""

#     release_order = forms.ModelChoiceField(
#         queryset=WarehouseReleaseOrder.objects.all(), help_text="Please choose existing WRO."
#     )

#     def __init__(self, *args, **kwargs) -> None:
#         super().__init__(*args, **kwargs)

#         self.fields["release_order"].widget.attrs["class"] = "form-control core-select2"


# class PickingListWroRemoveForm(CoreForm):
#     """Form to create PickingListItem based on select WRO only."""

#     release_order = forms.ModelChoiceField(queryset=WarehouseReleaseOrder.objects.all(), help_text="Removing this WRO.")

#     def __init__(self, *args, **kwargs) -> None:
#         super().__init__(*args, **kwargs)
#         self.fields["release_order"].disabled = True

from decimal import Decimal

from django.conf import settings
from django.db import models
from django.db.models import Sum
from django.urls import reverse
from django.utils.functional import cached_property
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _

from wms.cores.models import (
    DISPLAY_EMPTY_VALUE,
    AbstractBaseModel,
    AbstractSystemNumberModel,
    AbstractSortableModel,
    greater_than_zero,
)
from wms.cores.utils import localtime_now, normalize_decimal, calculate_base_uom_to_expected_conversion_uom

from wms.apps.inventories.models import Stock
# from wms.apps.rackings.models import Rack, RackTransaction
from wms.apps.releases.models import WarehouseReleaseOrderItem
from wms.apps.settings.utils import uom_converter


class PickingList(AbstractSystemNumberModel, AbstractBaseModel):
    """PickingList model for Warehouse Smart System.

    Available fields:

    * created                     (AbstractBaseModel => TimeStampedModel)
    * modified                    (AbstractBaseModel => TimeStampedModel)
    * created_by                  (AbstractBaseModel)
    * modified_by                 (AbstractBaseModel)
    * system_number               (AbstractSystemNumberModel)
    * issued_by                   (FK)
    * expected_completion_date
    * completion_datetime
    * status
    * release_from                (FK)
    * remark

    """

    class Status(models.TextChoices):
        NEW = "New", _("New")
        PROCESSING = "Processing", _("Processing")
        COMPLETED = "Completed", _("Completed")

    name = models.CharField(verbose_name=_("Name"), max_length=255, blank=True, null=True)
    issued_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.PROTECT)
    expected_completion_date = models.DateField(verbose_name=_("Expected Completion Date"), default=localtime_now)
    completion_datetime = models.DateTimeField(
        verbose_name=_("Completion Date Time"), blank=True, null=True, default=None
    )
    status = models.CharField(verbose_name=_("Status"), max_length=32, choices=Status.choices, default=Status.NEW)
    release_from = models.ForeignKey(
        "settings.Warehouse",
        related_name="release_from",
        limit_choices_to={"is_storage": True},
        on_delete=models.PROTECT,
    )
    remark = models.TextField(verbose_name=_("Remark"), max_length=512, blank=True)

    class Meta(AbstractBaseModel.Meta):
        ordering = ["-system_number"]

    def __str__(self):
        return f"{self.system_number}"

    def get_absolute_url(self):
        return reverse("pickings:picking_lists:detail", kwargs={"pk": self.pk})

    @cached_property
    def get_ascending_sort_picking_list_items(self):
        return self.picking_list_items.all().order_by("sort_order")

    @cached_property
    def html_status_display(self):
        """Return nice HTML display for status."""
        html_class = ""

        if self.status == PickingList.Status.NEW:
            html_class = "badge bg-theme-status-warning"
        elif self.status == PickingList.Status.PROCESSING:
            html_class = "badge bg-theme-status-info"
        elif self.status == PickingList.Status.COMPLETED:
            html_class = "badge bg-theme-status-success"

        return format_html(f'<span class="{html_class}">{self.get_status_display()}</span>') or DISPLAY_EMPTY_VALUE

    def regenerate_picking_list_item(self):
        """Regenerate the whole picking list item that belong to this picking list based on all related WROs.

        Steps:
        1. Get current picking list items with format:
           {stock_pk: consolidated_quantity, ...}
        2. Generate new picking list items with the latest WROs with format:
           {new_stock_pk: new_consolidated_quantity, ...}
        3. Compare current picking list items and new piking list items:
           - If stock_pk not in new_stock_pk,
             keep for DELETE
           - if stock_pk in new_stock_pk, if consolidated_quantity does not equals to new_consolidated_quantity,
             keep for update
           - if stock_pk in new_stock_pk, if consolidated_quantity equals to new_consolidated_quantity,
             SKIP UPDATE
           - if new_stock_pk not in stock_pk,
             keep for CREATE
        """

        existing_items = {}
        current_picking_list_items = self.picking_list_items.all()
        for item in current_picking_list_items:
            existing_items[item.stock.pk] = normalize_decimal(item.consolidated_quantity)

        # Sorting by keys in ascending order
        existing_items = dict(sorted(existing_items.items(), key=lambda item: item[0]))

        # print("existing_items")
        # print(existing_items)

        new_items = {}
        all_wro_items = WarehouseReleaseOrderItem.objects.filter(release_order__picking_list=self)

        for wro_item in all_wro_items:
            stock_filter = Stock.objects.filter(
                warehouse=self.release_from,
                batch_no=wro_item.batch_no,
                expiry_date=wro_item.expiry_date,
                item=wro_item.item,
            )

            if stock_filter.exists():
                stock = stock_filter.first()

                # Convert to quantity based on Item's default UOM
                converted_quantity = uom_converter(
                    origin_uom=wro_item.uom, target_uom=wro_item.item.uom, quantity=wro_item.quantity
                )

                if stock.pk in new_items:
                    new_items[stock.pk] += converted_quantity
                else:
                    new_items[stock.pk] = converted_quantity

        # Sorting by keys in ascending order
        new_items = dict(sorted(new_items.items(), key=lambda item: item[0]))

        # print("new_items")
        # print(new_items)

        # Find keys to delete (present in existing_items but not in new_items)
        to_delete = [key for key in existing_items if key not in new_items]

        # Find keys to update (present in both but with different values)
        to_update = [key for key in existing_items if key in new_items and existing_items[key] != new_items[key]]

        # Find keys to create (present in new_items but not in existing_items)
        to_create_new = [key for key in new_items if key not in existing_items]

        # print("Keys to delete:", to_delete)
        # print("Keys to update:", to_update)
        # print("Keys to create:", to_create_new)

        if to_delete:
            self.picking_list_items.filter(stock__pk__in=to_delete).delete()

        if to_update:
            for key in to_update:
                # Fetch the new quantity from `new_items`
                new_quantity = new_items[key]

                # Update the `quantity` field on `picking_list_items` with matching stock primary key
                self.picking_list_items.filter(stock__pk=key).update(consolidated_quantity=new_quantity)

        if to_create_new:
            new_items_to_create = [
                PickingListItem(picking_list=self, stock_id=key, consolidated_quantity=new_items[key])
                for key in to_create_new
            ]
            PickingListItem.objects.bulk_create(new_items_to_create)


class PickingListItem(AbstractBaseModel, AbstractSortableModel):
    """PickingListItem model for Warehouse Smart System.

    Available fields:

    * created                    (AbstractBaseModel => TimeStampedModel)
    * modified                   (AbstractBaseModel => TimeStampedModel)
    * created_by                 (AbstractBaseModel)
    * modified_by                (AbstractBaseModel)
    * sort_order                 (AbstractSortableModel)
    * picking_list               (FK)
    * status
    * stock                      (FK)
    * consolidated_quantity
    * remark
    * approved_by                (FK)

    """

    class Status(models.TextChoices):
        NEW = "New", _("New")
        APPROVED = "Approved", _("Approved")

    picking_list = models.ForeignKey(
        "pickings.PickingList", related_name="picking_list_items", on_delete=models.CASCADE
    )
    status = models.CharField(verbose_name=_("Status"), max_length=32, choices=Status.choices, default=Status.NEW)
    stock = models.ForeignKey("inventories.Stock", on_delete=models.CASCADE)
    consolidated_quantity = models.DecimalField(
        verbose_name=_("Quantity"),
        max_digits=19,
        decimal_places=6,
        default=Decimal("0"),
        validators=[greater_than_zero],
    )
    remark = models.TextField(verbose_name=_("Remark"), max_length=512, blank=True)
    approved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL, related_name="+", on_delete=models.RESTRICT, null=True, blank=True
    )

    class Meta(AbstractBaseModel.Meta):
        ordering = ["sort_order"]
        # single index
        models.Index(fields=["picking_list"]),
        models.Index(fields=["stock"]),
        # composite index
        # - no matter the query from which point starting to query, it's optimized
        models.Index(fields=["picking_list", "stock"]),

    def __str__(self) -> str:
        stock_info = f"{self.stock.item.code} :: {self.stock.item.name}"
        return f"{self.picking_list.system_number} :: {stock_info} :: {self.consolidated_quantity}"

    def get_absolute_url(self):
        return reverse("pickings:picking_lists:detail", kwargs={"pk": self.picking_list.pk})

    @cached_property
    def get_position(self):
        """Return position of item in Picking List."""
        position = (
            list(
                self.__class__.objects.filter(picking_list=self.picking_list)
                .order_by("sort_order")
                .values_list("pk", flat=True)
            ).index(self.pk)
            + 1
        )

        return position

    @cached_property
    def get_normalized_consolidated_quantity(self):
        """Return decimal's field normalized value."""
        return normalize_decimal(self.consolidated_quantity)

    @cached_property
    def get_outbound_uom_display_conversion(self):
        """Return outbound uom display conversion in dict format.

        Assumption:
        - suppose to support up to multiple conversion dict in 1 big dict:

        Return:
            outbound_uom_display_conversion_dict = {
                # structure example:
                "uom_display_name": {
                    "converted_uom": converted_uom,
                    "converted_quantity": converted_quantity,
                    "base_uom": base_uom,
                    "base_quantity": base_quantity,
                },

                #value example (I.E: 1 carton = 10 PCE):
                "carton": {
                    "converted_uom": "carton",
                    "converted_quantity": 10,
                    "base_uom": "PCE",
                    "base_quantity": 100,
                },
                #value example (I.E: 1 pallet = 200 PCE):
                "pallet": {
                    "converted_uom": "pallet",
                    "converted_quantity": 10,
                    "base_uom": "PCE",
                    "base_quantity": 2000,
                },
            }
        """

        outbound_uom_display_conversion_dict = {}

        all_uom_display_qs = self.stock.item.outbound_uom_display_conversions.all().order_by("base_value")

        for uom_display_obj in all_uom_display_qs:
            uom_display_quantity = self.consolidated_quantity / uom_display_obj.base_value

            if uom_display_quantity.as_tuple().exponent < 0:
                uom_display_number, base_display_number = calculate_base_uom_to_expected_conversion_uom(
                    uom_display_quantity, uom_display_obj.base_value
                )
                outbound_uom_display_conversion_dict[uom_display_obj.uom_display_name] = {
                    "converted_uom": uom_display_obj.uom_display_name,
                    "converted_quantity": uom_display_number,
                    "base_uom": self.stock.item.uom.symbol,
                    "base_quantity": base_display_number,
                }
            else:
                outbound_uom_display_conversion_dict[uom_display_obj.uom_display_name] = {
                    "converted_uom": uom_display_obj.uom_display_name,
                    "converted_quantity": uom_display_quantity,
                    "base_uom": self.stock.item.uom.symbol,
                    "base_quantity": 0,
                }

        return outbound_uom_display_conversion_dict

    @cached_property
    def get_total_available_rack_transaction_balance(self):
        from wms.apps.rackings.models import RackTransaction
        return RackTransaction.objects.balance_by_stock(
            # rack=self.rack,
            stock=self.stock,
            balance_type="available",
        )

    @cached_property
    def get_total_reserved_rack_transaction_balance(self):
        from wms.apps.rackings.models import RackTransaction
        return RackTransaction.objects.balance_by_stock(
            # rack=self.rack,
            stock=self.stock,
            balance_type="reserved",
        )

    # @cached_property
    # def racks(self):
    #     return Rack.objects.filter(
    #         warehouse=self.picking_list.release_from,
    #         rackstorage__stock=self.stock,
    #     )

    # @cached_property
    # def html_rack_display(self):
    #     """Return nice HTML display for all involved racks."""
    #     racks = self.racks

    #     li_html = "".join([f"<li class='showmore-item'>{rack.__str__().replace(' ', '&nbsp;')}</li>" for rack in racks])

    #     if li_html:
    #         return format_html(f"<ul class='list-unstyled mb-0 showmore-items'>{li_html}</li>")
    #     else:
    #         return DISPLAY_EMPTY_VALUE

    # @cached_property
    # def excel_rack_display(self):
    #     """Return Excel display for all involved racks.

    #     Requirement from Joann, only print max 5 in excel.
    #     """
    #     racks = self.racks

    #     return (
    #         format_html("\r\n".join([rack.__str__().replace(" ", "&nbsp;") for rack in racks[:5]]))
    #         or DISPLAY_EMPTY_VALUE
    #     )

    # @cached_property
    # def reserved_rack_transactions(self):
    #     release_order_item_qs = WarehouseReleaseOrderItem.objects.filter(
    #         release_order__picking_list=self.picking_list,
    #         release_order__warehouses=self.picking_list.release_from,
    #         item=self.stock.item,
    #         batch_no=self.stock.batch_no,
    #         expiry_date=self.stock.expiry_date,
    #     )

    #     return RackTransaction.objects.select_related(
    #         "rackstorage",
    #         "rackstorage__rack",
    #         "rackstorage__stock",
    #         "warehouse_release_order_item",
    #     ).filter(
    #         type=RackTransaction.Type.RESERVED,
    #         warehouse_release_order_item__in=release_order_item_qs,
    #     )

    # @cached_property
    # def html_reserved_rack_display(self):
    #     formatted_html = ""

    #     reserved_racks = (
    #         self.reserved_rack_transactions.values("rackstorage")
    #         .order_by()
    #         .annotate(
    #             sum=Sum("quantity"),
    #         )
    #         .values_list("rackstorage__rack__full_name", "sum")
    #     )

    #     li_html = "".join(
    #         [
    #             (
    #                 f"<li class='showmore-item'>{rack.replace(' ', '&nbsp;')}&nbsp;"
    #                 f"[{round(abs(quantity), self.stock.item.uom.unit_precision)}]</li>"
    #             )
    #             for rack, quantity in reserved_racks
    #         ]
    #     )

    #     if li_html:
    #         formatted_html += f"<ul class='list-unstyled mb-0 showmore-items'>{li_html}</ul>"

    #     incomplete_reservation = WarehouseReleaseOrderItem.objects.filter(
    #         release_order__picking_list=self.picking_list,
    #         reservation_status=WarehouseReleaseOrderItem.ReservationStatus.RESERVED_INCOMPLETE,
    #     )
    #     if incomplete_reservation.exists():
    #         formatted_html += "<i class='fa fa-exclamation-circle fa-fw text-warning'></i>"

    #     return format_html(formatted_html) if formatted_html else DISPLAY_EMPTY_VALUE

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)

        if self.status == PickingListItem.Status.APPROVED:
            all_picking_list_item = self.picking_list.picking_list_items.all()
            all_new_picking_list_item = all_picking_list_item.filter(status=PickingListItem.Status.NEW)

            if all_new_picking_list_item.count() > 0:
                self.picking_list.status = PickingList.Status.PROCESSING
            else:
                self.picking_list.status = PickingList.Status.Completed

            self.picking_list.save(update_fields=["status"])

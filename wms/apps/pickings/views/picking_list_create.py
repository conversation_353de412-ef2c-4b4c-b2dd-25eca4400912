from django.contrib import messages
from django.db import transaction
from django.db.models import Q
from django.http import HttpResponse
from django.shortcuts import redirect
from django.urls import reverse
from django.utils.translation import gettext_lazy as _
from django.views.generic import FormView, TemplateView

from django_tables2 import RequestConfig

from wms.apps.pickings.tables.picking_list import PickingListSelectReleaseOrderTable
from wms.apps.settings.models import Warehouse
from wms.cores.views import CoreCreateView
from wms.cores.mixins import FormKwargsRequestMixin

from wms.apps.pickings.forms import PickingListCreateForm, PickingListReleaseOrderSelectionForm
from wms.apps.pickings.models import PickingList
from wms.apps.releases.models import WarehouseReleaseOrder


class PickingListCreateView(FormKwargsRequestMixin, TemplateView):
    """
    View for creating a new Picking List using a two-step HTMX approach.

    Step 1: Basic information form
    Step 2: Release order selection table
    """
    template_name = "pickings/picking_lists/create.html"
    permission_required = ["pickings.add_pickinglist"]
    cancel_url = "pickings:picking_lists:list"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Check if we're preserving form data from a back button click
        if self.request.GET.get('preserve') == 'true':
            # Try to get data from session first
            form_data = self.request.session.get('picking_list_form_data', {})
            if form_data:
                # Create a form with the session data
                context['form'] = PickingListCreateForm(initial=form_data, request=self.request)
            else:
                # Fall back to GET parameters if session data is not available
                initial_data = {}
                if 'name' in self.request.GET:
                    initial_data['name'] = self.request.GET.get('name')
                if 'expected_completion_date' in self.request.GET:
                    initial_data['expected_completion_date'] = self.request.GET.get('expected_completion_date')
                if 'release_from' in self.request.GET:
                    initial_data['release_from'] = self.request.GET.get('release_from')
                if 'remark' in self.request.GET:
                    initial_data['remark'] = self.request.GET.get('remark')

                context['form'] = PickingListCreateForm(initial=initial_data, request=self.request)
        else:
            context['form'] = PickingListCreateForm(request=self.request)

        context['selection_form'] = PickingListReleaseOrderSelectionForm()
        context['section_title'] = _("Create Picking List")
        return context


class PickingListLoadReleaseOrdersView(FormKwargsRequestMixin, FormView):
    """
    HTMX view that validates the basic information form and returns the release orders table.
    """
    form_class = PickingListCreateForm
    template_name = "pickings/picking_lists/partials/release_orders_table.html"
    htmx_base_url = None  # Will be set dynamically in get_htmx_base_url

    def get_htmx_base_url(self):
        """
        Override to provide the dynamic URL for HTMX requests.
        This URL will be used for all HTMX requests including sorting and pagination.
        """
        # Get the base URL
        base_url = reverse("pickings:picking_lists:load_release_orders")

        # Always return the base URL - the table will handle sorting parameters
        return base_url

    def form_valid(self, form):
        # Get the warehouse from the form
        warehouse = form.cleaned_data['release_from']

        # Query release orders for this warehouse that are in NEW status and not assigned to a picking list
        queryset = WarehouseReleaseOrder.objects.filter(
            warehouses=warehouse,
            status=WarehouseReleaseOrder.Status.NEW,
            picking_list__isnull=True
        ).distinct()

        # Store the warehouse ID in the session for search functionality
        self.request.session['warehouse_id'] = warehouse.pk

        # Apply search if provided
        query = self.request.GET.get('query', '').strip()
        if query:
            queryset = queryset.filter(
                Q(system_number__icontains=query) |
                Q(customer_reference__icontains=query) |
                Q(tag__icontains=query) |
                Q(deliveryorder__system_number__icontains=query) |
                Q(consignor_picking_list_no__icontains=query) |
                Q(issued_by__username__icontains=query)
            ).distinct()

        # Create the table
        table = PickingListSelectReleaseOrderTable(queryset)
        table.htmx_base_url = self.get_htmx_base_url()
        table.table_height = 'max-h-[calc(65svh)]'
        RequestConfig(self.request, paginate={"per_page": 10}).configure(table)

        # Store form data in session for later use
        self.request.session['picking_list_step1_data'] = {
            'name': form.cleaned_data['name'],
            'issued_by_id': form.cleaned_data['issued_by'].pk,
            'expected_completion_date': form.cleaned_data['expected_completion_date'].isoformat(),
            'release_from_id': warehouse.pk,
            'remark': form.cleaned_data['remark'],
        }

        # Also store the raw form data for the Back button
        self.request.session['picking_list_form_data'] = {
            'name': form.cleaned_data['name'],
            'issued_by': form.cleaned_data['issued_by'].pk,
            'expected_completion_date': form.cleaned_data['expected_completion_date'].isoformat(),
            'release_from': warehouse.pk,
            'remark': form.cleaned_data['remark'],
        }

        return self.render_to_response({
            'table': table,
            'form': form,
            'selection_form': PickingListReleaseOrderSelectionForm(),
            'warehouse': warehouse,
        })

    def get(self, request, *args, **kwargs):
        # Check if this is a search, sort, or pagination request
        if request.GET or request.headers.get('HX-Request'):
            # Get the warehouse ID from the session
            warehouse_id = request.session.get('warehouse_id')
            if not warehouse_id:
                # If no warehouse ID in session, return an error
                return HttpResponse(
                    '<div class="p-4 mb-4 text-sm text-red-700 bg-red-100 rounded-lg">Session expired. Please start again.</div>',
                    status=400
                )

            # Get the warehouse
            try:
                warehouse = Warehouse.objects.get(pk=warehouse_id)
            except Warehouse.DoesNotExist:
                return HttpResponse(
                    '<div class="p-4 mb-4 text-sm text-red-700 bg-red-100 rounded-lg">Invalid warehouse. Please start again.</div>',
                    status=400
                )

            # Query release orders for this warehouse
            queryset = WarehouseReleaseOrder.objects.filter(
                warehouses=warehouse,
                status=WarehouseReleaseOrder.Status.NEW,
                picking_list__isnull=True
            ).distinct()

            # Apply search if provided
            query = request.GET.get('query', '').strip()
            if query:
                queryset = queryset.filter(
                    Q(system_number__icontains=query) |
                    Q(customer_reference__icontains=query) |
                    Q(tag__icontains=query) |
                    Q(deliveryorder__system_number__icontains=query) |
                    Q(consignor_picking_list_no__icontains=query) |
                    Q(issued_by__username__icontains=query)
                ).distinct()

            # Create the table
            table = PickingListSelectReleaseOrderTable(queryset)
            table.htmx_base_url = self.get_htmx_base_url()
            table.table_height = 'max-h-[calc(100vh-20%)]'
            RequestConfig(request, paginate={"per_page": 10}).configure(table)

            # Get the form data from session
            form_data = request.session.get('picking_list_form_data', {})
            if form_data:
                form = PickingListCreateForm(initial=form_data, request=request)
            else:
                form = PickingListCreateForm(request=request)

            # Render the response
            return self.render_to_response({
                'table': table,
                'form': form,
                'selection_form': PickingListReleaseOrderSelectionForm(),
                'warehouse': warehouse,
            })

        # If not a filter request, redirect to the create page
        return redirect('pickings:picking_lists:create')

    def form_invalid(self, form):
        # Create a context with the form containing errors
        context = self.get_context_data(form=form)

        # Render the entire form template with errors
        from django.template.loader import render_to_string
        html = render_to_string('pickings/picking_lists/partials/basic_info_form.html', context, self.request)

        # Return the rendered form with a 422 status code to indicate validation error
        # Using 422 (Unprocessable Entity) instead of 400 to distinguish it from other errors
        return HttpResponse(html, status=422)


class PickingListSubmitView(FormView):
    """
    HTMX view that processes the complete form submission and creates the picking list.
    """
    form_class = PickingListReleaseOrderSelectionForm
    htmx_base_url = None  # Will be set dynamically in get_htmx_base_url

    def get_htmx_base_url(self):
        """
        Override to provide the dynamic URL for HTMX requests.
        This URL will be used for all HTMX requests including sorting and pagination.
        """
        # Get the base URL
        base_url = reverse("pickings:picking_lists:submit")

        # Always return the base URL - the table will handle sorting parameters
        return base_url

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['request'] = self.request
        return kwargs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get the step 1 data from session
        step1_data = self.request.session.get('picking_list_step1_data')
        if step1_data and 'release_from_id' in step1_data:
            # Get the warehouse
            try:
                warehouse = Warehouse.objects.get(pk=step1_data['release_from_id'])

                # Query release orders for this warehouse that are in NEW status and not assigned to a picking list
                queryset = WarehouseReleaseOrder.objects.filter(
                    warehouses=warehouse,
                    status=WarehouseReleaseOrder.Status.NEW,
                    picking_list__isnull=True
                ).distinct()

                # Apply search if provided
                query = self.request.GET.get('query', '').strip()
                if query:
                    queryset = queryset.filter(
                        Q(system_number__icontains=query) |
                        Q(customer_reference__icontains=query) |
                        Q(tag__icontains=query) |
                        Q(deliveryorder__system_number__icontains=query) |
                        Q(consignor_picking_list_no__icontains=query) |
                        Q(issued_by__username__icontains=query)
                    ).distinct()

                # Create the table
                table = PickingListSelectReleaseOrderTable(queryset)
                RequestConfig(self.request, paginate={"per_page": 50}).configure(table)

                # Add to context
                context['table'] = table
                context['warehouse'] = warehouse
            except Warehouse.DoesNotExist:
                pass

        # Add selection form to context
        context['selection_form'] = kwargs.get('form') or self.get_form()

        return context

    def form_valid(self, form):
        # Get the step 1 data from session
        step1_data = self.request.session.get('picking_list_step1_data')
        if not step1_data:
            return HttpResponse(
                '<div class="p-4 mb-4 text-sm text-red-700 bg-red-100 rounded-lg">Session expired. Please start again.</div>',
                status=400
            )

        # Get the selected release order IDs
        release_order_ids = form.cleaned_data['selected_release_orders']

        try:
            with transaction.atomic():
                # Create the picking list
                picking_list = PickingList.objects.create(
                    name=step1_data['name'],
                    issued_by_id=step1_data['issued_by_id'],
                    expected_completion_date=step1_data['expected_completion_date'],
                    release_from_id=step1_data['release_from_id'],
                    remark=step1_data['remark'],
                    status=PickingList.Status.NEW,
                )

                # Link the release orders to the picking list
                WarehouseReleaseOrder.objects.filter(pk__in=release_order_ids).update(picking_list=picking_list)

                # Generate picking list items
                picking_list.regenerate_picking_list_item()

                # Clear the session data
                if 'picking_list_step1_data' in self.request.session:
                    del self.request.session['picking_list_step1_data']
                if 'picking_list_form_data' in self.request.session:
                    del self.request.session['picking_list_form_data']

                # Add success message
                messages.success(self.request, _(f"Picking List {picking_list.system_number} successfully created"))

                # Return a redirect response for HTMX
                response = HttpResponse()
                response['HX-Redirect'] = reverse('pickings:picking_lists:panel', kwargs={'pk': picking_list.pk})
                return response

        except Exception as e:
            # Handle any errors
            return HttpResponse(
                f'<div class="p-4 mb-4 text-sm text-red-700 bg-red-100 rounded-lg">Error creating picking list: {str(e)}</div>',
                status=500
            )

    def form_invalid(self, form):
        # Create a context with the form containing errors
        context = self.get_context_data(form=form)

        # Render the release orders table template with errors
        from django.template.loader import render_to_string
        html = render_to_string('pickings/picking_lists/partials/release_orders_table.html', context, self.request)

        # Return the rendered form with a 422 status code to indicate validation error
        return HttpResponse(html, status=422)


# Register view functions
picking_list_create_view = PickingListCreateView.as_view()
picking_list_load_release_orders_view = PickingListLoadReleaseOrdersView.as_view()
picking_list_submit_view = PickingListSubmitView.as_view()

import json
from django.contrib import messages
from django.db import transaction
from django.db.models import Q
from django.http import HttpResponse
from django.shortcuts import get_object_or_404, render, redirect
from django.urls import reverse
from django.utils.translation import gettext_lazy as _
from django.views.generic import FormView, TemplateView

from django_tables2 import RequestConfig

from wms.cores.mixins import FormKwargsRequestMixin

from wms.apps.pickings.forms.picking_list_add_wro import PickingListAddReleaseOrderForm
from wms.apps.pickings.models import PickingList
from wms.apps.pickings.tables.picking_list import PickingListSelectReleaseOrderTable
from wms.apps.releases.models import WarehouseReleaseOrder


class PickingListAddReleaseOrderView(FormKwargsRequestMixin, TemplateView):
    """
    View for displaying the form to select release orders to add to a picking list.
    """
    template_name = "pickings/picking_lists/partials/add_release_order_form.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get the picking list
        picking_list = get_object_or_404(PickingList, pk=self.kwargs['pk'])
        context['picking_list'] = picking_list

        # Check if this is a table-only request
        if self.request.htmx and not self.request.GET.get('hxf'):
            self.template_name = "pickings/picking_lists/partials/release_order_table_only.html"

        # Check if the picking list is in a status that allows adding release orders
        if picking_list.status != PickingList.Status.NEW:
            context['error'] = _("Release orders can only be added to picking lists in 'New' status.")
            return context

        # Query release orders for this warehouse that are in NEW status and not assigned to a picking list
        queryset = WarehouseReleaseOrder.objects.filter(
            warehouses=picking_list.release_from,
            status=WarehouseReleaseOrder.Status.NEW,
            picking_list__isnull=True
        ).distinct()

        # Apply search if provided
        query = self.request.GET.get('query', '').strip()
        if query:
            queryset = queryset.filter(
                Q(system_number__icontains=query) |
                Q(customer_reference__icontains=query) |
                Q(tag__icontains=query) |
                Q(deliveryorder__system_number__icontains=query) |
                Q(consignor_picking_list_no__icontains=query) |
                Q(issued_by__username__icontains=query)
            ).distinct()

        # Create the table
        table = PickingListSelectReleaseOrderTable(queryset)
        table.htmx_base_url = self.request.path
        table.htmx_target = 'release-order-table-container'
        RequestConfig(self.request, paginate={"per_page": 10}).configure(table)

        # Add to context
        context['table'] = table

        # Only add form to context if not a table-only request
        context['form'] = PickingListAddReleaseOrderForm(request=self.request)

        return context


class PickingListAddReleaseOrderSubmitView(FormKwargsRequestMixin, FormView):
    """
    View for processing the form submission to add release orders to a picking list.
    """
    form_class = PickingListAddReleaseOrderForm

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['request'] = self.request
        return kwargs

    def form_valid(self, form):
        # Get the picking list
        picking_list = get_object_or_404(PickingList, pk=self.kwargs['pk'])

        # Check if the picking list is in a status that allows adding release orders
        if picking_list.status != PickingList.Status.NEW:
            if self.request.headers.get('HX-Request'):
                headers = {
                    'HX-Trigger': json.dumps({
                        "closeModalEvent": None,
                        "showNotificationEvent": {
                            "message": "Release orders can only be added to picking lists in 'New' status.",
                            "type": "error"
                        }
                    })
                }
                return HttpResponse(status=400, headers=headers)
            messages.error(self.request, _("Release orders can only be added to picking lists in 'New' status."))
            return redirect('pickings:picking_lists:detail', pk=picking_list.pk)

        # Get the selected release order IDs
        release_order_ids = form.cleaned_data['selected_release_orders']

        try:
            with transaction.atomic():
                # Link the release orders to the picking list
                WarehouseReleaseOrder.objects.filter(pk__in=release_order_ids).update(picking_list=picking_list)

                # Generate picking list items
                picking_list.regenerate_picking_list_item()

                # Add success message
                messages.success(self.request, _("Release orders successfully added to the picking list."))

                # For HTMX requests, return a success response with a refresh trigger
                if self.request.headers.get('HX-Request'):
                    # Get the URL for the release order list view
                    release_order_list_url = reverse('pickings:picking_lists:detail', kwargs={'pk': picking_list.pk})

                    # Set up HTMX triggers for success notification, modal close, and content refresh
                    trigger_data = {
                        "closeModalEvent": None,
                        "showNotificationEvent": {
                            "message": "Release orders successfully added to the picking list!",
                            "type": "success"
                        },
                        "refreshTabContent": {
                            "url": release_order_list_url
                        }
                    }

                    headers = {
                        'HX-Trigger': json.dumps(trigger_data)
                    }

                    # Return an empty response with headers
                    return HttpResponse('', headers=headers, status=200)

                # For regular form submissions, redirect to the picking list detail page
                return redirect('pickings:picking_lists:detail', pk=picking_list.pk)

        except Exception as e:
            # If an error occurs, return an error response
            if self.request.headers.get('HX-Request'):
                headers = {
                    'HX-Trigger': json.dumps({
                        "closeModalEvent": None,
                        "showNotificationEvent": {
                            "message": f"Failed to add release orders: {str(e)}",
                            "type": "error"
                        }
                    })
                }
                return HttpResponse(status=500, headers=headers)

            messages.error(self.request, _("Failed to add release orders: %(error)s") % {'error': str(e)})
            return redirect('pickings:picking_lists:detail', pk=picking_list.pk)

    def form_invalid(self, form):
        # For HTMX requests, return the form with errors
        if self.request.htmx:
            picking_list = get_object_or_404(PickingList, pk=self.kwargs['pk'])

            # Query release orders for this warehouse that are in NEW status and not assigned to a picking list
            queryset = WarehouseReleaseOrder.objects.filter(
                warehouses=picking_list.release_from,
                status=WarehouseReleaseOrder.Status.NEW,
                picking_list__isnull=True
            ).distinct()

            # Create the table
            table = PickingListSelectReleaseOrderTable(queryset)
            table.htmx_base_url = reverse('pickings:picking_lists:add_release_order', kwargs={'pk': picking_list.pk})
            table.htmx_target = 'release-order-table-container'
            RequestConfig(self.request, paginate={"per_page": 10}).configure(table)

            # Render the form with errors
            return render(
                self.request,
                'pickings/picking_lists/partials/add_release_order_form.html',
                {
                    'picking_list': picking_list,
                    'table': table,
                    'form': form,
                }
            )

        # For regular form submissions, redirect to the picking list detail page with an error message
        messages.error(self.request, _("Please select at least one release order."))
        return redirect('pickings:picking_lists:detail', pk=self.kwargs['pk'])


# Register view functions
picking_list_add_release_order_view = PickingListAddReleaseOrderView.as_view()
picking_list_add_release_order_submit_view = PickingListAddReleaseOrderSubmitView.as_view()

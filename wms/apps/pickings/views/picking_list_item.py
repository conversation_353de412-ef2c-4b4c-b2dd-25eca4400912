# from typing import Any

# from django.contrib import messages
# from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
# from django.db.models import BLANK_CHOICE_DASH
# from django.shortcuts import redirect, render
# from django.urls import reverse
# from django.utils.translation import gettext_lazy as _

# from wss.cores.views import CoreDetailView, CoreFormView

# from wss.apps.pickings.forms import PickingListWROItemCreateForm, PickingListWroRemoveForm
# from wss.apps.pickings.models import PickingList, PickingListItem
# from wss.apps.releases.models import WarehouseReleaseOrder

# ############
# # FOR HTMX #
# ############


# class PickingListItemTrView(LoginRequiredMixin, PermissionRequiredMixin, CoreDetailView):
#     """Partial page to display selected PickingListItem's table tr HTML
#     based on given PickingListItem's pk."""

#     model = PickingListItem
#     template_name = "pickings/picking_lists/partials/htmx/_item_tr.html"

#     permission_required = ("pickings.view_pickinglistitem",)


# picking_list_item_tr_view = PickingListItemTrView.as_view()


# class PickingListWROItemCreateView(LoginRequiredMixin, PermissionRequiredMixin, CoreFormView):
#     """Partial page to add PickingListItem based on given Picking List's pk."""

#     form_class = PickingListWROItemCreateForm
#     template_name = "pickings/picking_lists/partials/htmx/_wro_item_create_form.html"

#     permission_required = ("pickings.add_pickinglistitem",)

#     success_message = _("Picking list %(numbering)s's items successfully added")

#     def get_picking_list_obj(self) -> PickingList:
#         return PickingList.objects.get(pk=self.kwargs["picking_list_pk"])

#     def get_form(self):
#         form = super().get_form()
#         wro_qs = WarehouseReleaseOrder.objects.filter(
#             warehouses__in=[self.picking_list_obj.release_from],
#             picking_list__isnull=True,
#             status=WarehouseReleaseOrder.Status.NEW,
#         )
#         wro_choices = BLANK_CHOICE_DASH
#         form.fields["release_order"].choices = wro_choices + [(wro.pk, wro) for wro in wro_qs]
#         return form

#     def get_initial(self) -> dict[str, Any]:
#         initial = super().get_initial()

#         self.picking_list_obj = self.get_picking_list_obj()

#         return initial

#     def get_context_data(self, **kwargs) -> Any:
#         context = super().get_context_data(**kwargs)
#         context["picking_list_obj"] = self.picking_list_obj
#         return context

#     def get_success_url(self) -> str:
#         return reverse("pickings:picking_lists:detail", kwargs={"pk": self.kwargs["picking_list_pk"]})

#     def get_success_message(self, cleaned_data) -> str:
#         return self.success_message % {"numbering": self.picking_list_obj.numbering}

#     def post(self, request, *args, **kwargs):
#         """
#         Handle POST requests: instantiate a form instance with the passed
#         POST variables and then check if it's valid.
#         """
#         form = self.get_form()

#         if form.is_valid():
#             wro = form.cleaned_data["release_order"]
#             wro.picking_list = self.picking_list_obj
#             wro.save(
#                 update_fields=[
#                     "picking_list",
#                 ]
#             )

#             self.picking_list_obj.regenerate_picking_list_item()

#             form_valid = self.form_valid(form)
#             return form_valid
#         else:
#             return self.form_invalid(form)


# picking_list_wro_item_create_view = PickingListWROItemCreateView.as_view()


# class PickingListWroRemoveView(LoginRequiredMixin, PermissionRequiredMixin, CoreFormView):
#     """Partial page to add PickingListItem based on given Picking List's pk."""

#     form_class = PickingListWroRemoveForm
#     template_name = "pickings/picking_lists/partials/htmx/_wro_remove_form.html"

#     permission_required = (
#         "releases.change_warehousereleaseorder",
#         "pickings.change_pickinglist",
#     )

#     success_message = _("Warehouse Release Order numbering %(numbering)s successfully Removed")

#     def get_removed_wro_obj(self):
#         return WarehouseReleaseOrder.objects.get(pk=self.kwargs["pk"])

#     def get_form(self):
#         form = super().get_form()
#         wro_qs = WarehouseReleaseOrder.objects.filter(
#             warehouses__in=[self.removed_wro_obj.picking_list.release_from],
#             picking_list__isnull=False,
#             status=WarehouseReleaseOrder.Status.NEW,
#         )
#         wro_choices = BLANK_CHOICE_DASH
#         form.fields["release_order"].choices = wro_choices + [(wro.pk, wro) for wro in wro_qs]
#         return form

#     def get_initial(self):
#         initial = super().get_initial()

#         self.removed_wro_obj = self.get_removed_wro_obj()
#         self.picking_list = self.removed_wro_obj.picking_list
#         initial["release_order"] = self.removed_wro_obj

#         return initial

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         context["removed_wro_obj"] = self.removed_wro_obj
#         return context

#     def get_success_url(self):
#         return reverse("pickings:picking_lists:detail", kwargs={"pk": self.picking_list.pk})

#     def get_success_message(self, cleaned_data):
#         return self.success_message % {"numbering": self.removed_wro_obj.numbering}

#     def post(self, request, *args, **kwargs):
#         """
#         Handle POST requests: instantiate a form instance with the passed
#         POST variables and then check if it's valid.
#         """
#         form = self.get_form()

#         if form.is_valid():
#             if self.picking_list.status != PickingList.Status.NEW:
#                 messages.error(self.request, "Only New Picking List can be remove WRO")

#                 # Special handle for HTMX
#                 if request.headers.get("hx-request") == "true":
#                     return render(request, "partials/_alert.html")

#                 return redirect(reverse("pickings:picking_lists:detail", kwargs={"pk": self.kwargs["picking_list_pk"]}))

#             self.removed_wro_obj.picking_list = None
#             self.removed_wro_obj.save(
#                 update_fields=[
#                     "picking_list",
#                 ]
#             )

#             self.picking_list.regenerate_picking_list_item()

#             form_valid = self.form_valid(form)
#             return form_valid
#         else:
#             return self.form_invalid(form)


# picking_list_wro_remove_view = PickingListWroRemoveView.as_view()

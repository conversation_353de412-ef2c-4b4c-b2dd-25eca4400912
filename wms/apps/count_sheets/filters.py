# from django.db.models import Q, QuerySet
# from django.utils.translation import gettext_lazy as _

# import django_filters as filters

# from wss.apps.rackings.models.rack import Rack

# from .models import CountSheet


# def get_floor_chamber_rack_choices():
#     choices = []
#     racks = Rack.objects.filter(rack_type=Rack.RackType.RACK, numchild__gt=0)
#     for rack in racks:
#         choices.append([rack.__str__(), rack.__str__()])
#     return choices


# class CountSheetFilter(filters.FilterSet):
#     keyword = filters.CharFilter(
#         label=_("Doc ID contains"),
#         method="custom_keyword_filter",
#     )
#     floor_chamber_rack = filters.MultipleChoiceFilter(choices=get_floor_chamber_rack_choices)

#     class Meta:
#         model = CountSheet
#         fields = ["warehouse", "floor_chamber_rack"]

#     def __init__(self, data=None, queryset=None, *, request=None, prefix=None):
#         super().__init__(data=data, queryset=queryset, request=request, prefix=prefix)

#         self.filters["warehouse"].field.widget.attrs.update({"class": "form-control core-select2"})
#         self.filters["floor_chamber_rack"].label = _("Floor")
#         self.filters["floor_chamber_rack"].field.widget.attrs.update({"class": "form-control core-select2"})

#     def custom_keyword_filter(self, queryset: QuerySet[CountSheet], name: str, value) -> QuerySet[CountSheet]:
#         qs = queryset

#         for keyword in value.split():
#             qs = qs.filter(Q(doc_id__icontains=keyword))

#         return qs.distinct()

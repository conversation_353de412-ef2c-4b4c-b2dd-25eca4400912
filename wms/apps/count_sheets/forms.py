# from django import forms
# from django.core.exceptions import ValidationError
# from django.utils.translation import gettext_lazy as _

# from wms.cores.forms import CoreModelForm
# from wms.cores.utils import get_warehouses_qs_with_racking

# from wms.apps.rackings.models import Rack
# from wms.apps.settings.models import Warehouse

# from .models import CountSheet


# class CountSheetForm(CoreModelForm):
#     """Form for CountSheet."""

#     floor_chamber_rack = forms.ModelChoiceField(
#         label=_("Floor"), required=True, queryset=Rack.objects.filter(rack_type=Rack.RackType.RACK, numchild__gt=0)
#     )
#     level = forms.CharField(widget=forms.Select())

#     class Meta:
#         model = CountSheet
#         fields = [
#             "doc_id",
#             "issued_by",
#             "warehouse",
#             "floor_chamber_rack",
#             "level",
#         ]

#     def __init__(self, *args, **kwargs):
#         super().__init__(*args, **kwargs)

#         self.fields["issued_by"].disabled = True

#         # Override the wizard class for the fields
#         self.fields["warehouse"].widget.attrs["class"] = "form-control core-select2 warehouse"
#         self.fields["floor_chamber_rack"].widget.attrs["class"] = "form-control core-select2 floor-chamber-rack"
#         self.fields["level"].widget.attrs["class"] = "form-control core-select2 level"

#         # Override dropdown choices during runtime
#         self.fields["warehouse"].queryset = get_warehouses_qs_with_racking()

#         # To update Floor's form field in UpdateView based on CountSheet's warehouse FK
#         count_sheet = self.initial.get("count_sheet", None)
#         if count_sheet is not None:
#             warehouse_pk = self.request.POST.get("warehouse")
#             warehouse = Warehouse.objects.get(pk=warehouse_pk) if warehouse_pk is not None else count_sheet.warehouse
#             self.fields["floor_chamber_rack"].queryset = Rack.objects.filter(
#                 rack_type=Rack.RackType.RACK,
#                 numchild__gt=0,
#                 warehouse=warehouse,
#             )

#     def clean_floor_chamber_rack(self):
#         data = self.cleaned_data["floor_chamber_rack"]

#         if not isinstance(data, Rack):
#             raise ValidationError(f"Selected Floor {data.full_name} is not a valid Rack.")
#         return data

#     def clean_level(self):
#         """
#         Save the selected LEVELs into string format, separated by strings. I.e.:
#         "1, 3, 4"
#         """
#         data = self.request.POST.getlist("level", [])

#         if len(data) == 0:
#             raise ValidationError("You need to select at least 1 available Level")

#         data = ", ".join(data)
#         return data

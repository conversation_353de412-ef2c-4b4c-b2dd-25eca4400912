# from typing import Any, Union

# from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
# from django.db.models import BLANK_CHOICE_DASH, QuerySet
# from django.http import HttpRequest, HttpResponse
# from django.urls import reverse, reverse_lazy
# from django.utils.translation import gettext_lazy as _

# from django_filters.views import FilterView
# from django_tables2 import SingleTableMixin
# from django_weasyprint import WeasyTemplateResponseMixin
# from openpyxl import Workbook
# from openpyxl.styles import Alignment, Font

# from wms.cores.utils import localtime_now
# from wms.cores.views import (
#     CoreCreateView,
#     CoreDataTablesView,
#     CoreDeleteView,
#     CoreListView,
#     CoreTemplateView,
#     CoreUpdateView,
# )

# from wms.apps.count_sheets.forms import CountSheetForm
# from wms.apps.rackings.models import Rack, Rack<PERSON>alance
# from wms.apps.settings.models.warehouse import Warehouse

# from ..filters import CountSheetFilter
# from ..models import CountSheet
# from ..tables import CountSheetDataTables


# class CountSheetListView(LoginRequiredMixin, PermissionRequiredMixin, FilterView, SingleTableMixin, CoreListView):
#     """Page to show all CountSheet. This page use DataTables server side."""

#     model = CountSheet
#     template_name = "count_sheets/list.html"

#     table_class = CountSheetDataTables
#     filterset_class = CountSheetFilter

#     # To prevent query all object as it will use ajax call in template
#     object_list = CountSheet.objects.none()

#     header_title = "Count Sheets"
#     selected_page = "reports"
#     selected_subpage = "Count Sheets"

#     permission_required = ("count_sheets.view_countsheet",)

#     def get_queryset(self):
#         """To prevent query all object."""
#         return self.object_list


# count_sheet_list_view = CountSheetListView.as_view()


# class CountSheetDeleteView(LoginRequiredMixin, PermissionRequiredMixin, CoreDeleteView):
#     """Page to delete selected CountSheet based on given CountSheet's pk."""

#     model = CountSheet
#     success_url = reverse_lazy("count_sheets:list")
#     success_message = _("Count Sheet %(doc_id)s successfully deleted")

#     permission_required = ("count_sheets.delete_countsheet",)

#     def get_success_message(self):
#         return self.success_message % {"doc_id": self.object.doc_id}


# count_sheet_delete_view = CountSheetDeleteView.as_view()


# class CountSheetCreateAndUpdateMixin:
#     """Mixin for Create and Update CountSheet."""

#     model = CountSheet
#     form_class = CountSheetForm
#     template_name = "count_sheets/create_or_update.html"

#     selected_page = "reports"
#     selected_subpage = "Count Sheets"

#     def get_initial(self) -> dict[str, Any]:
#         initial = super().get_initial()
#         initial["issued_by"] = self.request.user

#         if self.object is not None:
#             initial["count_sheet"] = self.object

#         return initial

#     def get_success_url(self) -> str:
#         return reverse("count_sheets:list")


# class CountSheetCreateView(CountSheetCreateAndUpdateMixin, LoginRequiredMixin, PermissionRequiredMixin, CoreCreateView):
#     """Page to create CountSheet."""

#     permission_required = ("count_sheets.add_countsheet",)

#     success_message = _("Count Sheet %(doc_id)s successfully created")

#     header_title = "New Count Sheet"


# count_sheet_create_view = CountSheetCreateView.as_view()


# class CountSheetUpdateView(CountSheetCreateAndUpdateMixin, LoginRequiredMixin, PermissionRequiredMixin, CoreUpdateView):
#     """Page to update CountSheet."""

#     permission_required = ("count_sheets.change_countsheet",)

#     success_message = _("Count Sheet %(doc_id)s successfully updated")

#     header_title = "Update Count Sheet"

#     def get_context_data(self, **kwargs) -> dict[str, Any]:
#         context = super().get_context_data(**kwargs)
#         selected_floor = Rack.objects.get(full_name=self.object.floor_chamber_rack)
#         context["selected_floor_pk"] = selected_floor.pk
#         return context


# count_sheet_update_view = CountSheetUpdateView.as_view()


# class CountSheetPDFView(WeasyTemplateResponseMixin, CoreTemplateView):
#     """Function to generate PDF for Count Sheet."""

#     template_name = "count_sheets/count_sheet_pdf.html"

#     pdf_attachment = False

#     permission_required = ("count_sheets.view_count_sheet",)

#     def get_count_sheet_obj(self):
#         count_sheet = CountSheet.objects.get(pk=self.kwargs["pk"])
#         return count_sheet

#     def get_count_sheet_racks_dict(self, count_sheet):

#         # prepared_dict = {
#         #     "5-1-A-1-1-1": [stock_obj]
#         #     "5-1-A-1-1-1": [
#         #         stock_obj_1,
#         #         stock_obj_2,
#         #     ]
#         #     "5-1-A-1-1-2": [stock_obj]
#         #     "5-1-A-1-1-3": [stock_obj]
#         #     "5-1-A-1-2-1": ""
#         #     "5-1-A-1-2-2": ""
#         #     "5-1-A-1-2-3": [stock_obj]
#         # }

#         prepared_dict = {}

#         level_str = count_sheet.level
#         cleaned_level_list = [e.strip() for e in level_str.split(",") if e.strip()]

#         rack_obj = Rack.objects.get(full_name=count_sheet.floor_chamber_rack, rack_type=Rack.RackType.RACK)
#         level_qs = rack_obj.get_descendants().filter(rack_type=Rack.RackType.LEVEL, name__in=cleaned_level_list)

#         for level in level_qs:
#             pallets_qs = level.get_descendants()

#             for pallet in pallets_qs:
#                 rack_storage_qs = pallet.rackstorage_set.all()
#                 prepared_dict[pallet.full_name] = []

#                 if rack_storage_qs:
#                     # prepared_dict[pallet.full_name] = []
#                     for rack_storage in rack_storage_qs:
#                         prepared_dict[pallet.full_name].append(rack_storage.stock)

#         return prepared_dict

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         count_sheet = self.get_count_sheet_obj()
#         count_sheet_dict = self.get_count_sheet_racks_dict(count_sheet)

#         context["count_sheet"] = count_sheet
#         context["count_sheet_dict"] = count_sheet_dict
#         return context

#     def get_pdf_filename(self):
#         count_sheet = self.get_count_sheet_obj()
#         return f"{count_sheet.doc_id}-{localtime_now().strftime('%Y%m%d-%H%M')}.pdf"


# count_sheet_pdf_view = CountSheetPDFView.as_view()


# class CountSheetWithQtyPDFView(WeasyTemplateResponseMixin, CoreTemplateView):
#     """Function to generate PDF for Count Sheet."""

#     template_name = "count_sheets/count_sheet_with_qty_pdf.html"

#     pdf_attachment = False

#     permission_required = ("count_sheets.view_count_sheet",)

#     def get_count_sheet_obj(self):
#         count_sheet = CountSheet.objects.get(pk=self.kwargs["pk"])
#         return count_sheet

#     def get_count_sheet_racks_dict(self, count_sheet):

#         # prepared_dict = {
#         #     "5-1-A-1-1-1": [rack_balance_obj]
#         #     "5-1-A-1-1-1": [
#         #         rack_balance_obj_1,
#         #         rack_balance_obj_2,
#         #     ]
#         #     "5-1-A-1-1-2": [rack_balance_obj]
#         #     "5-1-A-1-1-3": [rack_balance_obj]
#         #     "5-1-A-1-2-1": ""
#         #     "5-1-A-1-2-2": ""
#         #     "5-1-A-1-2-3": [rack_balance_obj]
#         # }

#         prepared_dict = {}

#         level_str = count_sheet.level
#         cleaned_level_list = [e.strip() for e in level_str.split(",") if e.strip()]

#         rack_obj = Rack.objects.get(full_name=count_sheet.floor_chamber_rack, rack_type=Rack.RackType.RACK)
#         level_qs = rack_obj.get_descendants().filter(rack_type=Rack.RackType.LEVEL, name__in=cleaned_level_list)

#         for level in level_qs:
#             pallets_qs = level.get_descendants()

#             for pallet in pallets_qs:
#                 rack_storage_qs = pallet.rackstorage_set.all()
#                 prepared_dict[pallet.full_name] = []

#                 if rack_storage_qs:
#                     # prepared_dict[pallet.full_name] = []
#                     for rack_storage in rack_storage_qs:
#                         rack_balance = RackBalance.objects.get(rack=rack_storage.rack, stock=rack_storage.stock)
#                         prepared_dict[pallet.full_name].append(rack_balance)

#         print("prepared_dict: ", prepared_dict)
#         return prepared_dict

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         count_sheet = self.get_count_sheet_obj()
#         count_sheet_dict = self.get_count_sheet_racks_dict(count_sheet)

#         context["count_sheet"] = count_sheet
#         context["count_sheet_dict"] = count_sheet_dict
#         return context

#     def get_pdf_filename(self):
#         count_sheet = self.get_count_sheet_obj()
#         return f"{count_sheet.doc_id}-{localtime_now().strftime('%Y%m%d-%H%M')}.pdf"


# count_sheet_with_qty_pdf_view = CountSheetWithQtyPDFView.as_view()


# ##################
# # FOR DataTables #
# ##################


# class CountSheetDataTablesView(LoginRequiredMixin, PermissionRequiredMixin, CoreDataTablesView):
#     """JSON for DataTables in list page."""

#     model = CountSheet
#     table_class = CountSheetDataTables
#     filterset_class = CountSheetFilter

#     permission_required = ("count_sheets.view_countsheet",)

#     def get_queryset(self) -> QuerySet[CountSheet]:
#         return self.model.objects.select_related("issued_by").all()


# count_sheet_datatables_view = CountSheetDataTablesView.as_view()


# ############
# # FOR HTMX #
# ############


# class CountSheetFloorDropdownListView(LoginRequiredMixin, PermissionRequiredMixin, CoreListView):
#     """
#     Partial page to show available select2 dropdown Floor-Chamber-Rack values based on selected Warehouse's pk.
#     """

#     model = Rack
#     template_name = "count_sheets/partials/htmx/_single_dropdown.html"

#     permission_required = ("count_sheets.view_countsheet",)

#     def get_warehouse(self) -> Warehouse:
#         pk = self.request.GET.get("warehouse_pk", None)

#         if pk not in ["", None]:
#             warehouse = Warehouse.objects.get(pk=pk)
#         else:
#             # Default to selecting 3PL2
#             warehouse = Warehouse.objects.get(slug="3pl2")

#         return warehouse

#     def get_count_sheet_filter(self) -> Union[CountSheet, None]:
#         pk = self.request.GET.get("count_sheet_filter_pk", None)

#         if pk not in ["", None]:
#             count_sheet_filter = CountSheet.objects.get(pk=pk)
#         else:
#             count_sheet_filter = None
#         return count_sheet_filter

#     def get_context_data(self, **kwargs) -> dict[str, Any]:
#         context = super().get_context_data(**kwargs)
#         self.count_sheet_filter = self.get_count_sheet_filter()

#         if self.count_sheet_filter is not None:
#             # Only display pre-selected M2M Levels for created/existed CountSheet
#             context["pre_selected_options"] = self.count_sheet_filter.floor_chamber_rack

#         warehouse = self.get_warehouse()
#         floors = warehouse.rack_set.filter(rack_type=Rack.RackType.RACK, numchild__gt=0)
#         context["object_list"] = floors
#         context["blank_choice_dash"] = BLANK_CHOICE_DASH[0][1]

#         return context


# count_sheet_floor_dropdown_list_view = CountSheetFloorDropdownListView.as_view()


# class CountSheetMultiSelectLevelDropdownListView(LoginRequiredMixin, PermissionRequiredMixin, CoreListView):
#     """
#     Partial page to show multiple select Level values based on selected CountSheets's pk.

#     - CountSheet is responsible for pre-populating multiple pre-selected values in Level form field.
#     - Level form field select2 dropdown choices is based on selected value in Floor's form field.
#     """

#     model = Rack
#     template_name = "count_sheets/partials/htmx/_multi_select_dropdown.html"

#     permission_required = ("count_sheets.view_countsheet",)

#     def get_rack(self) -> Rack:
#         pk = self.request.GET.get("rack_pk", None)

#         if pk not in ["", None]:
#             rack = Rack.objects.get(pk=pk)
#         else:
#             rack = Rack.objects.get(full_name=self.count_sheet_filter.floor_chamber_rack)

#         return rack

#     def get_count_sheet_filter(self) -> Union[CountSheet, None]:
#         pk = self.request.GET.get("count_sheet_filter_pk", None)

#         if pk not in ["", None]:
#             count_sheet_filter = CountSheet.objects.get(pk=pk)
#         else:
#             count_sheet_filter = None
#         return count_sheet_filter

#     def get_all_levels_by_rack(self) -> list:
#         """Get all available Level dropdown values based on Rack."""
#         rack = self.get_rack()
#         pallet_nodes = rack.get_descendants().filter(rack_type=Rack.RackType.PALLET, numchild=0)
#         distinct_level = set()

#         for node in pallet_nodes:
#             name_list = node.full_name.split("-")

#             # To prevent IndexError when preparing distinct Level dropdown options
#             if len(name_list) >= 2:
#                 distinct_level.add(name_list[-2])

#         return sorted(list(distinct_level))

#     def get_context_data(self, **kwargs) -> dict[str, Any]:
#         context = super().get_context_data(**kwargs)
#         self.count_sheet_filter = self.get_count_sheet_filter()

#         if self.count_sheet_filter is not None:
#             # Only display pre-selected M2M Levels for created/existed CountSheet
#             context["pre_selected_options"] = self.count_sheet_filter.level.split(", ")

#         context["object_list"] = self.get_all_levels_by_rack()

#         return context


# count_sheet_multi_select_level_dropdown_list_view = CountSheetMultiSelectLevelDropdownListView.as_view()


# ####################
# # FOR Export Excel #
# ####################


# def count_sheet_export_to_xlsx(request: HttpRequest, pk: int) -> HttpResponse:
#     """
#     Downloads Count Sheet's involved rack as an Excel file.
#     """

#     count_sheet = CountSheet.objects.get(pk=pk)

#     response = HttpResponse(
#         content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
#     )
#     today_date = localtime_now().strftime("%Y.%m.%d")
#     response["Content-Disposition"] = f"attachment; filename={count_sheet.doc_id}-{today_date}.xlsx"

#     workbook = Workbook()

#     # Get active worksheet/tab
#     worksheet = workbook.active
#     # worksheet.title = "Stock Items"

#     # Define the titles for columns
#     balance_columns = [
#         "Location",
#         "Product Code",
#         "Description",
#         "Batch",
#         "Expiry Date",
#         "QTY",
#         "Remarks",
#     ]
#     balance_row_num = 1

#     # Assign the titles for each cell of the header
#     for col_num, column_title in enumerate(balance_columns, 1):
#         cell = worksheet.cell(row=balance_row_num, column=col_num)
#         cell.value = column_title
#         cell.font = Font(bold=True)
#         cell.alignment = Alignment(horizontal="center")

#     balance_row_num += 1

#     prepared_dict = {}
#     level_str = count_sheet.level
#     cleaned_level_list = [e.strip() for e in level_str.split(",") if e.strip()]

#     rack_obj = Rack.objects.get(full_name=count_sheet.floor_chamber_rack, rack_type=Rack.RackType.RACK)
#     level_qs = rack_obj.get_descendants().filter(rack_type=Rack.RackType.LEVEL, name__in=cleaned_level_list)

#     for level in level_qs:
#         pallets_qs = level.get_descendants()

#         for pallet in pallets_qs:
#             rack_storage_qs = pallet.rackstorage_set.all()
#             prepared_dict[pallet.full_name] = []

#             if rack_storage_qs:
#                 prepared_dict[pallet.full_name] = []
#                 for rack_storage in rack_storage_qs:
#                     rack_balance = RackBalance.objects.get(rack=rack_storage.rack, stock=rack_storage.stock)
#                     prepared_dict[pallet.full_name].append(rack_balance)

#     index = 1

#     for pallet_name, rack_balance_list in prepared_dict.items():
#         if rack_balance_list:
#             for rack_balance in rack_balance_list:
#                 index += 1
#                 # Define the data for each cell in the row
#                 row = [
#                     pallet_name,
#                     rack_balance.stock.item.code,
#                     rack_balance.stock.item.name,
#                     rack_balance.stock.batch_no,
#                     rack_balance.stock.expiry_date,
#                     float(rack_balance.formatted_balance),
#                     "",
#                 ]

#                 # Assign the data for each cell of the row
#                 for col_num, cell_value in enumerate(row, 1):
#                     cell = worksheet.cell(row=index, column=col_num)
#                     cell.value = cell_value
#                     cell.alignment = Alignment(horizontal="center")
#         else:
#             index += 1
#             # Define the data for each cell in the row
#             row = [
#                 pallet_name,
#                 "",
#                 "",
#                 "",
#                 "",
#                 "",
#                 "",
#             ]

#             # Assign the data for each cell of the row
#             for col_num, cell_value in enumerate(row, 1):
#                 cell = worksheet.cell(row=index, column=col_num)
#                 cell.value = cell_value
#                 cell.alignment = Alignment(horizontal="center")

#     workbook.save(response)

#     return response

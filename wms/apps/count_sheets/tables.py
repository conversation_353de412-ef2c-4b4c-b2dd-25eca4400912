# from django.utils.translation import gettext_lazy as _

# import django_tables2 as tables

# from wms.cores.models import DISPLAY_EMPTY_VALUE
# from wms.cores.tables import TABLE_ATTRS_CLASS

# from .models import CountSheet


# class CountSheetDataTables(tables.Table):
#     """Table used on count_sheet list page."""

#     actions = tables.TemplateColumn(
#         verbose_name=_("Actions"),
#         orderable=False,
#         template_name="count_sheets/partials/tables/server_side/_actions.html",
#         attrs={"th": {"class": "text-right"}, "td": {"class": "text-right"}},
#         exclude_from_export=True,
#     )

#     class Meta:
#         model = CountSheet
#         order_by = "doc_id"  # Must follow the first row display in the table
#         default = DISPLAY_EMPTY_VALUE
#         template_name = "partials/tables/_datatables_server_side.html"
#         attrs = {"class": TABLE_ATTRS_CLASS, "id": "count_sheet_datatables", "style": "display: none;"}
#         row_attrs = {"data-id": lambda record: record.pk}

#         fields = [
#             "doc_id",
#             "issued_by",
#             "warehouse",
#             "floor_chamber_rack",
#             "level",
#         ]
#         sequence = [
#             "doc_id",
#             "issued_by",
#             "warehouse",
#             "floor_chamber_rack",
#             "level",
#             "actions",
#         ]

#     def render_actions(self, column, record, table, value, bound_column, bound_row):
#         """Add permission checking into actions column."""
#         change_perms = self.request.user.has_perm("count_sheets.change_countsheet")
#         delete_perms = self.request.user.has_perm("count_sheets.delete_countsheet")
#         column.extra_context = {
#             "change_perms": change_perms,
#             "delete_perms": delete_perms,
#         }
#         return column.render(record, table, value, bound_column, **{"bound_row": bound_row})

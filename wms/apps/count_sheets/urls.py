# from django.urls import path

# from wss.apps.count_sheets.views import (
#     count_sheet_create_view,
#     count_sheet_datatables_view,
#     count_sheet_delete_view,
#     count_sheet_export_to_xlsx,
#     count_sheet_floor_dropdown_list_view,
#     count_sheet_list_view,
#     count_sheet_multi_select_level_dropdown_list_view,
#     count_sheet_pdf_view,
#     count_sheet_update_view,
#     count_sheet_with_qty_pdf_view,
# )

# app_name = "count_sheets"

# urlpatterns = [
#     path("", view=count_sheet_list_view, name="list"),
#     path("create/", view=count_sheet_create_view, name="create"),
#     path("delete/<int:pk>/", view=count_sheet_delete_view, name="delete"),
#     path("update/<int:pk>/", view=count_sheet_update_view, name="update"),
#     path("export/<int:pk>/", view=count_sheet_pdf_view, name="export-count-sheet"),
#     path("export-with-qty/<int:pk>/", view=count_sheet_with_qty_pdf_view, name="export-count-sheet-with-qty"),
#     path("export-without-qty-excel/<int:pk>/", view=count_sheet_export_to_xlsx, name="export-without-qty-excel"),
#     path("datatables/", view=count_sheet_datatables_view, name="datatables"),
#     path("dropdown/levels/", view=count_sheet_multi_select_level_dropdown_list_view, name="level_dropdown"),
#     path("dropdown/floor/", view=count_sheet_floor_dropdown_list_view, name="floor_dropdown"),
# ]

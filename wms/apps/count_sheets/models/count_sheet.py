from django.conf import settings
from django.db import models
from django.utils.translation import gettext_lazy as _

from wms.cores.models import AbstractBaseModel


class CountSheet(AbstractBaseModel):
    """CountSheet model for Warehouse Smart System.

    Available fields:

    * created               (AbstractBaseModel => TimeStampedModel)
    * modified              (AbstractBaseModel => TimeStampedModel)
    * created_by            (AbstractBaseModel)
    * modified_by           (AbstractBaseModel)
    * doc_id
    * issued_by
    * warehouse
    * floor_chamber_rack
    * level

    """

    doc_id = models.Char<PERSON>ield(verbose_name=_("Doc ID"), max_length=32, unique=True)
    issued_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.PROTECT)
    warehouse = models.ForeignKey("settings.Warehouse", limit_choices_to={"is_storage": True}, on_delete=models.PROTECT)
    floor_chamber_rack = models.Char<PERSON><PERSON>(verbose_name=_("Floor"), max_length=32, blank=True, null=True)
    level = models.CharField(verbose_name=_("Level"), max_length=32, blank=True, null=True)

    class Meta(AbstractBaseModel.Meta):
        pass

    def __str__(self) -> str:
        return f"{self.doc_id}"

    def get_all_rackstorages_order_by_levels(self):
        """
        Helper function to return list of data based on a given CountSheet.

        The main idea is that:
        - Each row consist of rack & storage (pointing to either available or empty stock).
        - Need to sort by Level (depth=5).
        """
        from wms.apps.rackings.models import Rack, RackStorage

        floor = Rack.objects.get(full_name=self.floor_chamber_rack, rack_type=Rack.RackType.RACK)
        levels = self.level.split(", ")

        racks = floor.get_descendants().filter(rack_type=Rack.RackType.PALLET, numchild=0).order_by("full_name")
        hashmap = {}

        for rack in racks:
            full_name_list = rack.full_name.split("-")
            if len(full_name_list) >= 2:
                if full_name_list[-2] in levels:
                    hashmap[rack.pk] = full_name_list[-2]

        # Sort the hashmap/dict based on value, rather than key
        sorted_hashmap = dict(sorted(hashmap.items(), key=lambda x: x[1]))

        results = []

        for key in sorted_hashmap.keys():
            rack = Rack.objects.get(pk=key)
            rack_storages = RackStorage.objects.filter(rack=rack)

            if rack_storages.count() > 0:
                for rack_storage in rack_storages:
                    results.append(rack_storage)
            else:
                results.append(rack)

        return results

from decimal import Decimal

from django.conf import settings
from django.db import models
from django.db.models import Sum
from django.urls import reverse
from django.utils.functional import cached_property
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _

from wms.cores.models import (
    DISPLAY_EMPTY_VALUE,
    AbstractBaseModel,
    AbstractSystemNumberModel,
    AbstractSortableModel,
    AbstractStockInModel,
    not_equal_zero,
)
from wms.cores.utils import localtime_now

from wms.apps.settings.utils import uom_converter


class Adjustment(AbstractSystemNumberModel, AbstractBaseModel):
    """Adjustment model for Warehouse Management System.

    Available fields:

    * created               (AbstractBaseModel => TimeStampedModel)
    * modified              (AbstractBaseModel => TimeStampedModel)
    * created_by            (AbstractBaseModel)
    * modified_by           (AbstractBaseModel)
    * system_number         (AbstractSystemNumberModel)
    * issued_by
    * adjustment_datetime
    * deliver_to
    * status
    * remark
    * customer_reference

    """

    class Status(models.TextChoices):
        DRAFT = "Draft", _("Draft")
        PROCESSING = "Processing", _("Processing")
        COMPLETED = "Completed", _("Completed")
        COMPLETED_WITH_REJECT = "Completed with reject", _("Completed with reject")

    issued_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    adjustment_datetime = models.DateTimeField(verbose_name=_("Adjustment Date Time"), default=localtime_now)
    deliver_to = models.ForeignKey(
        "settings.Warehouse",
        related_name="adjustments",
        limit_choices_to={"is_storage": True},
        on_delete=models.PROTECT,
    )
    status = models.CharField(verbose_name=_("Status"), max_length=32, choices=Status.choices, default=Status.DRAFT)
    remark = models.TextField(verbose_name=_("Remark"), max_length=512, blank=True)
    customer_reference = models.CharField(verbose_name=_("Customer Reference"), max_length=64, blank=True)

    class Meta(AbstractBaseModel.Meta):
        ordering = ["-system_number"]

    def __str__(self):
        return f"{self.system_number}"

    def get_absolute_url(self):
        return reverse("adjustments:adjustment:detail", kwargs={"pk": self.pk})

    @cached_property
    def html_status_display(self):
        """Return nice HTML display for status."""
        html_class = ""

        if self.status == Adjustment.Status.DRAFT:
            html_class = "no-export badge bg-theme-status-warning"
        elif self.status == Adjustment.Status.PROCESSING:
            html_class = "no-export badge bg-theme-status-info"
        elif self.status == Adjustment.Status.COMPLETED:
            html_class = "no-export badge bg-theme-status-success"
        elif self.status == Adjustment.Status.COMPLETED_WITH_REJECT:
            html_class = "no-export badge bg-theme-status-error"

        return format_html(f'<span class="{html_class}">{self.get_status_display()}</span>') or DISPLAY_EMPTY_VALUE


class AdjustmentItem(AbstractBaseModel, AbstractSortableModel):
    """AdjustmentItem model for Warehouse Management System.

    Available fields:

    * created           (AbstractBaseModel => TimeStampedModel)
    * modified          (AbstractBaseModel => TimeStampedModel)
    * created_by        (AbstractBaseModel)
    * modified_by       (AbstractBaseModel)
    * sort_order        (AbstractSortableModel)
    * adjustment
    * item
    * uom
    * quantity
    * batch_no
    * expiry_date
    * remark
    * status

    """

    class Status(models.TextChoices):
        DRAFT = "Draft", _("Draft")
        APPROVED = "Approved", _("Approved")
        REJECTED = "Rejected", _("Rejected")

    adjustment = models.ForeignKey("adjustments.Adjustment", on_delete=models.CASCADE)
    item = models.ForeignKey("inventories.Item", on_delete=models.CASCADE)
    uom = models.ForeignKey(
        "settings.UnitOfMeasure",
        verbose_name=_("UOM"),
        on_delete=models.RESTRICT,
        help_text=_("The item will be measured in terms of this unit (e.g.: kg, pcs, box)."),
    )
    quantity = models.DecimalField(
        verbose_name=_("Quantity"), max_digits=19, decimal_places=6, default=Decimal("0"), validators=[not_equal_zero]
    )
    batch_no = models.CharField(verbose_name=_("Batch No."), max_length=255, default="N/A")
    expiry_date = models.DateField(verbose_name=_("Expiry Date"), blank=True, null=True)
    remark = models.TextField(verbose_name=_("Remark"), max_length=512, blank=True)
    status = models.CharField(verbose_name=_("Status"), max_length=32, choices=Status.choices, default=Status.DRAFT)

    class Meta(AbstractBaseModel.Meta):
        ordering = ["sort_order"]

    def __str__(self):
        return f"{self.adjustment.system_number} :: {self.item.name} :: {self.quantity}"

    @cached_property
    def get_position(self):
        """Return position of item in Adjustment."""
        position = (
            list(
                self.__class__.objects.filter(adjustment=self.adjustment)
                .order_by("sort_order")
                .values_list("pk", flat=True)
            ).index(self.pk)
            + 1
        )

        return position

    @cached_property
    def get_expected_quantity(self):
        """Return current item's expected quantity"""

        expected_quantity = round(self.quantity, self.uom.unit_precision)
        return expected_quantity

    @cached_property
    def get_expected_converted_accurate_quantity(self):
        """Return current item's converted accurate expected quantity"""

        converted_quantity = uom_converter(
            origin_uom=self.uom,
            target_uom=self.item.uom,
            quantity=self.quantity,
        )

        return converted_quantity

    def get_adjustmentstockin(self):
        """Get All AdjustmentStockIn"""
        return self.adjustmentstockin_set.all()

    def create_adjustmentstockin(self, approved_by):
        """Create child AdjustmentStockIn

        Action:
        - get or create AdjustmentStockIn

        Rules:
        - To prevent AdjustmentStockIn being create on every save()
        """
        adjustment_stock_in = None
        if self.status == AdjustmentItem.Status.APPROVED and self.get_adjustmentstockin().count() == 0:
            adjustment_stock_in = AdjustmentStockIn.objects.create(
                adjustment_item=self,
                deliver_to=self.adjustment.deliver_to,
                approved_by=approved_by,
                approved_quantity=self.quantity,
                batch_no=self.batch_no,
                stock_in_datetime=self.adjustment.adjustment_datetime,
                item=self.item,
                uom=self.uom,
            )
        return adjustment_stock_in

    @cached_property
    def get_transaction_approved_quantity(self):
        """Return current item's approved quantity based on transaction's system quantity."""

        transaction_system_quantity = self.adjustmentstockin_set.all().aggregate(
            Sum("transaction__system_quantity")
        ).get("transaction__system_quantity__sum", Decimal("0")) or Decimal("0")

        transaction_system_quantity = round(transaction_system_quantity, self.item.uom.unit_precision)
        return transaction_system_quantity

    @cached_property
    def html_status_display(self):
        """Return nice HTML display for status."""
        html_class = ""

        if self.status == AdjustmentItem.Status.DRAFT:
            html_class = "no-export badge bg-theme-status-warning"
        elif self.status == AdjustmentItem.Status.APPROVED:
            html_class = "no-export badge bg-theme-status-success"
        elif self.status == AdjustmentItem.Status.REJECTED:
            html_class = "no-export badge bg-theme-status-error"

        return format_html(f'<span class="{html_class}">{self.get_status_display()}</span>') or DISPLAY_EMPTY_VALUE


    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)

        if self.status == AdjustmentItem.Status.APPROVED:

            all_adjustmentitem = self.adjustment.adjustmentitem_set.all()
            all_draft_adjustmentitem = all_adjustmentitem.filter(status=AdjustmentItem.Status.DRAFT)
            all_rejected_adjustmentitem = all_adjustmentitem.filter(status=AdjustmentItem.Status.REJECTED)

            if all_draft_adjustmentitem.count() > 0:
                self.adjustment.status = Adjustment.Status.PROCESSING
            elif all_rejected_adjustmentitem.count() > 0:
                self.adjustment.status = Adjustment.Status.COMPLETED_WITH_REJECT
            else:
                self.adjustment.status = Adjustment.Status.COMPLETED

            self.adjustment.save(update_fields=["status"])

        elif self.status == AdjustmentItem.Status.REJECTED:

            all_adjustmentitem = self.adjustment.adjustmentitem_set.all()
            all_draft_adjustmentitem = all_adjustmentitem.filter(status=AdjustmentItem.Status.DRAFT)

            if all_draft_adjustmentitem.count() > 0:
                self.adjustment.status = Adjustment.Status.PROCESSING
            else:
                self.adjustment.status = Adjustment.Status.COMPLETED_WITH_REJECT

            self.adjustment.save(update_fields=["status"])


class AdjustmentStockIn(AbstractBaseModel, AbstractStockInModel):
    """AdjustmentStockIn model for Warehouse Management System.

    Available fields:

    * created           (AbstractBaseModel => TimeStampedModel)
    * modified          (AbstractBaseModel => TimeStampedModel)
    * created_by        (AbstractBaseModel)
    * modified_by       (AbstractBaseModel)
    * deliver_to        (AbstractStockInModel)
    * approved_by       (AbstractStockInModel)
    * approved_quantity (AbstractStockInModel)
    * batch_no          (AbstractStockInModel)
    * expiry_date       (AbstractStockInModel)
    * stock_in_datetime (AbstractStockInModel)
    * item              (AbstractStockInModel)
    * uom               (AbstractStockInModel)
    * remark            (AbstractStockInModel)
    * transaction       (AbstractStockInModel)
    * adjustment_item

    """

    uom = models.ForeignKey(
        "settings.UnitOfMeasure", related_name="adjustment_stock_in_uoms", verbose_name=_("UOM"), on_delete=models.RESTRICT
    )
    adjustment_item = models.ForeignKey(
        "adjustments.AdjustmentItem",
        on_delete=models.CASCADE,
        help_text=_("Each Adjustment Item indicate a relation to item's delievered quantity."),
    )

    class Meta(AbstractBaseModel.Meta):
        ordering = ["stock_in_datetime", "pk"]

    def __str__(self):
        return f"{self.item.name} :: {self.deliver_to.name} :: {self.approved_quantity}"

    def save(self, *args, **kwargs):
        """Override save for AdjustmentStockIn.

        Set status of adjustment_item based on total_received_rejected_quantity.
        """
        stock_in_channel = "adjustment"
        super().save(stock_in_channel=stock_in_channel, *args, **kwargs)

        total_approved_quantity = self.adjustment_item.get_transaction_approved_quantity

        if total_approved_quantity == self.adjustment_item.get_expected_converted_accurate_quantity:
            self.adjustment_item.status = AdjustmentItem.Status.APPROVED
        else:
            self.adjustment_item.status = AdjustmentItem.Status.REJECTED

        self.adjustment_item.save(update_fields=["status"])

import datetime

from django.db.models import Q, Sum, OuterRef, Subquery, Value, DecimalField
from django.db.models.functions import Coalesce
from django.utils.translation import gettext_lazy as _
from django_filters import filters, FilterSet, ChoiceFilter

from wms.apps.inventories.models import Item, Transaction, Stock
from wms.apps.adjustments.models import Adjustment
from wms.cores.forms.widget import CoreSelectWidget, CoreSelectMultipleWidget, CoreTextWidget, CoreDateWidget, \
    CoreDateRangeWidget


class AdjustmentFilter(FilterSet):
    """
    Filter class for Item list view.
    Provides filtering capabilities for Item attributes.
    """
    date_range = filters.DateFromToRangeFilter(
        label=_("Adjustment Date Range"),
        field_name="adjustment_datetime",
        widget=CoreDateRangeWidget()
    )

    status = filters.ChoiceFilter(
        choices=Adjustment.Status.choices,
        label=_("Status"),
        widget=CoreSelectWidget()
    )

    class Meta:
        model = Adjustment
        fields = ['date_range', 'status']


class AdjustmentDataTableFilter(FilterSet):
    """
    Filter class for Adjustment DataTable list view.
    Provides filtering capabilities for Stock attributes.

    Widget attrs:
        'data-auto-submit' only work with filter_auto.html
    """
    status = filters.ChoiceFilter(
        choices=Adjustment.Status.choices,
        label=_("Status"),
        widget=CoreSelectWidget(attrs={
            'data-auto-submit': 'true'
        })
    )

    class Meta:
        model = Adjustment
        fields = ['status']


#############################################################
# Old Code, to be remove after everything is stabled.
#############################################################


# import datetime

# from django.db.models import Q
# from django.utils.translation import gettext_lazy as _

# import django_filters as filters

# from wss.cores.filters import AbstractHistoryFilter

# from .models import AdjustNote


# class AdjustNoteFilter(filters.FilterSet):
#     start_date = filters.DateFilter(
#         label=_("Adjustment Start Date"),
#         field_name="adjust_datetime",
#         lookup_expr=("gte"),
#     )
#     end_date = filters.DateFilter(
#         label=_("Adjustment End Date"),
#         field_name="adjust_datetime",
#         method="end_date_filter",
#     )
#     keyword = filters.CharFilter(
#         label=_("Numbering or Status contains"),
#         method="custom_keyword_filter",
#     )

#     class Meta:
#         model = AdjustNote
#         fields = [
#             "status",
#         ]

#     def __init__(self, data=None, queryset=None, *, request=None, prefix=None):
#         super().__init__(data=data, queryset=queryset, request=request, prefix=prefix)

#         self.filters["start_date"].field.widget.attrs.update({"class": "form-control"})
#         self.filters["end_date"].field.widget.attrs.update({"class": "form-control"})
#         self.filters["status"].field.widget.attrs.update({"class": "form-control core-select2"})

#     def custom_keyword_filter(self, queryset, name, value):
#         qs = queryset

#         for keyword in value.split():
#             qs = qs.filter(
#                 Q(numbering__icontains=keyword)
#                 | Q(status__icontains=keyword)
#                 | Q(issued_by__email__icontains=keyword)
#                 | Q(customer_reference__icontains=keyword)
#                 | Q(remark__icontains=keyword)
#             )

#         return qs.distinct()

#     def end_date_filter(self, queryset, name, value):
#         qs = queryset

#         added_one_day_end_date = value + datetime.timedelta(days=1)
#         qs = qs.filter(adjust_datetime__lt=added_one_day_end_date)

#         return qs


# class AdjustNoteHistoryFilter(AbstractHistoryFilter):
#     """Filter class for AdjustNote's History DataTables."""

#     pass

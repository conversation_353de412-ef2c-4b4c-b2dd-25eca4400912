from django import forms
from django.core.exceptions import ValidationError
from django.utils.timezone import localtime

from wms.apps.settings.models import UnitOfMeasure
from wms.apps.adjustments.models import Adjustment, AdjustmentItem
from wms.cores.forms.fields import CoreModelForm, CoreChoiceField, FormFieldSize, CoreCharField, CoreDateField
from wms.apps.inventories.models import Stock, Item
from wms.cores.forms.widget import CoreSelectWidget, CoreNumberWidget, CoreTextWidget, CoreDateWidget
from django.forms import ChoiceField


class AdjustmentForm(CoreModelForm):
    class Meta:
        model = Adjustment
        fields = [
            "issued_by",
            "adjustment_datetime",
            "deliver_to",
            "remark",
        ]

    def __init__(self, *args, **kwargs):
        # To use self.warehouse_release_order for initial values, validation, etc.
        self.warehouse_release_order = kwargs.pop("warehouse_release_order", None)

        super().__init__(*args, **kwargs)

        self.fields["issued_by"].disabled = True
        self.fields["adjustment_datetime"].label = 'Time'
        self.fields["remark"].widget.attrs.update({"cols": 70, "rows": 4})
        # Add an ID to deliver_to for easier JS selection
        self.fields['deliver_to'].widget.attrs.update({'id': 'id_deliver_to'})

        # Explicitly set the datetime input format
        self.fields['adjustment_datetime'].input_formats = ['%Y-%m-%d %I:%M:%S %p']


class NoValidationChoiceField(ChoiceField):
    def validate(self, value):
        pass  # Skip validation


class AdjustmentItemForm(CoreModelForm):
    item = forms.CharField(
        required=True,
        widget=CoreSelectWidget(
            attrs={
                'class': 'adjustment-item-item w-xl',
                'data-api-url': '/api/items/',
                'required': 'required',
            }
        )
    )

    batch_no = NoValidationChoiceField(
        choices=[],
        required=False,
        widget=CoreSelectWidget(
            attrs={
                'class': 'adjustment-item-batch_no w-40',
                'disabled': 'disabled',
                'tags': 'true',  # enabled select2 dynamic option
            }
        )
    )
    expiry_date = NoValidationChoiceField(
        choices=[],
        required=False,
        widget=CoreSelectWidget(
            attrs={
                'class': 'adjustment-item-expiry_date w-40',
                'tags': 'true',  # enabled select2 dynamic option
            }
        )
    )

    quantity = forms.IntegerField(
        required=True,
        widget=CoreNumberWidget(
            attrs={
                'class': 'adjustment-item-quantity w-25',
            },
        )
    )
    balance = forms.CharField(
        required=False,
        widget=CoreTextWidget(
            attrs={
                'class': 'adjustment-item-balance w-25',
                'disabled': 'disabled',
                'readonly': 'readonly'
            }
        )
    )
    uom = forms.CharField(
        required=False,
        widget=CoreTextWidget(
            attrs={
                'class': 'adjustment-item-uom w-25',
                'disabled': 'disabled',
                'readonly': 'readonly'
            }
        )
    )

    item_id = forms.IntegerField(
        widget=forms.HiddenInput(),
        required=False,
    )
    batch_hidden = forms.CharField(widget=forms.HiddenInput(), required=False)
    expiry_hidden = forms.CharField(widget=forms.HiddenInput(), required=False)
    uom_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)

    class Meta:
        model = AdjustmentItem
        fields = [
            "item",
            "batch_no",
            "expiry_date",
            "quantity",
            "balance",
            "uom",
        ]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # --- Handle instance (edit case) ---
        # If we have an instance with data, we need to populate the fields
        if self.instance and self.instance.pk:
            # If editing an existing adjustment item, we need to set the initial values
            if self.instance.item:
                # Set initial values for hidden fields
                self.initial['item_id'] = self.instance.item.id

                # Set the item display value
                self.fields['item'].initial = str(self.instance.item)

                # Set batch number if available
                if self.instance.batch_no:
                    self.initial['batch_hidden'] = self.instance.batch_no
                    self.fields['batch_no'].initial = self.instance.batch_no

                # Set expiry date if available
                if self.instance.expiry_date:
                    self.initial['expiry_hidden'] = self.instance.expiry_date
                    self.fields['expiry_date'].initial = self.instance.expiry_date

                # Set UOM if available
                if self.instance.uom:
                    self.initial['uom_id'] = self.instance.uom.id
                    self.initial['uom'] = str(self.instance.uom)

                # Set balance if needed
                if hasattr(self.instance, 'balance'):
                    self.initial['balance'] = self.instance.balance

        # --- Handle formset initial data (create case) ---
        if not self.instance or not self.instance.pk:
            initial = self.initial

            if 'item' in initial:
                item = initial.get('item')
                if hasattr(item, 'id'):
                    self.fields['item'].initial = str(item)
                    self.initial['item_id'] = item.id
                else:
                    self.fields['item'].initial = item

            if 'batch_no' in initial:
                self.fields['batch_no'].initial = initial['batch_no']
                self.initial['batch_hidden'] = initial['batch_no']

            if 'expiry_date' in initial:
                self.fields['expiry_date'].initial = initial['expiry_date']
                self.initial['expiry_hidden'] = initial['expiry_date']

            if 'uom' in initial:
                uom = initial.get('uom')
                if hasattr(uom, 'id'):
                    self.fields['uom'].initial = str(uom)
                    self.initial['uom_id'] = uom.id
                else:
                    self.fields['uom'].initial = uom

    def clean(self):
        """Main form cleaning method"""
        cleaned_data = super().clean()
        item_id = cleaned_data.get('item_id')

        expiry_date = cleaned_data.get('expiry_date')
        if expiry_date == 'N/A' or expiry_date == '':
            cleaned_data['expiry_date'] = None

        if not item_id:
            self.add_error('item', 'Please select a item [Batch, Expiry, UOM]')
            self.add_error('batch_no', '')
            self.add_error('expiry_date', '')
            self.add_error('uom', '')
            raise ValidationError("Item selection is required")
        try:
            cleaned_data.update(self.clean_item_and_uom())
        except ValidationError as e:
            # Add the error to the item field specifically
            self.add_error('item', str(e))
            raise
        except Exception as e:
            self.add_error('item', f"Error processing form: {str(e)}")
            raise ValidationError(f"Error processing form: {str(e)}")

        return cleaned_data

    def clean_item_and_uom(self):
        """Handle item and UOM validation with proper error handling"""
        cleaned_data = self.cleaned_data
        item_id = cleaned_data.get('item_id')
        uom_id = cleaned_data.get('uom_id')

        def get_item_by_id():
            """Attempt to get item directly by ID"""
            try:
                return Item.objects.get(id=item_id)
            except Item.DoesNotExist:
                return None

        def validate_uom():
            """Validate UOM selection"""
            if not uom_id:
                raise ValidationError("UOM selection is required when item is selected")

            try:
                from wms.apps.settings.models import UnitOfMeasure
                return UnitOfMeasure.objects.get(id=uom_id)
            except UnitOfMeasure.DoesNotExist:
                raise ValidationError("Invalid UOM selection")

        try:
            # Try to get item and validate
            item = get_item_by_id()  # or find_item_by_item()
            if not item:
                self.add_error('item', 'Invalid item selection')
                raise ValidationError("Invalid item selection")

            cleaned_data['item'] = item
            cleaned_data['item_id'] = item.id

            # Validate UOM
            try:
                cleaned_data['uom'] = validate_uom()
            except ValidationError as e:
                self.add_error('uom', str(e))
                raise

            return cleaned_data

        except ValidationError as e:
            raise e
        except Exception as e:
            self.add_error('item', f"Error processing item: {str(e)}")
            raise ValidationError(f"Error processing item: {str(e)}")

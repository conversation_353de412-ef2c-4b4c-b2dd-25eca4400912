# Generated by Django 5.1 on 2025-03-15 13:12

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('adjustments', '0001_initial'),
        ('inventories', '0001_initial'),
        ('settings', '0002_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='adjustmentitem',
            name='item',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventories.item'),
        ),
        migrations.AddField(
            model_name='adjustmentitem',
            name='modified_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Modified by'),
        ),
        migrations.AddField(
            model_name='adjustmentitem',
            name='uom',
            field=models.ForeignKey(help_text='The item will be measured in terms of this unit (e.g.: kg, pcs, box).', on_delete=django.db.models.deletion.RESTRICT, to='settings.unitofmeasure', verbose_name='UOM'),
        ),
        migrations.AddField(
            model_name='adjustmentstockin',
            name='adjustment_item',
            field=models.ForeignKey(help_text="Each Adjustment Item indicate a relation to item's delievered quantity.", on_delete=django.db.models.deletion.CASCADE, to='adjustments.adjustmentitem'),
        ),
        migrations.AddField(
            model_name='adjustmentstockin',
            name='approved_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='+', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='adjustmentstockin',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Created by'),
        ),
        migrations.AddField(
            model_name='adjustmentstockin',
            name='deliver_to',
            field=models.ForeignKey(limit_choices_to={'is_storage': True}, on_delete=django.db.models.deletion.PROTECT, to='settings.warehouse'),
        ),
        migrations.AddField(
            model_name='adjustmentstockin',
            name='item',
            field=models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='inventories.item'),
        ),
        migrations.AddField(
            model_name='adjustmentstockin',
            name='modified_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Modified by'),
        ),
        migrations.AddField(
            model_name='adjustmentstockin',
            name='transaction',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='inventories.transaction'),
        ),
        migrations.AddField(
            model_name='adjustmentstockin',
            name='uom',
            field=models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='adjustment_stock_in_uoms', to='settings.unitofmeasure', verbose_name='UOM'),
        ),
    ]

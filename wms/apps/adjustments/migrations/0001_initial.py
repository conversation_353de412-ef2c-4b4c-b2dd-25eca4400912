# Generated by Django 5.1 on 2025-03-15 13:12

import django.db.models.deletion
import django.utils.timezone
import model_utils.fields
import wms.cores.models
import wms.cores.utils
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('cores', '0001_create_extensions'),
        ('settings', '0002_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AdjustmentStockIn',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('batch_no', models.CharField(default='N/A', max_length=255, verbose_name='Batch No.')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='Expiry Date')),
                ('stock_in_datetime', models.DateTimeField(verbose_name='Stock In Date Time')),
                ('approved_quantity', models.DecimalField(decimal_places=6, default=Decimal('0'), max_digits=19, verbose_name='Approved Quantity')),
                ('remark', models.TextField(blank=True, max_length=512, verbose_name='Remark')),
            ],
            options={
                'ordering': ['stock_in_datetime', 'pk'],
                'get_latest_by': 'created',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='Adjustment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('system_number', models.CharField(blank=True, max_length=32, verbose_name='System Number')),
                ('adjustment_datetime', models.DateTimeField(default=wms.cores.utils.localtime_now, verbose_name='Adjustment Date Time')),
                ('status', models.CharField(choices=[('Draft', 'Draft'), ('Processing', 'Processing'), ('Completed', 'Completed'), ('Completed with reject', 'Completed with reject')], default='Draft', max_length=32, verbose_name='Status')),
                ('remark', models.TextField(blank=True, max_length=512, verbose_name='Remark')),
                ('customer_reference', models.CharField(blank=True, max_length=64, verbose_name='Customer Reference')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('deliver_to', models.ForeignKey(limit_choices_to={'is_storage': True}, on_delete=django.db.models.deletion.PROTECT, related_name='adjustments', to='settings.warehouse')),
                ('issued_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Modified by')),
            ],
            options={
                'ordering': ['-system_number'],
                'get_latest_by': 'created',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='AdjustmentItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('sort_order', models.PositiveIntegerField(default=0, verbose_name='Ordering')),
                ('quantity', models.DecimalField(decimal_places=6, default=Decimal('0'), max_digits=19, validators=[wms.cores.models.not_equal_zero], verbose_name='Quantity')),
                ('batch_no', models.CharField(default='N/A', max_length=255, verbose_name='Batch No.')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='Expiry Date')),
                ('remark', models.TextField(blank=True, max_length=512, verbose_name='Remark')),
                ('status', models.CharField(choices=[('Draft', 'Draft'), ('Approved', 'Approved'), ('Rejected', 'Rejected')], default='Draft', max_length=32, verbose_name='Status')),
                ('adjustment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='adjustments.adjustment')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
            ],
            options={
                'ordering': ['sort_order'],
                'get_latest_by': 'created',
                'abstract': False,
            },
        ),
    ]

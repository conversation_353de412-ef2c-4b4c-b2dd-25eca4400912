from django.contrib import messages
from django.http import JsonResponse
from django.shortcuts import get_object_or_404, render
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.views.decorators.http import require_POST

from wms.apps.adjustments.models import AdjustmentItem, AdjustmentStockIn


def handle_htmx_response(request, adjustment_item):
    """
    Helper function to handle HTMX responses for adjustment item actions.
    Returns the appropriate HTML response based on the target element.
    """
    context = {'record': adjustment_item, 'request': request}

    # Check which element is being targeted
    target = request.headers.get('HX-Target', '')

    if 'adjustment-item-status' in target:
        # Return just the status column
        return render(request, 'adjustments/adjustment_item_status.html', context)
    elif 'adjustment-item-actions' in target:
        # Return just the actions column
        return render(request, 'adjustments/adjustment_item_actions.html', context)
    elif 'adjustment-item-action-by' in target:
        # Return just the action_by column
        return render(request, 'adjustments/adjustment_item_action_by.html', context)
    else:
        # Default response with all columns
        actions_html = render(request, 'adjustments/adjustment_item_actions.html', context).content.decode('utf-8')
        status_html = render(request, 'adjustments/adjustment_item_status.html', context).content.decode('utf-8')
        action_by_html = render(request, 'adjustments/adjustment_item_action_by.html', context).content.decode('utf-8')

        response_data = {
            'actions': actions_html,
            'status': status_html,
            'action_by': action_by_html
        }
        return JsonResponse(response_data)


def adjustment_item_approve_form(request, pk):
    """
    Display the form for approving a adjustment item.
    """
    adjustment_item = get_object_or_404(AdjustmentItem, pk=pk)

    context = {
        'adjustment_item': adjustment_item,
        'request': request,
    }

    return render(request, 'adjustments/adjustment_item_approve_form.html', context)


def adjustment_item_reject_form(request, pk):
    """
    Display the form for rejecting a adjustment item.
    """
    adjustment_item = get_object_or_404(AdjustmentItem, pk=pk)

    context = {
        'adjustment_item': adjustment_item,
        'request': request,
    }

    return render(request, 'adjustments/adjustment_item_reject_form.html', context)


@require_POST
def adjustment_item_approve(request, pk):
    """
    Approve a adjustment item and create the necessary stock in/out transactions.
    """
    adjustment_item = get_object_or_404(AdjustmentItem, pk=pk)

    if adjustment_item.status != AdjustmentItem.Status.DRAFT:
        return JsonResponse({
            'success': False,
            'message': _('This adjustment item has already been processed.')
        }, status=400)

    # Get the remark from the form
    remark = request.POST.get('remark', '')

    # Update the adjustment item with the remark
    adjustment_item.remark = remark
    adjustment_item.save(update_fields=['remark'])

    # Create AdjustmentStockIn record
    adjustment_item.create_adjustmentstockin(approved_by=request.user)
    # AdjustmentStockIn.objects.create(
    #     adjustment_item=adjustment_item,
    #     approved_by=request.user,
    #     stock_in_datetime=timezone.now(),
    #     uom=adjustment_item.uom,
    #     remark=remark
    # )


    # The status update is handled in the AdjustmentStockIn.save() method
    # which will also update the parent Adjustment status
    # messages.success(request, _('Adjustment item approved successfully.'))

    # For HTMX requests, return the updated cells HTML
    if request.headers.get('HX-Request'):
        return handle_htmx_response(request, adjustment_item)

    # For non-HTMX requests, return JSON
    return JsonResponse({
        'success': True,
        'message': _('Adjustment item approved successfully.')
    })


@require_POST
def adjustment_item_reject(request, pk):
    """
    Reject a adjustment item.
    """
    adjustment_item = get_object_or_404(AdjustmentItem, pk=pk)

    if adjustment_item.status != AdjustmentItem.Status.DRAFT:
        return JsonResponse({
            'success': False,
            'message': _('This adjustment item has already been processed.')
        }, status=400)

    # Update the adjustment item status
    adjustment_item.status = AdjustmentItem.Status.REJECTED
    adjustment_item.remark = request.POST.get('remark', '')
    adjustment_item.save()

    # messages.success(request, _('Adjustment item rejected successfully.'))

    # For HTMX requests, return the updated cells HTML
    if request.headers.get('HX-Request'):
        return handle_htmx_response(request, adjustment_item)

    # For non-HTMX requests, return JSON
    return JsonResponse({
        'success': True,
        'message': _('Adjustment item rejected successfully.')
    })



#############################################################
# Old Code, to be remove after everything is stabled.
#############################################################


# from typing import Any

# from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
# from django.db.models import BLANK_CHOICE_DASH
# from django.urls import reverse
# from django.utils.translation import gettext_lazy as _

# from wss.cores.views import CoreCreateView, CoreDeleteView, CoreDetailView, CoreListView, CoreUpdateView

# from wss.apps.adjusts.models.adjust import AdjustNote
# from wss.apps.inventories.models import Item

# from ..forms import AdjustNoteItemCreateForm, AdjustNoteItemRejectForm, AdjustNoteStockInForm
# from ..models import AdjustNoteItem, AdjustNoteStockIn


# class AdjustNoteItemDeleteView(LoginRequiredMixin, PermissionRequiredMixin, CoreDeleteView):
#     """Page to delete selected AdjustNoteItem based on given AdjustNoteItem's pk."""

#     model = AdjustNoteItem
#     success_message = _("Adjust Note item %(item)s successfully deleted")

#     permission_required = ("adjusts.delete_adjustnoteitem",)

#     def get_success_url(self):
#         return reverse("adjusts:adjust_notes:items", kwargs={"pk": self.object.adjust_note.pk})

#     def get_success_message(self):
#         return self.success_message % {"item": self.object.item}


# adjust_note_item_delete_view = AdjustNoteItemDeleteView.as_view()


# ############
# # FOR HTMX #
# ############


# class AdjustNoteItemStockInsView(LoginRequiredMixin, PermissionRequiredMixin, CoreListView):
#     """Partial page to show all AdjustNoteItemStockIn based on given AdjustNoteItem's pk."""

#     model = AdjustNoteStockIn
#     template_name = "adjusts/adjust_notes/partials/htmx/_item_stockin.html"

#     permission_required = ("adjusts.view_adjustnotestockin",)

#     def get_queryset(self):
#         return self.model.objects.filter(adjust_note_item__pk=self.kwargs["pk"])

#     def get_rejected_item_list(self):
#         return AdjustNoteItem.objects.filter(pk=self.kwargs["pk"], status=AdjustNoteItem.Status.REJECTED)

#     def get_adjust_note_item(self):
#         return AdjustNoteItem.objects.get(pk=self.kwargs["pk"])

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         context["adjust_note_item"] = self.get_adjust_note_item()
#         context["rejected_item_list"] = self.get_rejected_item_list()
#         return context


# adjust_note_item_stockins_view = AdjustNoteItemStockInsView.as_view()


# class AdjustNoteItemTrView(LoginRequiredMixin, PermissionRequiredMixin, CoreDetailView):
#     """Partial page to display selected AdjustNoteItem's table tr HTML
#     based on given AdjustNoteItem's pk."""

#     model = AdjustNoteItem
#     template_name = "adjusts/adjust_notes/partials/htmx/_item_tr.html"

#     permission_required = ("adjusts.view_adjustnoteitem",)


# adjust_note_item_tr_view = AdjustNoteItemTrView.as_view()


# class AdjustNoteItemApproveView(LoginRequiredMixin, PermissionRequiredMixin, CoreCreateView):
#     """Partial page to display selected AdjustNoteItem's approve form
#     based on given AdjustNoteItem's pk."""

#     model = AdjustNoteStockIn
#     form_class = AdjustNoteStockInForm
#     template_name = "adjusts/adjust_notes/partials/htmx/_item_approve_form.html"

#     permission_required = ("adjusts.add_adjustnotestockin",)

#     success_message = _("Adjust Note item %(item)s successfully stock in")

#     def get_approved_item_obj(self):
#         return AdjustNoteItem.objects.get(pk=self.kwargs["pk"])

#     def get_initial(self):
#         initial = super().get_initial()

#         self.approved_item_obj = self.get_approved_item_obj()
#         initial["deliver_to"] = self.approved_item_obj.adjust_note.deliver_to
#         initial["approved_by"] = self.request.user
#         initial["approved_quantity"] = self.approved_item_obj.get_expected_converted_accurate_quantity
#         initial["stock_in_datetime"] = self.approved_item_obj.adjust_note.adjust_datetime
#         initial["item"] = self.approved_item_obj.item
#         initial["adjust_note_item"] = self.approved_item_obj
#         initial["uom"] = self.approved_item_obj.item.uom.pk
#         initial["batch_no"] = self.approved_item_obj.batch_no
#         initial["expiry_date"] = self.approved_item_obj.expiry_date

#         return initial

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         context["approved_item_obj"] = self.approved_item_obj
#         return context

#     def get_success_url(self):
#         return reverse("adjusts:adjust_notes:detail", kwargs={"pk": self.approved_item_obj.adjust_note.pk})

#     def get_success_message(self, cleaned_data):
#         return self.success_message % {"item": self.approved_item_obj.item}


# adjust_note_item_approve_view = AdjustNoteItemApproveView.as_view()


# class AdjustNoteItemRejectView(LoginRequiredMixin, PermissionRequiredMixin, CoreUpdateView):
#     """Partial page to display selected AdjustNoteItem's reject form
#     based on given AdjustNoteItem's pk."""

#     model = AdjustNoteItem
#     form_class = AdjustNoteItemRejectForm
#     template_name = "adjusts/adjust_notes/partials/htmx/_item_reject_form.html"

#     permission_required = ("adjusts.change_adjustnoteitem",)

#     success_message = _("Adjust Note item %(item)s successfully rejected")

#     def get_rejected_item_obj(self):
#         return AdjustNoteItem.objects.get(pk=self.kwargs["pk"])

#     def get_initial(self):
#         initial = super().get_initial()

#         self.rejected_item_obj = self.get_rejected_item_obj()
#         initial["status"] = self.rejected_item_obj.Status.REJECTED

#         return initial

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         context["rejected_item_obj"] = self.rejected_item_obj
#         return context

#     def get_success_url(self):
#         return reverse("adjusts:adjust_notes:detail", kwargs={"pk": self.rejected_item_obj.adjust_note.pk})

#     def get_success_message(self, cleaned_data):
#         return self.success_message % {"item": self.rejected_item_obj.item}


# adjust_note_item_reject_view = AdjustNoteItemRejectView.as_view()


# class AdjustNoteItemCreateView(LoginRequiredMixin, PermissionRequiredMixin, CoreCreateView):
#     """Partial page to add AdjustNoteItem based on given AdjustNote's pk."""

#     model = AdjustNoteItem
#     form_class = AdjustNoteItemCreateForm
#     template_name = "adjusts/adjust_notes/partials/htmx/_item_create_form.html"

#     permission_required = ("adjusts.add_adjustnoteitem",)

#     success_message = _("Adjust Note item %(item)s successfully created")

#     def get_adj_obj(self) -> AdjustNote:
#         return AdjustNote.objects.get(pk=self.kwargs["adj_pk"])

#     def get_form(self):
#         form = super().get_form()
#         item_qs = Item.objects.active()
#         item_choices = BLANK_CHOICE_DASH
#         form.fields["item"].choices = item_choices + [(item.pk, item) for item in item_qs]
#         return form

#     def get_initial(self) -> dict[str, Any]:
#         initial = super().get_initial()

#         self.adj_obj = self.get_adj_obj()
#         initial["adjust_note"] = self.adj_obj

#         return initial

#     def get_context_data(self, **kwargs) -> Any:
#         context = super().get_context_data(**kwargs)
#         context["adj_obj"] = self.adj_obj
#         return context

#     def get_success_url(self) -> str:
#         return reverse("adjusts:adjust_notes:detail", kwargs={"pk": self.kwargs["adj_pk"]})

#     def get_success_message(self, cleaned_data) -> str:
#         return self.success_message % {"item": self.object.item}


# adjust_note_item_create_view = AdjustNoteItemCreateView.as_view()

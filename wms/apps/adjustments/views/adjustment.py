from crispy_forms.helper import FormHelper
from django.contrib import messages
from django.db import transaction
from django.db.models import QuerySet
from django.forms import inlineformset_factory, ValidationError
from django.shortcuts import redirect
from django.urls import reverse_lazy, reverse

from wms.cores.mixins import ExportTableMixin
from wms.cores.views import CoreSingleTableView, CoreCreateView, CoreUpdateView, CoreDetailView, CoreDataTableDetailView

from wms.apps.releases.models import WarehouseReleaseOrder

from wms.apps.adjustments.filters import AdjustmentFilter, AdjustmentDataTableFilter
from wms.apps.adjustments.tables import AdjustmentTable, AdjustmentDetailTable, AdjustmentItemTable
from wms.apps.adjustments.forms import AdjustmentForm, AdjustmentItemForm
from wms.apps.adjustments.models import Adjustment, AdjustmentItem


class AdjustmentListView(ExportTableMixin, CoreSingleTableView):
    """
    View for displaying and managing the list of Items.
    Supports:
    - Pagination
    - Column visibility
    - Searching
    - Export functionality
    - HTMX partial updates
    """
    model = Adjustment
    table_class = AdjustmentTable
    template_name = "cores/list_table.html"
    partial_template_name = "cores/table_partial.html"
    queryset = Adjustment.objects.all()
    filterset_class = AdjustmentFilter

    # Search configuration
    search_fields = ["system_number"]

    # Export configuration
    export_name = "adjustments"
    export_permission = []  # Empty list means no specific permissions required


class AdjustmentFormsetMixin:
    """Mixin to handle AdjustmentItem inline formset with Crispy Forms (Table Layout)."""
    formset = None

    def get_initial_items(self, warehouse_release_order: WarehouseReleaseOrder = None):
        """
        To retrieve and derive a list of initial AdjustmentItems from the WarehouseReleaseOrderItems
        of the given WarehouseReleaseOrder.
        """
        items = []

        if warehouse_release_order:
            for wro_item in warehouse_release_order.warehouse_release_order_items.all():
                items.append(
                    {
                        "item": wro_item.item,
                        "batch_no": wro_item.batch_no,
                        "expiry_date": wro_item.expiry_date,
                        "quantity": wro_item.quantity,
                        "uom": wro_item.uom,
                    }
                )

        return items

    def get_formset(self):
        """Creates and returns the AdjustmentItem formset with a Crispy Helper for table layout."""
        adjustment_item_form_set = inlineformset_factory(
            Adjustment,
            AdjustmentItem,
            form=AdjustmentItemForm,
            extra=1,
            validate_min=True,
            # can_delete=True
        )

        # Create the helper for the formset
        helper = FormHelper()
        helper.form_tag = False
        helper.disable_csrf = True
        helper.template = 'tailwind/table_inline_formset.html'
        helper.formset_header_title = "Items"

        if self.request.method == 'POST':
            formset_instance = adjustment_item_form_set(
                self.request.POST,
                self.request.FILES,
                instance=self.object,
                prefix='items'
            )
        else:
            initial = []

            # If warehouse_release_order is available, preload item data
            if hasattr(self, "warehouse_release_order") and self.warehouse_release_order:
                initial = self.get_initial_items(self.warehouse_release_order)
                adjustment_item_form_set.extra = len(initial) if initial else 1

            # For update view, set extra=0 if there are existing items
            else:
                initial_extra = 0 if (self.object and self.object.adjustmentitem_set.exists()) else 1
                adjustment_item_form_set.extra = initial_extra

            formset_instance = adjustment_item_form_set(
                instance=self.object,
                prefix='items',
                initial=initial
            )

        # Set the request on each form in the formset
        for form in formset_instance:
            form.request = self.request

        formset_instance.helper = helper
        return formset_instance

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        if self.formset is None:
            self.formset = self.get_formset()

        # Ensure management form data is correct
        if self.object and not self.request.POST:  # If this is an update view
            total_forms = self.object.adjustmentitem_set.count()
            self.formset.management_form.initial['TOTAL_FORMS'] = total_forms
            self.formset.management_form.initial['INITIAL_FORMS'] = total_forms

        context['formset'] = self.formset
        return context

    # form_valid and form_invalid remain the same
    def form_valid(self, form):
        context = self.get_context_data()
        formset = context['formset']  # Get formset with helper

        if formset.is_valid():
            # Save the main Adjustment object
            adjustment_object = form.save()
            # Store the Adjustment object in self.object
            self.object = adjustment_object
            formset.instance = adjustment_object

            # To handle delete adjustment items
            adjustment_items = adjustment_object.adjustmentitem_set.all()
            updated_items = []

            for each_formset in formset:
                updated_items.append(each_formset.cleaned_data.get("stock_id"))

            for item in adjustment_items:
                if item not in updated_items:
                    item.delete()

            # Set request on each form in the formset to ensure created_by and modified_by are set
            for adjustment in formset:
                if hasattr(adjustment, 'instance') and adjustment.instance:
                    # Only set request if the form has an instance
                    adjustment.request = self.request

            # Save the formset but store the result in a separate variable
            formset.save()

            return super().form_valid(form)
        else:
            return super().form_invalid(form)

    def form_invalid(self, form):
        """Handle invalid main form or formset."""
        # Ensure the invalid formset (with helper) is put back into the context
        if self.formset is None or not hasattr(self.formset, 'helper'):
            self.formset = self.get_formset()  # Recreate formset with helper
        elif not hasattr(self.formset, 'helper'):
            self.formset.helper = self.get_formset().helper  # Re-attach helper

        return super().form_invalid(form)


# --- Existing Mixin (from context) ---
class AdjustmentCreateAndUpdateMixin:
    """Mixin for Adjustment create and update."""

    model = Adjustment
    form_class = AdjustmentForm
    template_name = "adjustments/adjustment_form.html"  # Ensure this template renders the formset
    # Define success_url here or in specific views
    # success_url = reverse_lazy("adjustments:adjustment_list")
    cancel_url = reverse_lazy("adjustments:adjustment:list")

    def get_initial(self):
        initial = super().get_initial()
        # Only set issued_by for new objects (CreateView)
        if self.object is None:
            initial["issued_by"] = self.request.user
        return initial


class AdjustmentCreateView(AdjustmentFormsetMixin, AdjustmentCreateAndUpdateMixin, CoreCreateView):
    """View to create a new Adjustment with its items."""
    # Attributes from CoreCreateView/AdjustmentCreateAndUpdateMixin might be needed
    section_title = "Create Adjustment"
    section_desc = ""
    submit_text = "Save"
    success_url = "adjustments:adjustment:panel"
    cancel_url = "adjustments:adjustment:list"

    def get(self, request, *args, **kwargs):

        # try to check if URL's PARAM exist any wro_pk, if yes, it should be reverse_pgi from a specific WRO/DO
        if self.request.GET.get("wro_pk", None):
            if not self.request.user.is_superuser and not self.request.user.groups.filter(name="Superadmin").exists():
                messages.error(self.request, "You do not have permission to perform this action.")
                return redirect(reverse("adjustments:adjustment:create"))

            try:
                self.warehouse_release_order = WarehouseReleaseOrder.objects.get(pk=self.request.GET.get("wro_pk"))
            except WarehouseReleaseOrder.DoesNotExist:
                messages.error(self.request, "Warehouse Release Order does not exist.")
                return redirect(reverse("adjustments:adjustment:create"))

            if self.warehouse_release_order.status != WarehouseReleaseOrder.Status.COMPLETED:
                messages.error(self.request, "Warehouse Release Order is not Completed.")
                return redirect(reverse("adjustments:adjustment:create"))

            if self.warehouse_release_order.is_reverse_pgi is True:
                messages.error(self.request, "Warehouse Release Order is already reversed.")
                return redirect(reverse("adjustments:adjustment:create"))

        return super().get(request, *args, **kwargs)

    def get_form_kwargs(self):
        """
        To make sure the reverse_pgi of AdjustmentObj from a WRO have the correct warehouse_release_order
        """
        kwargs = super().get_form_kwargs()
        kwargs["warehouse_release_order"] = getattr(self, "warehouse_release_order", None)
        return kwargs

    def get_initial(self, *args, **kwargs):
        initial = super().get_initial(*args, **kwargs)

        # Pre-populate data if request is a Reverse PGI from WRO
        if getattr(self, "warehouse_release_order", None):
            initial["deliver_to"] = self.warehouse_release_order.warehouses.order_by("-path").first()
            initial["remark"] = f"From {self.warehouse_release_order.system_number}"

        return initial

    def form_valid(self, form):
        response = super().form_valid(form)

        # if url PARAM exist wro_pk and reached form_valid, meaning reverse_pgi(AdjustmentObj) is created,
        # Mark this WRO/DO's is_reverse_pgi to True
        wro_pk = self.request.POST.get("wro_pk", None)
        if wro_pk:
            warehouse_release_order = WarehouseReleaseOrder.objects.filter(pk=wro_pk)
            if warehouse_release_order.exists():
                wro = warehouse_release_order.first()
                wro.is_reverse_pgi = True
                wro.save()

        return response


class AdjustmentUpdateView(AdjustmentFormsetMixin, AdjustmentCreateAndUpdateMixin, CoreUpdateView):
    """View to update an existing Adjustment and its items."""
    section_title = "Update Adjustment"
    section_desc = ""
    submit_text = "Update"
    success_url = "adjustments:adjustment:panel"

    def get(self, request, *args, **kwargs):
        self.object = self.get_object()
        if self.object.status != Adjustment.Status.DRAFT:
            messages.error(self.request, "Only Draft Adjustment can be edited")
            return redirect(reverse("adjustments:adjustment:list"))
        return super().get(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        self.formset.extra = 0

        return context


class AdjustmentDetailHomeView(CoreDetailView):
    """
    View for displaying the main detail page of a Adjustment.
    This serves as the home view for the adjustment detail panel.
    """
    model = Adjustment
    template_name = 'adjustments/mains/home.html'
    context_object_name = "adjustment"


class AdjustmentDetailView(CoreDetailView):
    """
    View for displaying the detailed information of a Adjustment.
    This is used as a partial view within the detail panel.
    """
    model = Adjustment
    template_name = 'adjustments/partials/detail.html'
    context_object_name = "adjustment"


class AdjustmentDataTableDetailView(CoreDataTableDetailView):
    """
    View that combines a detail view with a data table.
    Provides a split panel layout with table and detail view.
    """
    model = Adjustment
    table_class = AdjustmentDetailTable
    context_object_name = "adjustment"
    partial_view = AdjustmentDetailHomeView
    search_fields = ["system_number"]
    filterset_class = AdjustmentDataTableFilter  # Uncomment when filter class is created


class AdjustmentItemListView(ExportTableMixin, CoreSingleTableView):
    """
    View for displaying and managing the list of Items.
    Supports:
    - Pagination
    - Column visibility
    - Searching
    - Export functionality
    - HTMX partial updates
    """
    model = AdjustmentItem
    table_class = AdjustmentItemTable
    template_name = "cores/datatable_detail_view_table.html"
    partial_template_name = "cores/table_partial.html"

    # Search configuration
    search_fields = ["item__code", "batch_no", "item__name"]

    # Export configuration
    export_name = "adjustment_items"
    export_permission = []  # Empty list means no specific permissions required

    # HTMX configuration, override default to prevent htmx target conflict
    # Map this with own partial element id, use this when under single page there is more than 1 htmx table
    htmx_target = 'table-content-partial'

    def get_queryset(self) -> QuerySet:
        queryset = super().get_queryset()
        # Apply additional filter for this specific view
        return queryset.filter(adjustment_id=self.kwargs.get('pk'))

    def get_htmx_base_url(self):
        """
        Override to provide the dynamic URL based on the current item's PK.
        This URL will be used for all HTMX requests and querystring operations.
        """
        pk = self.kwargs.get('pk')
        if pk:
            return reverse("adjustments:adjustment:item_list", kwargs={"pk": pk})
        return None


#############################################################
# Old Code, to be remove after everything is stabled.
#############################################################

# from typing import Any

# from django.contrib import messages
# from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
# from django.db.models import BLANK_CHOICE_DASH, QuerySet
# from django.shortcuts import redirect
# from django.urls import reverse, reverse_lazy
# from django.utils.translation import gettext_lazy as _

# from actstream.models import Action
# from django_filters.views import FilterView
# from django_tables2 import SingleTableMixin

# from wss.cores.actstream import merge_actstream_to_parent, query_actstream
# from wss.cores.views import (
#     CoreBaseHistoryModifiedView,
#     CoreCreateView,
#     CoreDataTablesView,
#     CoreDeleteView,
#     CoreDetailDataTablesView,
#     CoreDetailView,
#     CoreListView,
#     CoreUpdateView,
# )

# from wss.apps.inventories.models import Item
# from wss.apps.releases.models import WarehouseReleaseOrder

# from ..filters import AdjustNoteFilter, AdjustNoteHistoryFilter
# from ..forms import AdjustNoteForm, AdjustNoteInfoUpdateForm, AdjustNoteItemInlineFormSet
# from ..models import AdjustNote, AdjustNoteItem, AdjustNoteStockIn
# from ..tables import (
#     AdjustNoteDataTables,
#     AdjustNoteDetailDataTables,
#     AdjustNoteHistoryDataTables,
#     AdjustNoteRejectDataTables,
# )


# class AdjustNoteFormsetValid:
#     """Adjust formset validation action."""

#     formset_class = AdjustNoteItemInlineFormSet

#     def form_valid(self, form):
#         context = self.get_context_data()
#         formset = context["adjust_note_item_formset"]

#         if formset.is_valid():
#             # To determine if it is creating a new formset or updating an existing formset
#             new_formset = formset.instance._state.adding

#             self.object = form.save()
#             formset.instance = self.object

#             # To handle delete adjust items
#             adjust_items = self.object.adjustnoteitem_set.all()
#             updated_items = []

#             for each_formset in formset:
#                 updated_items.append(each_formset.cleaned_data.get("id"))

#             for item in adjust_items:
#                 if item not in updated_items:
#                     item.delete()

#             instances = formset.save()

#             if new_formset:
#                 merge_actstream_to_parent(instances=instances, parent=self.object)

#             return super().form_valid(form)
#         else:
#             return super().form_invalid(form)


# class AdjustNoteListView(LoginRequiredMixin, PermissionRequiredMixin, FilterView, SingleTableMixin, CoreListView):
#     """Page to show all AdjustNote. This page use DataTables server side."""

#     model = AdjustNote
#     template_name = "adjusts/adjust_notes/list.html"
#     table_class = AdjustNoteDataTables
#     filterset_class = AdjustNoteFilter

#     # To prevent query all object as it will use ajax call in template
#     object_list = AdjustNote.objects.none()

#     header_title = "Adjustments"
#     selected_page = "inventories"
#     selected_subpage = "adjust_notes"

#     permission_required = ("adjusts.view_adjustnote",)

#     def get_queryset(self):
#         """To prevent query all object."""
#         return self.object_list


# adjust_note_list_view = AdjustNoteListView.as_view()


# class AdjustNoteDetailView(LoginRequiredMixin, PermissionRequiredMixin, CoreDetailDataTablesView):
#     """Page to display selected AdjustNote based on given AdjustNote's pk.
#     This page use DataTables server side."""

#     model = AdjustNote
#     template_name = "adjusts/adjust_notes/detail.html"

#     table_class = AdjustNoteDetailDataTables

#     header_title = "Adjustments"
#     selected_page = "inventories"
#     selected_subpage = "adjust_notes"

#     permission_required = ("adjusts.view_adjustnote",)

#     def get_total_rejected_count(self):
#         return AdjustNoteItem.objects.filter(
#             adjust_note__pk=self.kwargs["pk"],
#             status=AdjustNoteItem.Status.REJECTED,
#         ).count()

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         # Round to 0 because currently defect list always use EA
#         context["total_rejected_count"] = round(self.get_total_rejected_count(), 0)
#         return context


# adjust_note_detail_view = AdjustNoteDetailView.as_view()


# class AdjustNoteCreateAndUpdateMixin:
#     """Mixin for Create and Update."""

#     model = AdjustNote
#     form_class = AdjustNoteForm
#     template_name = "adjusts/adjust_notes/create_or_update.html"

#     selected_page = "inventories"
#     selected_subpage = "adjust_notes"

#     def get_initial(self):
#         initial = super().get_initial()

#         if self.object is None:
#             initial["issued_by"] = self.request.user

#         return initial

#     def get_success_url(self):
#         return reverse("adjusts:adjust_notes:detail", kwargs={"pk": self.object.pk})

#     def get_success_message(self, cleaned_data):
#         return self.success_message % {"numbering": self.object.numbering}


# class AdjustNoteCreateView(
#     AdjustNoteCreateAndUpdateMixin,
#     LoginRequiredMixin,
#     PermissionRequiredMixin,
#     AdjustNoteFormsetValid,
#     CoreCreateView,
# ):
#     """Page to create AdjustNote."""

#     success_message = _("Adjust Note %(numbering)s successfully created")

#     header_title = "New Adjustment"

#     permission_required = ("adjusts.add_adjustnote",)

#     def get(self, request, *args, **kwargs):
#         if self.request.GET.get("wro_pk", None):
#             if not self.request.user.is_superuser and not self.request.user.groups.filter(name="Superadmin").exists():
#                 messages.error(self.request, "You do not have permission to perform this action.")
#                 return redirect(reverse("adjusts:adjust_notes:create"))

#             try:
#                 self.warehouse_release_order = WarehouseReleaseOrder.objects.get(pk=self.request.GET.get("wro_pk"))
#             except WarehouseReleaseOrder.DoesNotExist:
#                 messages.error(self.request, "Warehouse Release Order does not exist.")
#                 return redirect(reverse("adjusts:adjust_notes:create"))

#             if self.warehouse_release_order.status != WarehouseReleaseOrder.Status.COMPLETED:
#                 messages.error(self.request, "Warehouse Release Order is not Completed.")
#                 return redirect(reverse("adjusts:adjust_notes:create"))

#             if self.warehouse_release_order.is_reverse_pgi is True:
#                 messages.error(self.request, "Warehouse Release Order is already reversed.")
#                 return redirect(reverse("adjusts:adjust_notes:create"))

#         return super().get(request, *args, **kwargs)

#     def get_form_kwargs(self):
#         kwargs = super().get_form_kwargs()
#         kwargs["warehouse_release_order"] = getattr(self, "warehouse_release_order", None)
#         return kwargs

#     def get_initial(self, *args, **kwargs):
#         initial = super().get_initial(*args, **kwargs)

#         # Pre-populate data if request is a Reverse PGI from WRO
#         if getattr(self, "warehouse_release_order", None):
#             initial["deliver_to"] = self.warehouse_release_order.warehouses.order_by("-path").first()
#             initial["remark"] = f"From {self.warehouse_release_order.numbering}"

#         return initial

#     def get_initial_items(self, warehouse_release_order: WarehouseReleaseOrder = None):
#         """
#         To retrieve and derive a list of initial AdjustNoteItems from the WarehouseReleaseOrderItems
#         of the given WarehouseReleaseOrder.
#         """
#         items = []

#         if warehouse_release_order:
#             for wro_item in warehouse_release_order.warehouse_release_order_items.all():
#                 items.append(
#                     {
#                         "item": wro_item.item,
#                         "batch_no": wro_item.batch_no,
#                         "expiry_date": wro_item.expiry_date,
#                         "quantity": wro_item.quantity,
#                         "uom": wro_item.uom,
#                     }
#                 )

#         return items

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)

#         if getattr(self, "warehouse_release_order", None):
#             self.initial_items = self.get_initial_items(warehouse_release_order=self.warehouse_release_order)

#         if self.request.POST:
#             adjust_note_item_formset = self.formset_class(self.request.POST)
#         else:
#             adjust_note_item_formset = self.formset_class(initial=getattr(self, "initial_items", []))
#             adjust_note_item_formset.extra = max(len(getattr(self, "initial_items", [])), 1)

#         context["adjust_note_item_formset"] = adjust_note_item_formset
#         context["formset_title"] = context["adjust_note_item_formset"].model._meta.verbose_name_plural.capitalize()
#         context["blank_choice_dash"] = BLANK_CHOICE_DASH[0][1]

#         # To pre-populate selected ADJ Items (in case form is invalid and has to redirect back to CreateView page)
#         item_pk_arr, item_display_arr = [], []
#         post_data = self.request.POST

#         if post_data:
#             for key, value in post_data.items():
#                 if key.endswith("-item") and value != "":
#                     item_pk_arr.append(value)
#                     item = Item.objects.get(pk=value)
#                     item_display_arr.append(f"{item.code} :: {item.name}")
#         elif getattr(self, "initial_items", []):
#             # Ensure item fields are also pre-populated if initial_items contains data
#             for adj_item in self.initial_items:
#                 item_pk_arr.append(adj_item["item"].pk)
#                 item_display_arr.append(f"{adj_item['item'].code} :: {adj_item['item'].name}")

#         context["item_pk_arr"] = item_pk_arr
#         context["item_display_arr"] = item_display_arr

#         return context

#     def form_valid(self, form):
#         response = super().form_valid(form)

#         wro_pk = self.request.POST.get("wro_pk", None)
#         if wro_pk:
#             warehouse_release_order = WarehouseReleaseOrder.objects.filter(pk=wro_pk)
#             if warehouse_release_order.exists():
#                 wro = warehouse_release_order.first()
#                 wro.is_reverse_pgi = True
#                 wro.save()

#         return response


# adjust_note_create_view = AdjustNoteCreateView.as_view()


# class AdjustNoteUpdateView(
#     AdjustNoteCreateAndUpdateMixin, LoginRequiredMixin, PermissionRequiredMixin, AdjustNoteFormsetValid, CoreUpdateView
# ):
#     """Page to update selected AdjustNote based on given AdjustNote's pk."""

#     success_message = _("Adjust Note %(numbering)s successfully updated")

#     header_title = "Update Adjust"

#     permission_required = ("adjusts.change_adjustnote",)

#     def get(self, request, *args, **kwargs):
#         self.object = self.get_object()
#         if self.object.status == "Completed":
#             messages.error(self.request, "Completed Adjust Note cannot be edited")
#             return redirect(reverse("adjusts:adjust_notes:list"))
#         return super().get(request, *args, **kwargs)

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)

#         self.adjust_note = (
#             AdjustNote.objects.select_related("created_by", "issued_by", "deliver_to")
#             .prefetch_related("adjustnoteitem_set")
#             .get(pk=self.object.pk)
#         )
#         self.optimized_item_qs = Item.objects.prefetch_related("itemphoto_set")

#         if self.request.POST:
#             adjust_note_item_formset = self.formset_class(
#                 self.request.POST, self.request.FILES, instance=self.adjust_note
#             )
#             for form in adjust_note_item_formset:
#                 form.fields["item"].queryset = self.optimized_item_qs
#         else:
#             adjust_note_item_formset = self.formset_class(instance=self.adjust_note)
#             for form in adjust_note_item_formset:
#                 form.fields["item"].queryset = self.optimized_item_qs

#         adjust_note_item_formset.extra = 0

#         context["adjust_note_item_formset"] = adjust_note_item_formset
#         context["formset_title"] = context["adjust_note_item_formset"].model._meta.verbose_name_plural.capitalize()
#         context["blank_choice_dash"] = BLANK_CHOICE_DASH[0][1]

#         # To pre-populate selected AdjustNote Items
#         item_pk_arr, item_display_arr = [], []

#         for adj_item in self.object.adjustnoteitem_set.all():
#             item_pk_arr.append(adj_item.item.pk)
#             item_display_arr.append(f"{adj_item.item.code} :: {adj_item.item.name}")

#         context["item_pk_arr"] = item_pk_arr
#         context["item_display_arr"] = item_display_arr

#         return context


# adjust_note_update_view = AdjustNoteUpdateView.as_view()


# class AdjustNoteDeleteView(LoginRequiredMixin, PermissionRequiredMixin, CoreDeleteView):
#     """Page to delete selected AdjustNote based on given AdjustNote's pk."""

#     model = AdjustNote
#     success_url = reverse_lazy("adjusts:adjust_notes:list")
#     success_message = _("Adjust %(numbering)s successfully deleted")

#     permission_required = ("adjusts.delete_adjustnote",)

#     def get(self, request, *args, **kwargs):
#         self.object = self.get_object()
#         if self.object.status == "Completed":
#             messages.error(self.request, "Completed Adjust Note cannot be deleted")
#             return redirect(reverse("adjusts:adjust_notes:list"))
#         return super().get(request, *args, **kwargs)


# adjust_note_delete_view = AdjustNoteDeleteView.as_view()


# ##################
# # FOR DataTables #
# ##################


# class AdjustNoteDataTablesView(LoginRequiredMixin, PermissionRequiredMixin, CoreDataTablesView):
#     """JSON for DataTables in list page."""

#     model = AdjustNote
#     table_class = AdjustNoteDataTables
#     filterset_class = AdjustNoteFilter

#     permission_required = ("adjusts.view_adjustnote",)

#     def get_queryset(self) -> QuerySet[AdjustNote]:
#         return (
#             self.model.objects.select_related("created_by", "issued_by", "deliver_to")
#             .prefetch_related("adjustnoteitem_set")
#             .all()
#         )


# adjust_note_datatables_view = AdjustNoteDataTablesView.as_view()


# class AdjustNoteDetailDataTablesView(AdjustNoteDataTablesView):
#     """JSON for DataTables in detail page."""

#     table_class = AdjustNoteDetailDataTables


# adjust_note_detail_datatables_view = AdjustNoteDetailDataTablesView.as_view()


# class AdjustNoteHistoryDataTablesView(LoginRequiredMixin, PermissionRequiredMixin, CoreDataTablesView):
#     """JSON for DataTables in AdjustNote History's page."""

#     model = Action
#     table_class = AdjustNoteHistoryDataTables
#     filterset_class = AdjustNoteHistoryFilter
#     permission_required = ("adjusts.view_adjustnote",)

#     def get_adjust_note(self) -> AdjustNote:
#         self.adjust_note = (
#             AdjustNote.objects.select_related("created_by", "issued_by", "deliver_to")
#             .prefetch_related("adjustnoteitem_set")
#             .get(pk=self.kwargs["pk"])
#         )

#         return self.adjust_note

#     def get_adjust_note_items(self) -> QuerySet[AdjustNoteItem]:
#         return self.adjust_note.adjustnoteitem_set.all()

#     def get_adjust_note_stockins(self) -> QuerySet[AdjustNoteStockIn]:
#         return AdjustNoteStockIn.objects.filter(adjust_note_item__adjust_note=self.adjust_note)

#     def get_queryset(self) -> QuerySet[Action]:
#         self.get_adjust_note()

#         action_adjust_note_qs = query_actstream(target=self.adjust_note)
#         action_adjust_note_stockin_qs = query_actstream(target=self.get_adjust_note_stockins())

#         action_qs = action_adjust_note_qs | action_adjust_note_stockin_qs

#         return action_qs


# adjust_note_history_list_datatables_view = AdjustNoteHistoryDataTablesView.as_view()


# ############
# # FOR HTMX #
# ############


# class AdjustNoteInfoDetailView(LoginRequiredMixin, PermissionRequiredMixin, CoreDetailView):
#     """Partial page to display selected AdjustNote's info based on given AdjustNote's pk."""

#     model = AdjustNote
#     template_name = "adjusts/adjust_notes/partials/htmx/_info.html"

#     permission_required = ("adjusts.view_adjustnote",)


# adjust_note_info_detail_view = AdjustNoteInfoDetailView.as_view()


# class AdjustNoteInfoUpdateView(LoginRequiredMixin, PermissionRequiredMixin, CoreUpdateView):
#     """Partial page to update selected AdjustNote's info based on given AdjustNote's pk."""

#     model = AdjustNote
#     form_class = AdjustNoteInfoUpdateForm
#     template_name = "adjusts/adjust_notes/partials/htmx/_info_form.html"
#     success_message = _("Adjust Note basic information successfully updated")

#     permission_required = ("adjusts.change_adjustnote",)

#     def get_success_url(self):
#         return reverse("adjusts:adjust_notes:info", kwargs={"pk": self.object.pk})


# adjust_note_info_update_view = AdjustNoteInfoUpdateView.as_view()


# class AdjustNoteStatusDetailView(LoginRequiredMixin, PermissionRequiredMixin, CoreDetailView):
#     """Partial page to show Adjustment's status."""

#     model = AdjustNote
#     template_name = "adjusts/adjust_notes/partials/htmx/_status.html"

#     permission_required = ("adjusts.view_adjustnote",)


# adjust_note_status_detail_view = AdjustNoteStatusDetailView.as_view()


# class AdjustNoteRejectsListView(LoginRequiredMixin, PermissionRequiredMixin, SingleTableMixin, CoreListView):
#     """Partial page to show all Rejected AdjustNoteItem based on given AdjustNote's pk."""

#     model = AdjustNoteItem
#     table_class = AdjustNoteRejectDataTables
#     template_name = "adjusts/adjust_notes/partials/htmx/_rejects.html"

#     permission_required = ("adjusts.view_adjustnote",)

#     def get_queryset(self):
#         return self.model.objects.filter(
#             adjust_note__pk=self.kwargs["pk"],
#             status=AdjustNoteItem.Status.REJECTED,
#         )

#     def get_total_rejected_count(self):
#         return self.object_list.count()

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         # Round to 0 because currently defect list always use EA
#         context["total_rejected_count"] = round(self.get_total_rejected_count(), 0)
#         return context


# adjust_note_rejects_list_view = AdjustNoteRejectsListView.as_view()


# class AdjustNoteItemsListView(LoginRequiredMixin, PermissionRequiredMixin, CoreListView):
#     """Partial page to show all AdjustNoteItem based on given AdjustNote's pk."""

#     model = AdjustNoteItem
#     template_name = "adjusts/adjust_notes/partials/htmx/_items.html"

#     permission_required = ("adjusts.view_adjustnote",)

#     def get_queryset(self):
#         return self.model.objects.filter(adjust_note__pk=self.kwargs["pk"])


# adjust_note_items_list_view = AdjustNoteItemsListView.as_view()


# class AdjustNoteHistoryListView(LoginRequiredMixin, PermissionRequiredMixin, SingleTableMixin, CoreListView):
#     """
#     Partial page to show all Actstreams based on given AdjustNote's pk as target_object_id.
#     This page use DataTables server side.
#     """

#     model = Action
#     template_name = "adjusts/adjust_notes/partials/htmx/_history.html"
#     table_class = AdjustNoteHistoryDataTables

#     # To prevent query all object as it will use ajax call in template
#     object_list = Action.objects.none()
#     permission_required = ("adjusts.view_adjustnote",)

#     def get_queryset(self):
#         """To prevent query all object."""
#         return self.object_list

#     def get_adjust_note(self) -> AdjustNote:
#         return AdjustNote.objects.get(pk=self.kwargs["pk"])

#     def get_context_data(self, **kwargs) -> Any:
#         context = super().get_context_data(**kwargs)
#         context["object"] = self.get_adjust_note()
#         return context


# adjust_note_history_list_view = AdjustNoteHistoryListView.as_view()


# class AdjustNoteHistoryModifiedView(CoreBaseHistoryModifiedView):
#     """
#     Partial pop up view to show the differences in Adjustment history view.
#     """

#     permission_required = ("adjusts.view_adjustnote",)


# adjust_note_history_modified_view = AdjustNoteHistoryModifiedView.as_view()

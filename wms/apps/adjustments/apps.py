from django.apps import AppConfig
from django.utils.translation import gettext_lazy as _


class AdjustmentConfig(AppConfig):
    name = "wms.apps.adjustments"
    verbose_name = _("Adjustments")

    def ready(self):
        from actstream import registry

        import wms.apps.adjustments.signals  # noqa F401
        from wms.apps.adjustments.models import Adjustment, AdjustmentItem, AdjustmentStockIn

        registry.register(Adjustment, AdjustmentItem, AdjustmentStockIn)

        # To prevent related activity stream being removed when object is deleted.

        def not_target_actions(field):
            return field.name not in ["target_actions", "action_object_actions"]

        # adjustments apps
        Adjustment._meta.private_fields = list(filter(not_target_actions, Adjustment._meta.private_fields))
        AdjustmentItem._meta.private_fields = list(filter(not_target_actions, AdjustmentItem._meta.private_fields))
        AdjustmentStockIn._meta.private_fields = list(
            filter(not_target_actions, AdjustmentStockIn._meta.private_fields)
        )

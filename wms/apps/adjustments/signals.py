import logging

from django.db.models.signals import post_save
from django.dispatch import receiver

from wms.cores.actstream import register_stream, get_current_user_from_request

from .models import Adjustment, AdjustmentItem, AdjustmentStockIn

logger = logging.getLogger(__name__)


#################
# FOR ACTSTREAM #
#################

register_stream(Adjustment, logger=logger)
register_stream(AdjustmentItem, logger=logger, parent_field="adjustment")
register_stream(AdjustmentStockIn, logger=logger)


###################
# FOR APP SIGNALS #
###################


@receiver(post_save, sender=Adjustment)
def generate_system_number(sender, instance, created=False, **kwargs):
    """To generate system number on instance."""
    if created:
        print("WTFFFFF")
        instance.generate_system_number()
        logger.info(f"SIGNAL: {sender.__name__}: generated system number for {instance}")


@receiver(post_save, sender=AdjustmentItem)
def create_stockin(sender, instance, created=False, **kwargs):
    """To create assign approved user into stock in."""
    if instance.status == AdjustmentItem.Status.APPROVED:
        approved_by = get_current_user_from_request()
        instance.create_adjustmentstockin(approved_by=approved_by)

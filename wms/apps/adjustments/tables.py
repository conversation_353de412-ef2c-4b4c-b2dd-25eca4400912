import itertools

import django_tables2 as tables
from django.conf import settings
from django.urls import reverse, NoReverseMatch

from wms.apps.adjustments.models import Adjustment, AdjustmentItem
from wms.cores.columns import HTMXColumn
from django.utils.translation import gettext_lazy as _


class AdjustmentTable(tables.Table):
    section_title = "Adjustment Lists"
    section_name = "Adjustment"

    system_number = tables.LinkColumn(
        "adjustments:adjustment:panel",
        args=[tables.utils.A("pk")],
        verbose_name="Number"
    )
    status = tables.Column()
    adjustment_datetime = tables.DateTimeColumn(
        verbose_name=_("Adjustment Date"),
        format=settings.DATETIME_FORMAT,
        attrs={
            "td": {
                "class": "whitespace-nowrap",
            },
        }
    )

    created = tables.DateTimeColumn(
        format=settings.DATETIME_FORMAT,
        attrs={
            "td": {
                "class": "whitespace-nowrap",
            },
        }
    )

    actions = tables.TemplateColumn(
        verbose_name="Actions",
        template_name="tables/table_actions_column.html", orderable=False
    )

    @property
    def create_url(self):
        try:
            return reverse('adjustments:adjustment:create')
        except NoReverseMatch:
            return None

    # def get_row_class(self, record):
    #     """Returns the appropriate class based on the status value"""
    #     return record.html_status_class if record else 'bg-inherit'

    class Meta:
        model = Adjustment
        order_by = '-system_number'
        template_name = "tables/table_htmx.html"
        # row_attrs = {
        #     'class': lambda record: AdjustmentTable.get_row_class(None, record)
        # }
        fields = (
            "system_number",
            "status",
            "issued_by",
            "adjustment_datetime",
            "created",
        )

    # def render_status(self, value, record):
    #     """Return nice html label display for status."""
    #     return record.html_status_display

    def render_actions(self, value, record, column):
        """
        Conditionally show edit button based on Adjustment status.
        Only show edit button when Adjustment status is not COMPLETED or COMPLETED_WITH_REJECT.
        """
        from django.template.loader import get_template

        # Create a context dictionary for the template
        context = {}

        # Only add edit_url to context if the adjustment is not completed
        context['view_url'] = "adjustments:adjustment:panel"
        if record.status in [Adjustment.Status.DRAFT]:
            context['edit_url'] = "adjustments:adjustment:update"

        # Always add the record to the context
        context['record'] = record

        # Render the template with our custom context
        template = get_template("tables/table_actions_column.html")
        return template.render(context)

    def render_status(self, record):
        """Return nice html label display for status."""
        return record.html_status_display


class AdjustmentDetailTable(tables.Table):
    """
    Table for displaying adjustment details in the detail view.
    Used by AdjustmentDataTableDetailView.
    """
    system_number = HTMXColumn(
        url_name="adjustments:adjustment:detail_home",
        target_id="detail-panel",
        verbose_name=_("Number"),
        push_url=True,
        push_url_name="adjustments:adjustment:panel",
    )
    status = tables.Column()

    class Meta:
        model = Adjustment
        fields = ("system_number", "status")
        order_by = '-system_number'
        template_name = "tables/table_htmx.html"

    def __init__(self, *args, object_pk=None, **kwargs):
        super().__init__(*args, **kwargs)
        self.object_pk = object_pk

    def render_status(self, record):
        """Return nice html label display for status."""
        return record.html_status_display

class AdjustmentItemTable(tables.Table):

    row_number = tables.Column(
        verbose_name="#",
        empty_values=(),
        orderable=True,
        order_by="sort_order"
    )
    item_code = tables.Column(
        verbose_name=_("Code"),
        accessor="item.code",
    )
    item_name = tables.Column(verbose_name=_("Name"), accessor="item.name")
    batch_no = tables.Column(
        verbose_name=_("Batch"),
        accessor="batch_no",
        attrs={
            "td": {"class": "font-bold"},
        }
    )
    expiry_date = tables.DateTimeColumn(
        verbose_name=_("Expiry"),
        accessor="expiry_date",
        format=settings.DATE_FORMAT,
        attrs={
            "td": {
                "class": "whitespace-nowrap",
            },
        }
    )
    quantity = tables.Column(verbose_name=_("Qty"), orderable=False)
    uom = tables.Column(verbose_name=_("UOM"), orderable=False)
    status = tables.Column(
        verbose_name=_("Status"),
        attrs={
            "td": {"class": "status-cell"}
        }
    )
    action_by = tables.TemplateColumn(
        verbose_name=_("Action By"),
        template_name="adjustments/adjustment_item_action_by.html",
        orderable=False,
        attrs={
            "td": {"class": "action-by-cell"}
        }
    )

    actions = tables.TemplateColumn(
        verbose_name=_("Actions"),
        template_name="adjustments/adjustment_item_actions.html",
        orderable=False
    )

    class Meta:
        model = AdjustmentItem
        order_by = 'sort_order'
        template_name = "tables/table_htmx.html"
        fields = (
            "row_number",
            "item_code",
            "item_name",
            "batch_no",
            "expiry_date",
            "quantity",
            "uom",
            "status",
            "action_by",
            "actions"
        )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.counter = 0

    def render_row_number(self, value, record):
        # Use the get_position method from the model to get the fixed position
        # This ensures the row number stays consistent regardless of sorting
        return str(record.get_position)

    def render_quantity(self, value, record):
        """Format the quantity with appropriate precision."""
        return f"{round(record.quantity, record.uom.unit_precision)}"

    def render_uom(self, value, record):
        """Display the UOM symbol."""
        return record.uom.symbol

    def render_status(self, value, record):
        """Display the status in a readable format."""
        from django.template.loader import get_template

        # Create a context dictionary for the template
        context = {'record': record}

        # Render the template with our custom context
        template = get_template("adjustments/adjustment_item_status.html")
        return template.render(context)


#############################################################
# Old Code, to be remove after everything is stabled.
#############################################################


# from django.urls import reverse
# from django.utils.translation import gettext_lazy as _

# import django_tables2 as tables
# from actstream.models import Action

# from wss.cores.models import DISPLAY_EMPTY_VALUE
# from wss.cores.tables import (
#     HTMX_LIST_ATTRS_CLASS,
#     HTMX_LIST_SM_ATTRS_CLASS,
#     PARTIAL_LIST_ATTRS_CLASS,
#     TABLE_ATTRS_CLASS,
#     AbstractHistoryDataTables,
# )
# from wss.cores.utils import convert_camel_case_to_space

# from .models import AdjustNote, AdjustNoteItem


# class AdjustNoteDataTables(tables.Table):
#     """Table used on adjust_notes list page."""

#     numbering = tables.Column(verbose_name=_("Numbering"), linkify=True)
#     adjust_date = tables.TemplateColumn(
#         verbose_name=_("Adjustment Date"),
#         template_code="{{ record.adjust_datetime }}",
#     )
#     actions = tables.TemplateColumn(
#         verbose_name=_("Actions"),
#         orderable=False,
#         template_name="adjusts/adjust_notes/partials/tables/_actions.html",
#         attrs={"th": {"class": "text-right"}, "td": {"class": "text-right"}},
#         exclude_from_export=True,
#     )

#     class Meta:
#         model = AdjustNote
#         order_by = "-numbering"  # Must follow the first row display in the table
#         default = DISPLAY_EMPTY_VALUE
#         template_name = "partials/tables/_datatables_server_side.html"
#         attrs = {"class": TABLE_ATTRS_CLASS, "id": "adjust_notes_datatables", "style": "display: none;"}
#         row_attrs = {"data-id": lambda record: record.pk}

#         fields = [
#             "numbering",
#             "status",
#             "issued_by",
#             "customer_reference",
#             "adjust_date",
#             "created",
#         ]
#         sequence = [
#             "numbering",
#             "status",
#             "issued_by",
#             "customer_reference",
#             "adjust_date",
#             "created",
#         ]

#     def render_status(self, value, record):
#         """Return nice html label display for status."""
#         return record.html_status_display

#     def render_actions(self, column, record, table, value, bound_column, bound_row):
#         """Add permission checking into actions column."""
#         change_perms = self.request.user.has_perm("adjusts.change_adjustnote")
#         delete_perms = self.request.user.has_perm("adjusts.delete_adjustnote")
#         column.extra_context = {
#             "change_perms": change_perms,
#             "delete_perms": delete_perms,
#         }
#         return column.render(record, table, value, bound_column, **{"bound_row": bound_row})


# class AdjustNoteDetailDataTables(tables.Table):
#     """Table used on adjust_notes detail page."""

#     numbering = tables.Column(verbose_name=_("Numbering"), linkify=True)

#     class Meta:
#         model = AdjustNote
#         order_by = "-numbering"  # Must follow the first row display in the table
#         default = DISPLAY_EMPTY_VALUE
#         template_name = "partials/tables/_datatables_server_side.html"
#         attrs = {"class": PARTIAL_LIST_ATTRS_CLASS, "id": "adjust_notes_datatables", "style": "display: none;"}
#         row_attrs = {"data-id": lambda record: record.pk}

#         fields = [
#             "pk",
#             "numbering",
#             "status",
#         ]
#         sequence = [
#             "pk",
#             "numbering",
#             "status",
#         ]

#     def render_pk(self, value, record):
#         return f"highlight_id_{value}"

#     def render_status(self, value, record):
#         """Return nice html label display for status."""
#         return record.html_status_display


# class AdjustNoteRejectDataTables(tables.Table):
#     """Table used on adjust_notes defect list page."""

#     item_code = tables.Column(
#         verbose_name=_("CODE"),
#         accessor="item",
#     )
#     item_name = tables.Column(
#         verbose_name=_("NAME"),
#         accessor="item",
#     )

#     class Meta:
#         model = AdjustNoteItem
#         default = DISPLAY_EMPTY_VALUE
#         # Need to override table footer
#         template_name = "adjusts/adjust_notes/partials/tables/_reject_compose_datatables.html"
#         attrs = {
#             "class": HTMX_LIST_ATTRS_CLASS,
#             "id": "adjust_notes_reject_datatables",
#             "style": "display: none;",
#             "thead": {"class": "bg-danger"},
#         }
#         row_attrs = {"data-id": lambda record: record.pk}

#         fields = [
#             "quantity",
#             "remark",
#         ]
#         sequence = [
#             "item_code",
#             "item_name",
#             "quantity",
#             "remark",
#         ]

#     def render_item_code(self, value, record):
#         return f"{value.code}"

#     def render_item_name(self, value, record):
#         return f"{value.name}"

#     def render_quantity(self, value, record):
#         return f"{round(value, record.uom.unit_precision)} {record.uom.symbol}"


# class AdjustNoteHistoryDataTables(AbstractHistoryDataTables):
#     """Table used on AdjustNote's History tab."""

#     class Meta(AbstractHistoryDataTables.Meta):
#         attrs = {
#             "class": HTMX_LIST_SM_ATTRS_CLASS,
#             "id": "adjust_note_history_datatables",
#             "style": "display: none;",
#             "thead": {"class": "bg-secondary"},
#         }

#     def render_verb(self, value: str, record: Action) -> str:
#         if value == "created AdjustNoteStockIn":
#             return "Approved Adjust Note Item"

#         value_string = convert_camel_case_to_space(value)
#         value_string = value_string[0].upper() + value_string[1:]

#         if value.startswith("modified"):
#             link = reverse("adjusts:adjust_notes:history-modified", kwargs={"pk": record.pk})
#             htmx_modal_attributes = (
#                 'href="#" style="text-decoration: underline dotted" '
#                 'data-toggle="modal" data-target="#modalXl" '
#                 'hx-target="#modalXlBody" hx-swap="innerHTML"'
#             )
#             return f'<a {htmx_modal_attributes} hx-get="{link}">{value_string}</a>'
#         else:
#             return value_string

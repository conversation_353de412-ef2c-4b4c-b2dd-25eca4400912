from django_admin_listfilter_dropdown.filters import RelatedDropdownFilter
from django.contrib import admin

from wms.cores.admin import BaseModelAdmin, BaseTabularInline

from ..models import Adjustment, AdjustmentItem, AdjustmentStockIn


class AdjustmentItemInline(BaseTabularInline):
    model = AdjustmentItem
    extra = 3


@admin.register(Adjustment)
class AdjustmentAdmin(BaseModelAdmin):
    """Django admin for Adjustment."""

    list_display = [
        "system_number",
        "issued_by",
        "adjustment_datetime",
        "deliver_to",
        "status",
        "remark",
    ]
    inlines = [
        AdjustmentItemInline,
    ]
    search_fields = ["system_number"]
    list_filter = ["status"]


@admin.register(AdjustmentStockIn)
class AdjustmentStockInAdmin(BaseModelAdmin):
    list_display = [
        "deliver_to",
        "approved_by",
        "approved_quantity",
        "batch_no",
        "stock_in_datetime",
        "item",
        "uom",
        "remark",
        "transaction",
        "adjustment_item",
    ]
    search_fields = (
        "adjustment_item__adjustment__system_number",
        "deliver_to__name",
        "item__name",
    )
    list_filter = [
        ("item", RelatedDropdownFilter),
    ]
    date_hierarchy = "stock_in_datetime"
    raw_id_fields = (
        "transaction",
        "adjustment_item",
    )

# from decimal import Decimal
# from typing import Any

# from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
# from django.db.models import Sum
# from django.db.models.query import QuerySet
# from django.urls import reverse
# from django.utils.translation import gettext_lazy as _

# from django_filters.views import FilterView
# from django_tables2 import SingleTableMixin

# from wss.cores.views import CoreCreateView, CoreDataTablesView, CoreDetailDataTablesView, CoreDetailView, CoreListView

# from wss.apps.inventories.models.stock import Stock
# from wss.apps.rackings.models.rack import RackTransaction

# from ..filters import GoodsReceivedNoteFilter
# from ..forms import RackingGoodsReceivedNoteStockInForm
# from ..models import GoodsReceivedNote, GoodsReceivedNoteDefectStockIn, GoodsReceivedNoteItem
# from ..tables import RackingGoodsReceivedNoteDataTables, RackingGoodsReceivedNoteDetailDataTables


# class RackingGoodsReceivedNoteListView(
#     LoginRequiredMixin, PermissionRequiredMixin, FilterView, SingleTableMixin, CoreListView
# ):
#     """Page to show all RackingGoodsReceivedNote for racking stock-in. This page use DataTables server side."""

#     model = GoodsReceivedNote
#     template_name = "receives/rackings/list.html"
#     table_class = RackingGoodsReceivedNoteDataTables
#     filterset_class = GoodsReceivedNoteFilter

#     # To prevent query all object as it will use ajax call in template
#     object_list = GoodsReceivedNote.objects.none()

#     header_title = "Rackings"
#     selected_page = "racking_goods_received_notes"
#     selected_subpage = None

#     permission_required = ("receives.view_goodsreceivednote",)

#     def get_queryset(self):
#         """To prevent query all object."""
#         return self.object_list


# racking_goods_received_note_list_view = RackingGoodsReceivedNoteListView.as_view()


# class RackingGoodsReceivedNoteDetailView(LoginRequiredMixin, PermissionRequiredMixin, CoreDetailDataTablesView):
#     """
#     Page to display selected RackingGoodsReceivedNote based on given RackingGoodsReceivedNote's pk.
#     This page use DataTables server side.
#     """

#     model = GoodsReceivedNote
#     template_name = "receives/rackings/detail.html"
#     table_class = RackingGoodsReceivedNoteDetailDataTables

#     header_title = "Rackings"
#     selected_page = "racking_goods_received_notes"
#     selected_subpage = None

#     permission_required = ("receives.view_goodsreceivednote",)

#     def get_queryset(self) -> QuerySet[GoodsReceivedNote]:
#         """
#         Need to overwrite get_queryset in order to fix CoreDetailDataTablesView.get_selected_object_index function.
#         """
#         user_warehouse = []
#         user = self.request.user
#         user_warehouse = list(user.warehouses.all().values_list("pk", flat=True))

#         # filter GRN base on warehouse and also user permission on warehouse
#         return self.model.objects.filter(deliver_to__pk__in=user_warehouse, available_for_racking=True)

#     def get_total_rejected_quantity(self):
#         return GoodsReceivedNoteDefectStockIn.objects.filter(
#             goods_received_note_item__goods_received_note__pk=self.kwargs["pk"]
#         ).aggregate(Sum("approved_quantity")).get("approved_quantity__sum", Decimal("0")) or Decimal("0")

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         # Round to 0 because currently defect list always use EA
#         context["total_rejected_quantity"] = round(self.get_total_rejected_quantity(), 0)
#         return context


# racking_goods_received_note_detail_view = RackingGoodsReceivedNoteDetailView.as_view()


# ##################
# # FOR DataTables #
# ##################


# class RackingGoodsReceivedNoteDataTablesView(LoginRequiredMixin, PermissionRequiredMixin, CoreDataTablesView):
#     """JSON for DataTables in list page."""

#     model = GoodsReceivedNote
#     table_class = RackingGoodsReceivedNoteDataTables
#     filterset_class = GoodsReceivedNoteFilter

#     permission_required = ("receives.view_goodsreceivednote",)

#     def get_queryset(self) -> QuerySet[GoodsReceivedNote]:

#         user_warehouse = []
#         user = self.request.user
#         user_warehouse = list(user.warehouses.all().values_list("pk", flat=True))

#         # filter GRN base on warehouse and also user permission on warehouse & is available_for_racking
#         return self.model.objects.filter(deliver_to__pk__in=user_warehouse, available_for_racking=True)


# racking_goods_received_note_datatables_view = RackingGoodsReceivedNoteDataTablesView.as_view()


# class RackingGoodsReceivedNoteDetailDataTablesView(RackingGoodsReceivedNoteDataTablesView):
#     """JSON for DataTables in detail page."""

#     table_class = RackingGoodsReceivedNoteDetailDataTables


# racking_goods_received_note_detail_datatables_view = RackingGoodsReceivedNoteDetailDataTablesView.as_view()


# ############
# # FOR HTMX #
# ############


# class RackingGoodsReceivedNoteItemsListView(LoginRequiredMixin, PermissionRequiredMixin, CoreListView):
#     """Partial page to show all RackingGoodsReceivedNoteItem based on given RackingGoodsReceivedNote's pk."""

#     model = GoodsReceivedNoteItem
#     template_name = "receives/rackings/partials/htmx/_items.html"

#     permission_required = ("receives.view_goodsreceivednote",)

#     def get_queryset(self) -> QuerySet[GoodsReceivedNoteItem]:
#         return self.model.objects.filter(goods_received_note__pk=self.kwargs["pk"])


# racking_goods_received_note_items_list_view = RackingGoodsReceivedNoteItemsListView.as_view()


# class RackingGoodsReceivedNoteItemStockInView(LoginRequiredMixin, PermissionRequiredMixin, CoreCreateView):
#     """
#     Partial page to display selected GoodsReceivedNoteItem's > Racking stock in form
#     based on given GoodsReceivedNoteItem's pk.
#     """

#     model = RackTransaction
#     form_class = RackingGoodsReceivedNoteStockInForm
#     template_name = "receives/rackings/partials/htmx/_item_stock_in_form.html"

#     permission_required = ("rackings.add_racktransaction",)

#     success_message = _("GRN item %(item)s successfully stock in")

#     def get_received_item_obj(self):
#         return GoodsReceivedNoteItem.objects.get(pk=self.kwargs["pk"])

#     def get_initial(self) -> dict[str, Any]:
#         initial = super().get_initial()

#         self.received_item_obj = self.get_received_item_obj()
#         stock = Stock.objects.get(
#             warehouse=self.received_item_obj.goods_received_note.deliver_to,
#             item=self.received_item_obj.item,
#             batch_no=self.received_item_obj.batch_no or "N/A",
#             expiry_date=self.received_item_obj.expiry_date,
#             item__consignor=self.received_item_obj.goods_received_note.consignor,
#         )
#         initial["stock"] = stock
#         initial["goods_received_note"] = self.received_item_obj.goods_received_note
#         initial["goods_received_note_item"] = self.received_item_obj

#         # Initialise form quantity based on remaining received quantity to be stock-in for racking
#         initial["quantity"] = self.received_item_obj.racking_quantity_status_dict.get(
#             "remaining_quantity", Decimal("0")
#         )

#         return initial

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         context["received_item_obj"] = self.received_item_obj
#         return context

#     def get_success_url(self):
#         return reverse("receives:racking_goods_received_notes:detail", kwargs={"pk": self.kwargs["pk"]})

#     def get_success_message(self, cleaned_data):
#         return self.success_message % {"item": self.received_item_obj.item}


# racking_goods_received_note_item_stock_in_view = RackingGoodsReceivedNoteItemStockInView.as_view()


# class RackingGoodsReceivedNoteItemTrView(LoginRequiredMixin, PermissionRequiredMixin, CoreDetailView):
#     """
#     Partial page to display selected RackingGoodsReceivedNoteItem's table tr HTML
#     based on given GoodsReceivedNoteItem's pk.
#     """

#     model = GoodsReceivedNoteItem
#     template_name = "receives/rackings/partials/htmx/_item_tr.html"

#     permission_required = ("receives.view_goodsreceivednoteitem",)


# racking_goods_received_note_item_tr_view = RackingGoodsReceivedNoteItemTrView.as_view()

import json
from decimal import Decimal, InvalidOperation

from django.contrib import messages
from django.http import HttpResponse
from django.shortcuts import get_object_or_404, render
from django.template.loader import render_to_string
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.views.decorators.http import require_POST
from django.urls import reverse

from wms.cores.views import CoreCreateView
from wms.apps.receives.forms.goods_received_note_item_receive import GoodsReceivedNoteStockInForm
from wms.apps.receives.forms.goods_received_note_item_add import GoodsReceivedNoteItemAddForm
from wms.apps.receives.models import GoodsReceivedNoteItem, GoodsReceivedNote, GoodsReceivedNoteStockIn
from wms.apps.settings.models import UnitOfMeasure
from wms.apps.inventories.models import Transaction, Item


class GoodsReceivedNoteItemStockInFormView(CoreCreateView):
    """
    Display and process the form for receiving a GRN item.
    """
    model = GoodsReceivedNoteStockIn
    form_class = GoodsReceivedNoteStockInForm
    template_name = 'receives/partials/goods_received_note_item_receive_form.html'
    success_url = 'receives:goods_received_notes:detail'

    def dispatch(self, request, *args, **kwargs):
        # Get the GRN item
        self.goods_received_note_item = get_object_or_404(GoodsReceivedNoteItem, pk=self.kwargs['pk'])

        allowed_statuses = [
            GoodsReceivedNote.Status.NEW,
            GoodsReceivedNote.Status.PARTIALLY_RECEIVED,
        ]

        # Check if the parent order is in 'NEW' status
        if self.goods_received_note_item.goods_received_note.status not in allowed_statuses:
            if self.request.htmx:
                headers = {
                    'HX-Trigger': json.dumps({
                        "closeModalEvent": None,
                        "showNotificationEvent": {
                            "message": "Receiving is only allowed for GRNs in 'New' status.",
                            "type": "error"
                        }
                    })
                }
                return HttpResponse(status=400, headers=headers)
            messages.error(request, _("Receiving is only allowed for GRNs in 'New' status."))
            # Return an empty response or a message indicating the restriction
            return HttpResponse(status=204)  # No content, or render a message template

        return super().dispatch(request, *args, **kwargs)

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['goods_received_note_item'] = self.goods_received_note_item
        return kwargs

    def form_valid(self, form):
        from wms.apps.inventories.models import Stock

        try:
            # Set required fields before saving
            form.instance.deliver_to = self.goods_received_note.deliver_to
            form.instance.goods_received_note_item = self.goods_received_note_item
            form.instance.item = self.goods_received_note_item.item
            form.instance.uom = self.goods_received_note_item.uom
            form.instance.stock_in_datetime = timezone.now()
            form.instance.approved_by = self.request.user
            # form.instance.batch_no = self.goods_received_note_item.batch_no
            # form.instance.expiry_date = self.goods_received_note_item.expiry_date

            # # Find the matching stock record
            # warehouse = form.cleaned_data['warehouse']
            # stock_filter = {
            #     'item': self.goods_received_note_item.item,
            #     'warehouse': warehouse,
            # }

            # # Add batch_no if specified
            # if self.goods_received_note_item.batch_no:
            #     stock_filter['batch_no'] = self.goods_received_note_item.batch_no

            # # Add expiry_date if specified
            # if self.goods_received_note_item.expiry_date:
            #     stock_filter['expiry_date'] = self.goods_received_note_item.expiry_date

            # # Try to find the matching stock
            # stock = Stock.objects.filter(**stock_filter).first()
            # if not stock:
            #     # If no stock found, add an error and return form_invalid
            #     form.add_error('warehouse',
            #                    _("No matching stock found in this warehouse with the specified batch and expiry date."))
            #     return self.form_invalid(form)

            # form.instance.stock = stock

            # Set quantity and system_quantity equal to pick_quantity initially
            # system_quantity may be adjusted by the model's save method if needed
            receive_qty = Decimal(str(form.cleaned_data['receive_quantity']))
            form.instance.approved_quantity = receive_qty
            # form.instance.system_quantity = receive_qty

            # save the form data into DB
            response = super().form_valid(form)

            # Refresh the goods_received_note_item to get updated values
            self.goods_received_note_item.refresh_from_db()

            # Check if this is an HTMX request
            if self.request.headers.get('HX-Request'):
                # For HTMX requests, return a JSON response with updated HTML fragments

                # Prepare context for templates
                context = {'record': self.goods_received_note_item, 'request': self.request}
                actions_id = f"grn-item-actions-{self.goods_received_note_item.pk}"
                status_id = f"grn-item-status-{self.goods_received_note_item.pk}"
                percentage_id = f"grn-item-percentage-{self.goods_received_note_item.pk}"
                action_by_id = f"grn-item-action-by-{self.goods_received_note_item.pk}"
                quantity_id = f"grn-item-quantity-{self.goods_received_note_item.pk}"

                actions_html = render_to_string(
                    'receives/partials/goods_received_note_item_actions.html',
                    context,
                    request=self.request
                )
                status_html = render_to_string(
                    'receives/partials/goods_received_note_item_status.html',
                    context,
                    request=self.request
                )
                percentage_html = render_to_string(
                    'receives/partials/goods_received_note_item_percentage.html',
                    context,
                    request=self.request
                )
                action_by_html = render_to_string(
                    'receives/partials/goods_received_note_item_action_by.html',
                    context,
                    request=self.request
                )
                quantity_html = render_to_string(
                    'receives/partials/goods_received_note_item_quantity.html',
                    context,
                    request=self.request
                )

                # Return JSON response with all HTML fragments
                response_html = f"""
                <div id="{actions_id}" hx-swap-oob="true">{actions_html}</div>
                <div id="{status_id}" hx-swap-oob="true">{status_html}</div>
                <div id="{percentage_id}" hx-swap-oob="true">{percentage_html}</div>
                <div id="{action_by_id}" hx-swap-oob="true">{action_by_html}</div>
                <div id="{quantity_id}" hx-swap-oob="true">{quantity_html}</div>
                <div></div>
                """

                trigger_data = {
                    "closeModalEvent": None,
                    "showNotificationEvent": {
                        "message": "Item received successfully!",
                        "type": "success"
                    }
                }

                headers = {
                    'HX-Trigger': json.dumps(trigger_data)
                }
                response = HttpResponse(response_html, headers=headers)
                return response

            # For regular form submissions, return the standard redirect response
            return response

        except Exception as e:
            # If any exception occurs during processing, add it as a form error
            form.add_error(None, str(e))
            return self.form_invalid(form)

    def get_success_url(self):
        # Redirect to the GRN detail page
        return reverse(
            'receives:goods_received_notes:detail',
            kwargs={'pk': self.goods_received_note_item.goods_received_note.pk}
        )

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        context.update({
            'goods_received_note_item': self.goods_received_note_item,
        })

        return context


def goods_received_note_item_delete_form(request, pk):
    """
    Display the confirmation form for deleting a GRN item.
    """
    goods_received_note_item = get_object_or_404(GoodsReceivedNoteItem, pk=pk)

    # Check if the item has any receives
    if goods_received_note_item.get_received_quantity > 0:
        if request.headers.get('HX-Request'):
            headers = {
                'HX-Trigger': json.dumps({
                    "closeModalEvent": None,
                    "showNotificationEvent": {
                        "message": "Deletion is only allowed for items with no receives.",
                        "type": "error"
                    }
                })
            }
            return HttpResponse(status=400, headers=headers)
        messages.error(request, _("Deletion is only allowed for items with no receives."))
        # Return an empty response or a message indicating the restriction
        return HttpResponse(status=204)  # No content, or render a message template

    # Check if this is the last item in the GRN
    item_count = goods_received_note_item.goods_received_note.goodsreceivednoteitem_set.count()
    if item_count <= 1:
        if request.headers.get('HX-Request'):
            headers = {
                'HX-Trigger': json.dumps({
                    "closeModalEvent": None,
                    "showNotificationEvent": {
                        "message": "Cannot delete the last item in a GRN.",
                        "type": "error"
                    }
                })
            }
            return HttpResponse(status=400, headers=headers)
        messages.error(request, _("Cannot delete the last item in a GRN."))
        # Return an empty response or a message indicating the restriction
        return HttpResponse(status=204)  # No content, or render a message template

    context = {
        'goods_received_note_item': goods_received_note_item,
        'request': request,
    }

    return render(request, 'receives/partials/goods_received_note_item_delete_form.html', context)


@require_POST
def goods_received_note_item_delete(request, pk):
    """
    Delete a GRN item.
    """
    goods_received_note_item = get_object_or_404(GoodsReceivedNoteItem, pk=pk)

    # Check if the GRN is in New status
    if goods_received_note_item.goods_received_note.status != 'New':
        if request.headers.get('HX-Request'):
            headers = {
                'HX-Trigger': json.dumps({
                    "closeModalEvent": None,
                    "showNotificationEvent": {
                        "message": "Deletion is only allowed for GRNs in 'New' status.",
                        "type": "error"
                    }
                })
            }
            return HttpResponse(status=400, headers=headers)
        messages.error(request, _("Deletion is only allowed for GRNs in 'New' status."))
        # Return an empty response or a message indicating the restriction
        # Use 400 Bad Request as the action is invalid at this state
        return HttpResponse(status=400, content="Deletion not allowed for this order status.")

    # Check if the item has any receives
    if goods_received_note_item.get_received_quantity > 0:
        if request.headers.get('HX-Request'):
            headers = {
                'HX-Trigger': json.dumps({
                    "closeModalEvent": None,
                    "showNotificationEvent": {
                        "message": "Deletion is only allowed for items with no receives.",
                        "type": "error"
                    }
                })
            }
            return HttpResponse(status=400, headers=headers)
        messages.error(request, _("Deletion is only allowed for items with no receives."))
        # Return an empty response or a message indicating the restriction
        # Use 400 Bad Request as the action is invalid at this state
        return HttpResponse(status=400, content="Deletion not allowed for items with receives.")

    # Check if this is the last item in the GRN
    item_count = goods_received_note_item.goods_received_note.goodsreceivednoteitem_set.count()
    if item_count <= 1:
        if request.headers.get('HX-Request'):
            headers = {
                'HX-Trigger': json.dumps({
                    "closeModalEvent": None,
                    "showNotificationEvent": {
                        "message": "Cannot delete the last item in a GRN.",
                        "type": "error"
                    }
                })
            }
            return HttpResponse(status=400, headers=headers)
        messages.error(request, _("Cannot delete the last item in a GRN."))
        # Return an empty response or a message indicating the restriction
        # Use 400 Bad Request as the action is invalid at this state
        return HttpResponse(status=400, content="Cannot delete the last item in a GRN.")

    try:
        goods_received_note_item.delete()
        response = HttpResponse(status=200)
        if request.headers.get('HX-Request'):
            trigger_data = {
                "closeModalEvent": None,
                "showNotificationEvent": {
                    "message": "Item deleted successfully!",
                    "type": "success"
                }
            }
            headers = {
                'HX-Trigger': json.dumps(trigger_data)
            }
            response = HttpResponse(status=200, headers=headers)
        return response
    except Exception as e:
        messages.error(request, _("Failed to delete GRN item: %(error)s") % {'error': str(e)})
        # Return an error response for HTMX
        return HttpResponse(status=500, content=f"Error deleting item: {str(e)}")



class GoodsReceivedNoteItemAddView(CoreCreateView):
    """
    Display and process the form for adding a new GRN item.
    """
    model = GoodsReceivedNoteItem
    form_class = GoodsReceivedNoteItemAddForm
    template_name = 'receives/partials/goods_received_note_item_add_form.html'
    success_url = 'receives:goods_received_notes:detail'

    def dispatch(self, request, *args, **kwargs):
        # Get the GRN
        self.goods_received_note = get_object_or_404(GoodsReceivedNote, pk=self.kwargs['pk'])

        allowed_statuses = [
            GoodsReceivedNote.Status.NEW,
            GoodsReceivedNote.Status.PARTIALLY_RECEIVED,
        ]

        # Check if the GRN is in 'NEW' status
        if self.goods_received_note.status not in allowed_statuses:
            if self.request.htmx:
                headers = {
                    'HX-Trigger': json.dumps({
                        "closeModalEvent": None,
                        "showNotificationEvent": {
                            "message": "Items can only be added to GRNs in 'New' or 'Partially Received' status.",
                            "type": "error"
                        }
                    })
                }
                return HttpResponse(status=400, headers=headers)
            messages.error(request, _("Items can only be added to GRNs in 'New' or 'Partially Received' status."))
            return HttpResponse(status=400, content="GRN not eligible for adding items.")

        return super().dispatch(request, *args, **kwargs)

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['goods_received_note'] = self.goods_received_note
        return kwargs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['goods_received_note'] = self.goods_received_note
        return context

    def form_valid(self, form):
        try:
            # Get the item and UOM from the form
            item_id = form.cleaned_data.get('item_id')
            uom_id = form.cleaned_data.get('uom_id')

            # Get the actual objects
            item = get_object_or_404(Item, pk=item_id)
            uom = get_object_or_404(UnitOfMeasure, pk=uom_id)

            # Create the GRN item
            goods_received_note_item = form.save(commit=False)
            goods_received_note_item.item = item
            goods_received_note_item.uom = uom
            goods_received_note_item.goods_received_note = self.goods_received_note
            goods_received_note_item.created_by = self.request.user
            goods_received_note_item.batch_no = form.cleaned_data.get('batch_no')
            goods_received_note_item.expiry_date = form.cleaned_data.get('expiry_date')
            goods_received_note_item.save()

            # For HTMX requests, return a success response and trigger refreshes
            if self.request.headers.get('HX-Request'):
                # Get the detail URL for the GRN
                detail_url = reverse('receives:goods_received_notes:detail', kwargs={'pk': self.goods_received_note.pk})

                # Set up HTMX triggers for success notification, modal close, and tab refresh
                trigger_data = {
                    "closeModalEvent": None,
                    "showNotificationEvent": {
                        "message": "Item added successfully!",
                        "type": "success"
                    },
                    "refreshTabContent": {
                        "url": detail_url
                    }
                }

                headers = {
                    'HX-Trigger': json.dumps(trigger_data)
                }

                # Return an empty response with headers - no need for row_html since we're refreshing the whole tab
                return HttpResponse('', headers=headers, status=200)

            # For regular form submissions, return the standard redirect response
            return super().form_valid(form)

        except Exception as e:
            # If any exception occurs during processing, add it as a form error
            form.add_error(None, str(e))
            return self.form_invalid(form)

    def form_invalid(self, form):
        # For HTMX requests, return the form with errors
        if self.request.headers.get('HX-Request'):
            return self.render_to_response(self.get_context_data(form=form))

        # For regular form submissions, return the standard response
        return super().form_invalid(form)


#############################################################
# Old Code, to be remove after everything is stabled.
#############################################################


# from typing import Any, Union

# from django.contrib import messages
# from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
# from django.db.models import BLANK_CHOICE_DASH
# from django.http import HttpRequest
# from django.http.response import HttpResponse, HttpResponseRedirect
# from django.shortcuts import redirect
# from django.urls import reverse
# from django.utils.translation import gettext_lazy as _

# from wss.cores.utils import localtime_now
# from wss.cores.views import CoreCreateView, CoreDeleteView, CoreDetailView, CoreListView

# from wss.apps.inventories.models import Item

# from ..forms import (
#     GoodsReceivedNoteDefectForm,
#     GoodsReceivedNoteDefectStockInForm,
#     GoodsReceivedNoteItemCreateForm,
#     GoodsReceivedNoteStockInForm,
# )
# from ..models import (
#     GoodsReceivedNote,
#     GoodsReceivedNoteDefect,
#     GoodsReceivedNoteDefectStockIn,
#     GoodsReceivedNoteItem,
#     GoodsReceivedNoteStockIn,
# )


# class GoodsReceivedNoteItemDeleteView(LoginRequiredMixin, PermissionRequiredMixin, CoreDeleteView):
#     """Page to delete selected GoodsReceivedNoteItem based on given GoodsReceivedNoteItem's pk."""

#     model = GoodsReceivedNoteItem
#     success_message = _("GRN item %(item)s successfully deleted")

#     permission_required = ("receives.delete_goodsreceivednoteitem",)

#     def get_success_url(self):
#         return reverse("receives:goods_received_notes:items", kwargs={"pk": self.object.goods_received_note.pk})

#     def get_success_message(self):
#         return self.success_message % {"item": self.object.item}


# goods_received_note_item_delete_view = GoodsReceivedNoteItemDeleteView.as_view()


# ############
# # FOR HTMX #
# ############


# class GoodsReceivedNoteItemStockInsView(LoginRequiredMixin, PermissionRequiredMixin, CoreListView):
#     """Partial page to show all GoodsReceivedNoteStockIn based on given GoodsReceivedNoteItem's pk."""

#     model = GoodsReceivedNoteStockIn
#     template_name = "receives/goods_received_notes/partials/htmx/_item_stockin.html"

#     permission_required = ("receives.view_goodsreceivednotestockin",)

#     def get_queryset(self):
#         return self.model.objects.filter(goods_received_note_item__pk=self.kwargs["pk"])

#     def get_rejected_item_list(self):
#         return GoodsReceivedNoteDefectStockIn.objects.filter(goods_received_note_item__pk=self.kwargs["pk"])

#     def get_goods_received_note_item(self):
#         return GoodsReceivedNoteItem.objects.get(pk=self.kwargs["pk"])

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         context["goods_received_note_item"] = self.get_goods_received_note_item()
#         context["rejected_item_list"] = self.get_rejected_item_list()
#         return context


# goods_received_note_item_stockins_view = GoodsReceivedNoteItemStockInsView.as_view()


# class GoodsReceivedNoteItemTrView(LoginRequiredMixin, PermissionRequiredMixin, CoreDetailView):
#     """Partial page to display selected GoodsReceivedNoteItem's table tr HTML
#     based on given GoodsReceivedNoteItem's pk."""

#     model = GoodsReceivedNoteItem
#     template_name = "receives/goods_received_notes/partials/htmx/_item_tr.html"

#     permission_required = ("receives.view_goodsreceivednoteitem",)


# goods_received_note_item_tr_view = GoodsReceivedNoteItemTrView.as_view()


# class GoodsReceivedNoteItemReceiveView(LoginRequiredMixin, PermissionRequiredMixin, CoreCreateView):
#     """Partial page to display selected GoodsReceivedNoteItem's receive form
#     based on given GoodsReceivedNoteItem's pk."""

#     model = GoodsReceivedNoteStockIn
#     form_class = GoodsReceivedNoteStockInForm
#     template_name = "receives/goods_received_notes/partials/htmx/_item_receive_form.html"

#     permission_required = ("receives.add_goodsreceivednotestockin",)

#     success_message = _("GRN item %(item)s successfully stock in")

#     def get_received_item_obj(self):
#         return GoodsReceivedNoteItem.objects.get(pk=self.kwargs["pk"])

#     def get_initial(self):
#         initial = super().get_initial()

#         self.received_item_obj = self.get_received_item_obj()
#         initial["deliver_to"] = self.received_item_obj.goods_received_note.deliver_to
#         initial["approved_by"] = self.request.user
#         initial["stock_in_datetime"] = localtime_now()
#         initial["item"] = self.received_item_obj.item
#         initial["goods_received_note_item"] = self.received_item_obj
#         initial["uom"] = self.received_item_obj.item.default_stock_in_uom.pk
#         if self.received_item_obj.batch_no:
#             initial["batch_no"] = self.received_item_obj.batch_no
#         initial["expiry_date"] = self.received_item_obj.expiry_date

#         # Pre-fill item code and received quantity for specific consignors
#         consignors_slug = ["mitsubishi-motors", "fresenius-medical-care-sdn-bhd"]
#         if self.received_item_obj.goods_received_note.consignor.slug in consignors_slug:
#             initial["item_code"] = self.received_item_obj.item.code
#             # Initialise form quantity based on remaining quantity to be received
#             if self.received_item_obj.get_received_quantity < self.received_item_obj.quantity:
#                 initial["approved_quantity"] = round(
#                     self.received_item_obj.quantity - self.received_item_obj.get_received_quantity,
#                     self.received_item_obj.uom.unit_precision,
#                 )

#         return initial

#     def post(self, request: HttpRequest, *args: str, **kwargs: Any) -> Union[HttpResponseRedirect, HttpResponse]:
#         goods_received_note = self.get_received_item_obj().goods_received_note
#         if goods_received_note.status == GoodsReceivedNote.Status.OBSOLETE:
#             messages.error(request, "Cannot perform Receive on GRN with Obsolete status")
#             return redirect(self.get_success_url())

#         return super().post(request, *args, **kwargs)

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         context["received_item_obj"] = self.received_item_obj
#         return context

#     def get_success_url(self):
#         return reverse("receives:goods_received_notes_item:stockins", kwargs={"pk": self.kwargs["pk"]})

#     def get_success_message(self, cleaned_data):
#         return self.success_message % {"item": self.received_item_obj.item}


# goods_received_note_item_receive_view = GoodsReceivedNoteItemReceiveView.as_view()


# class GoodsReceivedNoteItemReceiveDefectView(LoginRequiredMixin, PermissionRequiredMixin, CoreCreateView):
#     """Partial page to display selected GoodsReceivedNoteItem's defect receive form
#     based on given GoodsReceivedNoteItem's pk."""

#     model = GoodsReceivedNoteDefectStockIn
#     form_class = GoodsReceivedNoteDefectStockInForm
#     template_name = "receives/goods_received_notes/partials/htmx/_item_receive_defect_form.html"

#     permission_required = ("receives.add_goodsreceivednotedefectstockin",)

#     success_message = _("GRN item %(item)s successfully stock in defect")

#     def get_received_item_obj(self):
#         return GoodsReceivedNoteItem.objects.get(pk=self.kwargs["pk"])

#     def get_initial(self):
#         initial = super().get_initial()

#         self.received_item_obj = self.get_received_item_obj()
#         initial["deliver_to"] = self.received_item_obj.goods_received_note.deliver_to
#         initial["approved_by"] = self.request.user
#         initial["stock_in_datetime"] = localtime_now()
#         initial["item"] = self.received_item_obj.item
#         initial["goods_received_note_item"] = self.received_item_obj
#         initial["uom"] = self.received_item_obj.item.default_stock_in_uom

#         return initial

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         context["received_item_obj"] = self.received_item_obj
#         return context

#     def get_success_url(self):
#         return reverse("receives:goods_received_notes_item:stockins", kwargs={"pk": self.kwargs["pk"]})

#     def get_success_message(self, cleaned_data):
#         return self.success_message % {"item": self.received_item_obj.item}


# goods_received_note_item_receive_defect_view = GoodsReceivedNoteItemReceiveDefectView.as_view()


# class GoodsReceivedNoteItemRejectView(LoginRequiredMixin, PermissionRequiredMixin, CoreCreateView):
#     """Partial page to display selected GoodsReceivedNoteItem's reject form
#     based on given GoodsReceivedNoteItem's pk."""

#     model = GoodsReceivedNoteDefect
#     form_class = GoodsReceivedNoteDefectForm
#     template_name = "receives/goods_received_notes/partials/htmx/_item_reject_form.html"

#     permission_required = ("receives.add_goodsreceivednotedefect",)

#     success_message = _("GRN item %(item)s successfully stock in")

#     def get_received_item_obj(self):
#         return GoodsReceivedNoteItem.objects.get(pk=self.kwargs["pk"])

#     def get_initial(self):
#         initial = super().get_initial()

#         self.received_item_obj = self.get_received_item_obj()
#         initial["deliver_to"] = self.received_item_obj.goods_received_note.deliver_to
#         initial["rejected_by"] = self.request.user
#         initial["defect_datetime"] = localtime_now()
#         initial["item"] = self.received_item_obj.item
#         initial["goods_received_note_item"] = self.received_item_obj
#         initial["uom"] = self.received_item_obj.item.default_stock_in_uom

#         return initial

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         context["received_item_obj"] = self.received_item_obj
#         return context

#     def get_success_url(self):
#         return reverse("receives:goods_received_notes_item:stockins", kwargs={"pk": self.kwargs["pk"]})

#     def get_success_message(self, cleaned_data):
#         return self.success_message % {"item": self.received_item_obj.item}


# goods_received_note_item_reject_view = GoodsReceivedNoteItemRejectView.as_view()


# class GoodsReceivedNoteItemCreateView(LoginRequiredMixin, PermissionRequiredMixin, CoreCreateView):
#     """Partial page to add GoodsReceivedNoteItem based on given GoodsReceivedNote's pk."""

#     model = GoodsReceivedNoteItem
#     form_class = GoodsReceivedNoteItemCreateForm
#     template_name = "receives/goods_received_notes/partials/htmx/_item_create_form.html"

#     permission_required = ("receives.add_goodsreceivednoteitem",)

#     success_message = _("GRN item %(item)s successfully created")

#     def get_grn_obj(self):
#         return GoodsReceivedNote.objects.get(pk=self.kwargs["grn_pk"])

#     def get_form(self):
#         form = super().get_form()
#         item_qs = Item.objects.filter(consignor=self.grn_obj.consignor)
#         item_choices = BLANK_CHOICE_DASH
#         form.fields["item"].choices = item_choices + [(item.pk, item) for item in item_qs]
#         return form

#     def get_initial(self):
#         initial = super().get_initial()

#         self.grn_obj = self.get_grn_obj()
#         initial["goods_received_note"] = self.grn_obj

#         return initial

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         context["grn_obj"] = self.grn_obj
#         return context

#     def get_success_url(self):
#         return reverse("receives:goods_received_notes:detail", kwargs={"pk": self.kwargs["grn_pk"]})

#     def get_success_message(self, cleaned_data):
#         return self.success_message % {"item": self.object.item}


# goods_received_note_item_create_view = GoodsReceivedNoteItemCreateView.as_view()

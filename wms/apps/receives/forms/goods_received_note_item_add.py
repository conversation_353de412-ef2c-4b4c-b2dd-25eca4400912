from django import forms
from django.forms import <PERSON>Field
from django.utils.translation import gettext_lazy as _

from wms.cores.forms.fields import CoreModelForm
from wms.apps.inventories.models import Item
from wms.apps.settings.models import UnitOfMeasure
from wms.cores.forms.widget import CoreSelectWidget, CoreNumberWidget, CoreTextWidget
from wms.apps.receives.models import GoodsReceivedNoteItem


class NoValidationChoiceField(ChoiceField):
    def validate(self, value):
        pass  # Skip validation


class GoodsReceivedNoteItemAddForm(CoreModelForm):
    """
    Form for adding a single Goods Received Note Item via modal.
    """
    item = forms.CharField(
        required=True,
        widget=CoreSelectWidget(
            attrs={
                'class': 'goods-received-note-item w-full',
                'data-api-url': '/api/consignors/{consignor_id}/items/',
                'required': 'required'
            }
        )
    )

    batch_no = NoValidationChoiceField(
        choices=[],
        required=False,
        widget=CoreSelectWidget(
            attrs={
                'class': 'goods-received-note-batch w-full',
                'disabled': 'disabled',
                'tags': 'true',  # enabled select2 dynamic option
            }
        )
    )

    expiry_date = NoValidationChoiceField(
        choices=[],
        required=False,
        widget=CoreSelectWidget(
            attrs={
                'class': 'goods-received-note-expiry w-full',
                'disabled': 'disabled',
                'tags': 'true',  # enabled select2 dynamic option
            }
        )
    )

    quantity = forms.DecimalField(
        required=True,
        widget=CoreNumberWidget(
            attrs={
                'class': 'goods-received-note-quantity w-full',
            },
        )
    )

    uom = forms.CharField(
        required=False,
        widget=CoreTextWidget(
            attrs={
                'class': 'goods-received-note-uom w-full',
                'disabled': 'disabled',
                'readonly': 'readonly'
            }
        )
    )

    item_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)
    uom_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)

    # Hidden fields to store batch_no and expiry_date values
    batch_no_hidden = forms.CharField(widget=forms.HiddenInput(), required=False)
    expiry_date_hidden = forms.CharField(widget=forms.HiddenInput(), required=False)

    class Meta:
        model = GoodsReceivedNoteItem
        fields = [
            "item",
            "batch_no",
            "expiry_date",
            "quantity",
            "uom",
            "item_id",
            "uom_id",
            "batch_no_hidden",
            "expiry_date_hidden",
        ]

    def __init__(self, goods_received_note=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.goods_received_note = goods_received_note

        # Set the consignor_id data attribute for the item select if available
        if goods_received_note and goods_received_note.consignor:
            consignor_id = goods_received_note.consignor.id
            # Replace the placeholder in the data-api-url attribute
            self.fields['item'].widget.attrs['data-api-url'] = self.fields['item'].widget.attrs['data-api-url'].replace(
                '{consignor_id}', str(consignor_id))
            # Add a data-consignor-id attribute for direct access in JavaScript
            self.fields['item'].widget.attrs['data-consignor-id'] = str(consignor_id)

    def clean(self):
        cleaned_data = super().clean()

        # Validate item selection
        item_id = cleaned_data.get('item_id')
        if not item_id:
            self.add_error('item', _('Please select a valid item.'))
            return cleaned_data

        # Validate UOM
        uom_id = cleaned_data.get('uom_id')
        if not uom_id:
            self.add_error('uom', _('UOM is required.'))

            # Get the actual model instances
        try:
            item = Item.objects.get(id=item_id)
            cleaned_data['item'] = item
        except Item.DoesNotExist:
            self.add_error('item', _('Invalid item selection.'))

        try:
            uom = UnitOfMeasure.objects.get(id=uom_id)
            cleaned_data['uom'] = uom
        except UnitOfMeasure.DoesNotExist:
            self.add_error('uom', _('Invalid UOM selection.'))

        # Validate that UOM belongs to the selected item
        if item and uom and item.uom.id != uom.id:
            self.add_error('uom', _(f'The selected UOM ({uom}) does not match the item\'s UOM ({item.uom})'))

        # Validate batch_no and expiry_date if provided
        batch_no = cleaned_data.get('batch_no_hidden')
        expiry_date = cleaned_data.get('expiry_date_hidden')

        if expiry_date == 'N/A' or expiry_date == '':
            cleaned_data['expiry_date'] = None
            expiry_date = None

        if isinstance(expiry_date, str) and expiry_date:
            try:
                from datetime import datetime
                expiry_date = datetime.strptime(expiry_date, '%Y-%m-%d').date()
            except ValueError:
                self.add_error('expiry_date', _(f'Invalid date format: {expiry_date}'))

        # Validate quantity is greater than zero
        quantity = cleaned_data.get('quantity')
        if quantity is not None and quantity <= 0:
            self.add_error('quantity', _("Quantity must be greater than zero."))

        return cleaned_data

from django import forms
from django.utils.translation import gettext_lazy as _

from wms.cores.forms.fields import CoreModelForm
from wms.cores.forms.widget import CoreSelectWidget, CoreNumberWidget, CoreTextAreaWidget
from wms.apps.receives.models import GoodsReceivedNoteDefectStockIn
from wms.cores.utils import uom_choices_symbol


class GoodsReceivedNoteDefectStockInForm(CoreModelForm):
    """Form to create GoodsReceivedNoteDefectStockIn for rejecting items."""

    item_code = forms.CharField(label=_("Item Code"))
    uom = forms.ChoiceField(label=_("UOM"), choices=[])

    class Meta:
        model = GoodsReceivedNoteDefectStockIn
        fields = [
            "item_code",
            "reason",
            "approved_quantity",
            "uom",
            "batch_no",
            "expiry_date",
            "remark",
            "deliver_to",
            "approved_by",
            "stock_in_datetime",
            "item",
            "goods_received_note_item",
        ]
        widgets = {
            "reason": CoreSelectWidget(attrs={"class": "w-full"}),
            "approved_quantity": CoreNumberWidget(attrs={"class": "w-full", "step": "0.000001"}),
            "remark": CoreTextAreaWidget(attrs={"rows": 3, "class": "w-full"}),
            "deliver_to": forms.HiddenInput(),
            "approved_by": forms.HiddenInput(),
            "stock_in_datetime": forms.HiddenInput(),
            "item": forms.HiddenInput(),
            "goods_received_note_item": forms.HiddenInput(),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Override label
        self.fields["approved_quantity"].label = _("Reject Quantity")

        # Add onclick select text
        self.fields["batch_no"].widget.attrs["onClick"] = "this.select();"

        item = self.initial.get("item", None)
        uom = self.initial.get("uom", None)

        # Override UOM choices
        self.fields["uom"].choices = uom_choices_symbol(item=item, pre_selected_uom=uom)

    def clean(self):
        cleaned_data = super().clean()

        item_code = cleaned_data.get("item_code")
        item = cleaned_data.get("item")

        if item_code and item and item_code != item.code:
            msg = _("Item Code does not Match.")
            self.add_error("item_code", msg)

        return cleaned_data

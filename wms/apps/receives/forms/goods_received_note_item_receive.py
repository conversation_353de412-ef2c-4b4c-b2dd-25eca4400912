from django import forms
from django.db.models import BLANK_CHOICE_DASH
from django.utils.translation import gettext_lazy as _

from wms.cores.forms.fields import CoreModelForm, CoreModelChoiceField, FormFieldSize
from wms.cores.forms.widget import CoreNumberWidget, CoreTextAreaWidget
from wms.cores.utils import uom_choices_symbol

from wms.apps.receives.models import GoodsReceivedNoteItem, GoodsReceivedNoteStockIn
from wms.apps.settings.models import Warehouse
from decimal import Decimal
from wms.cores.utils import format_decimal_values


class GoodsReceivedNoteStockInForm(CoreModelForm):
    """Form to create GoodsReceivedNoteStockIn."""

    # Custom field for receive quantity
    receive_quantity = forms.DecimalField(
        label=_("Receive Quantity"),
        max_digits=19,
        decimal_places=6,
        widget=CoreNumberWidget(attrs={
            "class": "w-full",
        })
    )

    class Meta:
        model = GoodsReceivedNoteStockIn
        fields = [
            "receive_quantity",
            "remark",
            "deliver_to",
            "approved_by",
            "stock_in_datetime",
            "item",
            "goods_received_note_item",
            "uom",
            "batch_no",
            "expiry_date",
        ]
        widgets = {
            "remark": CoreTextAreaWidget(attrs={"rows": 3, "class": "w-full"}),
            "deliver_to": forms.HiddenInput(),
            "approved_by": forms.HiddenInput(),
            "stock_in_datetime": forms.HiddenInput(),
            "item": forms.HiddenInput(),
            "goods_received_note_item": forms.HiddenInput(),
            "uom": forms.HiddenInput(),
            "batch_no": forms.HiddenInput(),
            "expiry_date": forms.HiddenInput(),
        }

    def __init__(self, *args, **kwargs):
        self.goods_received_note_item = kwargs.pop('goods_received_note_item', None)
        super().__init__(*args, **kwargs)

        if self.goods_received_note_item:
            # Set initial values based on the GRN item
            remaining_quantity = (
                self.goods_received_note_item.quantity -
                self.goods_received_note_item.get_received_quantity
            )

            # Set initial receive quantity to remaining quantity if positive
            if remaining_quantity > 0:
                self.fields['receive_quantity'].initial = remaining_quantity

    def clean_receive_quantity(self):
        receive_quantity = self.cleaned_data.get('receive_quantity')

        if receive_quantity is None:
            raise forms.ValidationError(_("Receive quantity is required."))

        if receive_quantity <= 0:
            raise forms.ValidationError(_("Receive quantity must be greater than 0."))

        if self.goods_received_note_item:
            # Check if receive quantity exceeds remaining quantity
            remaining_quantity = (
                self.goods_received_note_item.quantity -
                self.goods_received_note_item.get_received_quantity
            )

            if receive_quantity > remaining_quantity:
                raise forms.ValidationError(
                    _("Receive quantity (%(receive_qty)s) cannot exceed remaining quantity (%(remaining_qty)s).") % {
                        'receive_qty': receive_quantity,
                        'remaining_qty': remaining_quantity
                    }
                )

        return receive_quantity


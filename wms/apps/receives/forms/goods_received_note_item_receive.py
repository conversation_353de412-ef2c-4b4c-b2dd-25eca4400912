from django import forms
from django.db.models import BLANK_CHOICE_DASH
from django.utils.translation import gettext_lazy as _

from wms.cores.forms.fields import CoreModelForm, CoreModelChoiceField, FormFieldSize
from wms.cores.forms.widget import CoreNumberWidget
from wms.cores.utils import uom_choices_symbol

from wms.apps.receives.models import GoodsReceivedNoteItem, GoodsReceivedNoteStockIn
from wms.apps.settings.models import Warehouse
from decimal import Decimal
from wms.cores.utils import format_decimal_values


class GoodsReceivedNoteStockInForm(CoreModelForm):
    """Form to create GoodsReceivedNoteStockIn."""


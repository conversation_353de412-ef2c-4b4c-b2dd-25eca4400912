# Generated by Django 5.1 on 2025-03-15 13:12

import django.core.validators
import django.db.models.deletion
import django.utils.timezone
import model_utils.fields
import wms.apps.receives.models.goods_received_note
import wms.cores.models
import wms.cores.utils
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('cores', '0001_create_extensions'),
        ('consignors', '0002_initial'),
        ('inventories', '0001_initial'),
        ('settings', '0002_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='GoodsReceivedNote',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('system_number', models.CharField(blank=True, max_length=32, verbose_name='System Number')),
                ('arrival_datetime', models.DateTimeField(default=wms.cores.utils.localtime_now, verbose_name='Arrival Date Time')),
                ('completion_datetime', models.DateTimeField(blank=True, default=None, null=True, verbose_name='Completion Date Time')),
                ('status', models.CharField(choices=[('New', 'New'), ('Partially Received', 'Partially Received'), ('Obsolete', 'Obsolete'), ('Completed', 'Completed')], default='New', max_length=32, verbose_name='Status')),
                ('customer_reference', models.CharField(blank=True, max_length=64, verbose_name='Customer Reference')),
                ('remark', models.TextField(blank=True, max_length=512, verbose_name='Remark')),
                ('imported_file', models.FileField(blank=True, null=True, upload_to='goods_received_notes', validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['txt', 'xml'])], verbose_name='Imported File')),
                ('container_date', models.DateField(blank=True, null=True, verbose_name='Container Date')),
                ('container_number', models.CharField(blank=True, max_length=64, verbose_name='Container Number')),
                ('container_size', models.CharField(blank=True, max_length=64, verbose_name='Container Size')),
                ('container_seal_number', models.CharField(blank=True, max_length=64, verbose_name='Container Seal Number')),
                ('container_no_of_pallet', models.IntegerField(blank=True, null=True, verbose_name='No. Of Pallet')),
                ('container_no_of_loose_carton', models.IntegerField(blank=True, null=True, verbose_name='No. Of Loose Carton')),
                ('container_length', models.DecimalField(blank=True, decimal_places=6, max_digits=19, null=True, verbose_name='Dimension (Length)')),
                ('container_width', models.DecimalField(blank=True, decimal_places=6, max_digits=19, null=True, verbose_name='Dimension (Width)')),
                ('container_height', models.DecimalField(blank=True, decimal_places=6, max_digits=19, null=True, verbose_name='Dimension (Height)')),
                ('container_dimension_unit', models.CharField(choices=[('m', 'm')], default='m', max_length=2, verbose_name='Dimension Unit')),
                ('container_cubicmeter', models.DecimalField(blank=True, decimal_places=6, max_digits=19, null=True, verbose_name='M3')),
                ('container_gross_weight_kg', models.DecimalField(blank=True, decimal_places=6, max_digits=19, null=True, verbose_name='Gross weight / pallet (KG)')),
                ('container_gross_weight_tonn', models.DecimalField(blank=True, decimal_places=6, max_digits=19, null=True, verbose_name='Gross weight (T)')),
                ('is_edi_confirmation_sent', models.BooleanField(default=False, verbose_name='Is EDI Confirmation Sent?')),
                ('is_from_edi', models.BooleanField(default=False, help_text='To differentiate it is manually created or from EDI', verbose_name='Is From EDI?')),
                ('consignor_inbound_delivery_no', models.CharField(blank=True, max_length=256, verbose_name='Inbound Delivery No')),
                ('consignor', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='consignors.consignor')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('deliver_to', models.ForeignKey(limit_choices_to={'is_storage': True}, on_delete=django.db.models.deletion.PROTECT, related_name='goods_received_notes', to='settings.warehouse')),
                ('issued_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Modified by')),
            ],
            options={
                'ordering': ['-system_number'],
                'get_latest_by': 'created',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='GoodsReceivedNoteItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('sort_order', models.PositiveIntegerField(default=0, verbose_name='Ordering')),
                ('quantity', models.DecimalField(decimal_places=6, default=Decimal('0'), max_digits=19, validators=[wms.cores.models.greater_than_zero], verbose_name='Quantity')),
                ('batch_no', models.CharField(blank=True, max_length=255, verbose_name='Batch No.')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='Expiry Date')),
                ('shipment_number', models.CharField(blank=True, max_length=64, verbose_name='Shipment Number')),
                ('pallet_number', models.CharField(blank=True, max_length=64, verbose_name='Pallet Number')),
                ('remark', models.TextField(blank=True, max_length=512, verbose_name='Remark')),
                ('status', models.CharField(choices=[('Open', 'Open'), ('Received (Partially)', 'Received (Partially)'), ('Received', 'Received')], default='Open', max_length=32, verbose_name='Status')),
                ('is_serial_no', models.BooleanField(default=False, verbose_name='Is Serial Number?')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('goods_received_note', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='receives.goodsreceivednote')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventories.item')),
                ('modified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Modified by')),
                ('uom', models.ForeignKey(help_text='The item will be measured in terms of this unit (e.g.: kg, pcs, box).', on_delete=django.db.models.deletion.RESTRICT, to='settings.unitofmeasure', verbose_name='UOM')),
            ],
            options={
                'ordering': ['sort_order'],
                'get_latest_by': 'created',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='GoodsReceivedNoteDefectStockIn',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('batch_no', models.CharField(default='N/A', max_length=255, verbose_name='Batch No.')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='Expiry Date')),
                ('stock_in_datetime', models.DateTimeField(verbose_name='Stock In Date Time')),
                ('approved_quantity', models.DecimalField(decimal_places=6, default=Decimal('0'), max_digits=19, verbose_name='Approved Quantity')),
                ('remark', models.TextField(blank=True, max_length=512, verbose_name='Remark')),
                ('reason', models.CharField(choices=[('DAMAGED', 'Damaged'), ('DENTED', 'Dented'), ('EXPIRED', 'Expired'), ('NOTRECEIVE', 'Not Receive'), ('OTHERS', 'Others')], max_length=10, verbose_name='Reason')),
                ('unit_price', models.DecimalField(decimal_places=4, default=Decimal('0'), max_digits=19, verbose_name='Cost Price')),
                ('total_price', models.DecimalField(decimal_places=4, default=Decimal('0'), help_text='Total Price = Quantity * unit_price.', max_digits=19, verbose_name='Total Price')),
                ('converted_quantity', models.DecimalField(decimal_places=6, default=Decimal('0'), max_digits=19, verbose_name='Converted Quantity')),
                ('approved_by', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('converted_uom', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='defect_converted_uoms', to='settings.unitofmeasure', verbose_name='Converted UOM')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('deliver_to', models.ForeignKey(limit_choices_to={'is_storage': True}, on_delete=django.db.models.deletion.PROTECT, to='settings.warehouse')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='inventories.item')),
                ('modified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Modified by')),
                ('transaction', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='inventories.transaction')),
                ('uom', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='settings.unitofmeasure', verbose_name='UOM')),
                ('goods_received_note_item', models.ForeignKey(help_text="Each Receive Note Item indicate a relation to item's delievered quantity.", on_delete=django.db.models.deletion.PROTECT, to='receives.goodsreceivednoteitem')),
            ],
            options={
                'ordering': ['stock_in_datetime', 'pk'],
                'get_latest_by': 'created',
                'abstract': False,
            },
            bases=(wms.apps.receives.models.goods_received_note.StockInRejectMixin, models.Model),
        ),
        migrations.CreateModel(
            name='GoodsReceivedNoteDefect',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('reason', models.CharField(choices=[('DAMAGED', 'Damaged'), ('DENTED', 'Dented'), ('EXPIRED', 'Expired'), ('NOTRECEIVE', 'Not Receive'), ('OTHERS', 'Others')], max_length=10, verbose_name='Reason')),
                ('rejected_quantity', models.DecimalField(decimal_places=6, default=Decimal('0'), max_digits=19, verbose_name='Rejected Quantity')),
                ('converted_rejected_quantity', models.DecimalField(decimal_places=6, default=Decimal('0'), max_digits=19, verbose_name='Converted Quantity')),
                ('batch_no', models.CharField(default='N/A', max_length=255, verbose_name='Batch No.')),
                ('defect_datetime', models.DateTimeField(verbose_name='Defect Date Time')),
                ('remark', models.TextField(blank=True, max_length=512, verbose_name='Remark')),
                ('is_migrated', models.BooleanField(default=False, help_text='Designates whether it has been migrated into GoodsReceivedNoteDefectStockIn with Transaction.', verbose_name='Is Migrated?')),
                ('converted_rejected_uom', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='converted_rejected_uoms', to='settings.unitofmeasure', verbose_name='Converted UOM')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('deliver_to', models.ForeignKey(limit_choices_to={'is_storage': True}, on_delete=django.db.models.deletion.PROTECT, to='settings.warehouse')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='inventories.item')),
                ('modified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Modified by')),
                ('rejected_by', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('uom', models.ForeignKey(help_text='UOM for rejected item. Limited to EA for now.', on_delete=django.db.models.deletion.RESTRICT, related_name='uoms', to='settings.unitofmeasure', verbose_name='UOM')),
                ('goods_received_note_item', models.ForeignKey(help_text="Each Receive Note Item indicate a relation to item's rejected quantity.", on_delete=django.db.models.deletion.PROTECT, to='receives.goodsreceivednoteitem')),
            ],
            options={
                'ordering': ['defect_datetime', 'pk'],
                'get_latest_by': 'created',
                'abstract': False,
            },
            bases=(wms.apps.receives.models.goods_received_note.StockInRejectMixin, models.Model),
        ),
        migrations.CreateModel(
            name='GoodsReceivedNoteStockIn',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('batch_no', models.CharField(default='N/A', max_length=255, verbose_name='Batch No.')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='Expiry Date')),
                ('stock_in_datetime', models.DateTimeField(verbose_name='Stock In Date Time')),
                ('approved_quantity', models.DecimalField(decimal_places=6, default=Decimal('0'), max_digits=19, verbose_name='Approved Quantity')),
                ('remark', models.TextField(blank=True, max_length=512, verbose_name='Remark')),
                ('unit_price', models.DecimalField(decimal_places=4, default=Decimal('0'), max_digits=19, verbose_name='Cost Price')),
                ('total_price', models.DecimalField(decimal_places=4, default=Decimal('0'), help_text='Total Price = Quantity * unit_price.', max_digits=19, verbose_name='Total Price')),
                ('converted_quantity', models.DecimalField(decimal_places=6, default=Decimal('0'), max_digits=19, verbose_name='Converted Quantity')),
                ('approved_by', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('converted_uom', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='converted_uoms', to='settings.unitofmeasure', verbose_name='Converted UOM')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('deliver_to', models.ForeignKey(limit_choices_to={'is_storage': True}, on_delete=django.db.models.deletion.PROTECT, to='settings.warehouse')),
                ('goods_received_note_item', models.ForeignKey(help_text="Each Receive Note Item indicate a relation to item's delievered quantity.", on_delete=django.db.models.deletion.PROTECT, to='receives.goodsreceivednoteitem')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='inventories.item')),
                ('modified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Modified by')),
                ('transaction', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='inventories.transaction')),
                ('uom', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='settings.unitofmeasure', verbose_name='UOM')),
            ],
            options={
                'ordering': ['stock_in_datetime', 'pk'],
                'get_latest_by': 'created',
                'abstract': False,
            },
            bases=(wms.apps.receives.models.goods_received_note.StockInRejectMixin, models.Model),
        ),
    ]

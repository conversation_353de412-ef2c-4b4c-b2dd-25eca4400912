import logging

from django.db.models.signals import post_save, post_delete, pre_save
from django.dispatch import receiver

from wms.cores.actstream import register_stream

from .models import (
    GoodsReceivedNote,
    GoodsReceivedNoteDefect,
    GoodsReceivedNoteItem,
    GoodsReceivedNoteStockIn,
)

logger = logging.getLogger(__name__)


#################
# FOR ACTSTREAM #
#################

register_stream(GoodsReceivedNote, logger=logger)
register_stream(GoodsReceivedNoteItem, logger=logger, parent_field="goods_received_note")
register_stream(GoodsReceivedNoteStockIn, logger=logger)
register_stream(GoodsReceivedNoteDefect, logger=logger)


###################
# FOR APP SIGNALS #
###################


@receiver(post_save, sender=GoodsReceivedNote)
def generate_system_number(sender, instance, created=False, **kwargs):
    """To generate system number on instance."""
    if created:
        instance.generate_system_number()
        logger.info(f"SIGNAL: {sender.__name__}: generated system number for {instance}")


@receiver(post_delete, sender=GoodsReceivedNote)
def auto_delete_imported_file_on_delete(sender, instance, **kwargs):
    """Deletes imported_file from filesystem when GoodsReceivedNote is deleted."""
    if instance.imported_file:
        instance.imported_file.delete(save=False)


@receiver(pre_save, sender=GoodsReceivedNote)
def auto_delete_imported_file_on_change(sender, instance, **kwargs):
    """Deletes old imported_file from filesystem when GoodsReceivedNote is updated with new imported_file."""
    if not instance.pk:
        return False

    try:
        old_file = sender.objects.get(pk=instance.pk).imported_file
    except sender.DoesNotExist:
        return False

    new_file = instance.imported_file
    if not old_file == new_file:
        try:
            old_file.delete(save=False)
        except Exception:
            return False

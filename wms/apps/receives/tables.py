# from django.urls import reverse
# from django.utils.translation import gettext_lazy as _

# import django_tables2 as tables
# from actstream.models import Action

# from wss.cores.models import DISPLAY_EMPTY_VALUE
# from wss.cores.tables import (
#     HTMX_LIST_ATTRS_CLASS,
#     HTMX_LIST_SM_ATTRS_CLASS,
#     PARTIAL_LIST_ATTRS_CLASS,
#     TABLE_ATTRS_CLASS,
#     AbstractHistoryDataTables,
# )
# from wss.cores.utils import convert_camel_case_to_space

# from .models import GoodsReceivedNote, GoodsReceivedNoteDefectStockIn


# class GoodsReceivedNoteDataTables(tables.Table):
#     """Table used on goods_received_note list page."""

#     numbering = tables.Column(verbose_name=_("Numbering"), linkify=True)
#     arrival_datetime = tables.DateTimeColumn(
#         verbose_name=_("Arrival Date"),
#         accessor="arrival_datetime",
#         format="Y-m-d",
#     )
#     created = tables.DateTimeColumn(
#         verbose_name=_("Created Date"),
#         accessor="created",
#         format="Y-m-d",
#     )
#     actions = tables.TemplateColumn(
#         verbose_name=_("Actions"),
#         orderable=False,
#         template_name="receives/goods_received_notes/partials/tables/_actions.html",
#         attrs={"th": {"class": "text-right"}, "td": {"class": "text-right"}},
#         exclude_from_export=True,
#     )

#     class Meta:
#         model = GoodsReceivedNote
#         order_by = "numbering"  # Must follow the first row display in the table
#         default = DISPLAY_EMPTY_VALUE
#         template_name = "partials/tables/_datatables_server_side.html"
#         attrs = {"class": TABLE_ATTRS_CLASS, "id": "goods_received_note_datatables", "style": "display: none;"}
#         row_attrs = {"data-id": lambda record: record.pk}

#         fields = [
#             "numbering",
#             "consignor",
#             "customer_reference",
#             "status",
#             "remark",
#             "issued_by",
#             "deliver_to",
#             "arrival_datetime",
#             "created",
#         ]
#         sequence = [
#             "numbering",
#             "consignor",
#             "customer_reference",
#             "status",
#             "remark",
#             "issued_by",
#             "deliver_to",
#             "arrival_datetime",
#             "created",
#         ]

#     def render_status(self, value, record):
#         """Return nice html label display for status."""
#         return record.html_status_display

#     def value_status(self, value, record):
#         """
#         The django-tables2 is to render the status's cell value
#         into TableExport without html label display.
#         """
#         return value

#     def render_actions(self, column, record, table, value, bound_column, bound_row):
#         """Add permission checking into actions column."""
#         change_perms = self.request.user.has_perm("receives.change_goods_received_note")
#         delete_perms = self.request.user.has_perm("receives.delete_goodsreceivednote")
#         superadmin_group_perms = (
#             self.request.user.is_superuser or self.request.user.groups.filter(name="Superadmin").exists()
#         )
#         column.extra_context = {
#             "change_perms": change_perms,
#             "delete_perms": delete_perms,
#             "superadmin_group_perms": superadmin_group_perms,
#         }
#         return column.render(record, table, value, bound_column, **{"bound_row": bound_row})


# class GoodsReceivedNoteDetailDataTables(tables.Table):
#     """Table used on goods_received_note detail page."""

#     numbering = tables.Column(verbose_name=_("Numbering"), linkify=True)

#     class Meta:
#         model = GoodsReceivedNote
#         order_by = "-numbering"  # Must follow the first row display in the table
#         default = DISPLAY_EMPTY_VALUE
#         template_name = "partials/tables/_datatables_server_side.html"
#         attrs = {"class": PARTIAL_LIST_ATTRS_CLASS, "id": "goods_received_note_datatables", "style": "display: none;"}
#         row_attrs = {"data-id": lambda record: record.pk}

#         fields = [
#             "pk",
#             "numbering",
#             "status",
#             "consignor",
#         ]
#         sequence = [
#             "pk",
#             "numbering",
#             "status",
#             "consignor",
#         ]

#     def render_pk(self, value, record):
#         return f"highlight_id_{value}"

#     def render_status(self, value, record):
#         """Return nice html label display for status."""
#         return record.html_status_display


# class GoodsReceivedNoteDefectDataTables(tables.Table):
#     """Table used on goods_received_note defect list page."""

#     item_code = tables.Column(
#         verbose_name=_("CODE"),
#         accessor="goods_received_note_item__item",
#     )
#     item_name = tables.Column(
#         verbose_name=_("NAME"),
#         accessor="goods_received_note_item__item",
#     )
#     reason = tables.Column(verbose_name=_("REASON"))
#     approved_quantity = tables.Column(
#         verbose_name=_("REJ"),
#         attrs={
#             "td": {"class": "text-right"},
#             "th": {"class": "table-tooltip", "data-toggle": "tooltip", "data-placement": "top", "title": _("Rejected")},
#         },
#     )
#     remark = tables.Column(verbose_name=_("REMARK"))
#     stock_in_datetime = tables.Column(verbose_name=_("DATE TIME"))
#     approved_by = tables.Column(
#         verbose_name=_("REJ BY"),
#         attrs={
#             "th": {
#                 "class": "table-tooltip",
#                 "data-toggle": "tooltip",
#                 "data-placement": "top",
#                 "title": _("Rejected By"),
#             }
#         },
#     )

#     class Meta:
#         model = GoodsReceivedNoteDefectStockIn
#         default = DISPLAY_EMPTY_VALUE
#         # Need to override table footer
#         template_name = "receives/goods_received_notes/partials/tables/_defect_compose_datatables.html"
#         attrs = {
#             "class": HTMX_LIST_ATTRS_CLASS,
#             "id": "goods_received_note_defect_datatables",
#             "style": "display: none;",
#             "thead": {"class": "bg-danger"},
#         }
#         row_attrs = {"data-id": lambda record: record.pk}

#         fields = [
#             "reason",
#             "approved_quantity",
#             "remark",
#             "stock_in_datetime",
#             "approved_by",
#         ]
#         sequence = [
#             "item_code",
#             "item_name",
#             "reason",
#             "approved_quantity",
#             "remark",
#             "stock_in_datetime",
#             "approved_by",
#         ]

#     def render_item_code(self, value, record):
#         return f"{value.code}"

#     def render_item_name(self, value, record):
#         return f"{value.name}"

#     def render_approved_quantity(self, value, record):
#         return f"{round(value, record.uom.unit_precision)} {record.uom.symbol}"


# class GoodsReceivedNoteHistoryDataTables(AbstractHistoryDataTables):
#     """Table used on goods_received_note History tab."""

#     class Meta(AbstractHistoryDataTables.Meta):
#         attrs = {
#             "class": HTMX_LIST_SM_ATTRS_CLASS,
#             "id": "goods_received_note_history_datatables",
#             "style": "display: none;",
#             "thead": {"class": "bg-secondary"},
#         }

#     def render_verb(self, value: str, record: Action) -> str:
#         if value == "created GoodsReceivedNoteStockIn":
#             return "Approved Goods Received Note Item"

#         value_string = convert_camel_case_to_space(value)
#         value_string = value_string[0].upper() + value_string[1:]

#         if value.startswith("modified"):
#             link = reverse("receives:goods_received_notes:history-modified", kwargs={"pk": record.pk})
#             htmx_modal_attributes = (
#                 'href="#" style="text-decoration: underline dotted" '
#                 'data-toggle="modal" data-target="#modalXl" '
#                 'hx-target="#modalXlBody" hx-swap="innerHTML"'
#             )
#             return f'<a {htmx_modal_attributes} hx-get="{link}">{value_string}</a>'
#         else:
#             return value_string


# class RackingGoodsReceivedNoteDataTables(tables.Table):
#     """Table used on Goods Received Note > Racking list page."""

#     numbering = tables.Column(verbose_name=_("Numbering"))
#     arrival_datetime = tables.DateTimeColumn(
#         verbose_name=_("Arrival Date"),
#         accessor="arrival_datetime",
#         format="Y-m-d",
#     )
#     racking_progress = tables.Column(
#         verbose_name=_("Progress"),
#         accessor="html_racking_progress_display",
#         orderable=False,
#     )
#     created = tables.DateTimeColumn(
#         verbose_name=_("Created Date"),
#         accessor="created",
#         format="Y-m-d",
#     )
#     actions = tables.TemplateColumn(
#         verbose_name=_("Actions"),
#         orderable=False,
#         template_name="receives/rackings/partials/tables/_actions.html",
#         attrs={"th": {"class": "text-right"}, "td": {"class": "text-right"}},
#         exclude_from_export=True,
#     )

#     class Meta:
#         model = GoodsReceivedNote
#         order_by = "numbering"  # Must follow the first row display in the table
#         default = DISPLAY_EMPTY_VALUE
#         template_name = "partials/tables/_datatables_server_side.html"
#         attrs = {"class": TABLE_ATTRS_CLASS, "id": "racking_goods_received_note_datatables", "style": "display: none;"}
#         row_attrs = {"data-id": lambda record: record.pk}

#         fields = [
#             "numbering",
#             "consignor",
#             "customer_reference",
#             "remark",
#             "issued_by",
#             "deliver_to",
#             "arrival_datetime",
#             "created",
#         ]
#         sequence = [
#             "numbering",
#             "consignor",
#             "customer_reference",
#             "racking_progress",
#             "remark",
#             "issued_by",
#             "deliver_to",
#             "arrival_datetime",
#             "created",
#         ]

#     def render_numbering(self, value: str, record: GoodsReceivedNote) -> str:
#         link = reverse(
#             "receives:racking_goods_received_notes:detail",
#             kwargs={"pk": record.pk},
#         )
#         return f'<a href="{link}">{value}</a>'

#     def value_status(self, value, record):
#         """
#         The django-tables2 is to render the status's cell value
#         into TableExport without html label display.
#         """
#         return value


# class RackingGoodsReceivedNoteDetailDataTables(tables.Table):
#     """Table used on Goods Received Note > Racking detail page."""

#     numbering = tables.Column(verbose_name=_("GRN"))
#     racking_progress = tables.Column(
#         verbose_name=_("Progress"),
#         accessor="html_racking_progress_display",
#         orderable=False,
#     )

#     class Meta:
#         model = GoodsReceivedNote
#         order_by = "-numbering"  # Must follow the first row display in the table
#         default = DISPLAY_EMPTY_VALUE
#         template_name = "partials/tables/_datatables_server_side.html"
#         attrs = {
#             "class": PARTIAL_LIST_ATTRS_CLASS,
#             "id": "racking_goods_received_note_datatables",
#             "style": "display: none;",
#         }
#         row_attrs = {"data-id": lambda record: record.pk}

#         fields = [
#             "pk",
#             "numbering",
#         ]
#         sequence = [
#             "pk",
#             "numbering",
#             "racking_progress",
#         ]

#     def render_numbering(self, value: str, record: GoodsReceivedNote) -> str:
#         link = reverse(
#             "receives:racking_goods_received_notes:detail",
#             kwargs={"pk": record.pk},
#         )
#         return f'<a href="{link}">{value}</a>'

#     def render_pk(self, value, record: GoodsReceivedNote):
#         return f"highlight_id_{value}"

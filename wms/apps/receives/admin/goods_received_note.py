from django.contrib import admin

from wms.cores.admin import BaseModelAdmin, BaseTabularInline

from ..models import (
    GoodsReceivedNote,
    GoodsReceivedNoteDefect,
    GoodsReceivedNoteDefectStockIn,
    GoodsReceivedNoteItem,
    GoodsReceivedNoteStockIn,
)


class GoodsReceivedNoteItemInline(BaseTabularInline):
    model = GoodsReceivedNoteItem
    extra = 3

    raw_id_fields = ("item",)


@admin.register(GoodsReceivedNote)
class GoodsReceivedNoteAdmin(BaseModelAdmin):
    """Django admin for GoodsReceivedNote."""

    list_display = [
        "system_number",
        "issued_by",
        "consignor",
        "arrival_datetime",
        "deliver_to",
        "imported_file",
        "status",
        "completion_datetime",
        "is_edi_confirmation_sent",
        "is_from_edi",
        # "available_for_racking",
        # "is_racking_completed",
        "remark",
    ]
    inlines = [
        GoodsReceivedNoteItemInline,
    ]
    search_fields = ["system_number", "consignor__display_name"]
    list_filter = ["status"]


@admin.register(GoodsReceivedNoteItem)
class GoodsReceivedNoteItemAdmin(BaseModelAdmin):
    list_display = [
        "item",
        "quantity",
        "uom",
        "batch_no",
        "goods_received_note",
        "status",
        "is_serial_no",
    ]
    search_fields = [
        "item__code",
        "item__name",
        "goods_received_note__system_number",
    ]
    list_filter = [
        "goods_received_note",
        "item",
        "status",
    ]


@admin.register(GoodsReceivedNoteStockIn)
class GoodsReceivedNoteStockInAdmin(BaseModelAdmin):
    list_display = [
        "deliver_to",
        "approved_by",
        "approved_quantity",
        "batch_no",
        "stock_in_datetime",
        "item",
        "uom",
        "converted_quantity",
        "converted_uom",
        "remark",
        "transaction",
        "goods_received_note_item",
        "unit_price",
        "total_price",
    ]
    search_fields = (
        "goods_received_note_item__goods_received_note__system_number",
        "deliver_to__name",
        "item__name",
    )
    list_filter = [
        "item",
    ]
    date_hierarchy = "stock_in_datetime"
    raw_id_fields = (
        "transaction",
        "goods_received_note_item",
    )

    def get_readonly_fields(self, request, obj=None):
        if obj:  # obj is not None, so this is an edit
            return [
                "approved_by",
                "approved_quantity",
                "batch_no",
                "stock_in_datetime",
                "item",
                "uom",
                "goods_received_note_item",
                "converted_quantity",
                "converted_uom",
            ]  # Return a list or tuple of readonly fields' names
        else:  # This is an addition
            return []


@admin.register(GoodsReceivedNoteDefect)
class GoodsReceivedNoteDefectAdmin(BaseModelAdmin):
    list_display = [
        "reason",
        "goods_received_note_item",
        "deliver_to",
        "rejected_by",
        "rejected_quantity",
        "batch_no",
        "defect_datetime",
        "item",
        "uom",
        "converted_rejected_quantity",
        "converted_rejected_uom",
        "remark",
        "is_migrated",
    ]
    search_fields = (
        "goods_received_note_item__goods_received_note__system_number",
        "deliver_to__name",
        "item__name",
    )
    list_filter = [
        "item",
        "is_migrated",
    ]
    date_hierarchy = "defect_datetime"
    raw_id_fields = ("goods_received_note_item",)

    def get_readonly_fields(self, request, obj=None):
        if obj:  # obj is not None, so this is an edit
            return [
                "goods_received_note_item",
                "rejected_by",
                "rejected_quantity",
                "batch_no",
                "defect_datetime",
                "item",
                "uom",
                "converted_rejected_quantity",
                "converted_rejected_uom",
            ]  # Return a list or tuple of readonly fields' names
        else:  # This is an addition
            return []


@admin.register(GoodsReceivedNoteDefectStockIn)
class GoodsReceivedNoteDefectStockInAdmin(BaseModelAdmin):
    list_display = [
        "deliver_to",
        "reason",
        "approved_by",
        "approved_quantity",
        "batch_no",
        "stock_in_datetime",
        "item",
        "uom",
        "converted_quantity",
        "converted_uom",
        "remark",
        "transaction",
        "goods_received_note_item",
        "unit_price",
        "total_price",
    ]
    search_fields = (
        "goods_received_note_item__goods_received_note__system_number",
        "deliver_to__name",
        "item__name",
    )
    list_filter = [
        "item",
        "goods_received_note_item__goods_received_note__consignor",
    ]
    date_hierarchy = "stock_in_datetime"
    raw_id_fields = (
        "transaction",
        "goods_received_note_item",
    )

    def get_readonly_fields(self, request, obj=None):
        if obj:  # obj is not None, so this is an edit
            return [
                "approved_by",
                "approved_quantity",
                "batch_no",
                "stock_in_datetime",
                "item",
                "uom",
                "goods_received_note_item",
                "converted_quantity",
                "converted_uom",
            ]  # Return a list or tuple of readonly fields' names
        else:  # This is an addition
            return []

# from decimal import Decimal

# from django import forms
# from django.core.exceptions import ValidationError
# from django.utils.translation import gettext_lazy as _

# from wss.cores.forms import CoreForm, CoreHtmxModelForm, CoreModelForm
# from wss.cores.forms.mixins import FormUOMSymbolMixin
# from wss.cores.utils import get_all_leaf_rack_choices, uom_choices_symbol

# from wss.apps.consignors.models import Consignor
# from wss.apps.inventories.models import Item
# from wss.apps.rackings.models.rack import Rack, RackStorage, RackTransaction
# from wss.apps.receives.imports.mitsubishi import ImportMitsubishi
# from wss.apps.settings.models import UnitOfMeasure, Warehouse

# from .models import (
#     GoodsReceivedNote,
#     GoodsReceivedNoteDefect,
#     GoodsReceivedNoteDefectStockIn,
#     GoodsReceivedNoteItem,
#     GoodsReceivedNoteStockIn,
# )


# class GoodsReceivedNoteItemForm(CoreModelForm):
#     """GRN item form."""

#     item = forms.CharField(widget=forms.Select())
#     consignor_pk = forms.CharField(required=False, widget=forms.HiddenInput())

#     def __init__(self, *args, **kwargs):
#         super().__init__(*args, **kwargs)

#         try:
#             ea_uom = UnitOfMeasure.objects.get(symbol="EA")
#         except Exception:
#             pass
#         else:
#             self.fields["uom"].initial = ea_uom

#         self.fields["uom"].choices = uom_choices_symbol()
#         self.fields["item"].widget.attrs["class"] = "form-control core-select2 item-of-consignor"

#     def clean_item(self):
#         pk = self.cleaned_data["item"]
#         try:
#             data = Item.objects.get(pk=pk)
#         except Item.DoesNotExist:
#             raise ValidationError("Selected Item does not exist.")
#         return data

#     def clean(self):
#         """Check that if selected inline item is belongs to parent form's consignor."""
#         cleaned_data = super().clean()

#         consignor_pk = cleaned_data.get("consignor_pk")
#         item = cleaned_data.get("item")
#         consignor = Consignor.objects.get(pk=consignor_pk)

#         if item and str(item.consignor.pk) != consignor_pk:
#             msg = _(f"The selected item does not belongs to consignor {consignor}.")
#             self.add_error("item", msg)


# GoodsReceivedNoteItemInlineFormSet = forms.inlineformset_factory(
#     GoodsReceivedNote,
#     GoodsReceivedNoteItem,
#     form=GoodsReceivedNoteItemForm,
#     fields=("item", "quantity", "batch_no", "expiry_date", "uom"),
#     extra=1,
# )


# class GoodsReceivedNoteForm(CoreModelForm):
#     """Form to create GoodsReceivedNote."""

#     class Meta:
#         model = GoodsReceivedNote
#         fields = [
#             "consignor",
#             "issued_by",
#             "deliver_to",
#             "arrival_datetime",
#             "customer_reference",
#             "remark",
#         ]
#         widgets = {
#             "remark": forms.Textarea(attrs={"cols": 80, "rows": 5}),
#         }

#     def __init__(self, *args, **kwargs):
#         warehouse_release_order = kwargs.pop("warehouse_release_order", None)
#         super().__init__(*args, **kwargs)

#         self.fields["issued_by"].disabled = True
#         self.fields["consignor"].widget.attrs["class"] = "form-control core-select2 update-items"

#         if warehouse_release_order:
#             self.fields["deliver_to"].queryset = warehouse_release_order.warehouses
#         elif self.request.user.is_superuser is True:
#             self.fields["deliver_to"].queryset = Warehouse.objects.filter(is_storage=True)
#         else:
#             user_warehouses = self.request.user.warehouses.all()
#             self.fields["deliver_to"].queryset = Warehouse.objects.filter(pk__in=user_warehouses, is_storage=True)

#         instance = getattr(self, "instance", None)
#         if instance and instance.status != GoodsReceivedNote.Status.NEW:
#             self.fields["consignor"].disabled = True
#             self.fields["deliver_to"].disabled = True
#             self.fields["customer_reference"].disabled = True


# class GoodsReceivedNoteImportStep1Form(CoreForm):
#     """Form to import GoodsReceivedNote. Step 1"""

#     deliver_to = forms.ChoiceField()
#     imported_file = forms.FileField(required=True)

#     def __init__(self, *args, **kwargs):
#         """Populating the choices of  the favorite_choices field using the favorites_choices kwargs"""

#         qs = Warehouse.objects.filter(is_storage=True)
#         warehouse_choices = []

#         for warehouse in qs:
#             warehouse_choices.append([warehouse.pk, warehouse.full_name])

#         super().__init__(*args, **kwargs)

#         self.fields["deliver_to"].choices = warehouse_choices

#     def clean_deliver_to(self):
#         pk = self.cleaned_data["deliver_to"]
#         try:
#             data = Warehouse.objects.get(pk=pk)
#         except Warehouse.DoesNotExist:
#             raise ValidationError("Warehouse does not exist.")
#         return data

#     def clean_imported_file(self):
#         data = self.cleaned_data["imported_file"]

#         self.imported_object = ImportMitsubishi(data)

#         is_valid = self.imported_object.is_valid()
#         if is_valid is False:
#             raise ValidationError(self.imported_object.error_messages_list)
#         else:
#             # To build cleaned data in import class
#             self.imported_object.cleaned()

#         return data


# class GoodsReceivedNoteImportStep2Form(CoreForm):
#     """Form to import GoodsReceivedNote. Step 2."""

#     customer_reference = forms.CharField(label=_("Customer Reference"), required=False)
#     arrival_datetime = forms.DateTimeField(label=_("Arrival Date Time"))


# class GoodsReceivedNoteItemNewImportForm(CoreForm):
#     """Use as formset in wizard."""

#     ACTION_CHOICES = [
#         ["new", _("Create as NEW")],
#         ["existing", _("Replace EXISTING")],
#         ["skip", _("SKIP")],
#     ]

#     running_number = forms.CharField(required=True)
#     item_code = forms.CharField(required=False, help_text=_("This will be the new item code."))
#     item_name = forms.CharField(required=False, help_text=_("This will be the new item name."))
#     item = forms.ModelChoiceField(
#         required=False, queryset=Item.objects.all(), blank=True, help_text=_("Please choose existing item.")
#     )
#     quantity = forms.DecimalField(required=True)
#     uom = forms.ModelChoiceField(label=_("UOM"), required=True, queryset=UnitOfMeasure.objects.all())
#     shipment_number = forms.CharField(required=True, widget=forms.HiddenInput())
#     pallet_number = forms.CharField(required=True, widget=forms.HiddenInput())
#     actions = forms.ChoiceField(
#         label=_("Actions"),
#         required=True,
#         choices=ACTION_CHOICES,
#         help_text=_("Please choose an action for missing item."),
#     )

#     def __init__(self, item_queryset=None, *args, **kwargs):

#         super().__init__(*args, **kwargs)

#         self.fields["running_number"].widget.attrs["readonly"] = True
#         self.fields["shipment_number"].widget.attrs["readonly"] = True
#         self.fields["pallet_number"].widget.attrs["readonly"] = True

#         if item_queryset:
#             self.fields["item"].queryset = item_queryset


# GoodsReceivedNoteItemNewImportFormSet = forms.formset_factory(GoodsReceivedNoteItemNewImportForm, extra=0)


# class GoodsReceivedNoteItemExistingImportForm(CoreForm):
#     """Use as formset in wizard."""

#     running_number = forms.CharField(required=True)
#     item = forms.ModelChoiceField(required=True, queryset=Item.objects.all())
#     quantity = forms.DecimalField(required=True)
#     uom = forms.ModelChoiceField(label=_("UOM"), required=True, queryset=UnitOfMeasure.objects.all())
#     shipment_number = forms.CharField(required=True, widget=forms.HiddenInput())
#     pallet_number = forms.CharField(required=True, widget=forms.HiddenInput())

#     def __init__(self, item_queryset=None, *args, **kwargs):

#         super().__init__(*args, **kwargs)

#         self.fields["running_number"].widget.attrs["readonly"] = True
#         self.fields["shipment_number"].widget.attrs["readonly"] = True
#         self.fields["pallet_number"].widget.attrs["readonly"] = True

#         if item_queryset:
#             self.fields["item"].queryset = item_queryset


# GoodsReceivedNoteItemExistingImportFormSet = forms.formset_factory(GoodsReceivedNoteItemExistingImportForm, extra=0)


# class GoodsReceivedNoteUpdateStatusMixin(CoreModelForm):
#     """Mixin class to perform update on GoodsReceivedNote status."""

#     class Meta:
#         model = GoodsReceivedNote
#         fields = [
#             "status",
#         ]

#     def __init__(self, *args, **kwargs):
#         super().__init__(*args, **kwargs)

#         self.fields["status"].disabled = True


# class GoodsReceivedNoteObsoleteForm(GoodsReceivedNoteUpdateStatusMixin):
#     """Form to perform Obsolete for GoodsReceivedNote."""

#     pass


# ############
# # FOR HTMX #
# ############


# class GoodsReceivedNoteInfoUpdateForm(CoreHtmxModelForm):
#     """Form to update GoodsReceivedNote's info."""

#     class Meta:
#         model = GoodsReceivedNote
#         fields = [
#             "consignor",
#             "issued_by",
#             "deliver_to",
#             "arrival_datetime",
#             "customer_reference",
#             "remark",
#         ]
#         widgets = {
#             "remark": forms.Textarea(attrs={"cols": 80, "rows": 5}),
#         }

#     def __init__(self, *args, **kwargs):
#         super().__init__(*args, **kwargs)

#         self.fields["issued_by"].disabled = True
#         self.fields["consignor"].disabled = True

#         instance = getattr(self, "instance", None)
#         if instance and instance.status != GoodsReceivedNote.Status.NEW:

#             self.fields["deliver_to"].disabled = True
#             self.fields["customer_reference"].disabled = True


# class GoodsReceivedNoteContainerUpdateForm(CoreHtmxModelForm):
#     """Form to update GoodsReceivedNote's container."""

#     class Meta:
#         model = GoodsReceivedNote
#         fields = [
#             "container_date",
#             "container_number",
#             "container_size",
#             "container_seal_number",
#             "container_no_of_pallet",
#             "container_no_of_loose_carton",
#             "container_length",
#             "container_width",
#             "container_height",
#             "container_dimension_unit",
#             # Comment out as container_cubicmeter is auto calculate
#             # "container_cubicmeter",
#             "container_gross_weight_kg",
#             "container_gross_weight_tonn",
#         ]

#     def __init__(self, *args, **kwargs):
#         super().__init__(*args, **kwargs)

#         # Override label
#         self.fields["container_date"].label = _("Date")
#         self.fields["container_number"].label = _("Number")
#         self.fields["container_size"].label = _("Size")
#         self.fields["container_seal_number"].label = _("Seal Number")
#         self.fields["container_no_of_pallet"].label = _("No. of Pallet")
#         self.fields["container_no_of_loose_carton"].label = _("No. of Loose Carton")


# class GoodsReceivedNoteStockInForm(CoreHtmxModelForm, FormUOMSymbolMixin):
#     """Form to create GoodsReceivedNoteStockIn."""

#     item_code = forms.CharField(label=_("Item Code"))
#     uom = forms.ChoiceField(label=_("UOM"), choices=[])

#     class Meta:
#         model = GoodsReceivedNoteStockIn
#         fields = [
#             "item_code",
#             "approved_quantity",
#             "uom",
#             "batch_no",
#             "expiry_date",
#             "remark",
#             "deliver_to",
#             "approved_by",
#             "stock_in_datetime",
#             "item",
#             "goods_received_note_item",
#         ]
#         widgets = {
#             "remark": forms.Textarea(attrs={"cols": 80, "rows": 9}),
#             "deliver_to": forms.HiddenInput(),
#             "approved_by": forms.HiddenInput(),
#             "stock_in_datetime": forms.HiddenInput(),
#             "item": forms.HiddenInput(),
#             "goods_received_note_item": forms.HiddenInput(),
#         }

#     def __init__(self, *args, **kwargs):
#         super().__init__(*args, **kwargs)

#         # Override label
#         self.fields["approved_quantity"].label = _("Received Quantity")

#         # Add onclick select text
#         self.fields["batch_no"].widget.attrs["onClick"] = "this.select();"

#         item = self.initial.get("item", None)
#         uom = self.initial.get("uom", None)

#         # Override UOM choices
#         self.fields["uom"].choices = uom_choices_symbol(item=item, pre_selected_uom=uom)

#     def clean(self):
#         cleaned_data = super().clean()

#         item_code = cleaned_data["item_code"]
#         item = cleaned_data["item"]

#         if item_code != item.code:
#             msg = _("Item Code does not Match.")
#             self.add_error("item_code", msg)

#     def save(self, commit=True):
#         """
#         Save this form's self.instance object if commit=True.
#         """
#         if self.errors:
#             raise ValueError(
#                 "The %s could not be %s because the data didn't validate."
#                 % (
#                     self.instance._meta.object_name,
#                     "created" if self.instance._state.adding else "changed",
#                 )
#             )
#         if commit:
#             self.instance.save()

#             # expiry_date = self.cleaned_data.get("expiry_date")

#             # Stock.objects.update_expiry_date(
#             #     item=self.instance.item, batch_no=self.instance.batch_no, expiry_date=expiry_date
#             # )

#         return self.instance


# class GoodsReceivedNoteDefectStockInForm(CoreHtmxModelForm, FormUOMSymbolMixin):
#     """Form to create GoodsReceivedNoteDefectStockIn."""

#     item_code = forms.CharField(label=_("Item Code"))
#     uom = forms.ChoiceField(label=_("UOM"), choices=[])

#     class Meta:
#         model = GoodsReceivedNoteDefectStockIn
#         fields = [
#             "item_code",
#             "reason",
#             "approved_quantity",
#             "uom",
#             "batch_no",
#             "expiry_date",
#             "remark",
#             "deliver_to",
#             "approved_by",
#             "stock_in_datetime",
#             "item",
#             "goods_received_note_item",
#         ]
#         widgets = {
#             "remark": forms.Textarea(attrs={"cols": 80, "rows": 9}),
#             "deliver_to": forms.HiddenInput(),
#             "approved_by": forms.HiddenInput(),
#             "stock_in_datetime": forms.HiddenInput(),
#             "item": forms.HiddenInput(),
#             "goods_received_note_item": forms.HiddenInput(),
#         }

#     def __init__(self, *args, **kwargs):
#         super().__init__(*args, **kwargs)

#         # Override label
#         self.fields["approved_quantity"].label = _("Reject Quantity")

#         # Add onclick select text
#         self.fields["batch_no"].widget.attrs["onClick"] = "this.select();"

#         item = self.initial.get("item", None)
#         uom = self.initial.get("uom", None)

#         # Override UOM choices
#         self.fields["uom"].choices = uom_choices_symbol(item=item, pre_selected_uom=uom)

#     def clean(self):
#         cleaned_data = super().clean()

#         item_code = cleaned_data["item_code"]
#         item = cleaned_data["item"]

#         if item_code != item.code:
#             msg = _("Item Code does not Match.")
#             self.add_error("item_code", msg)

#     def save(self, commit=True):
#         """
#         Save this form's self.instance object if commit=True.
#         """
#         if self.errors:
#             raise ValueError(
#                 "The %s could not be %s because the data didn't validate."
#                 % (
#                     self.instance._meta.object_name,
#                     "created" if self.instance._state.adding else "changed",
#                 )
#             )
#         if commit:
#             self.instance.save()

#             # expiry_date = self.cleaned_data.get("expiry_date")

#             # Stock.objects.update_expiry_date(
#             #     item=self.instance.item, batch_no=self.instance.batch_no, expiry_date=expiry_date
#             # )

#         return self.instance


# class GoodsReceivedNoteDefectForm(CoreHtmxModelForm):
#     """Form to create GoodsReceivedNoteDefect."""

#     item_code = forms.CharField(label=_("Item Code"))

#     class Meta:
#         model = GoodsReceivedNoteDefect
#         fields = [
#             "item_code",
#             "reason",
#             "rejected_quantity",
#             "uom",
#             "batch_no",
#             "remark",
#             "deliver_to",
#             "rejected_by",
#             "defect_datetime",
#             "item",
#             "goods_received_note_item",
#         ]
#         widgets = {
#             "remark": forms.Textarea(attrs={"cols": 80, "rows": 9}),
#             "deliver_to": forms.HiddenInput(),
#             "rejected_by": forms.HiddenInput(),
#             "defect_datetime": forms.HiddenInput(),
#             "item": forms.HiddenInput(),
#             "goods_received_note_item": forms.HiddenInput(),
#         }

#     def __init__(self, *args, **kwargs):
#         super().__init__(*args, **kwargs)

#         self.fields["uom"].disabled = True

#         # Add onclick select text
#         self.fields["batch_no"].widget.attrs["onClick"] = "this.select();"

#     def clean(self):
#         cleaned_data = super().clean()

#         item_code = cleaned_data["item_code"]
#         item = cleaned_data["item"]

#         if item_code != item.code:
#             msg = _("Item Code does not Match.")
#             self.add_error("item_code", msg)


# class GoodsReceivedNoteItemCreateForm(CoreHtmxModelForm, FormUOMSymbolMixin):
#     """Form to create GoodsReceivedNoteItem."""

#     class Meta:
#         model = GoodsReceivedNoteItem
#         fields = [
#             "goods_received_note",
#             "item",
#             "batch_no",
#             "expiry_date",
#             "uom",
#             "quantity",
#         ]
#         widgets = {
#             "goods_received_note": forms.HiddenInput(),
#         }

#     def __init__(self, *args, **kwargs):
#         super().__init__(*args, **kwargs)

#         try:
#             ea_uom = UnitOfMeasure.objects.get(symbol="EA")
#         except Exception:
#             pass
#         else:
#             self.fields["uom"].initial = ea_uom

#         self.fields["uom"].choices = uom_choices_symbol()
#         self.fields["batch_no"].required = True
#         self.fields["batch_no"].initial = "N/A"
#         self.fields["item"].widget.attrs["class"] = "form-control core-select2 item-of-goodsreceivednote"

#     def clean(self):
#         """Check that if selected item is belongs to parent form's consignor."""
#         cleaned_data = super().clean()

#         consignor = cleaned_data["goods_received_note"].consignor
#         item = cleaned_data.get("item")

#         if item and item.consignor.pk != consignor.pk:
#             msg = _(f"The selected item does not belongs to consignor {item.consignor}.")
#             self.add_error("item", msg)


# class RackingGoodsReceivedNoteStockInForm(CoreHtmxModelForm, FormUOMSymbolMixin):
#     """Form to create GoodsReceivedNoteStockIn to Racking."""

#     rack = forms.ChoiceField(label=_("Rack (Pallet)"), choices=[])
#     rackstorage = forms.CharField(required=False, widget=forms.HiddenInput())
#     goods_received_note = forms.CharField(required=False, widget=forms.HiddenInput())
#     goods_received_note_item = forms.CharField(required=False, widget=forms.HiddenInput())

#     class Meta:
#         model = RackTransaction
#         fields = [
#             "rack",
#             "quantity",
#             "rackstorage",
#             "goods_received_note",
#             "goods_received_note_item",
#         ]

#     def __init__(self, *args, **kwargs):
#         super().__init__(*args, **kwargs)

#         # Override label
#         self.fields["quantity"].label = _("Stock In Quantity")

#         # Override choices
#         self.fields["rack"].choices = get_all_leaf_rack_choices()

#     def clean_quantity(self):
#         quantity = self.cleaned_data["quantity"]
#         grn_item = self.initial["goods_received_note_item"]
#         remaining_quantity = grn_item.racking_quantity_status_dict.get("remaining_quantity", Decimal("0"))

#         if quantity <= Decimal("0"):
#             raise ValidationError("Invalid stock in quantity - Cannot input negative or zero quantity.")

#         if quantity > remaining_quantity:
#             raise ValidationError(f"Invalid stock in quantity - exceeded remaining quantity ({remaining_quantity})")
#         return quantity

#     def clean_rackstorage(self) -> RackStorage:
#         rack_pk = self.cleaned_data["rack"]
#         rack = Rack.objects.get(pk=rack_pk)
#         stock = self.initial.get("stock")

#         rack_storage, _ = RackStorage.objects.get_or_create(rack=rack, stock=stock)

#         return rack_storage

#     def clean_goods_received_note(self) -> GoodsReceivedNote:
#         return self.initial["goods_received_note"]

#     def clean_goods_received_note_item(self) -> GoodsReceivedNoteItem:
#         return self.initial["goods_received_note_item"]

#     # def clean(self):
#     #     cleaned_data = super().clean()
#     #
#     #     rack_pk = cleaned_data.get('rack')
#     #     rack = Rack.objects.get(pk=rack_pk)
#     #     stock = self.initial.get('stock')
#     #
#     #     rack_storage, _ = RackStorage.objects.get_or_create(rack=rack, stock=stock)
#     #     cleaned_data['rackstorage'] = rack_storage
#     #
#     #     # item = cleaned_data["item"]
#     #     #
#     #     # if item_code != item.code:
#     #     #     msg = _("Item Code does not Match.")
#     #     #     self.add_error("item_code", msg)
#     #     return cleaned_data

#     def save(self, commit=True):
#         """
#         Save this form's self.instance object if commit=True.
#         """
#         if self.errors:
#             raise ValueError(
#                 "The %s could not be %s because the data didn't validate."
#                 % (
#                     self.instance._meta.object_name,
#                     "created" if self.instance._state.adding else "changed",
#                 )
#             )
#         if commit:
#             self.instance.save()

#         return self.instance

from django.db.models import Q
from django.utils.translation import gettext_lazy as _
from django_filters import filters, FilterSet

from wms.apps.receives.models import GoodsReceivedNote
from wms.cores.forms.widget import CoreSelectWidget, CoreSelectMultipleWidget, CoreDateRangeWidget, CoreBooleanWidget
from wms.cores.utils import get_user_warehouse_choices


class GoodsReceivedNoteFilter(FilterSet):
    """
    Filter class for the Release Order list view.
    Provides filtering capabilities for Release Order attributes.
    """
    date_range = filters.DateFromToRangeFilter(
        label=_("Arrival Date Range"),
        field_name="arrival_datetime",
        widget=CoreDateRangeWidget()
    )

    status = filters.MultipleChoiceFilter(
        choices=GoodsReceivedNote.Status.choices,
        label=_("Status"),
        widget=CoreSelectMultipleWidget()
    )

    consignor = filters.ModelChoiceFilter(
        queryset=None,  # Will be set in __init__
        label=_("Consignor"),
        widget=CoreSelectWidget()
    )

    deliver_to = filters.MultipleChoiceFilter(
        choices=[],  # Will be set in __init__
        label=_("Deliver To"),
        widget=CoreSelectMultipleWidget()
    )


    class Meta:
        model = GoodsReceivedNote
        fields = ['date_range', 'status', 'deliver_to', 'consignor']


    def __init__(self, data=None, queryset=None, *, request=None, prefix=None):
        super().__init__(data=data, queryset=queryset, request=request, prefix=prefix)

        # Set up dynamic filter choices
        from wms.apps.consignors.models import Consignor

        # Filter out the deliver_to based on User Role
        self.filters['deliver_to'].extra['choices'] = get_user_warehouse_choices(request.user)

        # Set up consignor queryset
        self.filters['consignor'].queryset = Consignor.objects.all()

    def filter_queryset(self, queryset):
        """
        Apply custom filtering logic beyond the standard field filtering.
        """
        queryset = super().filter_queryset(queryset)
        return queryset


#############################################################
# Old Code, to be remove after everything is stabled.
#############################################################


# import datetime

# from django.db.models import Q
# from django.utils.translation import gettext_lazy as _

# import django_filters as filters

# from wss.cores.filters import AbstractHistoryFilter
# from wss.cores.utils import get_user_warehouse_choices

# from .models import GoodsReceivedNote


# def get_status_choices():
#     status_choices = [(status, status) for status in GoodsReceivedNote.Status]
#     return status_choices


# class GoodsReceivedNoteFilter(filters.FilterSet):
#     """Filter class for GoodsReceivedNote DataTables."""

#     start_date = filters.DateFilter(
#         label=_("Arrival Start Date"),
#         field_name="arrival_datetime",
#         lookup_expr=("gte"),
#     )
#     end_date = filters.DateFilter(
#         label=_("Arrival End Date"),
#         field_name="arrival_datetime",
#         method="end_date_filter",
#     )
#     batch_keyword_search = filters.CharFilter(
#         label=_("Batch No."),
#         method="custom_batch_keyword_filter",
#     )
#     keyword_search = filters.CharFilter(
#         label=_("Keyword"),
#         method="custom_keyword_filter",
#     )
#     keyword = filters.CharFilter(
#         label=_("Keyword"),
#         method="custom_keyword_filter",
#     )  # Need to have keyword because still using search in DataTables
#     warehouse = filters.MultipleChoiceFilter(label=_("Warehouse"), field_name="deliver_to", choices=[])
#     status = filters.MultipleChoiceFilter(method="status_filter", choices=get_status_choices())

#     class Meta:
#         model = GoodsReceivedNote
#         fields = ["status", "consignor", "warehouse"]

#     def __init__(self, data=None, queryset=None, *, request=None, prefix=None):
#         super().__init__(data=data, queryset=queryset, request=request, prefix=prefix)

#         # Override choices within init function. Otherwise, it might trigger
#         # django.db.utils.ProgrammingError when you dropdb, re-createdb, run makemigrations & migrate.
#         # Filter out the warehouses based on User Role.
#         self.filters["warehouse"].extra["choices"] = get_user_warehouse_choices(request.user)

#         self.filters["start_date"].field.widget.attrs.update({"class": "form-control"})
#         self.filters["end_date"].field.widget.attrs.update({"class": "form-control"})
#         self.filters["consignor"].field.widget.attrs.update({"class": "form-control core-select2"})
#         self.filters["status"].field.widget.attrs.update({"class": "form-control core-select2"})
#         self.filters["keyword_search"].field.widget.attrs.update({"class": "form-control"})
#         self.filters["batch_keyword_search"].field.widget.attrs.update({"class": "form-control"})

#     def custom_keyword_filter(self, queryset, name, value):
#         qs = queryset

#         for keyword in value.split():
#             qs = qs.filter(
#                 Q(numbering__icontains=keyword)
#                 | Q(status__icontains=keyword)
#                 | Q(customer_reference__icontains=keyword)
#                 | Q(consignor__display_name__icontains=keyword)
#                 | Q(issued_by__email__icontains=keyword)
#                 | Q(remark__icontains=keyword)
#             )

#         return qs.distinct()

#     def end_date_filter(self, queryset, name, value):
#         qs = queryset

#         added_one_day_end_date = value + datetime.timedelta(days=1)
#         qs = qs.filter(arrival_datetime__lt=added_one_day_end_date)

#         return qs

#     def custom_batch_keyword_filter(self, queryset, name, value):
#         qs = queryset

#         for batch_keyword in value.split():
#             qs = qs.filter(goodsreceivednoteitem__batch_no__icontains=batch_keyword)

#         return qs.distinct()

#     def warehouse_filter(self, queryset, name, value):
#         qs = queryset

#         qs = qs.filter(deliver_to__in=value)

#         return qs.distinct()

#     def status_filter(self, queryset, name, value):
#         qs = queryset

#         qs = qs.filter(status__in=value)

#         return qs.distinct()


# class GoodsReceivedNoteHistoryFilter(AbstractHistoryFilter):
#     """Filter class for goods_received_note History DataTables."""

#     pass

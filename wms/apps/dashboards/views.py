from django.contrib.auth.mixins import PermissionRequiredMixin
from django.shortcuts import render
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page
from django.views.generic import ListView, TemplateView


# @method_decorator(cache_page(60 * 15), name='dispatch')
class DashboardSuperAdminView(TemplateView):
    template_name = 'home.html'
    submodule_name = 'super-admin'
    view_label = 'Super Admin'
    permission_required = ['dashboards.view_super_admin']


# @method_decorator(cache_page(60 * 15), name='dispatch')
class DashboardAdminView(TemplateView):
    template_name = 'home.html'
    submodule_name = 'admin'
    view_label = 'Admin'
    permission_required = ['dashboards.view_admin']


# @method_decorator(cache_page(60 * 15), name='dispatch')
class DashboardUserView():
    submodule_name = 'user'
    is_subview = True
    view_label = 'User'
    permission_required = ['dashboards.view_user']


# @method_decorator(cache_page(60 * 15), name='dispatch')
class DashboardUserInfoView(TemplateView):
    template_name = 'home.html'
    view_label = 'Info'
    permission_required = ['dashboards.view_user_info']

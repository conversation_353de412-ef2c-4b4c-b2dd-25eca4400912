from django.urls import path
from django.views.generic import RedirectView

from . import views
from .apps import DashboardConfig

# URL namespaces
app_name = DashboardConfig.app_name

# Dynamically generate URLs from modules.py
urlpatterns = [
    path('super-admin/', views.DashboardSuperAdminView.as_view(), name='super-admin'),
    # path('super-admin/info/', views.DashboardSuperAdminInfoView.as_view(), name='super-admin-info'),
    path('admin/', views.DashboardSuperAdminView.as_view(), name='admin'),
    # path('admin/info/', views.DashboardAdminInfoView.as_view(), name='admin-info'),
    path('user/', views.DashboardSuperAdminView.as_view(), name='user'),
    path('user/info/', views.DashboardSuperAdminView.as_view(), name='user-info'),
    path('user/info/', views.DashboardSuperAdminView.as_view(), name='user-info-2'),
]

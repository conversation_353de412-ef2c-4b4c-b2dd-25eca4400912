from django.apps import AppConfig
from django.utils.translation import gettext_lazy as _


class InventoriesConfig(AppConfig):
    name = "wms.apps.inventories"
    verbose_name = _("Inventories")

    def ready(self):
        try:
            """Enabled signals and actstream."""
            from actstream import registry

            import wms.apps.inventories.signals  # noqa F401
            from wms.apps.inventories.models import (
                Item,
                ItemCategory,
                ItemPhoto,
                Stock,
                ReservedStock,
                Transaction,
                ReservedTransaction,
                DailyStockBalance,
            )

            registry.register(
                Item,
                ItemCategory,
                ItemPhoto,
                Stock,
                ReservedStock,
                Transaction,
                ReservedTransaction,
                DailyStockBalance,
            )

            # To prevent related activity stream being removed when object is deleted.

            def not_target_actions(field):
                return field.name not in ["target_actions", "action_object_actions"]

            # consignors apps
            Item._meta.private_fields = list(filter(not_target_actions, Item._meta.private_fields))
            ItemCategory._meta.private_fields = list(filter(not_target_actions, ItemCategory._meta.private_fields))
            ItemPhoto._meta.private_fields = list(filter(not_target_actions, ItemPhoto._meta.private_fields))
            Stock._meta.private_fields = list(filter(not_target_actions, Stock._meta.private_fields))
            ReservedStock._meta.private_fields = list(filter(not_target_actions, ReservedStock._meta.private_fields))
            Transaction._meta.private_fields = list(filter(not_target_actions, Transaction._meta.private_fields))
            ReservedTransaction._meta.private_fields = list(
                filter(not_target_actions, ReservedTransaction._meta.private_fields)
            )
            DailyStockBalance._meta.private_fields = list(
                filter(not_target_actions, DailyStockBalance._meta.private_fields)
            )

        except ImportError:
            pass

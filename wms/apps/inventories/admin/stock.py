from django_admin_listfilter_dropdown.filters import DropdownFilter, RelatedDropdownFilter
# from admin_auto_filters.filters import AutocompleteFilter

from django.contrib import admin

from wms.cores.admin import BaseModelAdmin

from ..models import Stock, ReservedStock, DailyStockBalance


@admin.register(Stock)
class StockAdmin(BaseModelAdmin):
    """Django admin for Stock."""

    list_display = [
        "warehouse",
        "item",
        "batch_no",
        "expiry_date",
        # "balance",
        "db_balance",
        "current_total_reserved_amount",
        "available_balance",
        "reorder_level",
    ]
    search_fields = [
        "warehouse__name",
        "item__code",
        "item__name",
        "batch_no",
    ]
    autocomplete_fields = ["item"]
    list_filter = [
        ("warehouse", RelatedDropdownFilter),
        ("item", RelatedDropdownFilter),
        # ("batch_no", DropdownFilter),
    ]


@admin.register(ReservedStock)
class ReservedStockAdmin(BaseModelAdmin):
    """Django admin for ReservedStock."""

    list_display = [
        "stock",
    ]
    raw_id_fields = ("stock",)
    search_fields = [
        "stock__warehouse__name",
        "stock__item__code",
        "stock__item__name",
    ]
    list_filter = [
        "stock__warehouse__name",
        "stock__item__code",
        "stock__item__name",
    ]


@admin.register(DailyStockBalance)
class DailyStockBalanceAdmin(BaseModelAdmin):
    """Django admin for DailyStockBalance."""

    list_display = [
        "stock",
        "transaction_state",
        "balance",
        "recalculate_datetime",
    ]
    raw_id_fields = ("stock",)
    search_fields = [
        "stock__warehouse__name",
        "stock__item__code",
        "stock__item__name",
    ]
    list_filter = [
        ("stock__warehouse", RelatedDropdownFilter),
        ("stock__item", RelatedDropdownFilter),
        # "stock__item__name",
    ]

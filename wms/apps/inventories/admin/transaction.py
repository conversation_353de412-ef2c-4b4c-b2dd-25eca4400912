from django.contrib import admin

from wms.cores.admin import BaseModelAdmin
from ..models import Transaction, ReservedTransaction


@admin.register(Transaction)
class TransactionAdmin(BaseModelAdmin):
    list_display = [
        "stock",
        "transaction_datetime",
        "quantity",
        "uom",
        "system_quantity",
        "system_uom",
        # "balance",
        "get_current_transaction_balance",
        "remark",
        "system_number_ref",
        "is_internal_transfer",
        "is_adjustment",
        "is_grn",
        "is_wro",
        "customer_reference",
    ]
    raw_id_fields = ("stock",)
    search_fields = (
        "system_number_ref",
        "stock__pk",
        "stock__warehouse__name",
        "stock__item__code",
        "stock__item__name",
    )
    list_filter = [
        "is_internal_transfer",
        "is_adjustment",
        "is_grn",
        "is_grn_defect",
        "is_wro",
    ]
    date_hierarchy = "transaction_datetime"


@admin.register(ReservedTransaction)
class ReservedTransactionAdmin(BaseModelAdmin):
    list_display = [
        "status",
        "reserved_stock",
        "transaction_datetime",
        "input_quantity",
        "input_uom",
        "system_quantity",
        "system_uom",
        # "balance",
        "remark",
        "system_number_ref",
        "is_wro",
        "customer_reference",
    ]
    raw_id_fields = ("reserved_stock",)
    search_fields = (
        "reserved_stock__stock__pk",
        "reserved_stock__stock__warehouse__name",
        "reserved_stock__stock__item__code",
        "reserved_stock__stock__item__name",
    )
    date_hierarchy = "transaction_datetime"

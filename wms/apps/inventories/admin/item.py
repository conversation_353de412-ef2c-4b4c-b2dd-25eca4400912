from django_admin_listfilter_dropdown.filters import RelatedDropdownFilter
from django.contrib import admin

from import_export import resources
from import_export.admin import ImportExportModelAdmin
from treebeard.admin import TreeAdmin
from treebeard.forms import movenodeform_factory

from wms.cores.admin import BaseModelAdmin, BaseTabularInline

from ..models import ItemOutboundUOMDisplayConversion, Item, ItemCategory, ItemPhoto


@admin.register(ItemOutboundUOMDisplayConversion)
class ItemOutboundUOMDisplayConversionAdmin(BaseModelAdmin):
    """Django admin for ItemOutboundUOMDisplayConversion."""

    model = ItemOutboundUOMDisplayConversion
    list_display = [
        "pk",
        "base_value",
        "uom_display_name",
    ]
    search_fields = [
        "uom_display_name",
    ]
    list_filter = [
        "uom_display_name",
    ]


class ItemCategoryResource(resources.ModelResource):
    class Meta:
        model = ItemCategory
        skip_unchanged = True
        exclude = (
            "created_by",
            "created",
            "modified_by",
            "modified",
        )


class ItemResource(resources.ModelResource):
    class Meta:
        model = Item
        skip_unchanged = True
        exclude = (
            "activate_date",
            "deactivate_date",
            "sku",
            "created_by",
            "created",
            "modified_by",
            "modified",
        )


@admin.register(ItemCategory)
class ItemCategoryAdmin(BaseModelAdmin, TreeAdmin, ImportExportModelAdmin):
    """Django admin for ItemCategory."""

    resource_class = ItemCategoryResource

    list_display = [
        "name",
        "slug",
        "path",
        "depth",
        "numchild",
    ]
    search_fields = [
        "name",
    ]
    exclude = BaseModelAdmin.exclude + [
        "path",
        "depth",
        "numchild",
    ]
    form = movenodeform_factory(ItemCategory)


class ItemPhotoInline(BaseTabularInline):
    model = ItemPhoto
    extra = 3


@admin.register(Item)
class ItemAdmin(BaseModelAdmin, ImportExportModelAdmin):
    """Django admin for Item."""

    resource_class = ItemResource

    list_display = [
        "code",
        "name",
        "slug",
        "status",
        "uom",
        "item_type",
        "manage_type",
        "outbound_uom_conversion",
        "html_cover_photo_display",
        "html_photos_display",
        "html_dimensions_display",
        "html_weight_display",
        "html_categories_display",
        "html_outbound_uom_display_conversions_display",
        "html_warehouses_display",
        # "total_balance",
        # "db_balance",
        "barcode",
    ]
    inlines = [
        ItemPhotoInline,
    ]
    search_fields = [
        "code",
        "name",
        "sku",
    ]
    list_filter = [
        "status",
        "item_type",
        "categories",
        ("consignor", RelatedDropdownFilter),
    ]
    autocomplete_fields = [
        "categories",
    ]

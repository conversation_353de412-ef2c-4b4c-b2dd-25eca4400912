from decimal import Decimal

from django.contrib import admin
from django.db import models
from django.db.models import F, Sum
from django.utils import timezone
from django.utils.functional import cached_property
from django.utils.translation import gettext_lazy as _

from wms.cores.models import AbstractBaseModel
from wms.cores.utils import normalize_decimal

# from wms.apps.rackings.models.rack import Rack, RackBalance
from wms.apps.settings.utils import uom_converter

from ..managers import TransactionManager, ReservedTransactionManager


class Transaction(AbstractBaseModel):
    """Transaction model for Warehouse Management System.

    Available fields:
    * created           (AbstractBaseModel => TimeStampedModel)
    * modified          (AbstractBaseModel => TimeStampedModel)
    * created_by        (AbstractBaseModel)
    * modified_by       (AbstractBaseModel)
    * stock
    * transaction_datetime
    * quantity
    * uom
    * system_quantity
    * balance
    * remark
    * system_number_ref
    * is_internal_transfer
    * is_adjustment
    * is_grn
    * is_grn_defect
    * is_wro
    * customer_reference

    """

    # main important fields
    stock = models.ForeignKey("inventories.Stock", on_delete=models.CASCADE)
    transaction_datetime = models.DateTimeField(default=timezone.now)
    quantity = models.DecimalField(verbose_name=_("Quantity"), max_digits=19, decimal_places=6)
    uom = models.ForeignKey("settings.UnitOfMeasure", verbose_name=_("UOM"), on_delete=models.RESTRICT)
    system_quantity = models.DecimalField(
        verbose_name=_("System Quantity"), max_digits=19, decimal_places=6, default=0, editable=False
    )
    # balance = models.DecimalField(verbose_name=_("Balance"), max_digits=19, decimal_places=6, default=Decimal("0"))

    # Channel Info fields
    system_number_ref = models.CharField(verbose_name=_("System Number Ref"), max_length=255, blank=True, null=True)
    is_internal_transfer = models.BooleanField(verbose_name=_("Is Internal Transfer?"), default=False)
    is_adjustment = models.BooleanField(verbose_name=_("Is Adjustment?"), default=False)
    is_grn = models.BooleanField(verbose_name=_("Is GRN?"), default=False)
    is_grn_defect = models.BooleanField(verbose_name=_("Is GRN Defect?"), default=False)
    is_wro = models.BooleanField(verbose_name=_("Is WRO?"), default=False)
    customer_reference = models.CharField(verbose_name=_("Customer Reference"), max_length=64, blank=True, null=True)

    # extra fields
    remark = models.CharField(verbose_name=_("Remark"), max_length=128, blank=True)

    objects = TransactionManager()

    class Meta(AbstractBaseModel.Meta):
        ordering = ["-transaction_datetime", "-pk"]
        indexes = [
            # single index
            models.Index(fields=["stock"]),
            models.Index(fields=["transaction_datetime"]),
            # composite index
            models.Index(fields=["stock", "transaction_datetime"]),  # Composite Index
        ]

    def __str__(self):
        return f"{self.stock.item.name} :: {self.stock.warehouse.name} :: {self.quantity}"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.old_system_quantity = self.system_quantity or Decimal("0")

    @admin.display(description=_("System UOM"))
    @cached_property
    def system_uom(self):
        return self.stock.item.uom

    # @cached_property
    # def get_formatted_balance(self):
    #     """Return formatted balance."""
    #     formatted_balance = round(self.balance, self.system_uom.unit_precision)
    #     return formatted_balance

    @cached_property
    def get_formatted_system_quantity(self):
        """Return formatted system_quantity."""
        formatted_system_quantity = round(self.system_quantity, self.system_uom.unit_precision)
        return formatted_system_quantity

    @cached_property
    def get_previous_transactions(self):
        """To get all previous transactions."""

        previous_transactions = Transaction.objects.filter(
            stock=self.stock, transaction_datetime__lte=self.transaction_datetime
        ).order_by("transaction_datetime", "pk")

        if previous_transactions.exists():
            if self.pk:
                position = list(previous_transactions.values_list("pk", flat=True)).index(self.pk)
                previous_transactions_pks = list(previous_transactions[:position].values_list("pk", flat=True))
                previous_transactions = previous_transactions.filter(pk__in=previous_transactions_pks)
                # previous_transactions = previous_transactions.exclude(pk=self.pk)

        return previous_transactions

    # @cached_property
    # def get_previous_transactions_balance(self):
    #     """To get total balance of all previous transactions."""

    #     previous_transactions = self.get_previous_transactions
    #     balance = previous_transactions.aggregate(Sum("system_quantity")).get(
    #         "system_quantity__sum", Decimal("0")
    #     ) or Decimal("0")

    #     return balance

    @cached_property
    def get_upcoming_transactions(self):
        """Get affected transactions which need to be calculated again. This is to filter all upcoming transactions.

        When calling this method, the self object must have pk exists.
        """

        upcoming_transactions = Transaction.objects.filter(
            stock=self.stock, transaction_datetime__gte=self.transaction_datetime
        ).order_by("transaction_datetime", "pk")

        if self.pk:
            position = list(upcoming_transactions.values_list("pk", flat=True)).index(self.pk)
            position += 1
            upcoming_transactions_pks = list(upcoming_transactions[position:].values_list("pk", flat=True))
            upcoming_transactions = upcoming_transactions.filter(pk__in=upcoming_transactions_pks)
        else:
            pks = upcoming_transactions.filter(transaction_datetime=self.transaction_datetime).values("pk")
            if pks:
                upcoming_transactions = upcoming_transactions.exclude(pk__in=pks)

        return upcoming_transactions

    @admin.display(description=_("Balance"))
    @cached_property
    def get_current_transaction_balance(self):
        """
        return the current transaction's balance in consideration of DailyStockBalance's record

        For Example(read from bottom to top):

        - transaction 6: 5/1/2025, +60 PCE (on morning 8AM, the balance(210) = 150 +60)
        ----- At this point, 5/1/2025 12AM/3AM Cronjob will recalculate the overall accurate balance.
        ----- and store into DailyStockBalance obj(which in this scenario will be 150 balance)
        - transaction 5*: 4/1/2025, +50 PCE (on 5/1/2025, balance(150) = 150)<- transaction_state = this transaction obj
        - transaction 4*: 4/1/2025, +40 PCE (on 5/1/2025, balance(100) = 150 - 50)
        - transaction 3 : 3/1/2025, +30 PCE (on 5/1/2025, balance(60) = 150 - 50 - 40)
        - transaction 2 : 2/1/2025, +20 PCE (on 5/1/2025, balance(30) = 150 - 50 - 40 - 30)
        - transaction 1 : 1/1/2025, +10 PCE (on 5/1/2025, balance(10) = 150 - 50 - 40 - 30 - 20)

        """
        balance = 0
        daily_stock_balance = self.stock.dailystockbalance

        # check if this transaction is the DailyStockBalance's state, skip the calculation.
        if hasattr(self, 'dailystockbalance'):
            balance = self.dailystockbalance.balance

        # if current daily_stock_balance have a transaction_state
        # this means daily_stock_balance must have a balance inside.
        elif hasattr(daily_stock_balance, 'transaction_state'):

            # check if this transaction's transaction_datetime is future than daily_stock_balance
            if self.transaction_datetime > daily_stock_balance.recalculate_datetime:

                total_system_quantity_to_add = Transaction.objects.filter(
                    stock=self.stock,
                    transaction_datetime__gt=daily_stock_balance.recalculate_datetime,
                    transaction_datetime__lte=self.transaction_datetime,
                ).aggregate(
                    total_system_quantity=Sum("system_quantity")
                )["total_system_quantity"] or 0

                balance = daily_stock_balance.balance + total_system_quantity_to_add
            # check if this transaction's transaction_datetime is previous than daily_stock_balance
            elif self.transaction_datetime < daily_stock_balance.recalculate_datetime:

                total_system_quantity_to_deduct = Transaction.objects.filter(
                    stock=self.stock,
                    transaction_datetime__gt=self.transaction_datetime,
                    transaction_datetime__lte=daily_stock_balance.recalculate_datetime,
                ).aggregate(
                    total_system_quantity=Sum("system_quantity")
                )["total_system_quantity"] or 0

                balance = daily_stock_balance.balance - total_system_quantity_to_deduct

        # if it's brand new daily_stock_balance, without any transaction_state at all
        else:
            total_system_quantity_to_add = Transaction.objects.filter(
                stock=self.stock,
                transaction_datetime__lte=self.transaction_datetime,
            ).aggregate(
                total_system_quantity=Sum("system_quantity")
            )["total_system_quantity"] or 0

            balance = daily_stock_balance.balance + total_system_quantity_to_add

        return normalize_decimal(balance)

    # def update_balance(self, system_quantity_difference):
    #     """To update own balance and all upcoming transactions balance. And also update total in and total out."""

    #     all_previous_balance = self.get_previous_transactions_balance

    #     self.balance = all_previous_balance + system_quantity_difference
    #     self.save(update_fields=["balance"], skip_balance=True)

    #     upcoming_transactions = self.get_upcoming_transactions
    #     if upcoming_transactions.exists():
    #         upcoming_transactions.update(balance=F("balance") + system_quantity_difference)

    #     item = self.stock.item
    #     item.total_balance = Transaction.objects.balance_by_item(item)
    #     item.save(update_fields=["total_balance"])

    #     stock = self.stock
    #     stock.balance = Transaction.objects.balance_by_stock(stock)
    #     stock.total_in = Transaction.objects.total_in_by_stock(stock)
    #     stock.total_out = Transaction.objects.total_out_by_stock(stock)
    #     stock.save(update_fields=["balance", "total_in", "total_out"])

    def update_uom_in_use(self) -> None:
        """To update related UOM's in_use field to True."""
        uom = self.uom
        if uom.in_use is not True:
            uom.in_use = True
            uom.save()

        stock_uom = self.stock.item.uom
        if stock_uom is not True:
            stock_uom.in_use = True
            stock_uom.save()

    def save(self, skip_balance=False, *args, **kwargs):

        self.system_quantity = uom_converter(
            origin_uom=self.uom, target_uom=self.stock.item.uom, quantity=self.quantity, skip_unit_precision=True
        )

        # Get system_quantity difference
        if self.pk:
            system_quantity_difference = self.system_quantity - self.old_system_quantity
        else:
            system_quantity_difference = self.system_quantity

        super().save(*args, **kwargs)

        # Once transaction is created, make sure all the related UOM's in_use set to True
        self.update_uom_in_use()

        # if skip_balance is False:
        #     # Update own balance and all upcoming transactions balance.
        #     self.update_balance(system_quantity_difference)


class ReservedTransaction(AbstractBaseModel):
    """ReservedTransaction model for Warehouse Management System.

    Available fields:
    * created           (AbstractBaseModel => TimeStampedModel)
    * modified          (AbstractBaseModel => TimeStampedModel)
    * created_by        (AbstractBaseModel)
    * modified_by       (AbstractBaseModel)
    * status
    * reserved_stock
    * transaction_datetime
    * input_quantity
    * input_uom
    * system_quantity
    * remark
    * system_number_ref
    * is_wro
    * customer_reference

    """
    class Status(models.TextChoices):
        RESERVED = "Reserved", _("Reserved")
        RELEASED = "Released", _("Released")

    # main important fields
    status = models.CharField(verbose_name=_("Status"), max_length=32, choices=Status.choices, default=Status.RESERVED)
    reserved_stock = models.ForeignKey("inventories.ReservedStock", on_delete=models.CASCADE)
    transaction_datetime = models.DateTimeField(default=timezone.now)
    input_quantity = models.DecimalField(verbose_name=_("Quantity"), max_digits=19, decimal_places=6)
    input_uom = models.ForeignKey("settings.UnitOfMeasure", verbose_name=_("UOM"), on_delete=models.RESTRICT)
    system_quantity = models.DecimalField(
        verbose_name=_("System Quantity"), max_digits=19, decimal_places=6, default=0, editable=False
    )

    # Channel Info fields
    system_number_ref = models.CharField(verbose_name=_("System Number Ref"), max_length=255, blank=True)
    is_wro = models.BooleanField(verbose_name=_("Is WRO?"), default=False)
    customer_reference = models.CharField(verbose_name=_("Customer Reference"), max_length=64, blank=True)

    # extra fields
    remark = models.CharField(verbose_name=_("Remark"), max_length=128, blank=True)

    objects = ReservedTransactionManager()

    class Meta(AbstractBaseModel.Meta):
        ordering = ["-transaction_datetime", "-pk"]
        indexes = [
            # single index
            models.Index(fields=["status"]),
            models.Index(fields=["reserved_stock"]),
            models.Index(fields=["transaction_datetime"]),
            # composite index
            models.Index(fields=["reserved_stock", "transaction_datetime"]),
            models.Index(fields=["status", "reserved_stock", "transaction_datetime"]),
        ]

    def __str__(self):
        return f"{self.reserved_stock.stock.item.name} :: {self.reserved_stock.stock.warehouse.name} :: {self.quantity}"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.old_system_quantity = self.system_quantity or Decimal("0")

    @admin.display(description=_("System UOM"))
    @cached_property
    def system_uom(self):
        return self.reserved_stock.stock.item.uom

    @cached_property
    def get_formatted_system_quantity(self):
        """Return formatted system_quantity."""
        formatted_system_quantity = round(self.system_quantity, self.system_uom.unit_precision)
        return formatted_system_quantity

    def save(self, skip_balance=False, *args, **kwargs):

        # self.system_quantity = uom_converter(
        #     origin_uom=self.uom,
        #     target_uom=self.reserved_stock.stock.item.uom,
        #     quantity=self.quantity, skip_unit_precision=True
        # )

        # # Get system_quantity difference
        # if self.pk:
        #     system_quantity_difference = self.system_quantity - self.old_system_quantity
        # else:
        #     system_quantity_difference = self.system_quantity

        super().save(*args, **kwargs)

    @admin.display(description=_("System UOM"))
    @cached_property
    def system_uom(self):
        return self.stock.item.uom

    @cached_property
    def get_formatted_system_quantity(self):
        """Return formatted system_quantity."""
        formatted_system_quantity = round(self.system_quantity, self.system_uom.unit_precision)
        return formatted_system_quantity

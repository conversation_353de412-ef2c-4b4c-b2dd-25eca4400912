from decimal import Decimal

from django.contrib import admin
from django.contrib.postgres.indexes import GinIndex
from django.db import models
from django.urls import reverse
from django.utils.functional import cached_property
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _

from django_extensions.db.fields import Auto<PERSON><PERSON><PERSON>ield
from sorl.thumbnail import <PERSON><PERSON>ield
from treebeard.mp_tree import MP_Node

from wms.cores.models import DISPLAY_EMPTY_VALUE, AbstractActivatableModel, AbstractBaseModel, AbstractSortableModel
from wms.cores.utils import generate_thumbnail

from wms.apps.settings.models import UnitConversion, UnitOfMeasure

from .transaction import Transaction


# Good practice - Using a callable (function) as the default value
def default_dict():
    return {}


class ItemOutboundUOMDisplayConversion(AbstractBaseModel):
    """ItemOutboundUOMDisplayConversion model for Warehouse Management System.

    Functional Purpose:
    - In the Picking Process, the outbound (PickingList/Single WRO) sheet
      should show more informative Value+UOM instead of only the BaseUOM
      For Example:
            WRO/DO expects to release 24 PCE(BaseUOM) of Face Mask,
            instead of showing the floor picker "24 PCE", it should show something
            more practical as "1 BOX"(which maybe is actually == 24PCE)

    Available fields:

    * created           (AbstractBaseModel => TimeStampedModel)
    * modified          (AbstractBaseModel => TimeStampedModel)
    * created_by        (AbstractBaseModel)
    * modified_by       (AbstractBaseModel)
    * base_value
    * uom_display_name

    """

    base_value = models.DecimalField(
        verbose_name=_("Base Value"),
        help_text=_("I.E: Base 60 PCE = 1 defined uom"),
        max_digits=19,
        decimal_places=6,
        blank=True,
        null=True
    )
    uom_display_name = models.CharField(verbose_name=_("UOM Display Name"), max_length=64)

    class Meta(AbstractBaseModel.Meta):
        verbose_name_plural = "item outbound uom display conversions"
        unique_together = ["base_value", "uom_display_name"]
        ordering = ["uom_display_name", "base_value"]

    def __str__(self):
        return f"{self.base_value} Base to 1 {self.uom_display_name}"


class ItemCategory(AbstractBaseModel, MP_Node):
    """ItemCategory model for Warehouse Management System.

    Available fields:

    * created           (AbstractBaseModel => TimeStampedModel)
    * modified          (AbstractBaseModel => TimeStampedModel)
    * created_by        (AbstractBaseModel)
    * modified_by       (AbstractBaseModel)
    * path              (MP_Node)
    * depth             (MP_Node)
    * numchild          (MP_Node)
    * name
    * slug

    """

    name = models.CharField(verbose_name=_("Name"), max_length=255)
    slug = AutoSlugField(verbose_name=_("Slug"), populate_from=["name"], max_length=255, editable=True, unique=True)

    class Meta(AbstractBaseModel.Meta):
        verbose_name_plural = "item categories"
        ordering = ["path"]

    def __str__(self):
        space = "—" * (self.depth - 1)
        return f"{space} {self.name}"


class Item(AbstractBaseModel, AbstractActivatableModel, AbstractSortableModel):
    """Item model for Warehouse Management System.

    Available fields:

    * created           (AbstractBaseModel => TimeStampedModel)
    * modified          (AbstractBaseModel => TimeStampedModel)
    * created_by        (AbstractBaseModel)
    * modified_by       (AbstractBaseModel)
    * status            (AbstractActivatableModel => ActivatorModel)
    * activate_date     (AbstractActivatableModel => ActivatorModel)
    * deactivate_date   (AbstractActivatableModel => ActivatorModel)
    * sort_order        (AbstractSortableModel)
    * code
    * name
    * brand
    * slug
    * sku
    * uom
    * item_type
    * consignor
    * categories
    * outbound_uom_display_conversions
    * warehouses
    * length
    * width
    * height
    * dimension_unit
    * weight
    * weight_unit
    * total_balance
    * default_stock_in_uom
    * default_stock_out_uom
    * barcode
    * uom_json
    * no_of_racks
    * outbound_uom_conversion

    """

    class ItemType(models.TextChoices):
        # RAW_MATERIALS = "RW", _("Raw Materials (RW)")
        # COMPONENTS = "COM", _("Components (COM)")
        # WORK_IN_PROGRESS = "WIP", _("Work In Progress (WIP)")
        FINISHED_GOODS = "FG", _("Finished Goods (FG)")

    class ManageType(models.TextChoices):
        NO_MANAGED = "NM", _("No Managed (NM)")
        BATCH_MANAGED = "BM", _("Batch Managed (BM)")
        SERIAL_MANAGED = "SM", _("Serial Managed (SM)")

    class DimensionUnit(models.TextChoices):
        CM = "cm", _("cm")
        METER = "m", _("m")

    class WeightUnit(models.TextChoices):
        KG = "kg", _("kg")
        GRAM = "g", _("g")

    code = models.CharField(verbose_name=_("Item Code"), max_length=255)
    name = models.CharField(verbose_name=_("Item Name"), max_length=255)
    brand = models.CharField(verbose_name=_("Item Brand"), max_length=255, blank=True)
    slug = AutoSlugField(verbose_name=_("Slug"), populate_from=["code"], max_length=255, editable=True, unique=True)
    sku = models.CharField(
        verbose_name=_("SKU"), max_length=255, blank=True, help_text=_("The Stock Keeping Unit of the item.")
    )
    uom = models.ForeignKey(
        "settings.UnitOfMeasure",
        verbose_name=_("UOM"),
        on_delete=models.RESTRICT,
        help_text=_("The item will be measured in terms of this unit (e.g.: kg, pcs, box)."),
    )
    item_type = models.CharField(
        verbose_name=_("Item Type"), max_length=3, choices=ItemType.choices, default=ItemType.FINISHED_GOODS
    )
    # to differentiate the item manage type I.E: batch managed, serial managed, non-managed
    manage_type = models.CharField(
        verbose_name=_("Manage Type"), max_length=3, choices=ManageType.choices, default=ManageType.BATCH_MANAGED
    )
    consignor = models.ForeignKey("consignors.Consignor", on_delete=models.CASCADE)
    categories = models.ManyToManyField(
        "inventories.ItemCategory", related_name="categories", verbose_name=_("Categories"), blank=True
    )
    outbound_uom_display_conversions = models.ManyToManyField(
        "inventories.ItemOutboundUOMDisplayConversion",
        related_name="outbound_uom_display_conversions",
        verbose_name=_("Outbound UOM Display Conversion"),
        blank=True
    )
    warehouses = models.ManyToManyField(
        "settings.Warehouse", through="inventories.Stock", related_name="warehouses", verbose_name=_("Warehouses")
    )
    barcode = models.CharField(verbose_name=_("Barcode"), max_length=255, blank=True)

    # For Dimensions (Length X Width X Height)
    length = models.DecimalField(
        verbose_name=_("Dimension (Length)"), max_digits=19, decimal_places=6, blank=True, null=True
    )
    width = models.DecimalField(
        verbose_name=_("Dimension (Width)"), max_digits=19, decimal_places=6, blank=True, null=True
    )
    height = models.DecimalField(
        verbose_name=_("Dimension (Height)"), max_digits=19, decimal_places=6, blank=True, null=True
    )
    dimension_unit = models.CharField(
        verbose_name=_("Dimension Unit"), max_length=2, choices=DimensionUnit.choices, default=DimensionUnit.CM
    )

    weight = models.DecimalField(verbose_name=_("Weight"), max_digits=19, decimal_places=6, blank=True, null=True)
    weight_unit = models.CharField(
        verbose_name=_("Weight Unit"), max_length=2, choices=WeightUnit.choices, default=WeightUnit.KG
    )

    # total_balance = models.DecimalField(
    #     verbose_name=_("Stock on Hand"), max_digits=19, decimal_places=6, default=Decimal("0")
    # )
    uom_json = models.JSONField(default=default_dict, null=True, blank=True)
    # no_of_racks = models.PositiveIntegerField(verbose_name=_("No. of Racks"), default=0, db_index=True)
    outbound_uom_conversion = models.DecimalField(
        verbose_name=_("Unit / Carton"), max_digits=19, decimal_places=6, blank=True, null=True
    )

    class Meta(AbstractBaseModel.Meta):
        unique_together = ["consignor", "code"]
        ordering = ["status", "-activate_date"]
        indexes = [
            # GinIndex for `icontains` searches
            GinIndex(name="item_code_gin", fields=["code"], opclasses=["gin_trgm_ops"]),
            GinIndex(name="item_name_gin", fields=["name"], opclasses=["gin_trgm_ops"]),
            # single index for `exact` and order_by
            models.Index(fields=["code"]),
            models.Index(fields=["name"]),
            models.Index(fields=["consignor"]),
            # composite index
            models.Index(fields=["code", "name"]),
            models.Index(fields=["name", "code"]),
        ]

    def __str__(self):
        return f"{self.code} :: {self.name}"

    def get_absolute_url(self):
        return reverse("inventories:items:detail", kwargs={"slug": self.slug})

    @admin.display(description=_("Dimensions"))
    @cached_property
    def html_dimensions_display(self):
        """Return dimensions in (Length X Width X Height) format."""
        if self.length and self.width and self.height:
            return f"({self.length} x {self.width} x {self.height}) {self.dimension_unit}"
        else:
            return DISPLAY_EMPTY_VALUE

    @admin.display(ordering="weight", description=_("Weight"))
    @cached_property
    def html_weight_display(self):
        """Return weight display."""
        if self.weight:
            return f"{self.weight} {self.weight_unit}"
        else:
            return DISPLAY_EMPTY_VALUE

    @admin.display(description=_("Categories"))
    @cached_property
    def html_categories_display(self):
        """Return nice HTML display for all categories."""
        categories = self.categories.all()
        return format_html("<br />".join([category.__str__() for category in categories])) or DISPLAY_EMPTY_VALUE

    @admin.display(description=_("ItemOutboundUOMDisplayConversion"))
    @cached_property
    def html_outbound_uom_display_conversions_display(self):
        """Return nice HTML display for all outbound_uom_display_conversions."""
        conversion_qs = self.outbound_uom_display_conversions.all()
        return format_html("<br />".join([conversion.__str__() for conversion in conversion_qs])) or DISPLAY_EMPTY_VALUE

    @admin.display(description=_("Warehouses"))
    @cached_property
    def html_warehouses_display(self):
        """Return nice HTML display for all warehouses."""
        warehouses = self.warehouses.all().distinct()
        return (
            format_html("<br />".join([warehouse.__str__().replace(" ", "&nbsp;") for warehouse in warehouses]))
            or DISPLAY_EMPTY_VALUE
        )

    @admin.display(description=_("Photos"))
    @cached_property
    def html_photos_display(self):
        """Return nice HTML display for all photos."""
        photos = self.itemphoto_set.all()
        return format_html("".join([photo.get_photo_thumb(img_size="x32") for photo in photos])) or DISPLAY_EMPTY_VALUE

    @admin.display(description=_("Cover Photo"))
    @cached_property
    def html_cover_photo_display(self):
        """Return nice HTML display for cover photo."""
        photo = self.itemphoto_set.filter(is_cover=True)
        if photo.count() > 0:
            return photo.first().get_photo_thumb(img_size="x108")
        else:
            return DISPLAY_EMPTY_VALUE

    @admin.display(ordering="total_balance", description=_("DB Balance"))
    @cached_property
    def db_balance(self):
        return Transaction.objects.balance_by_item(self)

    # @admin.display(ordering="total_balance", description=_("Stock on Hand"))
    # @cached_property
    # def stock_on_hand(self):
    #     return round(self.total_balance, self.uom.unit_precision)

    # def recalculate_balance(self):
    #     stocks = self.stock_set.all()
    #     for stock in stocks:
    #         Transaction.objects.recalculate_balance_by_stock(stock)

    def get_available_uom(self, include_self=True):
        """To get a QuerySet of available UOM objects

        Return:
            <QuerySet [<UnitOfMeasure: OBJ>]>

        Usage:
            if include_self=True
            <QuerySet [<UnitOfMeasure: Tonne>, <UnitOfMeasure: Kilograms>, <UnitOfMeasure: Grams>]>

            if include_self=False
            <QuerySet [<UnitOfMeasure: Tonne>, <UnitOfMeasure: Grams>]>
        """

        available_uom_qs = None
        pk_list = []

        if include_self:
            pk_list.append(self.uom.pk)

        unit_conversion_target_pk_list = list(
            UnitConversion.objects.filter(origin=self.uom).values_list("target__pk", flat=True)
        )
        pk_list.extend(unit_conversion_target_pk_list)
        available_uom_qs = UnitOfMeasure.objects.filter(pk__in=pk_list)

        return available_uom_qs

    def warehouse_total_number_of_batch_no(self, warehouse=None):
        """return total number of batch no with warehouse consideration"""
        return self.stock_set.filter(warehouse=warehouse).order_by("batch_no").distinct("batch_no").count()

    @cached_property
    def total_number_of_batch_no(self):
        """return total number of batch no"""
        return self.stock_set.all().order_by("batch_no").distinct("batch_no").count()

    def balance_by_warehouse(self, warehouse):
        return Transaction.objects.balance_by_warehouse_item(warehouse, self)

    @cached_property
    def get_all_warehouses_involved_rack(self, balance_gt_zero=True):
        """return Queryset of Warehouses that involved Racks"""
        from wms.apps.settings.models import Warehouse

        if balance_gt_zero:
            return Warehouse.objects.filter(
                rack__rackstorage__stock__item=self, rack__rackbalance__balance__gt=0
            ).distinct()
        else:
            return Warehouse.objects.filter(rack__rackstorage__stock__item=self).distinct()

    @cached_property
    def leaf_node_racks(self, balance_gt_zero=True):
        """Return racks belong to this item and with balance > 0 on it."""
        from wms.apps.rackings.models import Rack

        if balance_gt_zero:
            return Rack.objects.filter(numchild=0, rackbalance__stock__item=self, rackbalance__balance__gt=0)
        else:
            return Rack.objects.filter(numchild=0, rackbalance__stock__item=self)

    def save(self, *args, **kwargs):

        # Convert to uppercase
        if not isinstance(self.code, int):
            self.code = self.code.upper()

        if isinstance(self.name, str):
            self.name = self.name.upper()

        if isinstance(self.brand, str):
            self.brand = self.brand.upper()

        # The default stock in UOM must be equal to self.uom or in self.get_available_uom()
        if self.uom:
            pass
            # available_uom = self.get_available_uom()
            # if not self.default_stock_in_uom or (
            #     self.default_stock_in_uom and self.default_stock_in_uom not in available_uom
            # ):
            #     self.default_stock_in_uom = self.uom
            #
            # if not self.default_stock_out_uom or (
            #     self.default_stock_out_uom and self.default_stock_out_uom not in available_uom
            # ):
            #     self.default_stock_out_uom = self.uom

        super().save(*args, **kwargs)


def item_directory_path(instance, filename):
    """File will be uploaded to MEDIA_ROOT/inventories/<item.pk>/<filename>"""
    return f"inventories/item/{instance.item.pk}/{filename}"


class ItemPhoto(AbstractBaseModel, AbstractSortableModel):
    """ItemPhoto model for Warehouse Management System.

    Available fields:

    * created           (AbstractBaseModel => TimeStampedModel)
    * modified          (AbstractBaseModel => TimeStampedModel)
    * created_by        (AbstractBaseModel)
    * modified_by       (AbstractBaseModel)
    * sort_order        (AbstractSortableModel)
    * item
    * photo

    """

    item = models.ForeignKey("inventories.Item", on_delete=models.CASCADE, verbose_name=_("Item"))
    photo = ImageField(verbose_name=_("Item Photo"), upload_to=item_directory_path)
    is_cover = models.BooleanField(verbose_name=_("Is Cover?"), default=False)

    class Meta(AbstractBaseModel.Meta):
        ordering = ["sort_order"]

    def __str__(self):
        return f"{self.item.name}'s photo"

    @admin.display(description=_("Item Photo"))
    def get_photo_thumb(self, img_size="x64"):
        return generate_thumbnail(self.photo, img_size)

    def save(self, *args, **kwargs):
        model = self.__class__

        # Only 1 photo can set as cover for each item
        if self.pk and self.is_cover:
            model.objects.exclude(pk=self.pk).filter(item=self.item).update(is_cover=False)

        super().save(*args, **kwargs)

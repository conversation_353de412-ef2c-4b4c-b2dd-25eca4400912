from decimal import Decimal

from django.contrib import admin
from django.contrib.postgres.indexes import GinIndex
from django.db import models
from django.db.models import F, Sum
from django.utils import timezone
from django.utils.functional import cached_property
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _

from wms.cores.models import DISPLAY_EMPTY_VALUE, AbstractBaseModel

# from wms.apps.rackings.models.rack import Rack, RackBalance

from .transaction import Transaction, ReservedTransaction

from ..managers import StockManager


class Stock(AbstractBaseModel):
    """Stock model for Warehouse Management System.

    Available fields:

    * created           (AbstractBaseModel => TimeStampedModel)
    * modified          (AbstractBaseModel => TimeStampedModel)
    * created_by        (AbstractBaseModel)
    * modified_by       (AbstractBaseModel)
    * warehouse
    * item
    * batch_no
    * balance
    * reorder_level
    * expiry_date

    """

    warehouse = models.ForeignKey("settings.Warehouse", on_delete=models.CASCADE, verbose_name=_("Warehouse"))
    item = models.ForeignKey("inventories.Item", on_delete=models.CASCADE, verbose_name=_("Item"))
    batch_no = models.CharField(verbose_name=_("Batch No."), max_length=255, default="N/A")
    expiry_date = models.DateField(verbose_name=_("Expiry Date"), blank=True, null=True)
    # balance = models.DecimalField(verbose_name=_("Balance"), max_digits=19, decimal_places=6, default=Decimal("0"))
    # total_in = models.DecimalField(verbose_name=_("Total In"), max_digits=19, decimal_places=6, default=Decimal("0"))
    # total_out = models.DecimalField(verbose_name=_("Total Out"), max_digits=19, decimal_places=6, default=Decimal("0"))
    reorder_level = models.DecimalField(
        verbose_name=_("Reorder Level"),
        max_digits=19,
        decimal_places=6,
        default=Decimal("0"),
        help_text=_("When the stock reaches the reorder level, a notification will be sent to you."),
    )

    objects = StockManager()

    class Meta(AbstractBaseModel.Meta):
        unique_together = ["warehouse", "item", "batch_no", "expiry_date"]
        ordering = [
            "warehouse__path",
        ]
        indexes = [
            # GinIndex for `icontains` searches
            GinIndex(name="stock_batch_no_gin", fields=["batch_no"], opclasses=["gin_trgm_ops"]),
            # single index
            models.Index(fields=["warehouse"]),
            models.Index(fields=["item"]),
            models.Index(fields=["batch_no"]),
            # composite index
            # - no matter the query from which point starting to query, it's optimized
            models.Index(fields=["warehouse", "item", "batch_no"]),
            models.Index(fields=["warehouse", "batch_no", "item"]),
            models.Index(fields=["item", "batch_no", "warehouse"]),
            models.Index(fields=["item", "warehouse", "batch_no"]),
        ]

    def __str__(self):
        return (
            f"{self.item.code} :: {self.item.name} [{self.batch_no}] :: {self.expiry_date} "
            f"({self.item.item_type}) :: {self.warehouse.name}"
        )

    @admin.display(ordering="balance", description=_("DB Balance"))
    @cached_property
    def db_balance(self):
        """Return the total remaining quantity of given stock."""
        return Transaction.objects.balance_by_stock(self)

    @admin.display(description=_("Reserved Amount"))
    @cached_property
    def current_total_reserved_amount(self):
        return ReservedTransaction.objects.current_total_reserved_amount_by_stock(self)

    @admin.display(description=_("Available Balance"))
    @cached_property
    def available_balance(self):
        available_balance = self.db_balance - self.current_total_reserved_amount
        return available_balance

    # def recalculate_balance(self):
    #     Transaction.objects.recalculate_balance_by_stock(self)

    # def recalculate_total_in_and_out(self):
    #     """Recalculate total_in and update to stock."""
    #     self.total_in = Transaction.objects.total_in_by_stock(self)
    #     self.total_out = Transaction.objects.total_out_by_stock(self)
    #     self.save(update_fields=["total_in", "total_out"])

    @cached_property
    def get_total_in(self):
        """Return total_in of given stock.
        total_in = the total of happened transaction that a stock accumulated
        """
        return Transaction.objects.total_in_by_stock(self)

    @cached_property
    def get_total_out(self):
        """Return total_out of given stock.
        total_out = the total of happened transaction that a stock accumulated
        """
        return Transaction.objects.total_out_by_stock(self)

    # @cached_property
    # def get_formatted_balance(self):
    #     """Return formatted balance."""
    #     formatted_balance = f"{round(self.balance, self.item.uom.unit_precision)}"
    #     return formatted_balance

    # @cached_property
    # def html_formatted_balance_display(self):
    #     """Return formatted balance with UOM in HTML."""
    #     html_formatted_balance = f"{self.get_formatted_balance} {self.item.uom.symbol}"
    #     return html_formatted_balance

    # @cached_property
    # def all_racks(self):
    #     """
    #     Returns Racks queryset that contains this stock.
    #     """
    #     rack_pks = RackBalance.objects.select_related("rack").filter(stock=self).values_list("rack__pk", flat=True)
    #     return Rack.objects.filter(pk__in=rack_pks)

    # @cached_property
    # def all_leaf_node_racks(self):
    #     """
    #     Returns Racks queryset that contains this stock, only for Rack at leaf nodes (no child node).
    #     """
    #     return self.all_racks.filter(numchild=0)

    # @cached_property
    # def get_outbound_uom_display_conversion(self):
    #     """Return outbound uom display conversion in dict format.

    #     Assumption:
    #     - suppose to support up to multiple conversion dict in 1 big dict:

    #     Return:
    #         outbound_uom_display_conversion_dict = {
    #             # structure example:
    #             "uom_display_name": {
    #                 "converted_uom": converted_uom,
    #                 "converted_quantity": converted_quantity,
    #                 "base_uom": base_uom,
    #                 "base_quantity": base_quantity,
    #             },

    #             #value example (I.E: 1 carton = 10 PCE):
    #             "carton": {
    #                 "converted_uom": "carton",
    #                 "converted_quantity": 10,
    #                 "base_uom": "PCE",
    #                 "base_quantity": 100,
    #             },
    #             #value example (I.E: 1 pallet = 200 PCE):
    #             "pallet": {
    #                 "converted_uom": "pallet",
    #                 "converted_quantity": 10,
    #                 "base_uom": "PCE",
    #                 "base_quantity": 2000,
    #             },
    #         }
    #     """

    #     outbound_uom_display_conversion_dict = {}

    #     all_uom_display_qs = self.item.outbound_uom_display_conversions.order_by("base_value")

    #     for uom_display_obj in all_uom_display_qs:
    #         uom_display_quantity = self.quantity / uom_display_obj.base_value

    #         if uom_display_quantity.as_tuple().exponent < 0:
    #             uom_display_number, base_display_number = calculate_base_uom_to_expected_conversion_uom(
    #                 uom_display_quantity, uom_display_obj.base_value
    #             )
    #             outbound_uom_display_conversion_dict[uom_display_obj.uom_display_name] = {
    #                 "converted_uom": uom_display_obj.uom_display_name,
    #                 "converted_quantity": uom_display_number,
    #                 "base_uom": self.item.uom.symbol,
    #                 "base_quantity": base_display_number,
    #             }
    #     return outbound_uom_display_conversion_dict


class ReservedStock(AbstractBaseModel):
    """Reserved Stock model for Warehouse Management System.

    Available fields:

    * created           (AbstractBaseModel => TimeStampedModel)
    * modified          (AbstractBaseModel => TimeStampedModel)
    * created_by        (AbstractBaseModel)
    * modified_by       (AbstractBaseModel)
    * stock

    """

    stock = models.OneToOneField(
        "inventories.Stock", verbose_name=_("Stock"), on_delete=models.CASCADE
    )

    class Meta(AbstractBaseModel.Meta):
        ordering = [
            "stock__warehouse__path",
        ]

    def __str__(self):
        return (
            f"{self.stock.item.code} :: {self.stock.item.name} [{self.stock.batch_no}] :: {self.stock.expiry_date} "
            f"({self.stock.item.item_type}) :: {self.stock.warehouse.name}"
        )


class DailyStockBalance(AbstractBaseModel):
    """Daily Stock Balance model for Warehouse Management System.

    Available fields:

    * created           (AbstractBaseModel => TimeStampedModel)
    * modified          (AbstractBaseModel => TimeStampedModel)
    * created_by        (AbstractBaseModel)
    * modified_by       (AbstractBaseModel)
    * stock
    * transaction_state
    * balance
    * recalculate_datetime

    TODO*: prepare a script that have a cronjob to recalculate the balance every night

    This model is to cater for the scenario of:
    *(read from bottom to top)
        - transaction 6: 5/1/2025, +60 PCE (on morning 8AM, the balance(210) = 150 +60)
        ----- At this point, 5/1/2025 12AM/3AM Cronjob will recalculate the overall accurate balance.
        ----- and store into DailyStockBalance obj(which in this scenario will be 150 balance)
        - transaction 5*: 4/1/2025, +50 PCE (on 5/1/2025, balance(150) = 150)<- transaction_state = this transaction obj
        - transaction 4*: 4/1/2025, +40 PCE (on 5/1/2025, balance(100) = 150 - 50)
        - transaction 3 : 3/1/2025, +30 PCE (on 5/1/2025, balance(60) = 150 - 50 - 40)
        - transaction 2 : 2/1/2025, +20 PCE (on 5/1/2025, balance(30) = 150 - 50 - 40 - 30)
        - transaction 1 : 1/1/2025, +10 PCE (on 5/1/2025, balance(10) = 150 - 50 - 40 - 30 - 20)

    """
    stock = models.OneToOneField(
        "inventories.Stock", verbose_name=_("Stock"), on_delete=models.CASCADE
    )
    transaction_state = models.OneToOneField(
        "inventories.Transaction",
        on_delete=models.SET_NULL,
        verbose_name=_("Transaction State"),
        help_text=_("The balance field's value is based on this transaction obj's state."),
        null=True,
        blank=True,
    )
    balance = models.DecimalField(verbose_name=_("Balance"), max_digits=19, decimal_places=6, default=Decimal("0"))
    recalculate_datetime = models.DateTimeField(default=timezone.now)


    class Meta(AbstractBaseModel.Meta):
        ordering = [
            "stock__warehouse__path",
        ]

        indexes = [
            # composite index
            models.Index(fields=["stock", "recalculate_datetime"]),
        ]

    def __str__(self):
        return (
            f"{self.stock.item.code} :: {self.stock.item.name} [{self.stock.batch_no}] :: {self.stock.expiry_date} "
            f"({self.stock.item.item_type}) :: {self.stock.warehouse.name} :: {round(self.balance, 0)} :: "
            f"({self.recalculate_datetime})"
        )

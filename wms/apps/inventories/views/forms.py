from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

from wms.apps.inventories.models import Item
from wms.cores.forms.fields import CoreModelForm, CoreCharField, CoreChoiceField
from wms.cores.utils import uom_choices_symbol


class ItemForm(CoreModelForm):

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields["uom"].choices = uom_choices_symbol()

    def clean(self):
        cleaned_data = super().clean()
        consignor = cleaned_data.get("consignor")
        code = cleaned_data.get("code")

        if consignor and code:
            # Check if an item with same consignor and code exists
            exists = Item.objects.filter(
                consignor=consignor,
                code__iexact=code
            ).exclude(pk=self.instance.pk if self.instance else None).exists()

            if exists:
                raise ValidationError({
                    "code": _("Item with this Code already exists for the selected Consignor."),
                })

        return cleaned_data

    class Meta:
        model = Item
        fields = [
            "code",
            "name",
            "brand",
            "uom",
            "item_type",
            "consignor",
            "length",
            "width",
            "height",
            "dimension_unit",
            "weight",
            "weight_unit",
        ]


class ItemUpdateForm(CoreModelForm):
    # Read-only display fields
    code = CoreCharField(disabled=True, required=False)
    uom = CoreChoiceField(disabled=True, required=False)

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        instance = kwargs.get('instance')

        if instance:
            self.fields["uom"].choices = uom_choices_symbol()

    def clean(self):
        cleaned_data = super().clean()
        if self.instance:
            # Preserve original values for read-only fields
            cleaned_data['code'] = self.instance.code
            cleaned_data['uom'] = self.instance.uom
        return cleaned_data

    class Meta:
        model = Item
        fields = [
            "code",  # Read-only
            "name",
            "brand",
            "uom",  # Read-only
            "item_type",
            "consignor",
            "length",
            "width",
            "height",
            "dimension_unit",
            "weight",
            "weight_unit",
        ]

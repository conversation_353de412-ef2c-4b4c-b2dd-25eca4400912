# from decimal import Decimal
# from typing import Any
from django.db.models import QuerySet
from django.urls import reverse_lazy, reverse
# from django.conf import settings
# from django.contrib import messages
# from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
# from django.core.paginator import Paginator
# from django.db.models import BLANK_CHOICE_DASH, Count, DecimalField, OuterRef, Q, QuerySet, Subquery, Value
# from django.db.models.functions import Coalesce
# from django.http import HttpRequest, HttpResponse, JsonResponse
# from django.shortcuts import redirect
# from django.urls import reverse, reverse_lazy
# from django.utils.translation import gettext_lazy as _
# from django.views import View
from django.views.generic import DeleteView, UpdateView, DetailView

# from actstream.models import Action
# from django_filters.views import FilterView
# from django_tables2 import SingleTableMixin
# from django_tables2.export.views import ExportMixin

# from wms.cores.actstream import query_actstream
# from wms.cores.utils import safe_referrer
# from wms.cores.views import (
#     CoreBaseBarcodeUpdateView,
#     CoreBaseHistoryModifiedView,
#     CoreCreateView,
#     CoreDataTablesView,
#     CoreDeleteView,
#     CoreDetailDataTablesView,
#     CoreDetailView,
#     CoreListView,
#     CoreUpdateView,
# )

from wms.apps.inventories.filters import (
    ItemFilter,
    # ItemHistoryFilter,
    # ItemRackStorageFilter,
    # ItemStockListFilter,
    # ItemStockRackListFilter,
)
from wms.apps.inventories.filters.item import ItemDataTableFilter, StockFilter
# from wms.apps.inventories.forms import (
#     ItemBarcodeUpdateForm,
#     ItemForm,
#     ItemInfoUpdateForm,
#     ItemUpdateForm,
#     SettingUpdateForm,
# )
# from wms.apps.inventories.models import Item, Stock
from wms.apps.inventories.tables.item import ItemDetailTable, ItemStockTable, ItemTable
from wms.apps.inventories.views.forms import ItemForm, ItemUpdateForm
# from wms.apps.inventories.utils import get_warehouse_stock_list
# from wms.apps.rackings.models import Rack, RackBalance, RackStorage
# from wms.apps.settings.models import Warehouse

from wms.cores.mixins import ExportTableMixin
from wms.cores.views import CoreSingleTableView, CoreDataTableDetailView, CoreCreateView, CoreDetailView, CoreUpdateView

from wms.apps.inventories.models import Item, Transaction, Stock


class ItemListView(ExportTableMixin, CoreSingleTableView):
    """
    View for displaying and managing the list of Items.
    Supports:
    - Pagination
    - Column visibility
    - Searching
    - Export functionality
    - HTMX partial updates
    """
    model = Item
    table_class = ItemTable
    template_name = "cores/list_table.html"
    partial_template_name = "cores/table_partial.html"
    queryset = Item.objects.all()
    filterset_class = ItemFilter

    # Search configuration
    search_fields = ["code", "name", "consignor__display_name"]

    # Export configuration
    export_name = "items"
    export_permission = []  # Empty list means no specific permissions required


class ItemStockListView(ExportTableMixin, CoreSingleTableView):
    """
    View for displaying and managing the list of Items.
    Supports:
    - Pagination
    - Column visibility
    - Searching
    - Export functionality
    - HTMX partial updates
    """
    model = Stock
    table_class = ItemStockTable
    template_name = "cores/datatable_detail_view_table.html"
    partial_template_name = "cores/table_partial.html"
    filterset_class = StockFilter

    # Search configuration
    search_fields = ["batch_no"]

    # Export configuration
    export_name = "item_stocks"
    export_permission = []  # Empty list means no specific permissions required

    # HTMX configuration, override default to prevent htmx target conflict
    # Map this with own partial element id, use this when under single page there is more than 1 htmx table
    htmx_target = 'table-content-partial'

    def get_queryset(self) -> QuerySet:
        queryset = super().get_queryset()
        # Apply additional filter for this specific view
        return queryset.filter(item__pk=self.kwargs.get('pk'))

    def get_htmx_base_url(self):
        """
        Override to provide the dynamic URL based on the current item's PK.
        This URL will be used for all HTMX requests and querystring operations.
        """
        pk = self.kwargs.get('pk')
        if pk:
            return reverse("inventories:items:stock_list", kwargs={"pk": pk})
        return None


class ItemCreateView(CoreCreateView):
    model = Item
    form_class = ItemForm
    template_name = "inventories/items/item_form.html"
    success_url = "inventories:items:panel"
    section_title = "Create Item"
    section_desc = ""
    submit_text = "Save"
    cancel_url = "inventories:items:list"


class ItemUpdateView(CoreUpdateView):
    model = Item
    form_class = ItemUpdateForm
    template_name = "inventories/items/item_form.html"
    success_url = "inventories:items:panel"
    section_title = "Update Item"
    section_desc = ""
    submit_text = "Update"
    cancel_url = "inventories:items:list"


class ItemDetailHomeView(CoreDetailView):
    model = Item
    template_name = 'inventories/items/mains/home.html'


class ItemDetailView(CoreDetailView):
    model = Item
    template_name = 'inventories/items/partials/detail.html'


class ItemStockView(CoreDetailView):
    model = Item
    template_name = 'inventories/items/partials/stock.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["warehouses_stock"] = Transaction.objects.sum_group_warehouse_by_item(
            item=self.object,
            defect=None  # Include all warehouses [defect or non defect]
        )

        context["stocks_total"] = Transaction.objects.stock_total_by_item(item=self.object)
        context["stocks_count"] = self.object.stock_set.count()
        return context


class ItemEventView(CoreDetailView):
    model = Item
    template_name = 'inventories/items/partials/event.html'


class ItemDataTableDetailView(CoreDataTableDetailView):
    model = Item
    table_class = ItemDetailTable
    context_object_name = "item"
    partial_view = ItemDetailHomeView
    search_fields = ["code", "name"]
    filterset_class = ItemDataTableFilter

# class ItemListView(LoginRequiredMixin, PermissionRequiredMixin, FilterView, SingleTableMixin, CoreListView):
#     """Page to show all Item. This page use DataTables server side."""

#     template_name = "inventories/items/list.html"

#     table_class = ItemDataTables
#     filterset_class = ItemFilter

#     # To prevent query all object as it will use ajax call in template
#     object_list = Item.objects.none()

#     header_title = "Items"
#     selected_page = "inventories"
#     selected_subpage = "items"

#     permission_required = ("inventories.view_item",)

#     def get_queryset(self):
#         """To prevent query all object."""
#         return self.object_list


# item_list_view = ItemListView.as_view()


# class ItemDetailView(LoginRequiredMixin, PermissionRequiredMixin, CoreDetailDataTablesView):
#     """Page to display selected Item based on given Item's slug. This page use DataTables server side."""

#     model = Item
#     template_name = "inventories/items/detail.html"

#     table_class = ItemDetailDataTables

#     header_title = "Items"
#     selected_page = "inventories"
#     selected_subpage = "items"

#     permission_required = ("inventories.view_item",)

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         context["warehouse_stock_list_on_hand"] = get_warehouse_stock_list(item=self.object, filter_type="on_hand")
#         context["warehouse_stock_list_defect"] = get_warehouse_stock_list(item=self.object, filter_type="defect")
#         context["stocks_count"] = self.object.stock_set.count()
#         return context


# item_detail_view = ItemDetailView.as_view()


# class ItemCreateAndUpdateMixin:
#     """Mixin for Create and Update."""

#     model = Item
#     form_class = ItemForm
#     template_name = "inventories/items/create_or_update.html"

#     selected_page = "inventories"
#     selected_subpage = "items"

#     def get_success_url(self):
#         return reverse("inventories:items:detail", kwargs={"slug": self.object.slug})


# class ItemCreateView(ItemCreateAndUpdateMixin, LoginRequiredMixin, PermissionRequiredMixin, CoreCreateView):
#     """Page to create Item."""

#     success_message = _("Item %(name)s successfully created")

#     header_title = "New Item"

#     permission_required = ("inventories.add_item",)


# item_create_view = ItemCreateView.as_view()


# class ItemUpdateView(ItemCreateAndUpdateMixin, LoginRequiredMixin, PermissionRequiredMixin, CoreUpdateView):
#     """Page to update selected Item based on given Item's slug."""

#     success_message = _("Item %(name)s successfully updated")

#     header_title = "Update Item"

#     form_class = ItemUpdateForm

#     permission_required = ("inventories.change_item",)


# item_update_view = ItemUpdateView.as_view()


# class ItemDeleteView(LoginRequiredMixin, PermissionRequiredMixin, CoreDeleteView):
#     """Page to delete selected Item based on given Item's slug."""

#     model = Item
#     success_url = reverse_lazy("inventories:items:list")
#     success_message = _("Item %(name)s successfully deleted")

#     permission_required = ("inventories.delete_item",)


# item_delete_view = ItemDeleteView.as_view()


# ##################
# # FOR DataTables #
# ##################


# class ItemDataTablesView(LoginRequiredMixin, PermissionRequiredMixin, CoreDataTablesView):
#     """JSON for DataTables in list page."""

#     model = Item
#     table_class = ItemDataTables
#     filterset_class = ItemFilter

#     permission_required = ("inventories.view_item",)

#     def get_queryset(self) -> QuerySet[Item]:
#         return (
#             self.model.objects.select_related("uom", "consignor")
#             .prefetch_related("categories")
#             .active()
#             .annotate(distinct_leaf_node_racks_count=Count("stock__rackstorage", distinct=True))
#         )


# item_datatables_view = ItemDataTablesView.as_view()


# class ItemDetailDataTablesView(ItemDataTablesView):
#     """JSON for DataTables in detail page."""

#     table_class = ItemDetailDataTables


# item_detail_datatables_view = ItemDetailDataTablesView.as_view()


# class ItemHistoryDataTablesView(LoginRequiredMixin, PermissionRequiredMixin, CoreDataTablesView):
#     """JSON for DataTables in Item History's page."""

#     model = Action
#     table_class = ItemHistoryDataTables
#     filterset_class = ItemHistoryFilter

#     permission_required = ("inventories.view_item",)

#     def get_queryset(self) -> QuerySet[Action]:
#         self.item = Item.objects.prefetch_related("itemphoto_set").get(pk=self.kwargs["pk"])
#         action_item_qs = query_actstream(target=self.item)

#         action_qs = action_item_qs

#         return action_qs


# item_history_list_datatables_view = ItemHistoryDataTablesView.as_view()


# class ItemStockListDataTablesView(LoginRequiredMixin, PermissionRequiredMixin, CoreDataTablesView):
#     """JSON for DataTables in list page."""

#     model = Stock
#     table_class = ItemStockListDataTables
#     filterset_class = ItemStockListFilter

#     permission_required = ("inventories.view_item",)

#     def get_queryset(self) -> QuerySet[Stock]:
#         return self.model.objects.filter(item__slug=self.kwargs["slug"]).order_by("batch_no")


# item_stock_list_datatables_view = ItemStockListDataTablesView.as_view()


# class ItemWarehouseStockRackListDataTablesView(LoginRequiredMixin, PermissionRequiredMixin, CoreDataTablesView):
#     """JSON for DataTables in list page."""

#     model = Rack
#     table_class = ItemStockRackListDataTables
#     filterset_class = ItemStockRackListFilter

#     permission_required = (
#         "inventories.view_item",
#         "settings.view_warehouse",
#         "rackings.view_rack",
#     )

#     def get_stock(self):
#         """To get stock based on given stock_pk."""
#         return Stock.objects.get(pk=self.kwargs["stock_pk"])

#     def get_warehouse(self):
#         """To get stock based on given stock_pk."""
#         return Warehouse.objects.get(slug=self.kwargs["warehouse_slug"])

#     def get_queryset(self) -> QuerySet[Rack]:
#         self.stock = self.get_stock()
#         self.warehouse = self.get_warehouse()

#         queryset = super().get_queryset()

#         return queryset.filter(rackstorage__stock=self.stock, warehouse=self.warehouse)

#     def get_table(self, **kwargs):
#         self.stock = self.get_stock()
#         self.warehouse = self.get_warehouse()

#         table = self.table_class(self.get_queryset(), stock=self.stock, warehouse=self.warehouse, **kwargs)

#         # Override the table's attrs to set the dynamic id
#         table.attrs["id"] = f"warehouse_{self.warehouse.pk}_stock_{self.stock.pk}_rack_datatables"

#         return table


# item_warehouse_stock_rack_list_datatables_view = ItemWarehouseStockRackListDataTablesView.as_view()


# class ItemRackStorageDataTablesView(LoginRequiredMixin, PermissionRequiredMixin, CoreDataTablesView):
#     """JSON for DataTables in list page."""

#     model = RackStorage
#     table_class = ItemRackStorageDataTables
#     filterset_class = ItemRackStorageFilter

#     permission_required = (
#         "settings.view_warehouse",
#         "rackings.view_rack",
#     )

#     def get_item(self):
#         return Item.objects.get(slug=self.kwargs["slug"])

#     def get_filterset_kwargs(self, filterset_class):
#         kwargs = super().get_filterset_kwargs(filterset_class)
#         kwargs["item"] = self.item
#         return kwargs

#     def get_queryset(self):
#         self.item = self.get_item()

#         # Get the balance for the corresponding rack and stock from RackBalance
#         balance_subquery = RackBalance.objects.filter(rack=OuterRef("rack"), stock=OuterRef("stock")).values("balance")[
#             :1
#         ]

#         # Annotate the balance into the RackStorage queryset
#         return (
#             self.model.objects.select_related("rack", "stock")
#             .filter(
#                 stock__item=self.item,
#             )
#             .annotate(
#                 rack_balance=Coalesce(Subquery(balance_subquery, output_field=DecimalField()), Value(Decimal("0")))
#             )
#             .filter(rack_balance__gt=0)
#         )


# item_rack_storage_datatables_view = ItemRackStorageDataTablesView.as_view()

# ############
# # FOR HTMX #
# ############


# class ItemStockListView(LoginRequiredMixin, PermissionRequiredMixin, FilterView, SingleTableMixin, CoreListView):
#     """Partial page to show all Item's Stock list based on given Item's slug."""

#     model = Stock
#     template_name = "inventories/items/partials/htmx/_stocks.html"
#     table_class = ItemStockListDataTables
#     filterset_class = ItemStockListFilter

#     # To prevent query all object as it will use ajax call in template
#     object_list = Stock.objects.none()

#     permission_required = ("inventories.view_item",)

#     def get_queryset(self):
#         """To prevent query all object."""
#         return self.object_list

#     def get_context_data(self, **kwargs) -> dict[str, Any]:
#         context = super().get_context_data(**kwargs)
#         item_slug = self.kwargs["slug"]
#         context["item"] = Item.objects.get(slug=item_slug)
#         return context


# item_stock_list_view = ItemStockListView.as_view()


# class ItemInfoDetailView(LoginRequiredMixin, PermissionRequiredMixin, CoreDetailView):
#     """Partial page to display selected Item's info page based on given Item's slug."""

#     model = Item
#     template_name = "inventories/items/partials/htmx/_info.html"

#     permission_required = ("inventories.view_item",)


# item_info_detail_view = ItemInfoDetailView.as_view()


# class ItemInfoUpdateView(LoginRequiredMixin, PermissionRequiredMixin, CoreUpdateView):
#     """Partial page to update selected Item's info page based on given Item's slug."""

#     model = Item
#     form_class = ItemInfoUpdateForm
#     template_name = "inventories/items/partials/htmx/_info_form.html"
#     success_message = _("Item %(name)s basic information successfully updated")

#     permission_required = ("inventories.change_item",)

#     def get_initial(self) -> dict[str, Any]:
#         initial = super().get_initial()
#         initial["uom"] = self.get_object().uom.pk
#         return initial

#     def get_success_url(self):
#         return reverse("inventories:items:info", kwargs={"slug": self.object.slug})


# item_info_update_view = ItemInfoUpdateView.as_view()


# class ItemSettingDetailView(LoginRequiredMixin, PermissionRequiredMixin, CoreDetailView):
#     """Partial page to display selected Item's setting page based on given Item's slug."""

#     model = Item
#     template_name = "inventories/items/partials/htmx/_setting.html"

#     permission_required = ("inventories.view_item",)


# item_setting_detail_view = ItemSettingDetailView.as_view()


# class ItemSettingUpdateView(LoginRequiredMixin, PermissionRequiredMixin, CoreUpdateView):
#     """Partial page to update selected Item's setting page based on given Item's slug."""

#     model = Item
#     form_class = SettingUpdateForm
#     template_name = "inventories/items/partials/htmx/_setting_form.html"
#     success_message = _("Item setting successfully updated")

#     permission_required = ("inventories.change_item",)

#     def get_success_url(self):
#         return reverse("inventories:items:setting", kwargs={"slug": self.object.slug})


# item_setting_update_view = ItemSettingUpdateView.as_view()


# class ItemAvailableUOMDropdownListView(LoginRequiredMixin, PermissionRequiredMixin, CoreListView):
#     """Partial page to show all available UOM based on given Item's pk."""

#     model = Item
#     template_name = "inventories/items/partials/htmx/_dropdown.html"

#     permission_required = ("inventories.view_item",)

#     def get_item(self) -> Item:
#         """Get item from item pk or stock pk. We use stock pk in transfer."""
#         item = None
#         item_pk = self.request.GET.get("item", None)
#         stock_pk = self.request.GET.get("stock", None)

#         if item_pk:
#             item = self.model.objects.get(pk=item_pk)
#         elif stock_pk:
#             stock = Stock.objects.get(pk=stock_pk)
#             item = stock.item

#         return item

#     def get_queryset(self) -> QuerySet:
#         item = self.get_item()
#         available_uoms = item.get_available_uom()
#         return available_uoms

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         item = self.get_item()
#         context["blank_choice_dash"] = BLANK_CHOICE_DASH[0][1]
#         context["default_uom"] = item.default_stock_in_uom
#         # UOM dropdown to only show symbol
#         context["uom_symbol"] = True
#         return context


# item_available_uom_dropdown_list_view = ItemAvailableUOMDropdownListView.as_view()


# class ItemHistoryListView(LoginRequiredMixin, PermissionRequiredMixin, SingleTableMixin, CoreListView):
#     """
#     Partial page to show all Actstreams based on given Item's pk as target_object_id.

#     This page use DataTables server side.
#     """

#     model = Action
#     table_class = ItemHistoryDataTables
#     template_name = "inventories/items/partials/htmx/_history.html"

#     # To prevent query all object as it will use ajax call in template
#     object_list = Action.objects.none()

#     permission_required = ("inventories.view_item",)

#     def get_queryset(self):
#         """To prevent query all object."""
#         return self.object_list

#     def get_item(self) -> Item:
#         return Item.objects.get(pk=self.kwargs["pk"])

#     def get_context_data(self, **kwargs) -> Any:
#         context = super().get_context_data(**kwargs)
#         context["object"] = self.get_item()
#         return context


# item_history_list_view = ItemHistoryListView.as_view()


# class ItemHistoryModifiedView(CoreBaseHistoryModifiedView):
#     """
#     Partial pop up view to show the differences in Item history view.
#     """

#     permission_required = ("inventories.view_item",)


# item_history_modified_view = ItemHistoryModifiedView.as_view()


# class ItemBarcodeDetailView(LoginRequiredMixin, PermissionRequiredMixin, CoreDetailView):
#     """Partial page to display selected Item's barcode info based on given Item's slug."""

#     model = Item
#     template_name = "inventories/items/partials/htmx/_barcode.html"

#     permission_required = ("inventories.view_item",)


# item_barcode_detail_view = ItemBarcodeDetailView.as_view()


# class ItemBarcodeUpdateView(CoreBaseBarcodeUpdateView):
#     """Partial page to update selected Item's barcode field based on given Item's slug."""

#     model = Item
#     form_class = ItemBarcodeUpdateForm

#     permission_required = ("inventories.change_item",)

#     def post(self, request: HttpRequest, *args: str, **kwargs: Any) -> HttpResponse:
#         """
#         Overriding parent class post() method to perform some validation before
#         updating the barcode value.
#         """

#         barcode = request.POST.get("barcode")
#         if self.model.objects.filter(barcode=barcode).exists():
#             # Validation to avoid updating duplicated barcode values for different/multiple Items
#             if self.model.objects.get(barcode=barcode).pk != self.get_object().pk:
#                 messages.error(
#                     request,
#                     _("Duplicated barcode value %s detected for other existing Item.") % barcode,
#                 )
#                 return redirect(safe_referrer(request, "."))

#         return super().post(request, *args, **kwargs)

#     def get_success_url(self) -> str:
#         return reverse("inventories:items:detail", kwargs={"slug": self.object.slug})

#     def get_context_data(self, **kwargs) -> dict[str, Any]:
#         context = super().get_context_data(**kwargs)
#         context["update_barcode_url"] = reverse("inventories:items:barcode_update", kwargs={"slug": self.object.slug})
#         return context


# item_barcode_update_view = ItemBarcodeUpdateView.as_view()


# class ItemBarcodeResetView(CoreBaseBarcodeUpdateView):
#     """Partial page to reset selected Item's barcode field based on given Item's slug."""

#     model = Item
#     form_class = ItemBarcodeUpdateForm
#     success_message = _("Item's barcode successfully reset")
#     template_name = None

#     permission_required = ("inventories.change_item",)

#     def get_success_url(self) -> str:
#         return reverse("inventories:items:barcode", kwargs={"slug": self.object.slug})


# item_barcode_reset_view = ItemBarcodeResetView.as_view()


# class ItemRackListView(LoginRequiredMixin, PermissionRequiredMixin, FilterView, SingleTableMixin, CoreListView):
#     """Partial page to show all ItemRack based on given Item's pk."""

#     model = RackStorage
#     template_name = "inventories/items/partials/htmx/_racks.html"

#     table_class = ItemRackStorageDataTables
#     filterset_class = ItemRackStorageFilter

#     # To prevent query all object as it will use ajax call in template
#     object_list = RackStorage.objects.none()

#     permission_required = (
#         "settings.view_warehouse",
#         "rackings.view_rack",
#     )

#     def get_queryset(self):
#         """To prevent query all object."""
#         return self.object_list

#     def get_item(self):
#         """To get item based on given slug."""
#         return Item.objects.get(slug=self.kwargs["slug"])

#     def get_filterset_kwargs(self, filterset_class):
#         kwargs = super().get_filterset_kwargs(filterset_class)
#         self.item = self.get_item()
#         kwargs["item"] = self.item
#         return kwargs

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         context["item"] = self.item
#         return context


# item_rack_list_view = ItemRackListView.as_view()


# class ItemWarehouseStockRackListView(
#     LoginRequiredMixin, PermissionRequiredMixin, FilterView, SingleTableMixin, CoreListView
# ):
#     """Partial page to display item's stock's rack list with given warehouse."""

#     model = Rack
#     template_name = "inventories/items/partials/htmx/_stock_rack_list.html"
#     table_class = ItemStockRackListDataTables
#     filterset_class = ItemStockRackListFilter

#     # To prevent query all object as it will use ajax call in template
#     object_list = Rack.objects.none()

#     permission_required = (
#         "inventories.view_item",
#         "settings.view_warehouse",
#         "rackings.view_rack",
#     )

#     def get_stock(self):
#         """To get stock based on given stock_pk."""
#         return Stock.objects.get(pk=self.kwargs["stock_pk"])

#     def get_warehouse(self):
#         """To get warehouse based on given warehouse_slug."""
#         return Warehouse.objects.get(slug=self.kwargs["warehouse_slug"])

#     def get_queryset(self):
#         """To prevent query all object."""
#         return self.object_list

#     def get_table(self, **kwargs):
#         self.stock = self.get_stock()
#         self.warehouse = self.get_warehouse()

#         table = self.table_class(self.get_queryset(), stock=self.stock, warehouse=self.warehouse, **kwargs)

#         # Override the table's attrs to set the dynamic id
#         table.attrs["id"] = f"warehouse_{self.warehouse.pk}_stock_{self.stock.pk}_rack_datatables"

#         return table

#     def get_context_data(self, **kwargs) -> dict[str, Any]:
#         context = super().get_context_data(**kwargs)
#         context["stock"] = self.stock
#         context["warehouse"] = self.warehouse
#         return context


# item_warehouse_stock_rack_list_view = ItemWarehouseStockRackListView.as_view()


# #####################
# # FOR JSON response #
# #####################


# class ItemScanBarcodeView(View):
#     """
#     Class-based view to return Item's code based on scanned barcode
#     value when WROItem is being scanned by picker.
#     """

#     def get(self, request, barcode: str) -> JsonResponse:
#         if Item.objects.filter(barcode=barcode).exists():
#             item = Item.objects.get(barcode=barcode)
#             response = JsonResponse({"code": item.code})
#         else:
#             response = JsonResponse({"code": None})

#         return response


# item_scan_barcode_view = ItemScanBarcodeView.as_view()


# def item_dropdown_list_view(request: HttpRequest) -> JsonResponse:
#     """
#     FBV to return paginated Item queryset in the form of JSONResponse, based on query params:
#     * q = Refers to search term when user keys in select2 input field
#     * page = Refers to which "page"/scroll based on the paginated queryset

#     Example:

#     - Sample output based on API call for endpoint:
#     {{baseUrl}}inventories/items/dropdown/items/?q=pearl&page=2

#     - Returns:
#     {
#       "results": [
#         {
#           "id": 20019,
#           "text": "MORPHO 9300 :: GOLD PEARL (25KG/CTN)"
#         },
#         {
#           "id": 20017,
#           "text": "MORPHO 225** :: BLUE PEARL (5KG/PACK)"
#         },
#         {
#           "id": 20015,
#           "text": "MORPHO 9219** :: PURPLE PEARL (5KG/PACK)"
#         },
#         ...
#       ],
#       "pagination": {
#         "more": true
#       }
#     }
#     """

#     page = request.GET.get("page", 1)

#     if request.GET.get("q", None) is not None:
#         items = Item.objects.filter(
#             Q(code__icontains=request.GET.get("q")) | Q(name__icontains=request.GET.get("q")),
#         )
#     else:
#         items = Item.objects.all()

#     results = [{"id": item.pk, "text": str(item)} for item in items]
#     paginator = Paginator(results, settings.LIMIT_AUTOCOMPLETE_RESULTS_PER_SCROLL)
#     results = paginator.get_page(page).object_list

#     # Determine whether to end the "infinite scroll"
#     pagination = True if len(results) >= settings.LIMIT_AUTOCOMPLETE_RESULTS_PER_SCROLL else False

#     return JsonResponse(
#         {
#             "results": results,
#             "pagination": {"more": pagination},
#         }
#     )


# class ExportAllItemStocksToXlsxView(ExportMixin, ItemStockListDataTablesView):
#     """
#     Class-based view (CBV) to export entire filtered queryset without pagination.

#     Take note:
#     This view behaves exactly like the default datatables Excel button columns,
#     with only difference that it simply prints out the ALL rows based on the
#     datatable view class that you inherit.

#     If there needs a case where you have to further customize the layout/styling
#     of certain rows, then you would have to create a new function-based view (FBV)
#     for more in-depth customization/override purposes.
#     """

#     # When we export entire/all rows, we disable pagination
#     table_pagination = False

#     def get_export_filename(self, export_format) -> str:
#         """Override filename."""

#         item = Item.objects.get(slug=self.kwargs["slug"])
#         wms = settings.PROJECT_NAME
#         self.export_name = f"{wms} Items {item.name}"

#         return f"{self.export_name}.{export_format}"


# export_all_item_stocks_to_xlsx_view = ExportAllItemStocksToXlsxView.as_view()

# from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
# from django.db.models import Count, Q
from django.db.models import QuerySet
from django.urls import reverse_lazy, reverse

# from django_filters.views import FilterView
# from django_tables2 import SingleTableMixin

# from wms.cores.views import CoreDataTablesView, CoreDetailDataTablesView, CoreDetailView, CoreListView

# from wms.apps.rackings.forms import FilterRackForm
# from wms.apps.settings.models import Warehouse

# from ..filters import StockFilter, WarehouseFilter, WarehouseItemsFilter, WarehouseRacksFilter
# from ..models import Stock
# from ..tables import (
#     StockSideTable,
#     WarehouseItemsTable,
#     WarehouseRacksSideTable,
#     WarehouseRacksTable,
#     WarehouseSideTable,
#     WarehouseTable,
# )

from wms.cores.mixins import ExportTableMixin
from wms.cores.views import CoreSingleTableView, CoreDataTableDetailView, CoreDetailView

from wms.apps.inventories.models import Item
from wms.apps.settings.models import Warehouse
from wms.apps.rackings.models import Rack

from wms.apps.inventories.filters import RackFilter
from wms.apps.inventories.tables import WarehouseTable, WarehouseItemsTable, WarehouseDetailTable, WarehouseRacksTable


###########################################################################
# List View
###########################################################################


class WarehouseListView(ExportTableMixin, CoreSingleTableView):
    """
    View for displaying and managing the list of Warehouses.
    Supports:
    - Pagination
    - Column visibility
    - Searching
    - Export functionality
    - HTMX partial updates
    """
    model = Warehouse
    table_class = WarehouseTable
    template_name = "inventories/warehouses/mains/list.html"
    partial_template_name = "inventories/warehouses/partials/table.html"  # Optional, for HTMX
    queryset = Warehouse.objects.all()
    # filterset_class = WarehouseFilter

    # Search configuration
    search_fields = ["name"]

    # Export configuration
    export_name = "warehouses"
    export_permission = []  # Empty list means no specific permissions required


###########################################################################
# DetailView | Detail Tab
###########################################################################


class WarehouseDetailHomeView(CoreDetailView):
    """
    Inventories > Warehouses > 2nd view (above's tabs)
    """
    model = Warehouse
    template_name = 'inventories/warehouses/mains/home.html'
    context_object_name = "warehouse"


class WarehouseDetailView(CoreDetailView):
    """
    Inventories > Warehouses > 3rd view (inside tab's detail data)
    """
    model = Warehouse
    template_name = 'inventories/warehouses/partials/detail.html'
    context_object_name = "warehouse"


# WarehouseItemListView lives in WarehouseDetailView's datatable
class WarehouseItemListView(ExportTableMixin, CoreSingleTableView):
    """

    Inventories > Warehouses > 4th view (inside tab's detail data's below datatable)

    View for displaying and managing the list of Items.
    Supports:
    - Pagination
    - Column visibility
    - Searching
    - Export functionality
    - HTMX partial updates
    """
    model = Item
    table_class = WarehouseItemsTable
    template_name = "cores/datatable_detail_view_table.html"
    partial_template_name = "cores/table_partial.html"
    # filterset_class = ItemFilter

    # Search configuration
    search_fields = ["code", "name",]

    # Export configuration
    export_name = "warehouse_items"
    export_permission = []  # Empty list means no specific permissions required

    # HTMX configuration, override default to prevent htmx target conflict
    # Map this with own partial element id, use this when under single page there is more than 1 htmx table
    htmx_target = 'table-content-partial'

    def get_warehouse(self):
        return Warehouse.objects.get(pk=self.kwargs["pk"])

    def get_queryset(self) -> QuerySet:
        self.warehouse = self.get_warehouse()
        queryset = super().get_queryset()
        return (
            queryset.select_related("consignor")
            .prefetch_related("categories")
            .filter(warehouses=self.warehouse)
            .distinct()
        )

    def get_table_kwargs(self):
        """Pass value to table."""
        return {"warehouse": self.warehouse}

    def get_htmx_base_url(self):
        """
        Override to provide the dynamic URL based on the current item's PK.
        This URL will be used for all HTMX requests and querystring operations.
        """
        pk = self.kwargs.get('pk')
        if pk:
            return reverse("inventories:warehouses:item_list", kwargs={"pk": pk})
        return None


###########################################################################
# DetailView | Rack Tab
###########################################################################

class WarehouseRackView(CoreDetailView):
    """
    Inventories > Warehouses > Rack Tab 5th view (inside tab's content view)
    """
    model = Warehouse
    template_name = 'inventories/warehouses/partials/rack.html'

    # def get_context_data(self, **kwargs):
    #     context = super().get_context_data(**kwargs)
    #     context["warehouses_stock"] = Transaction.objects.sum_group_warehouse_by_item(
    #         item=self.object,
    #         defect=None  # Include all warehouses [defect or non defect]
    #     )

    #     context["stocks_total"] = Transaction.objects.stock_total_by_item(item=self.object)
    #     context["stocks_count"] = self.object.stock_set.count()
    #     return context


# WarehouseItemListView lives in WarehouseDetailView's datatable
class WarehouseRackListView(ExportTableMixin, CoreSingleTableView):
    """

    Inventories > Warehouses > Rack > 6th view (inside tab's detail data's below datatable)

    View for displaying and managing the list of Items.
    Supports:
    - Pagination
    - Column visibility
    - Searching
    - Export functionality
    - HTMX partial updates
    """
    model = Rack
    table_class = WarehouseRacksTable
    template_name = "cores/datatable_detail_view_table.html"
    partial_template_name = "cores/table_partial.html"
    filterset_class = RackFilter

    # Search configuration
    search_fields = ["full_name",]

    # Export configuration
    export_name = "warehouse_racks"
    export_permission = []  # Empty list means no specific permissions required

    # HTMX configuration, override default to prevent htmx target conflict
    # Map this with own partial element id, use this when under single page there is more than 1 htmx table
    htmx_target = 'table-content-partial'

    def get_warehouse(self):
        return Warehouse.objects.get(pk=self.kwargs["pk"])

    def get_queryset(self) -> QuerySet:
        self.warehouse = self.get_warehouse()
        queryset = super().get_queryset()
        return (
            queryset.select_related("warehouse")
            .filter(warehouse=self.warehouse)
        )

    def get_table_kwargs(self):
        """Pass value to table."""
        return {"warehouse": self.warehouse}

    def get_filterset(self, filterset_class):
        data = self.request.GET.copy()

        # Only set the default if not already present in query params
        if 'rack_type' not in data:
            data.setlist('rack_type', [Rack.RackType.PALLET])

        return filterset_class(
            data=data,
            queryset=self.model.objects.filter(warehouse=self.warehouse),  # use base queryset, not filtered
            request=self.request,
        )

    def get_htmx_base_url(self):
        """
        Override to provide the dynamic URL based on the current item's PK.
        This URL will be used for all HTMX requests and querystring operations.
        """
        pk = self.kwargs.get('pk')
        if pk:
            return reverse("inventories:warehouses:rack_list", kwargs={"pk": pk})
        return None


###########################################################################
# DetailView | Main Panel
###########################################################################

class WarehouseDataTableDetailView(CoreDataTableDetailView):
    """
    Inventories > Warehouses > 1st view
    """
    model = Warehouse
    table_class = WarehouseDetailTable
    context_object_name = "warehouse"
    partial_view = WarehouseDetailHomeView
    search_fields = ["name"]
    # filterset_class = WarehouseDataTableFilter


# class WarehouseListView(LoginRequiredMixin, PermissionRequiredMixin, SingleTableMixin, CoreListView):
#     """Page to show all Warehouse."""

#     model = Warehouse
#     template_name = "inventories/warehouses/list.html"

#     table_class = WarehouseTable
#     table_pagination = False

#     header_title = "Warehouses"
#     selected_page = "inventories"
#     selected_subpage = "warehouses"

#     permission_required = (
#         "settings.view_warehouse",
#         "inventories.view_item",
#     )

#     def get_queryset(self):
#         if self.request.user.is_superuser is True:
#             return self.model.objects.filter(is_storage=True)
#         else:
#             request_user_warehouse_pk_list = list(self.request.user.warehouses.values_list("pk", flat=True))
#             return self.model.objects.filter(pk__in=request_user_warehouse_pk_list, is_storage=True)


# warehouse_list_view = WarehouseListView.as_view()


# class WarehouseDetailView(LoginRequiredMixin, PermissionRequiredMixin, CoreDetailDataTablesView):
#     """Page to display selected Warehouse based on given Warehouse's pk.
#     This page use DataTables server side."""

#     model = Warehouse
#     template_name = "inventories/warehouses/detail.html"

#     table_class = WarehouseSideTable

#     header_title = "Warehouses"
#     selected_page = "inventories"
#     selected_subpage = "warehouses"

#     permission_required = (
#         "settings.view_warehouse",
#         "inventories.view_item",
#     )

#     def get_queryset(self):
#         if self.request.user.is_superuser is True:
#             return self.model.objects.filter(is_storage=True)
#         else:
#             request_user_warehouse_pk_list = list(self.request.user.warehouses.values_list("pk", flat=True))
#             return self.model.objects.filter(pk__in=request_user_warehouse_pk_list, is_storage=True)


# warehouse_detail_view = WarehouseDetailView.as_view()


# class WarehouseStockDetailView(LoginRequiredMixin, PermissionRequiredMixin, SingleTableMixin, CoreDetailView):
#     """Page to display selected Stock based on given Stock's pk.
#     This page use DataTables server side."""

#     model = Stock
#     template_name = "inventories/warehouses/stock_detail.html"

#     table_class = StockSideTable

#     header_title = "Stocks"
#     selected_page = "inventories"
#     selected_subpage = "warehouses"

#     permission_required = (
#         "settings.view_warehouse",
#         "inventories.view_item",
#         "inventories.view_stock",
#     )

#     def get_object(self):
#         try:
#             stock = Stock.objects.get(
#                 warehouse__slug=self.kwargs["warehouse_slug"],
#                 item__slug=self.kwargs["item_slug"],
#                 batch_no="N/A",
#                 expiry_date=None,
#             )
#         except Stock.DoesNotExist:
#             stock = Stock.objects.filter(
#                 warehouse__slug=self.kwargs["warehouse_slug"],
#                 item__slug=self.kwargs["item_slug"],
#             ).first()

#         return stock

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         context["warehouse_slug"] = self.kwargs["warehouse_slug"]
#         context["item_slug"] = self.kwargs["item_slug"]
#         context["header_title"] = self.header_title + f" - {self.object.warehouse.name} :: {self.object.item.code}"

#         return context


# warehouse_stock_detail_view = WarehouseStockDetailView.as_view()


# class WarehouseRackDetailView(LoginRequiredMixin, PermissionRequiredMixin, CoreDetailDataTablesView):
#     """Page to display selected Warehouse's Rack based on given Warehouse's slug and Rack's pk."""

#     model = Rack
#     template_name = "inventories/warehouses/rack_detail.html"

#     table_class = WarehouseRacksSideTable

#     header_title = "Warehouses :: Racks"
#     selected_page = "inventories"
#     selected_subpage = "warehouses"

#     permission_required = (
#         "settings.view_warehouse",
#         "inventories.view_item",
#         "rackings.view_rack",
#     )

#     def get_object(self):
#         try:
#             rack = Rack.objects.get(
#                 warehouse__slug=self.kwargs["warehouse_slug"],
#                 pk=self.kwargs["rack_pk"],
#             )
#         except Rack.DoesNotExist:
#             rack = None

#         return rack

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         context["filter_form"] = FilterRackForm()
#         context["can_stock_in_out"] = False

#         if self.get_object() is not None:
#             if self.get_object().is_leaf():
#                 context["can_stock_in_out"] = True

#         return context


# warehouse_racks_detail_view = WarehouseRackDetailView.as_view()


# ############
# # FOR HTMX #
# ############


# class WarehouseItemsListView(LoginRequiredMixin, PermissionRequiredMixin, SingleTableMixin, CoreListView):
#     """Partial page to show all WarehouseItem based on given Warehouse's pk."""

#     model = Item
#     template_name = "inventories/warehouses/partials/htmx/_items.html"

#     table_class = WarehouseItemsTable

#     # To prevent query all object as it will use ajax call in template
#     object_list = Item.objects.none()

#     permission_required = (
#         "settings.view_warehouse",
#         "inventories.view_item",
#     )

#     def get_queryset(self):
#         """To prevent query all object."""
#         return self.object_list

#     def get_warehouse(self):
#         return Warehouse.objects.get(slug=self.kwargs["slug"])

#     def get_table_kwargs(self):
#         """Pass value to table."""
#         self.warehouse = self.get_warehouse()
#         return {"warehouse": self.warehouse}

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)

#         context["warehouse"] = self.warehouse
#         context["items_count"] = self.model.objects.filter(warehouses=self.warehouse).distinct().count()
#         return context


# warehouse_items_list_view = WarehouseItemsListView.as_view()


# class WarehouseRacksListView(LoginRequiredMixin, PermissionRequiredMixin, FilterView, SingleTableMixin, CoreListView):
#     """Partial page to show all WarehouseRack based on given Warehouse's pk."""

#     model = Rack
#     template_name = "inventories/warehouses/partials/htmx/_racks.html"

#     table_class = WarehouseRacksTable
#     filterset_class = WarehouseRacksFilter

#     # To prevent query all object as it will use ajax call in template
#     object_list = Rack.objects.none()

#     permission_required = (
#         "settings.view_warehouse",
#         "rackings.view_rack",
#     )

#     def get_queryset(self):
#         """To prevent query all object."""
#         return self.object_list

#     def get_warehouse(self):
#         return Warehouse.objects.get(slug=self.kwargs["slug"])

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)

#         context["warehouse"] = self.get_warehouse()
#         context["racks_count"] = self.model.objects.filter(warehouse=self.get_warehouse()).distinct().count()
#         return context


# warehouse_racks_list_view = WarehouseRacksListView.as_view()


# ##################
# # FOR DataTables #
# ##################


# class WarehouseDetailDataTablesView(LoginRequiredMixin, PermissionRequiredMixin, CoreDataTablesView):
#     """JSON for DataTables in detail page."""

#     model = Warehouse
#     table_class = WarehouseSideTable
#     filterset_class = WarehouseFilter

#     permission_required = (
#         "settings.view_warehouse",
#         "inventories.view_item",
#     )

#     def get_queryset(self):
#         if self.request.user.is_superuser is True:
#             return self.model.objects.filter(is_storage=True)
#         else:
#             request_user_warehouse_pk_list = list(self.request.user.warehouses.values_list("pk", flat=True))
#             return self.model.objects.filter(pk__in=request_user_warehouse_pk_list, is_storage=True)


# warehouse_detail_datatables_view = WarehouseDetailDataTablesView.as_view()


# class WarehouseItemsDataTablesView(LoginRequiredMixin, PermissionRequiredMixin, CoreDataTablesView):
#     """JSON for DataTables in list page."""

#     model = Item
#     table_class = WarehouseItemsTable
#     filterset_class = WarehouseItemsFilter

#     permission_required = (
#         "settings.view_warehouse",
#         "inventories.view_item",
#     )

#     def get_warehouse(self):
#         return Warehouse.objects.get(slug=self.kwargs["slug"])

#     def get_queryset(self):
#         self.warehouse = self.get_warehouse()
#         return (
#             self.model.objects.select_related("consignor")
#             .prefetch_related("categories")
#             .filter(warehouses=self.warehouse)
#             .distinct()
#         )

#     def get_table_kwargs(self):
#         """Pass value to table."""
#         return {"warehouse": self.warehouse}


# warehouse_items_datatables_view = WarehouseItemsDataTablesView.as_view()


# class WarehouseStockDetailDataTablesView(LoginRequiredMixin, PermissionRequiredMixin, CoreDataTablesView):
#     """JSON for DataTables in detail page."""

#     model = Stock
#     table_class = StockSideTable
#     filterset_class = StockFilter

#     permission_required = (
#         "settings.view_warehouse",
#         "inventories.view_item",
#         "inventories.view_stock",
#     )

#     def get_queryset(self):
#         return self.model.objects.filter(
#             warehouse__slug=self.kwargs["warehouse_slug"], item__slug=self.kwargs["item_slug"]
#         )


# warehouse_stock_detail_datatables_view = WarehouseStockDetailDataTablesView.as_view()


# class WarehouseRacksDataTablesView(LoginRequiredMixin, PermissionRequiredMixin, CoreDataTablesView):
#     """JSON for DataTables in list page."""

#     model = Rack
#     table_class = WarehouseRacksTable
#     filterset_class = WarehouseRacksFilter

#     permission_required = (
#         "settings.view_warehouse",
#         "rackings.view_rack",
#     )

#     def get_warehouse(self):
#         return Warehouse.objects.get(slug=self.kwargs["slug"])

#     def get_queryset(self):
#         return self.model.objects.filter(warehouse=self.get_warehouse()).annotate(
#             stock_count=Count("rackbalance", filter=Q(rackbalance__balance__gt=0))
#         )


# warehouse_racks_datatables_view = WarehouseRacksDataTablesView.as_view()


# class WarehouseRacksDetailDataTablesView(LoginRequiredMixin, PermissionRequiredMixin, CoreDataTablesView):
#     """JSON for DataTables in detail page."""

#     model = Rack
#     table_class = WarehouseRacksSideTable
#     filterset_class = WarehouseRacksFilter

#     permission_required = (
#         "settings.view_warehouse",
#         "rackings.view_rack",
#     )

#     def get_warehouse(self):
#         return Warehouse.objects.get(slug=self.kwargs["warehouse_slug"])

#     def get_queryset(self):
#         return self.model.objects.filter(warehouse=self.get_warehouse()).annotate(
#             stock_count=Count("rackbalance", filter=Q(rackbalance__balance__gt=0))
#         )


# warehouse_racks_detail_datatables_view = WarehouseRacksDetailDataTablesView.as_view()

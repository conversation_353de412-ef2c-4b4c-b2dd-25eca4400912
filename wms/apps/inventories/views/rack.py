# import calendar
# from typing import Any

# from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
# from django.db.models import F, QuerySet
# from django.http import HttpRequest, HttpResponse
# from django.urls import reverse
# from django.utils import timezone
# from django.utils.formats import date_format
from django.utils.translation import gettext_lazy as _
# from django.views.generic.dates import MonthArchiveView

# from django_filters.views import FilterView
# from django_tables2 import SingleTableMixin

from wms.cores.utils import localtime_now
# from wms.cores.views import CoreDataTablesView, CoreDetailView, CoreListView
# from wms.cores.views.base import CoreCreateView

# from wms.apps.inventories.forms import RackStockInOutForm
# from wms.apps.rackings.models import Rack, RackBalance, RackTransaction
# from wms.apps.rackings.models.rack import RackStorage
# from wms.apps.settings.models import Warehouse

# from ..filters import RackTransactionInOutFilter, WarehouseRacksStockFilter
# from ..models import Stock
# from ..tables import RackTransactionInOutTable, WarehouseRacksStockTable

from django.utils import timezone
from django.shortcuts import get_object_or_404
from django.db.models import QuerySet
from django.urls import reverse_lazy, reverse

from wms.apps.inventories.filters import (
    RackFilter,
    # RackDataTableFilter,
    RackStorageFilter,
    # RackTransactionFilter,
)
from wms.apps.inventories.tables import (
    RackDetailTable,
    RackStorageTable,
    RackStorageDetailTable,
    RackTransactionTable,
)
from wms.apps.inventories.forms import (
    OuterRackTransactionAdjustmentForm,
    RackTransactionAdjustmentForm,
    RackTransactionTransferForm,
)


from wms.cores.mixins import ExportTableMixin

from wms.cores.views import CoreSingleTableView, CoreDataTableDetailView, CoreCreateView, CoreDetailView, CoreUpdateView

from wms.apps.rackings.models import Rack, RackStorage, RackTransaction
# from wms.apps.inventories.models import Item, Transaction, Stock


class RackStorageListView(ExportTableMixin, CoreSingleTableView):
    """

    Racks > 4th view (inside tab's detail data's below datatable)

    View for displaying and managing the list of Items.
    Supports:
    - Pagination
    - Column visibility
    - Searching
    - Export functionality
    - HTMX partial updates
    """
    model = RackStorage
    table_class = RackStorageTable
    template_name = "inventories/racks/partials/rackstorage_list.html"
    partial_template_name = "inventories/racks/partials/table.html"
    filterset_class = RackStorageFilter

    # Search configuration
    search_fields = [
        "rack__full_name",
        "stock__item__name",
        "stock__item__code"
        "stock__batch_no"
    ]

    # Export configuration
    export_name = "rack_storages"
    export_permission = []  # Empty list means no specific permissions required

    # HTMX configuration, override default to prevent htmx target conflict
    # Map this with own partial element id, use this when under single page there is more than 1 htmx table
    htmx_target = 'table-content-partial'

    def get_queryset(self) -> QuerySet:
        queryset = super().get_queryset()
        # Apply additional filter for this specific view

        rack = Rack.objects.get(pk=self.kwargs.get('pk'))
        descendants_and_self_list = rack.get_descendants_and_self()

        return queryset.filter(rack__in=descendants_and_self_list)

    def get_htmx_base_url(self):
        """
        Override to provide the dynamic URL based on the current item's PK.
        This URL will be used for all HTMX requests and querystring operations.
        """
        pk = self.kwargs.get('pk')
        if pk:
            return reverse("inventories:racks:rackstorage_list", kwargs={"pk": pk})
        return None


class RackDetailHomeView(CoreDetailView):
    """
    Racks > 2nd view (above's tabs)
    """
    model = Rack
    template_name = 'inventories/racks/mains/home.html'


class RackDetailView(CoreDetailView):
    """
    Racks > 3rd view (inside tab's detail data)
    """
    model = Rack
    template_name = 'inventories/racks/partials/detail.html'


class RackDataTableDetailView(CoreDataTableDetailView):
    """
    Racks > 1st view
    """
    model = Rack
    table_class = RackDetailTable
    context_object_name = "rack"
    partial_view = RackDetailHomeView
    search_fields = ["full_name"]
    filterset_class = RackFilter


class OuterRackTransactionAdjustmentView(CoreCreateView):
    """
    Rack's outer adjustment view
    - fields need to fill in:
        - Rack(pallet)
        - Item
        - batch_no
        - expiry_date
        - warehouse
    """
    model = RackTransaction
    form_class = OuterRackTransactionAdjustmentForm
    template_name = "inventories/rack_transactions/forms/outer_adjustment_form.html"
    # success_url = "inventories:rackstorage:panel"
    section_title = "Adjust Rack Transaction"
    section_desc = ""
    submit_text = "Save"
    cancel_url = "inventories:warehouses:list"

    def dispatch(self, request, *args, **kwargs):
        """ dispatch is the earliest function to be called first."""
        self.rack = get_object_or_404(Rack, pk=self.kwargs["pk"])
        return super().dispatch(request, *args, **kwargs)

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs["pk"]  = self.kwargs.get("pk")

        return kwargs

    def get_initial(self):
        initial = super().get_initial()

        # Only set issued_by for new objects (CreateView)
        initial["type"] = RackTransaction.Type.ADJUSTMENT
        initial["rack"] = self.rack
        return initial

    def get_success_url(self):
        rack_pk = self.kwargs.get("pk")  # assuming you pass this via URL
        return reverse("inventories:racks:panel", kwargs={"pk": rack_pk})


###########################################################################
# RackStorage
###########################################################################


class RackTransactionListView(ExportTableMixin, CoreSingleTableView):
    """

    RackStorages > 4th view (inside tab's detail data's below datatable)

    View for displaying and managing the list of Items.
    Supports:
    - Pagination
    - Column visibility
    - Searching
    - Export functionality
    - HTMX partial updates
    """
    model = RackTransaction
    table_class = RackTransactionTable
    template_name = "inventories/rackstorages/partials/rack_transaction_list.html"
    partial_template_name = "inventories/rackstorages/partials/table.html"
    # filterset_class = RackTransactionFilter

    # Search configuration
    search_fields = [
        "rackstorage__rack__full_name",
        "rackstorage__stock__item__name",
        "rackstorage__stock__item__code"
        "rackstorage__stock__batch_no"
    ]

    # Export configuration
    export_name = "rack_transactions"
    export_permission = []  # Empty list means no specific permissions required

    # HTMX configuration, override default to prevent htmx target conflict
    # Map this with own partial element id, use this when under single page there is more than 1 htmx table
    htmx_target = 'table-content-partial'

    def get_queryset(self) -> QuerySet:
        queryset = super().get_queryset()
        # Apply additional filter for this specific view

        rackstorage = RackStorage.objects.get(pk=self.kwargs.get('pk'))

        return queryset.filter(
            rackstorage=rackstorage,
            is_reserved=False
        )

    def get_htmx_base_url(self):
        """
        Override to provide the dynamic URL based on the current item's PK.
        This URL will be used for all HTMX requests and querystring operations.
        """
        pk = self.kwargs.get('pk')
        if pk:
            return reverse("inventories:rackstorages:rack_transaction_list", kwargs={"pk": pk})
        return None


class RackStorageDetailHomeView(CoreDetailView):
    """
    RackStorages > 2nd view (above's tabs)
    """
    model = RackStorage
    template_name = 'inventories/rackstorages/mains/home.html'


class RackStorageDetailView(CoreDetailView):
    """
    RackStorages > 3rd view (inside tab's detail data)
    """
    model = RackStorage
    template_name = 'inventories/rackstorages/partials/detail.html'


class RackStorageDataTableDetailView(CoreDataTableDetailView):
    """
    RackStorages > 1st view
    """
    model = RackStorage
    table_class = RackStorageDetailTable
    context_object_name = "rackstorage"
    partial_view = RackStorageDetailHomeView
    search_fields = ["rack__full_name"]
    filterset_class = RackStorageFilter

class RackTransactionAdjustmentView(CoreCreateView):
    model = RackTransaction
    form_class = RackTransactionAdjustmentForm
    template_name = "inventories/rack_transactions/forms/adjustment_form.html"
    # success_url = "inventories:rackstorage:panel"
    section_title = "Adjust Rack Transaction"
    section_desc = ""
    submit_text = "Save"
    cancel_url = "inventories:warehouses:list"

    def dispatch(self, request, *args, **kwargs):
        """ dispatch is the earliest function to be called first."""
        self.rackstorage = get_object_or_404(RackStorage, pk=self.kwargs["pk"])
        return super().dispatch(request, *args, **kwargs)

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs["pk"]  = self.kwargs.get("pk")

        return kwargs

    def get_initial(self):
        initial = super().get_initial()

        # Only set issued_by for new objects (CreateView)
        initial["type"] = RackTransaction.Type.ADJUSTMENT
        initial["rackstorage"] = self.rackstorage
        return initial

    def get_success_url(self):
        rackstorage_pk = self.kwargs.get("pk")  # assuming you pass this via URL
        return reverse("inventories:rackstorages:panel", kwargs={"pk": rackstorage_pk})


class RackTransactionTransferView(CoreCreateView):
    model = RackTransaction
    form_class = RackTransactionTransferForm
    template_name = "inventories/rack_transactions/forms/transfer_form.html"
    # success_url = "inventories:rackstorage:panel"
    section_title = "Transfer Rack Transaction"
    section_desc = ""
    submit_text = "Save"
    cancel_url = "inventories:warehouses:list"

    def dispatch(self, request, *args, **kwargs):
        """ dispatch is the earliest function to be called first."""
        self.rackstorage = get_object_or_404(RackStorage, pk=self.kwargs["pk"])
        return super().dispatch(request, *args, **kwargs)

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs["pk"]  = self.kwargs.get("pk")

        return kwargs

    def get_initial(self):
        initial = super().get_initial()

        # Only set issued_by for new objects (CreateView)
        initial["type"] = RackTransaction.Type.TRANSFER_OUT
        initial["rackstorage"] = self.rackstorage
        return initial

    def form_valid(self, form):
        # Save the object
        # 1) this is to make sure the transfer_out transaction from the rack happened.
        response = super().form_valid(form)

        # 2) then the addition of quantity to transfer_to will be handle here.
        transfer_to = form.cleaned_data["transfer_to"]
        type = form.cleaned_data["type"]
        rackstorage = form.cleaned_data["rackstorage"]
        transaction_datetime = form.cleaned_data["transaction_datetime"]
        quantity = form.cleaned_data["quantity"]
        remark = form.cleaned_data["remark"]

        destination_rackstorage, _ = RackStorage.objects.get_or_create(
            rack=transfer_to,
            stock=rackstorage.stock,
        )

        rack_transaction, _ = RackTransaction.objects.get_or_create(
            type=RackTransaction.Type.TRANSFER_IN,
            rackstorage=destination_rackstorage,
            transaction_datetime=transaction_datetime,
            quantity=abs(quantity),  # making sure the quantity to be transfer_to is positive/addition
            remark=remark,
        )
        return response

    def get_success_url(self):
        rackstorage_pk = self.kwargs.get("pk")  # assuming you pass this via URL
        return reverse("inventories:rackstorages:panel", kwargs={"pk": rackstorage_pk})


# class ItemCreateView(CoreCreateView):
#     model = Item
#     form_class = ItemForm
#     template_name = "inventories/items/item_form.html"
#     success_url = "inventories:items:panel"
#     section_title = "Create Item"
#     section_desc = ""
#     submit_text = "Save"
#     cancel_url = "inventories:items:list"

    # def get_queryset(self):

    #     stock = self.model.objects.get(pk=self.kwargs["pk"])

    #     return self.model.objects.filter(
    #         warehouse=stock.warehouse,
    #         item=stock.item,
    #     )

    # def get_filterset(self, filterset_class):
    #     """
    #     To default the filter value especially on stock_on_hand filter field
    #     """
    #     data = self.request.GET.copy()

    #     # Only set initial if not already filtered
    #     if "stock_on_hand" not in data:
    #         data["stock_on_hand"] = "gt_zero"

    #     return filterset_class(data=data, queryset=self.get_queryset(), request=self.request)


# class RackTransactionDetailView(LoginRequiredMixin, PermissionRequiredMixin, SingleTableMixin, CoreDetailView):
#     """
#     Page to display selected Stock based on given Rack's pk.
#     Also accepts optional year & month in the path parameters:
#     {{ url }}/inventories/racks/<int:rack_pk>/stock/<int:pk>/year/<int:year>/month/<int:month>/

#     This page uses DataTables server side.
#     """

#     model = Stock
#     template_name = "inventories/racks/transaction_detail.html"

#     header_title = "Warehouses :: Racks :: Stock Transactions"
#     selected_page = "inventories"
#     selected_subpage = "warehouses"

#     permission_required = (
#         "settings.view_warehouse",
#         "inventories.view_item",
#         "rackings.view_rack",
#     )

#     def get_rack(self) -> Rack:
#         return Rack.objects.get(pk=self.kwargs["rack_pk"])

#     def get_rack_balance(self) -> RackBalance:
#         return RackBalance.objects.get(rack=self.get_rack(), stock=self.get_object())

#     def get_context_data(self, **kwargs) -> dict[str, Any]:
#         context = super().get_context_data(**kwargs)
#         context["rack"] = self.get_rack()
#         context["rack_balance"] = self.get_rack_balance()
#         context["can_stock_in_out"] = self.get_rack().is_leaf()
#         context["year"] = self.kwargs.get("year", None)
#         context["month"] = self.kwargs.get("month", None)
#         return context


# rack_transaction_detail_view = RackTransactionDetailView.as_view()


# ##################
# # FOR DataTables #
# ##################


# class WarehouseRacksStockDataTablesView(LoginRequiredMixin, PermissionRequiredMixin, CoreDataTablesView):
#     """JSON for DataTables in list page."""

#     model = Stock
#     table_class = WarehouseRacksStockTable
#     filterset_class = WarehouseRacksStockFilter

#     permission_required = (
#         "rackings.view_rack",
#         "inventories.view_stock",
#         "settings.view_warehouse",
#     )

#     def get_rack(self):
#         return Rack.objects.get(pk=self.kwargs["rack_pk"])

#     def get_queryset(self):
#         self.rack = self.get_rack()

#         return (
#             self.model.objects.select_related("warehouse")
#             .prefetch_related("rackbalance_set")
#             .filter(
#                 warehouse__slug=self.kwargs["warehouse_slug"], rackbalance__rack=self.rack, rackbalance__balance__gt=0
#             )
#             .annotate(rack_balance=F("rackbalance__balance"))
#         )

#     def get_table_kwargs(self):
#         """Pass value to table."""
#         return {"rack": self.rack}


# warehouse_racks_stock_datatables_view = WarehouseRacksStockDataTablesView.as_view()


# class RackTransactionInOutDataTablesView(LoginRequiredMixin, PermissionRequiredMixin, CoreDataTablesView):
#     """JSON for Stock-level's In/Out RackTransaction DataTables in list view based on selected Stock & Rack."""

#     model = RackTransaction
#     table_class = RackTransactionInOutTable
#     filterset_class = RackTransactionInOutFilter

#     permission_required = (
#         "rackings.view_rack",
#         "inventories.view_stock",
#         "settings.view_warehouse",
#     )

#     def get_rack(self) -> Rack:
#         return Rack.objects.prefetch_related("rackstorage_set").get(pk=self.kwargs["rack_pk"])

#     def get_stock(self) -> Stock:
#         return Stock.objects.get(pk=self.kwargs["pk"])

#     def get_queryset(self) -> QuerySet[RackTransaction]:
#         rack = self.get_rack()
#         stock = self.get_stock()

#         if rack.is_leaf() is True:
#             qs = self.model.objects.filter(rackstorage__rack=rack, rackstorage__stock=stock)
#         else:
#             # For non-leaf nodes, need to includes all the RackTransactions
#             # under the descendants rack nodes.
#             descendants = rack.get_descendants()

#             qs = self.model.objects.filter(rackstorage__rack__in=descendants, rackstorage__stock=stock)

#         return qs

#     def get_table_kwargs(self) -> dict[str, Any]:
#         """Pass value to table."""
#         table = super().get_table_kwargs()
#         table["rack"] = self.get_rack()
#         table["year"] = self.kwargs.get("year", None)
#         table["month"] = self.kwargs.get("month", None)
#         return table


# rack_transaction_in_out_datatables_view = RackTransactionInOutDataTablesView.as_view()


# class RackTransactionMonthlyInOutDataTablesView(
#     LoginRequiredMixin,
#     PermissionRequiredMixin,
#     CoreDataTablesView,
#     MonthArchiveView,
# ):
#     """
#     JSON for Item-level's In/Out Transaction DataTables in list view based on selected
#     Item & Rack, with year and month.
#     """

#     model = RackTransaction
#     table_class = RackTransactionInOutTable
#     filterset_class = RackTransactionInOutFilter
#     date_field = "transaction_datetime"
#     allow_future = True
#     allow_empty = True

#     permission_required = (
#         "rackings.view_rack",
#         "inventories.view_stock",
#         "settings.view_warehouse",
#     )

#     def get_rack(self) -> Rack:
#         return Rack.objects.get(pk=self.kwargs["rack_pk"])

#     def get_stock(self) -> Stock:
#         return Stock.objects.get(pk=self.kwargs["pk"])

#     def get_queryset(self) -> QuerySet[RackTransaction]:
#         month = self.kwargs["month"]
#         year = self.kwargs["year"]

#         rack = self.get_rack()
#         stock = self.get_stock()

#         if rack.is_leaf() is True:
#             qs = self.model.objects.filter(rackstorage__rack=rack, rackstorage__stock=stock).filter(
#                 transaction_datetime__year=year,
#                 transaction_datetime__month=month,
#             )
#         else:
#             # For non-leaf nodes, need to includes all the RackTransactions
#             # under the descendants rack nodes.
#             descendants = rack.get_descendants()

#             qs = self.model.objects.filter(rackstorage__rack__in=descendants, rackstorage__stock=stock).filter(
#                 transaction_datetime__year=year,
#                 transaction_datetime__month=month,
#             )

#         return qs

#     def get_table_kwargs(self) -> dict[str, Any]:
#         """Pass value to table."""
#         table = super().get_table_kwargs()
#         table["rack"] = self.get_rack()
#         table["year"] = self.kwargs.get("year", None)
#         table["month"] = self.kwargs.get("month", None)
#         return table


# rack_transaction_monthly_in_out_datatables_view = RackTransactionMonthlyInOutDataTablesView.as_view(month_format="%m")


# ############
# # FOR HTMX #
# ############


# class WarehouseRacksStockListView(
#     LoginRequiredMixin, PermissionRequiredMixin, FilterView, SingleTableMixin, CoreListView
# ):
#     """
#     Partial page to show all Stocks based on given Warehouse's slug and rack's pk.

#     This page use DataTables server side.
#     """

#     model = Stock
#     table_class = WarehouseRacksStockTable
#     template_name = "inventories/warehouses/partials/htmx/_stocks.html"
#     filterset_class = WarehouseRacksStockFilter

#     # To prevent query all object as it will use ajax call in template
#     object_list = Stock.objects.none()

#     permission_required = (
#         "rackings.view_rack",
#         "inventories.view_stock",
#         "settings.view_warehouse",
#     )

#     def get_rack(self):
#         return Rack.objects.get(pk=self.kwargs["rack_pk"])

#     def get_warehouse(self):
#         return Warehouse.objects.get(slug=self.kwargs["warehouse_slug"])

#     def get_queryset(self):
#         """To prevent query all object."""
#         return self.object_list

#     def get_table_kwargs(self):
#         """Pass value to table."""
#         self.rack = self.get_rack()
#         return {"rack": self.rack}

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         context["warehouse"] = self.get_warehouse()
#         context["rack"] = self.rack
#         return context


# warehouse_racks_stock_list_view = WarehouseRacksStockListView.as_view()


# class RackTransactionInOutListView(
#     LoginRequiredMixin, PermissionRequiredMixin, FilterView, SingleTableMixin, CoreListView
# ):
#     """
#     Partial page to show all Stock-level's StockIn/Out records based on selected
#     Stock & Rack, with year and month.
#     """

#     model = RackTransaction
#     template_name = "inventories/racks/partials/htmx/_stock_transactions.html"
#     table_class = RackTransactionInOutTable
#     filterset_class = RackTransactionInOutFilter

#     # To prevent query all object as it will use ajax call in template
#     object_list = RackTransaction.objects.none()

#     permission_required = (
#         "rackings.view_rack",
#         "inventories.view_stock",
#         "settings.view_warehouse",
#     )

#     def get_queryset(self):
#         """To prevent query all object."""
#         return self.object_list

#     def get_rack(self) -> Rack:
#         return Rack.objects.get(pk=self.kwargs["rack_pk"])

#     def get_stock(self) -> Stock:
#         return Stock.objects.get(pk=self.kwargs["pk"])

#     def get_table_kwargs(self) -> dict[str, Any]:
#         """Pass value to table."""
#         self.rack = self.get_rack()
#         self.stock = self.get_stock()

#         table = super().get_table_kwargs()
#         table["rack"] = self.rack
#         table["year"] = self.kwargs.get("year", None)
#         table["month"] = self.kwargs.get("month", None)
#         return table

#     def get_previous_month_record(self, year, month) -> RackTransaction:
#         # Convert year and month to integers
#         year = int(year)
#         month = int(month)
#         previous_record = None

#         # Calculate the previous month and year
#         if month == 1:
#             previous_month = 12
#             previous_year = year - 1
#         else:
#             previous_month = month - 1
#             previous_year = year

#         # Get the rack last leaf node
#         if not self.rack.is_leaf():
#             self.leaf_rack = (
#                 self.rack.get_descendants()
#                 .filter(
#                     numchild=0,
#                     rackstorage__stock=self.stock,
#                     rackstorage__racktransaction__transaction_datetime__month__lte=previous_month,
#                     rackstorage__racktransaction__transaction_datetime__year=previous_year,
#                 )
#                 .order_by("rackstorage__racktransaction__transaction_datetime", "rackstorage__racktransaction__created")
#                 .last()
#             )
#         else:
#             self.leaf_rack = self.rack

#         previous_record_qs = RackTransaction.objects.filter(
#             rackstorage__rack=self.leaf_rack,
#             rackstorage__stock=self.stock,
#             transaction_datetime__month__lte=previous_month,
#             transaction_datetime__year=previous_year,
#         ).order_by("transaction_datetime", "created")

#         if previous_record_qs.count() == 0:
#             pass
#         else:
#             while previous_record is None:
#                 previous_record = previous_record_qs.last()

#                 if previous_month == 1:
#                     previous_month = 12
#                     previous_year = year - 1
#                 else:
#                     previous_month -= 1
#                     previous_year = year

#         return previous_record

#     def get_context_data(self, **kwargs) -> dict[str, Any]:
#         context = super().get_context_data(**kwargs)

#         year = self.kwargs.get("year", None)
#         month = self.kwargs.get("month", None)

#         context["rack"] = self.rack
#         context["stock"] = self.stock
#         context["year"] = year
#         context["month"] = month

#         if year and month:
#             # Find the last day of the month
#             _, last_day = calendar.monthrange(year, month)
#             context["last_day"] = last_day
#             context["first_day"] = date_format(timezone.datetime(year, month, 1), format="SHORT_DATE_FORMAT")

#         context["month_name"] = calendar.month_abbr[month].upper() if month is not None else None

#         if all([year, month]):
#             context["previous_record"] = self.get_previous_month_record(year, month)
#             context["leaf_rack"] = self.leaf_rack

#         return context


# rack_transaction_in_out_list_view = RackTransactionInOutListView.as_view()


# class RackStockInOutCreateView(LoginRequiredMixin, PermissionRequiredMixin, CoreCreateView):
#     """Partial page to create a new RackTransaction object based on selected Rack and Stock."""

#     model = RackTransaction
#     form_class = RackStockInOutForm
#     template_name = "inventories/racks/partials/htmx/_stock_in_out_form.html"

#     permission_required = ("rackings.add_racktransaction",)

#     success_message = _("New Stock Card successfully created!")

#     def get_rack(self) -> Rack:
#         if not hasattr(self, "rack"):
#             self.rack = Rack.objects.get(pk=self.kwargs["pk"])
#         return self.rack

#     def get_stock(self, stock_pk: str = None) -> Stock | None:
#         if not hasattr(self, "stock"):
#             self.stock = Stock.objects.get(pk=stock_pk) if stock_pk else None
#         return self.stock

#     def get_initial(self) -> dict[str, Any]:
#         self.rack = self.get_rack()

#         initial = super().get_initial()
#         initial["rack"] = self.rack
#         initial["created_by"] = self.request.user

#         return initial

#     def get_context_data(self, **kwargs) -> dict[str, Any]:
#         context = super().get_context_data(**kwargs)
#         context["rack"] = self.rack
#         context["warehouse_pk"] = self.rack.warehouse.pk

#         # If form is invalid, we need to pre-populate the "Stock" dropdown field
#         # based on previous POST request.
#         form = self.get_form()
#         if not form.is_valid() and form.errors:
#             stock = self.get_stock()
#             context["selected_stock_pk"] = stock.pk if stock else ""
#             context["selected_stock_display"] = str(stock) if stock else ""
#         else:
#             context["selected_stock_pk"] = ""
#             context["selected_stock_display"] = ""

#         return context

#     def post(self, request: HttpRequest, *args: str, **kwargs: Any) -> HttpResponse:
#         """
#         To update RackStorage into POST request.
#         """
#         post_data = request.POST.copy()
#         stock_pk = post_data.get("stock", None)

#         rack = self.get_rack()
#         stock = self.get_stock(stock_pk=stock_pk)
#         if rack and stock:
#             rackstorage, _ = RackStorage.objects.get_or_create(rack=rack, stock=stock)
#             post_data.setlist("rackstorage", [f"{rackstorage.pk}"])

#         request.POST = post_data
#         return super().post(request, *args, **kwargs)

#     def get_success_url(self) -> str:
#         # Redirect to Rack's Stock's transactions view
#         year, month = self.object.transaction_datetime.strftime("%Y-%m-%d").split("-")[:2]

#         return reverse(
#             "inventories:racks:transaction_detail",
#             kwargs={
#                 "rack_pk": self.rack.pk,
#                 "pk": self.stock.pk,
#                 "year": int(year) or localtime_now().year,
#                 "month": int(month) or localtime_now().month,
#             },
#         )


# rack_stock_in_out_create_view = RackStockInOutCreateView.as_view()

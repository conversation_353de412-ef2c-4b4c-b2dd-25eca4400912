import logging

from django.db.models.signals import post_save, post_delete, pre_save
from django.dispatch import receiver

from wms.cores.actstream import register_stream

from .models import (
    Item,
    ItemCategory,
    ItemPhoto,
    Stock,
    ReservedStock,
    Transaction,
    ReservedTransaction,
    DailyStockBalance,
)

logger = logging.getLogger(__name__)

#################
# FOR ACTSTREAM #
#################


register_stream(ItemCategory, logger=logger)
register_stream(Item, logger=logger)
register_stream(ItemPhoto, logger=logger, parent_field="item")
register_stream(Stock, logger=logger)
register_stream(Transaction, logger=logger)
register_stream(ReservedTransaction, logger=logger)


###################
# FOR APP SIGNALS #
###################


@receiver(post_save, sender=Stock)
def auto_create_daily_stock_balance_on_stock_creation(sender, instance, **kwargs):
    """Create DailyStockBalance whenever a new stock is being created."""
    if hasattr(instance, 'dailystockbalance'):
        pass
    else:
        DailyStockBalance.objects.get_or_create(
            stock=instance
        )


@receiver(post_save, sender=Stock)
def auto_create_reserved_stock_on_stock_creation(sender, instance, **kwargs):
    """Create ReservedStock whenever a new stock is being created."""
    if hasattr(instance, 'reservedstock'):
        pass
    else:
        ReservedStock.objects.get_or_create(
            stock=instance
        )


@receiver(post_delete, sender=ItemPhoto)
def auto_delete_photo_on_delete(sender, instance, **kwargs):
    """Deletes photo from filesystem when ItemPhoto is deleted."""
    if instance.photo:
        instance.photo.delete(save=False)


@receiver(pre_save, sender=ItemPhoto)
def auto_delete_photo_on_change(sender, instance, **kwargs):
    """Deletes old photo from filesystem when ItemPhoto is updated with new photo."""
    if not instance.pk:
        return False

    try:
        old_file = sender.objects.get(pk=instance.pk).photo
    except sender.DoesNotExist:
        return False

    new_file = instance.photo
    if not old_file == new_file:
        try:
            old_file.delete(save=False)
        except Exception:
            return False

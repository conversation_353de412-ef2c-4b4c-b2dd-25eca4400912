# import datetime

# from django.db.models import QuerySet
from django.db.models import Q, Sum, OuterRef, Subquery, Value, DecimalField
from django.db.models.functions import Coalesce
from django.utils.translation import gettext_lazy as _

from django_filters import filters, FilterSet, ChoiceFilter

from wms.apps.rackings.models import Rack, RackStorage, RackTransaction

from wms.cores.forms.widget import CoreSelectWidget, CoreSelectMultipleWidget, CoreTextWidget


class AvailableQuantityFilter(ChoiceFilter):
    """
    Custom filter for available_quantity field that allows filtering by available rack transaction balance amount.
    """

    def filter(self, qs, value):
        if value == 'gt_zero':
            # Create a subquery to get the sum of system_quantity for each item
            rack_storage_subquery = RackTransaction.objects.filter(
                rackstorage=OuterRef('pk'),
                is_reserved=False,
            ).values('rackstorage').annotate(
                total=Sum('quantity')
            ).values('total')
            # Annotate the queryset with the result of the subquery
            # Use Coalesce to handle NULL values (items with no stock)

            print("qs.count(): ", qs.count())
            xx = qs.annotate(
                total_balance=Coalesce(
                    Subquery(rack_storage_subquery),
                    Value(0),
                    output_field=DecimalField()
                )
            ).filter(total_balance__gt=0)

            print("xx: ", xx)

            return qs.annotate(
                total_balance=Coalesce(
                    Subquery(rack_storage_subquery),
                    Value(0),
                    output_field=DecimalField()
                )
            ).filter(total_balance__gt=0)
        elif value == 'lt_zero':
            # Similar to above but filter for less than 0
            rack_storage_subquery = RackTransaction.objects.filter(
                rackstorage=OuterRef('pk'),
                is_reserved=False,
            ).values('rackstorage').annotate(
                total=Sum('quantity')
            ).values('total')
            # Annotate the queryset with the result of the subquery
            # Use Coalesce to handle NULL values (items with no stock)
            return qs.annotate(
                total_balance=Coalesce(
                    Subquery(rack_storage_subquery),
                    Value(0),
                    output_field=DecimalField()
                )
            ).filter(total_balance__lt=0)
        return qs  # Return all if 'all' is selected or no value provided


class ReservedQuantityFilter(ChoiceFilter):
    """
    Custom filter for available_quantity field that allows filtering by available rack transaction balance amount.
    """

    def filter(self, qs, value):
        if value == 'gt_zero':
            # Create a subquery to get the sum of system_quantity for each item
            rack_storage_subquery = RackTransaction.objects.filter(
                rack_storage=OuterRef('pk'),
                is_reserved=True,
            ).values('rack_storage').annotate(
                total=Sum('quantity')
            ).values('total')
            # Annotate the queryset with the result of the subquery
            # Use Coalesce to handle NULL values (items with no stock)
            return qs.annotate(
                total_balance=Coalesce(
                    Subquery(rack_storage_subquery),
                    Value(0),
                    output_field=DecimalField()
                )
            ).filter(total_balance__gt=0)
        elif value == 'lt_zero':
            # Similar to above but filter for less than 0
            rack_storage_subquery = RackTransaction.objects.filter(
                rack_storage=OuterRef('pk'),
                is_reserved=True,
            ).values('rack_storage').annotate(
                total=Sum('quantity')
            ).values('total')
            # Annotate the queryset with the result of the subquery
            # Use Coalesce to handle NULL values (items with no stock)
            return qs.annotate(
                total_balance=Coalesce(
                    Subquery(rack_storage_subquery),
                    Value(0),
                    output_field=DecimalField()
                )
            ).filter(total_balance__lt=0)
        return qs  # Return all if 'all' is selected or no value provided


class RackFilter(FilterSet):
    """
    Filter class for Rack list view.
    Provides filtering capabilities for Rack attributes.

    Widget attrs:
        'data-auto-submit' only work with filter_auto.html
    """

    rack_type = filters.MultipleChoiceFilter(
        label=_("Rack Type"),
        field_name="rack_type",
        choices=Rack.RackType,
        widget=CoreSelectMultipleWidget(attrs={
            'data-auto-submit': 'true'
        })
    )

    # warehouse = filters.MultipleChoiceFilter(
    #     choices=[],  # Will be populated dynamically
    #     label="Warehouse",
    #     widget=CoreSelectMultipleWidget()
    # )

    class Meta:
        model = Rack
        fields = ['rack_type']

    # def __init__(self, data=None, queryset=None, *, request=None, prefix=None):
    #     super().__init__(data=data, queryset=queryset, request=request, prefix=prefix)

    #     # Dynamically populate location choices from existing data
    #     if queryset is not None:
    #         # Update field choices after initialization
    #         self.form.fields['warehouse'].choices = get_warehouse_choices()


class RackStorageFilter(FilterSet):
    """
    Filter class for Rack list view.
    Provides filtering capabilities for Rack attributes.

    Widget attrs:
        'data-auto-submit' only work with filter_auto.html
    """

    available_quantity = AvailableQuantityFilter(
        choices=[
            ('all', _('All')),
            ('gt_zero', _('Greater than 0')),
            ('lt_zero', _('Less than 0')),
        ],
        empty_label=None,
        label=_("Available QTY"),
        widget=CoreSelectWidget(attrs={
            'data-auto-submit': 'true'
        })
    )
    reserved_quantity = ReservedQuantityFilter(
        choices=[
            ('all', _('All')),
            ('gt_zero', _('Greater than 0')),
            ('lt_zero', _('Less than 0')),
        ],
        empty_label=None,
        label=_("Reserved QTY"),
        widget=CoreSelectWidget(attrs={
            'data-auto-submit': 'true'
        })
    )

    # rack_type = filters.MultipleChoiceFilter(
    #     label=_("Rack Type"),
    #     field_name="rack_type",
    #     choices=Rack.RackType,
    #     widget=CoreSelectMultipleWidget(attrs={
    #         'data-auto-submit': 'true'
    #     })
    # )

    # # warehouse = filters.MultipleChoiceFilter(
    # #     choices=[],  # Will be populated dynamically
    # #     label="Warehouse",
    # #     widget=CoreSelectMultipleWidget()
    # # )

    class Meta:
        model = RackStorage
        fields = [
            "available_quantity",
            "reserved_quantity",
        ]

    # def __init__(self, data=None, queryset=None, *, request=None, prefix=None):
    #     super().__init__(data=data, queryset=queryset, request=request, prefix=prefix)

    #     # Dynamically populate location choices from existing data
    #     if queryset is not None:
    #         # Update field choices after initialization
    #         self.form.fields['warehouse'].choices = get_warehouse_choices()



# class RackTransactionInOutFilter(filters.FilterSet):
#     """Filterset class for Inventories > Warehouses > Racks > Stock > Transactions."""

#     start_date = filters.DateFilter(label=_("Start"), field_name="transaction_datetime", lookup_expr=("gte"))
#     end_date = filters.DateFilter(label=_("End"), field_name="transaction_datetime", method="end_date_filter")
#     type = filters.MultipleChoiceFilter(
#         label=_("Type"),
#         method="type_filter",
#         choices=RackTransaction.Type.choices,
#     )

#     class Meta:
#         model = RackTransaction
#         fields = []

#     def __init__(self, data=None, queryset=None, *, request=None, prefix=None):
#         super().__init__(data=data, queryset=queryset, request=request, prefix=prefix)

#         self.filters["start_date"].field.widget.attrs.update({"class": "form-control"})
#         self.filters["end_date"].field.widget.attrs.update({"class": "form-control"})
#         self.filters["type"].field.widget.attrs.update({"class": "form-control core-select2"})

#     def end_date_filter(self, queryset: QuerySet, name: str, value: datetime.date) -> QuerySet:
#         qs = queryset

#         added_one_day_end_date = value + datetime.timedelta(days=1)
#         qs = qs.filter(transaction_datetime__lt=added_one_day_end_date)

#         return qs

#     def type_filter(self, queryset: QuerySet, name: str, value: str) -> QuerySet:
#         qs = queryset
#         qs = qs.filter(type__in=value)
#         return qs.distinct()

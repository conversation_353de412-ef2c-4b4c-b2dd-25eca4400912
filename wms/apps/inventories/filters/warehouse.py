# import datetime

# from django.db.models import Count, Q, QuerySet
# from django.utils.translation import gettext_lazy as _

# import django_filters as filters

# from wms.cores.filters import CoreBooleanWidget

# from wms.apps.inventories.models import Item, Stock
# from wms.apps.rackings.models import Rack
# from wms.apps.settings.models import Warehouse


# class WarehouseFilter(filters.FilterSet):
#     """Filter class for Inventories > Warehouses."""

#     keyword = filters.CharFilter(
#         label=_("Branch or Name contains"),
#         method="custom_keyword_filter",
#     )

#     class Meta:
#         model = Warehouse
#         fields = []

#     def custom_keyword_filter(self, queryset, name, value):
#         qs = queryset

#         for keyword in value.split():
#             qs = qs.filter(Q(name__icontains=keyword) | Q(branch__name__icontains=keyword))

#         return qs.distinct()


# class WarehouseItemsFilter(filters.FilterSet):
#     """Filter class for Inventories > Warehouses > Items."""

#     keyword = filters.CharFilter(
#         label=_("Code or Name contains"),
#         method="custom_keyword_filter",
#     )

#     class Meta:
#         model = Item
#         fields = []

#     def custom_keyword_filter(self, queryset, name, value):
#         qs = queryset

#         for keyword in value.split():
#             qs = qs.filter(
#                 Q(code__icontains=keyword)
#                 | Q(name__icontains=keyword)
#                 | Q(brand__icontains=keyword)
#                 | Q(consignor__display_name__icontains=keyword)
#                 | Q(consignor__code__icontains=keyword)
#             )

#         return qs.distinct()


# class WarehouseRacksFilter(filters.FilterSet):
#     """Filter class for Inventories > Warehouses > Racks."""

#     with_stocks = filters.BooleanFilter(
#         label=_("With Stocks"), method="custom_with_stock_filter", widget=CoreBooleanWidget
#     )
#     search_keyword = filters.CharFilter(
#         label=_("Full Name contains"),
#         method="custom_keyword_filter",
#     )
#     # used in: Home > Inventories > Warehouses > 3PL2 > 5-2-T-23-4-1
#     full_name = filters.CharFilter(field_name="full_name", lookup_expr="icontains")

#     class Meta:
#         model = Rack
#         fields = [
#             "rack_type",
#             "full_name",
#         ]

#     def __init__(self, data=None, queryset=None, *, request=None, prefix=None):
#         super().__init__(data=data, queryset=queryset, request=request, prefix=prefix)

#         self.filters["rack_type"].field.widget.attrs.update({"class": "form-control core-select2"})
#         self.filters["with_stocks"].field.widget.attrs.update({"class": "form-control core-select2"})
#         self.filters["search_keyword"].field.widget.attrs.update({"class": "form-control"})

#     def custom_with_stock_filter(self, queryset: QuerySet[Rack], name: str, value: str) -> QuerySet[Rack]:
#         """Filter Rack that with Stocks on it."""

#         if value is True:
#             return queryset.annotate(stock_count=Count("rackbalance", filter=Q(rackbalance__balance__gt=0))).filter(
#                 stock_count__gt=0
#             )
#         elif value is False:
#             return queryset.annotate(stock_count=Count("rackbalance", filter=Q(rackbalance__balance__gt=0))).filter(
#                 stock_count=0
#             )
#         else:
#             return queryset

#     def custom_keyword_filter(self, queryset: QuerySet[Rack], name: str, value: str) -> QuerySet[Rack]:
#         qs = queryset

#         for keyword in value.split():
#             qs = qs.filter(Q(full_name__icontains=keyword))

#         return qs.distinct()


# class WarehouseRacksStockFilter(filters.FilterSet):
#     """Filterset class for Inventories > Warehouses > Racks > Stock."""

#     expiry_start_date = filters.DateFilter(
#         label=_("Start"),
#         field_name="expiry_date",
#         lookup_expr=("gte"),
#     )
#     expiry_end_date = filters.DateFilter(
#         label=_("End"),
#         field_name="expiry_date",
#         method="end_date_filter",
#     )
#     keyword = filters.CharFilter(
#         label=_("Batch number and Item Code contains"),
#         method="custom_keyword_filter",
#     )

#     class Meta:
#         model = Stock
#         fields = []

#     def __init__(self, data=None, queryset=None, *, request=None, prefix=None):
#         super().__init__(data=data, queryset=queryset, request=request, prefix=prefix)

#         self.filters["expiry_start_date"].field.widget.attrs.update({"class": "form-control"})
#         self.filters["expiry_end_date"].field.widget.attrs.update({"class": "form-control"})

#     def custom_keyword_filter(self, queryset, name, value):
#         qs = queryset

#         for keyword in value.split():
#             qs = qs.filter(
#                 Q(batch_no__icontains=keyword) | Q(item__code__icontains=keyword) | Q(item__name__icontains=keyword)
#             )

#         return qs.distinct()

#     def end_date_filter(self, queryset, name, value):
#         qs = queryset

#         added_one_day_end_date = value + datetime.timedelta(days=1)
#         qs = qs.filter(expiry_date__lt=added_one_day_end_date)

#         return qs

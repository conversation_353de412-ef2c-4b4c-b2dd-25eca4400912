# import datetime

# from django.db.models import Q, QuerySet

# import django_filters as filters

# from wms.cores.filters import AbstractHistoryFilter
from wms.cores.utils import get_item_brand_choices, get_item_consignor_choices, get_warehouse_choices  # get_rack_choices, get_user_warehouse_choices

# from wms.apps.inventories.models import Item, Stock
# from wms.apps.rackings.models import Rack, RackStorage

from django.db.models import Q, Sum, OuterRef, Subquery, Value, DecimalField
from django.db.models.functions import Coalesce
from django.utils.translation import gettext_lazy as _
from django_filters import filters, FilterSet, ChoiceFilter

from wms.apps.inventories.models import Item, Transaction, Stock
from wms.cores.forms.fields import FormFieldSize
from wms.cores.forms.widget import CoreSelectWidget, CoreSelectMultipleWidget, CoreTextWidget


class StockOnHandFilter(ChoiceFilter):
    """
    Custom filter for stock_on_hand field that allows filtering by balance amount.
    """

    def filter(self, qs, value):
        if value == 'gt_zero':
            # Create a subquery to get the sum of system_quantity for each item
            stock_subquery = Transaction.objects.filter(
                stock__item=OuterRef('pk')
            ).values('stock__item').annotate(
                total=Sum('system_quantity')
            ).values('total')

            # Annotate the queryset with the result of the subquery
            # Use Coalesce to handle NULL values (items with no stock)
            return qs.annotate(
                total_balance=Coalesce(
                    Subquery(stock_subquery),
                    Value(0),
                    output_field=DecimalField()
                )
            ).filter(total_balance__gt=0)
        elif value == 'lt_zero':
            # Similar to above but filter for less than 0
            stock_subquery = Transaction.objects.filter(
                stock__item=OuterRef('pk')
            ).values('stock__item').annotate(
                total=Sum('system_quantity')
            ).values('total')

            return qs.annotate(
                total_balance=Coalesce(
                    Subquery(stock_subquery),
                    Value(0),
                    output_field=DecimalField()
                )
            ).filter(total_balance__lt=0)
        return qs  # Return all if 'all' is selected or no value provided


class ItemFilter(FilterSet):
    """
    Filter class for Item list view.
    Provides filtering capabilities for Item attributes.
    """

    consignor = filters.MultipleChoiceFilter(
        choices=[],  # Will be populated dynamically
        field_name='consignor__code',
        label="Consignor",
        widget=CoreSelectMultipleWidget()
    )

    brand = filters.MultipleChoiceFilter(
        choices=[],  # Will be populated dynamically
        label="Brand",
        widget=CoreSelectMultipleWidget()
    )

    stock_on_hand = StockOnHandFilter(
        choices=[
            ('all', _('All')),
            ('gt_zero', _('Greater than 0')),
            ('lt_zero', _('Less than 0')),
        ],
        empty_label=None,
        label=_("SOH"),
        widget=CoreSelectWidget()
    )

    # status = filters.ChoiceFilter(
    #     choices=[
    #         ('active', 'Active'),
    #         ('inactive', 'Inactive'),
    #     ],
    #     empty_label="All Status",
    #     label="Status",
    #     widget=CoreSelectWidget()
    # )

    class Meta:
        model = Item
        fields = ['consignor', 'brand', 'stock_on_hand']

    def __init__(self, data=None, queryset=None, *, request=None, prefix=None):
        super().__init__(data=data, queryset=queryset, request=request, prefix=prefix)

        # Dynamically populate location choices from existing data
        if queryset is not None:
            # Update field choices after initialization
            self.form.fields['consignor'].choices = get_item_consignor_choices()
            self.form.fields['brand'].choices = get_item_brand_choices()


class ItemDataTableFilter(FilterSet):
    """
    Filter class for Item list view.
    Provides filtering capabilities for Item attributes.

    Widget attrs:
        'data-auto-submit' only work with filter_auto.html
    """

    stock_on_hand = StockOnHandFilter(
        choices=[
            ('all', _('All')),
            ('gt_zero', _('Greater than 0')),
            ('lt_zero', _('Less than 0')),
        ],
        empty_label=None,
        label=_("SOH"),
        widget=CoreSelectWidget(attrs={
            'data-auto-submit': 'true'
        })
    )

    class Meta:
        model = Item
        fields = ['stock_on_hand']


class StockFilter(FilterSet):
    """
    Filter class for Stock list view.
    Provides filtering capabilities for Stock attributes.

    Widget attrs:
        'data-auto-submit' only work with filter_auto.html
    """
    warehouse = filters.MultipleChoiceFilter(
        label=_("Warehouse"),
        field_name="warehouse",
        choices=get_warehouse_choices,
        widget=CoreSelectMultipleWidget(attrs={
            'data-auto-submit': 'true'
        })
    )

    class Meta:
        model = Stock
        fields = ['warehouse']

    # class ItemHistoryFilter(AbstractHistoryFilter):
    #     """Filter class for Item's History."""

    #     pass

    # class ItemStockListFilter(filters.FilterSet):
    #     """Filter class for Item > Stock."""

    #     expiry_start_date = filters.DateFilter(
    #         label=_("Expiry Start Date"),
    #         field_name="expiry_date",
    #         lookup_expr=("gte"),
    #     )
    #     expiry_end_date = filters.DateFilter(
    #         label=_("Expiry End Date"),
    #         field_name="expiry_date",
    #         method="end_date_filter",
    #     )
    #     warehouse = filters.MultipleChoiceFilter(label=_("Warehouse"), field_name="warehouse", choices=[])
    #     keyword_search = filters.CharFilter(
    #         label=_("Batch No contains"),
    #         method="custom_keyword_filter",
    #     )

    #     class Meta:
    #         model = Stock
    #         fields = ["warehouse"]

    #     def __init__(self, data=None, queryset=None, *, request=None, prefix=None):
    #         super().__init__(data=data, queryset=queryset, request=request, prefix=prefix)

    #         # Override choices within init function. Otherwise, it might trigger
    #         # django.db.utils.ProgrammingError when you dropdb, re-createdb, run makemigrations & migrate.
    #         self.filters["warehouse"].extra["choices"] = get_user_warehouse_choices(request.user)

    #         self.filters["expiry_start_date"].field.widget.attrs.update({"class": "form-control"})
    #         self.filters["expiry_end_date"].field.widget.attrs.update({"class": "form-control"})
    #         self.filters["keyword_search"].field.widget.attrs.update({"class": "form-control"})

    #     def warehouse_filter(self, queryset: QuerySet[Stock], name: str, value: list[int]) -> QuerySet[Stock]:
    #         qs = queryset

    #         qs = qs.filter(warehouse__pk__in=value)

    #         return qs.distinct()

    #     def custom_keyword_filter(self, queryset: QuerySet[Stock], name: str, value: str) -> QuerySet[Stock]:
    #         qs = queryset

    #         for keyword in value.split():
    #             qs = qs.filter(Q(batch_no__icontains=keyword))

    #         return qs.distinct()

    #     def end_date_filter(self, queryset, name, value):
    #         qs = queryset

    #         added_one_day_end_date = value + datetime.timedelta(days=1)
    #         qs = qs.filter(expiry_date__lt=added_one_day_end_date)

    #         return qs

    # class ItemStockRackListFilter(filters.FilterSet):
    #     """Filterset class for Rack."""

    #     keyword = filters.CharFilter(
    #         label=_("Full Name contains"),
    #         method="custom_keyword_filter",
    #     )

    #     class Meta:
    #         model = Rack
    #         fields = []

    #     def __init__(self, data=None, queryset=None, *, request=None, prefix=None):
    #         super().__init__(data=data, queryset=queryset, request=request, prefix=prefix)

    #         self.filters["keyword"].field.widget.attrs.update({"class": "form-control"})

    #     def custom_keyword_filter(self, queryset: QuerySet[Rack], name: str, value: str) -> QuerySet[Rack]:
    #         qs = queryset

    #         for keyword in value.split():
    #             qs = qs.filter(full_name__icontains=keyword)

    #         return qs.distinct()

    # class ItemRackStorageFilter(filters.FilterSet):
    #     """Filterset class for RackStorage."""

    #     expiry_start_date = filters.DateFilter(
    #         label=_("Expiry Start Date"),
    #         field_name="stock__expiry_date",
    #         lookup_expr=("gte"),
    #     )
    #     expiry_end_date = filters.DateFilter(
    #         label=_("Expiry End Date"),
    #         field_name="stock__expiry_date",
    #         method="end_date_filter",
    #     )
    #     search_keyword = filters.CharFilter(
    #         label=_("Batch No contains"),
    #         method="custom_keyword_filter",
    #     )
    #     custom_rack = filters.MultipleChoiceFilter(label=_("Racks"), field_name="rack", choices=[])

    #     class Meta:
    #         model = RackStorage
    #         fields = []

    #     def __init__(self, data=None, queryset=None, *, request=None, prefix=None, **kwargs):
    #         self.item = kwargs.pop("item", None)

    #         super().__init__(data=data, queryset=queryset, request=request, prefix=prefix, **kwargs)

    #         self.filters["custom_rack"].extra["choices"] = get_rack_choices(item=self.item)
    #         self.filters["expiry_start_date"].field.widget.attrs.update({"class": "form-control"})
    #         self.filters["expiry_end_date"].field.widget.attrs.update({"class": "form-control"})
    #         self.filters["search_keyword"].field.widget.attrs.update({"class": "form-control"})

    #     def custom_rack_filter(self, queryset: QuerySet[Rack], name: str, value: list[int]) -> QuerySet[Rack]:
    #         qs = queryset

    #         qs = qs.filter(rack__in=value)

    #         return qs.distinct()

    #     def custom_keyword_filter(self, queryset: QuerySet[RackStorage], name: str, value: str) -> QuerySet[RackStorage]:
    #         qs = queryset

    #         for keyword in value.split():
    #             qs = qs.filter(Q(stock__batch_no__icontains=keyword))

    #         return qs.distinct()

    #     def end_date_filter(self, queryset, name, value):
    #         qs = queryset

    #         added_one_day_end_date = value + datetime.timedelta(days=1)
    #         qs = qs.filter(stock__expiry_date__lt=added_one_day_end_date)

    #         return qs

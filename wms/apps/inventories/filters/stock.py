# import datetime

# from django.db.models import Char<PERSON>ield, F, Q, QuerySet
# from django.db.models.functions import Cast
# from django.utils.translation import gettext_lazy as _

# import django_filters as filters

from django.db.models import Q, Sum, OuterRef, Subquery, Value, DecimalField
from django.db.models.functions import Coalesce
from django.utils.translation import gettext_lazy as _
from django_filters import filters, FilterSet, ChoiceFilter

from wms.apps.inventories.models import Transaction, Stock
from wms.cores.forms.widget import CoreSelectWidget  # , CoreSelectMultipleWidget, CoreTextWidget


class StockOnHandFilter(ChoiceFilter):
    """
    Custom filter for stock_on_hand field that allows filtering by balance amount.
    """

    def filter(self, qs, value):
        if value == 'gt_zero':
            # Create a subquery to get the sum of system_quantity for each item
            stock_subquery = Transaction.objects.filter(
                stock=OuterRef('pk')
            ).values('stock').annotate(
                total=Sum('system_quantity')
            ).values('total')

            # Annotate the queryset with the result of the subquery
            # Use Coalesce to handle NULL values (items with no stock)
            return qs.annotate(
                total_balance=Coalesce(
                    Subquery(stock_subquery),
                    Value(0),
                    output_field=DecimalField()
                )
            ).filter(total_balance__gt=0)
        elif value == 'lt_zero':
            # Similar to above but filter for less than 0
            stock_subquery = Transaction.objects.filter(
                stock=OuterRef('pk')
            ).values('stock').annotate(
                total=Sum('system_quantity')
            ).values('total')

            return qs.annotate(
                total_balance=Coalesce(
                    Subquery(stock_subquery),
                    Value(0),
                    output_field=DecimalField()
                )
            ).filter(total_balance__lt=0)
        return qs  # Return all if 'all' is selected or no value provided


class StockDataTableFilter(FilterSet):
    """
    Filter class for Stock list view.
    Provides filtering capabilities for Stock attributes.

    Widget attrs:
        'data-auto-submit' only work with filter_auto.html
    """

    stock_on_hand = StockOnHandFilter(
        choices=[
            ('all', _('All')),
            ('gt_zero', _('Greater than 0')),
            ('lt_zero', _('Less than 0')),
        ],
        empty_label=None,
        label=_("SOH"),
        widget=CoreSelectWidget(attrs={
            'data-auto-submit': 'true'
        })
    )

    class Meta:
        model = Stock
        fields = ['stock_on_hand']


class TransactionFilter(FilterSet):
    """
    Filter class for Transaction list view.
    Provides filtering capabilities for Transaction attributes.

    Widget attrs:
        'data-auto-submit' only work with filter_auto.html
    """
    # warehouse = filters.MultipleChoiceFilter(
    #     label=_("Warehouse"),
    #     field_name="warehouse",
    #     choices=get_warehouse_choices,
    #     widget=CoreSelectMultipleWidget(attrs={
    #         'data-auto-submit': 'true'
    #     })
    # )
    start_date = filters.DateFilter(
        label=_("Start Date"),
        field_name="transaction_datetime",
        lookup_expr=("gte"),
    )
    end_date = filters.DateFilter(
        label=_("End Date"),
        field_name="transaction_datetime",
        method="end_date_filter",
    )

    class Meta:
        model = Transaction
        fields = []

    def end_date_filter(self, queryset, name, value):
        qs = queryset

        added_one_day_end_date = value + datetime.timedelta(days=1)
        qs = qs.filter(transaction_datetime__lt=added_one_day_end_date)

        return qs




# class StockFilter(filters.FilterSet):
#     """Filter class for Stock."""

#     keyword = filters.CharFilter(
#         label=_("Item Name contains"),
#         method="custom_keyword_filter",
#     )

#     class Meta:
#         model = Stock
#         fields = []

#     def custom_keyword_filter(self, queryset: QuerySet[Stock], name: str, value: str) -> QuerySet[Stock]:
#         qs = queryset
#         qs_exp, qs_bn = Stock.objects.none(), Stock.objects.none()

#         for keyword in value.split():
#             # When user wants to filter where 'expiry_date' contains '2024-09'
#             qs_exp = queryset.annotate(date_str=Cast(F("expiry_date"), output_field=CharField())).filter(
#                 date_str__icontains=keyword
#             )

#         for keyword in value.split():
#             qs_bn = qs.filter(Q(batch_no__icontains=keyword))

#         qs = qs_exp | qs_bn

#         return qs.distinct()


# class StockTransactionFilter(filters.FilterSet):
#     """Filter class for Stock's transaction."""

#     start_date = filters.DateFilter(
#         label=_("Start Date"),
#         field_name="transaction_datetime",
#         lookup_expr=("gte"),
#     )
#     end_date = filters.DateFilter(
#         label=_("End Date"),
#         field_name="transaction_datetime",
#         method="end_date_filter",
#     )
#     keyword = filters.CharFilter(
#         label=_("Remark or Transfer Number contains"),
#         method="custom_keyword_filter",
#     )

#     class Meta:
#         model = Transaction
#         fields = []

#     def __init__(self, data=None, queryset=None, *, request=None, prefix=None):
#         super().__init__(data=data, queryset=queryset, request=request, prefix=prefix)

#         self.filters["start_date"].field.widget.attrs.update({"class": "form-control"})
#         self.filters["end_date"].field.widget.attrs.update({"class": "form-control"})

#     def custom_keyword_filter(self, queryset, name, value):
#         qs = queryset

#         for keyword in value.split():
#             qs = qs.filter(
#                 Q(system_number_ref__icontains=keyword)
#                 | Q(customer_reference__icontains=keyword)
#                 | Q(remark__icontains=keyword)
#             )

#         return qs.distinct()

#     def end_date_filter(self, queryset, name, value):
#         qs = queryset

#         added_one_day_end_date = value + datetime.timedelta(days=1)
#         qs = qs.filter(transaction_datetime__lt=added_one_day_end_date)

#         return qs

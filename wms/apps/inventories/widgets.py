# from wss.cores.widgets import CoreGroupCheckboxSelectMultiple


# class AdminWarehouseCheckboxSelectMultipleMixin:
#     """This is a mix-in for admin to change `ManyToManyField` of Category to use CoreGroupCheckboxSelectMultiple."""

#     class Media:
#         js = ("admin/js/vendor/jquery/jquery.min.js",)

#     def formfield_for_dbfield(self, db_field, **kwargs):
#         if db_field.name == "warehouse":
#             return db_field.formfield(widget=CoreGroupCheckboxSelectMultiple())

#         return super().formfield_for_dbfield(db_field, **kwargs)

# from django import template

# register = template.Library()


# @register.filter
# def get_warehouse_balance(item, warehouse):
#     """To get the warehouse's item balance."""
#     return round(item.balance_by_warehouse(warehouse), item.uom.unit_precision)


# @register.filter
# def get_warehouse_total_number_of_batch_no(item, warehouse):
#     """To get the warehouse's item total number of batch_no."""
#     return item.warehouse_total_number_of_batch_no(warehouse)

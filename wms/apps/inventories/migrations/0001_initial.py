# Generated by Django 5.1 on 2025-03-15 13:12

import django.contrib.postgres.indexes
import django.db.models.deletion
import django.utils.timezone
import django_extensions.db.fields
import model_utils.fields
import sorl.thumbnail.fields
import wms.apps.inventories.models.item
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('cores', '0001_create_extensions'),
        ('consignors', '0002_initial'),
        ('settings', '0002_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ItemCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('path', models.CharField(max_length=255, unique=True)),
                ('depth', models.PositiveIntegerField()),
                ('numchild', models.PositiveIntegerField(default=0)),
                ('name', models.CharField(max_length=255, verbose_name='Name')),
                ('slug', django_extensions.db.fields.AutoSlugField(blank=True, editable=False, max_length=255, populate_from=['name'], unique=True, verbose_name='Slug')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('modified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Modified by')),
            ],
            options={
                'verbose_name_plural': 'item categories',
                'ordering': ['path'],
                'get_latest_by': 'created',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ItemOutboundUOMDisplayConversion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('base_value', models.DecimalField(blank=True, decimal_places=6, help_text='I.E: Base 60 PCE = 1 defined uom', max_digits=19, null=True, verbose_name='Base Value')),
                ('uom_display_name', models.CharField(max_length=64, verbose_name='UOM Display Name')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('modified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Modified by')),
            ],
            options={
                'verbose_name_plural': 'item outbound uom display conversions',
                'ordering': ['uom_display_name', 'base_value'],
                'get_latest_by': 'created',
                'abstract': False,
                'unique_together': {('base_value', 'uom_display_name')},
            },
        ),
        migrations.CreateModel(
            name='Item',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.IntegerField(choices=[(0, 'Inactive'), (1, 'Active')], default=1, verbose_name='status')),
                ('activate_date', models.DateTimeField(blank=True, help_text='keep empty for an immediate activation', null=True)),
                ('deactivate_date', models.DateTimeField(blank=True, help_text='keep empty for indefinite activation', null=True)),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('sort_order', models.PositiveIntegerField(default=0, verbose_name='Ordering')),
                ('code', models.CharField(max_length=255, verbose_name='Item Code')),
                ('name', models.CharField(max_length=255, verbose_name='Item Name')),
                ('brand', models.CharField(blank=True, max_length=255, verbose_name='Item Brand')),
                ('slug', django_extensions.db.fields.AutoSlugField(blank=True, editable=False, max_length=255, populate_from=['code'], unique=True, verbose_name='Slug')),
                ('sku', models.CharField(blank=True, help_text='The Stock Keeping Unit of the item.', max_length=255, verbose_name='SKU')),
                ('item_type', models.CharField(choices=[('FG', 'Finished Goods (FG)')], default='FG', max_length=3, verbose_name='Item Type')),
                ('manage_type', models.CharField(choices=[('NM', 'No Managed (NM)'), ('BM', 'Batch Managed (BM)'), ('SM', 'Serial Managed (SM)')], default='BM', max_length=3, verbose_name='Manage Type')),
                ('barcode', models.CharField(blank=True, max_length=255, verbose_name='Barcode')),
                ('length', models.DecimalField(blank=True, decimal_places=6, max_digits=19, null=True, verbose_name='Dimension (Length)')),
                ('width', models.DecimalField(blank=True, decimal_places=6, max_digits=19, null=True, verbose_name='Dimension (Width)')),
                ('height', models.DecimalField(blank=True, decimal_places=6, max_digits=19, null=True, verbose_name='Dimension (Height)')),
                ('dimension_unit', models.CharField(choices=[('cm', 'cm'), ('m', 'm')], default='cm', max_length=2, verbose_name='Dimension Unit')),
                ('weight', models.DecimalField(blank=True, decimal_places=6, max_digits=19, null=True, verbose_name='Weight')),
                ('weight_unit', models.CharField(choices=[('kg', 'kg'), ('g', 'g')], default='kg', max_length=2, verbose_name='Weight Unit')),
                ('uom_json', models.JSONField(blank=True, default=wms.apps.inventories.models.item.default_dict, null=True)),
                ('outbound_uom_conversion', models.DecimalField(blank=True, decimal_places=6, max_digits=19, null=True, verbose_name='Unit / Carton')),
                ('consignor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='consignors.consignor')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('modified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Modified by')),
                ('uom', models.ForeignKey(help_text='The item will be measured in terms of this unit (e.g.: kg, pcs, box).', on_delete=django.db.models.deletion.RESTRICT, to='settings.unitofmeasure', verbose_name='UOM')),
                ('categories', models.ManyToManyField(blank=True, related_name='categories', to='inventories.itemcategory', verbose_name='Categories')),
                ('outbound_uom_display_conversions', models.ManyToManyField(blank=True, related_name='outbound_uom_display_conversions', to='inventories.itemoutbounduomdisplayconversion', verbose_name='Outbound UOM Display Conversion')),
            ],
            options={
                'ordering': ['status', '-activate_date'],
                'get_latest_by': 'created',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ItemPhoto',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('sort_order', models.PositiveIntegerField(default=0, verbose_name='Ordering')),
                ('photo', sorl.thumbnail.fields.ImageField(upload_to=wms.apps.inventories.models.item.item_directory_path, verbose_name='Item Photo')),
                ('is_cover', models.BooleanField(default=False, verbose_name='Is Cover?')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventories.item', verbose_name='Item')),
                ('modified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Modified by')),
            ],
            options={
                'ordering': ['sort_order'],
                'get_latest_by': 'created',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='Stock',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('batch_no', models.CharField(default='N/A', max_length=255, verbose_name='Batch No.')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='Expiry Date')),
                ('reorder_level', models.DecimalField(decimal_places=6, default=Decimal('0'), help_text='When the stock reaches the reorder level, a notification will be sent to you.', max_digits=19, verbose_name='Reorder Level')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventories.item', verbose_name='Item')),
                ('modified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Modified by')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='settings.warehouse', verbose_name='Warehouse')),
            ],
            options={
                'ordering': ['warehouse__path'],
                'get_latest_by': 'created',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ReservedStock',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('modified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Modified by')),
                ('stock', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='inventories.stock', verbose_name='Stock')),
            ],
            options={
                'ordering': ['stock__warehouse__path'],
                'get_latest_by': 'created',
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='item',
            name='warehouses',
            field=models.ManyToManyField(related_name='warehouses', through='inventories.Stock', to='settings.warehouse', verbose_name='Warehouses'),
        ),
        migrations.CreateModel(
            name='Transaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('transaction_datetime', models.DateTimeField(default=django.utils.timezone.now)),
                ('quantity', models.DecimalField(decimal_places=6, max_digits=19, verbose_name='Quantity')),
                ('system_quantity', models.DecimalField(decimal_places=6, default=0, editable=False, max_digits=19, verbose_name='System Quantity')),
                ('system_number_ref', models.CharField(blank=True, max_length=255, null=True, verbose_name='System Number Ref')),
                ('is_internal_transfer', models.BooleanField(default=False, verbose_name='Is Internal Transfer?')),
                ('is_adjustment', models.BooleanField(default=False, verbose_name='Is Adjustment?')),
                ('is_grn', models.BooleanField(default=False, verbose_name='Is GRN?')),
                ('is_grn_defect', models.BooleanField(default=False, verbose_name='Is GRN Defect?')),
                ('is_wro', models.BooleanField(default=False, verbose_name='Is WRO?')),
                ('customer_reference', models.CharField(blank=True, max_length=64, null=True, verbose_name='Customer Reference')),
                ('remark', models.CharField(blank=True, max_length=128, verbose_name='Remark')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('modified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Modified by')),
                ('stock', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventories.stock')),
                ('uom', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='settings.unitofmeasure', verbose_name='UOM')),
            ],
            options={
                'ordering': ['-transaction_datetime', '-pk'],
                'get_latest_by': 'created',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='DailyStockBalance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('balance', models.DecimalField(decimal_places=6, default=Decimal('0'), max_digits=19, verbose_name='Balance')),
                ('recalculate_datetime', models.DateTimeField(default=django.utils.timezone.now)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('modified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Modified by')),
                ('stock', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='inventories.stock', verbose_name='Stock')),
                ('transaction_state', models.OneToOneField(blank=True, help_text="The balance field's value is based on this transaction obj's state.", null=True, on_delete=django.db.models.deletion.SET_NULL, to='inventories.transaction', verbose_name='Transaction State')),
            ],
            options={
                'ordering': ['stock__warehouse__path'],
                'get_latest_by': 'created',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ReservedTransaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('status', models.CharField(choices=[('Reserved', 'Reserved'), ('Released', 'Released')], default='Reserved', max_length=32, verbose_name='Status')),
                ('transaction_datetime', models.DateTimeField(default=django.utils.timezone.now)),
                ('input_quantity', models.DecimalField(decimal_places=6, max_digits=19, verbose_name='Quantity')),
                ('system_quantity', models.DecimalField(decimal_places=6, default=0, editable=False, max_digits=19, verbose_name='System Quantity')),
                ('system_number_ref', models.CharField(blank=True, max_length=255, verbose_name='System Number Ref')),
                ('is_wro', models.BooleanField(default=False, verbose_name='Is WRO?')),
                ('customer_reference', models.CharField(blank=True, max_length=64, verbose_name='Customer Reference')),
                ('remark', models.CharField(blank=True, max_length=128, verbose_name='Remark')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('input_uom', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='settings.unitofmeasure', verbose_name='UOM')),
                ('modified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Modified by')),
                ('reserved_stock', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventories.reservedstock')),
            ],
            options={
                'ordering': ['-transaction_datetime', '-pk'],
                'get_latest_by': 'created',
                'abstract': False,
                'indexes': [models.Index(fields=['status'], name='inventories_status_b57528_idx'), models.Index(fields=['reserved_stock'], name='inventories_reserve_6413cf_idx'), models.Index(fields=['transaction_datetime'], name='inventories_transac_d224fa_idx'), models.Index(fields=['reserved_stock', 'transaction_datetime'], name='inventories_reserve_47e3f5_idx'), models.Index(fields=['status', 'reserved_stock', 'transaction_datetime'], name='inventories_status_82ec91_idx')],
            },
        ),
        migrations.AddIndex(
            model_name='stock',
            index=django.contrib.postgres.indexes.GinIndex(fields=['batch_no'], name='stock_batch_no_gin', opclasses=['gin_trgm_ops']),
        ),
        migrations.AddIndex(
            model_name='stock',
            index=models.Index(fields=['warehouse'], name='inventories_warehou_fcbd47_idx'),
        ),
        migrations.AddIndex(
            model_name='stock',
            index=models.Index(fields=['item'], name='inventories_item_id_8a5c33_idx'),
        ),
        migrations.AddIndex(
            model_name='stock',
            index=models.Index(fields=['batch_no'], name='inventories_batch_n_ddbbc5_idx'),
        ),
        migrations.AddIndex(
            model_name='stock',
            index=models.Index(fields=['warehouse', 'item', 'batch_no'], name='inventories_warehou_d1c882_idx'),
        ),
        migrations.AddIndex(
            model_name='stock',
            index=models.Index(fields=['warehouse', 'batch_no', 'item'], name='inventories_warehou_bcae18_idx'),
        ),
        migrations.AddIndex(
            model_name='stock',
            index=models.Index(fields=['item', 'batch_no', 'warehouse'], name='inventories_item_id_9ed8a5_idx'),
        ),
        migrations.AddIndex(
            model_name='stock',
            index=models.Index(fields=['item', 'warehouse', 'batch_no'], name='inventories_item_id_aed504_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='stock',
            unique_together={('warehouse', 'item', 'batch_no', 'expiry_date')},
        ),
        migrations.AddIndex(
            model_name='item',
            index=django.contrib.postgres.indexes.GinIndex(fields=['code'], name='item_code_gin', opclasses=['gin_trgm_ops']),
        ),
        migrations.AddIndex(
            model_name='item',
            index=django.contrib.postgres.indexes.GinIndex(fields=['name'], name='item_name_gin', opclasses=['gin_trgm_ops']),
        ),
        migrations.AddIndex(
            model_name='item',
            index=models.Index(fields=['code'], name='inventories_code_cbce97_idx'),
        ),
        migrations.AddIndex(
            model_name='item',
            index=models.Index(fields=['name'], name='inventories_name_9b288c_idx'),
        ),
        migrations.AddIndex(
            model_name='item',
            index=models.Index(fields=['consignor'], name='inventories_consign_262f85_idx'),
        ),
        migrations.AddIndex(
            model_name='item',
            index=models.Index(fields=['code', 'name'], name='inventories_code_6c61ae_idx'),
        ),
        migrations.AddIndex(
            model_name='item',
            index=models.Index(fields=['name', 'code'], name='inventories_name_1c6ec2_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='item',
            unique_together={('consignor', 'code')},
        ),
        migrations.AddIndex(
            model_name='transaction',
            index=models.Index(fields=['stock'], name='inventories_stock_i_69201a_idx'),
        ),
        migrations.AddIndex(
            model_name='transaction',
            index=models.Index(fields=['transaction_datetime'], name='inventories_transac_fc593a_idx'),
        ),
        migrations.AddIndex(
            model_name='transaction',
            index=models.Index(fields=['stock', 'transaction_datetime'], name='inventories_stock_i_71733d_idx'),
        ),
        migrations.AddIndex(
            model_name='dailystockbalance',
            index=models.Index(fields=['stock', 'recalculate_datetime'], name='inventories_stock_i_987acf_idx'),
        ),
    ]

from django.contrib.humanize.templatetags.humanize import intcomma
from django.db.models import Sum, OuterRef, Subquery, F, Value, DecimalField
from django.db.models.functions import Coalesce
from django.utils.translation import gettext_lazy as _

import django_tables2 as tables

from wms.cores.columns import HTMXColumn
from wms.apps.inventories.models import Stock, Transaction


# from wms.cores.models import DISPLAY_EMPTY_VALUE
# from wms.cores.tables import PARTIAL_LIST_ATTRS_CLASS, TABLE_ATTRS_CLASS


class StockDetailTable(tables.Table):
    batch_no = HTMXColumn(
        url_name="inventories:stocks:detail_home",
        target_id="detail-panel",
        verbose_name=_("Batch No"),
        push_url=True,
        push_url_name="inventories:stocks:panel",
    )
    # expiry_date = tables.Column(
    #     verbose_name=_("Expiry Date"),
    #     accessor="expiry_date",
    #     orderable=True,
    # )
    stock_on_hand = tables.Column(
        verbose_name=_("SOH"),
        accessor="db_balance",
        orderable=True,
    )

    class Meta:
        model = Stock
        fields = ("batch_no", "expiry_date", "stock_on_hand")
        order_by = '-created_at'
        template_name = "tables/table_htmx.html"

    def __init__(self, *args, object_pk=None, **kwargs):
        super().__init__(*args, **kwargs)
        self.object_pk = object_pk

    def render_stock_on_hand(self, value):
        """
        Formats the 'stock_on_hand' value to display with a thousand separators.
        """
        return intcomma(value)

    def order_stock_on_hand(self, queryset, is_descending):
        """
        Custom ordering for stock_on_hand column.
        This uses a subquery to annotate the queryset with the sum of transactions.
        """
        from wms.apps.inventories.models import Transaction

        # Create a subquery to get the sum of system_quantity for each item
        stock_subquery = Transaction.objects.filter(
            stock=OuterRef('pk')
        ).values('stock').annotate(
            total=Sum('system_quantity')
        ).values('total')

        # Annotate the queryset with the result of the subquery
        # Use Coalesce to handle NULL values (items with no stock)
        queryset = queryset.annotate(
            total_balance=Coalesce(
                Subquery(stock_subquery),
                Value(0),
                output_field=DecimalField()
            )
        )

        # Order by the annotated field
        return queryset.order_by('-total_balance' if is_descending else 'total_balance'), True


class StockTransactionTable(tables.Table):
    selectable = False

    item_code = tables.Column(
        verbose_name=_("ITEM CODE"),
        accessor="stock.item.code",
    )
    system_quantity = tables.Column(
        verbose_name=_("QTY"),
        accessor="system_quantity",
        attrs={
            "th": {"class": "table-tooltip", "data-toggle": "tooltip", "data-placement": "top", "title": _("Quantity")}
        },
    )
    system_uom = tables.Column(
        verbose_name=_("UOM"),
        accessor="system_uom",
        attrs={
            "th": {
                "class": "table-tooltip",
                "data-toggle": "tooltip",
                "data-placement": "top",
                "title": _("Unit of Measures"),
            }
        },
    )
    transaction_balance = tables.Column(
        verbose_name=_("Balance"),
        accessor="get_current_transaction_balance",
        orderable=True,
    )

    transaction_date = tables.TemplateColumn(
        verbose_name=_("TRANSACTION DATE"),
        accessor="transaction_datetime",
        template_name="inventories/stocks/partials/datatable_column/_column_transaction_date.html",
    )
    # entered_date = tables.TemplateColumn(
    #     verbose_name=_("ENTERED DATE"),
    #     accessor="warehousereleaseorderstockout",
    #     template_name="inventories/stocks/partials/datatable_column/_column_entered_date.html",
    #     attrs={
    #         "th": {
    #             "class": "table-tooltip",
    #             "data-toggle": "tooltip",
    #             "data-placement": "top",
    #             "title": _("Entered Date"),
    #         }
    #     },
    # )
    customer_reference = tables.TemplateColumn(
        verbose_name=_("CUST REF"),
        accessor="warehousereleaseorderstockout",
        template_name="inventories/stocks/partials/datatable_column/_column_customer_reference.html",
        attrs={
            "th": {
                "class": "table-tooltip",
                "data-toggle": "tooltip",
                "data-placement": "top",
                "title": _("Customer Reference"),
            }
        },
    )
    xfer = tables.TemplateColumn(
        verbose_name=_("XFER"),
        accessor="transferstockinout",
        template_name="inventories/stocks/partials/datatable_column/_column_xfer.html",
        attrs={
            "th": {"class": "table-tooltip", "data-toggle": "tooltip", "data-placement": "top", "title": _("Transfer")}
        },
    )
    is_adjustment = tables.TemplateColumn(
        verbose_name=_("ADJ"),
        accessor="is_adjustment",
        template_name="inventories/stocks/partials/datatable_column/_column_adjustment.html",
        attrs={
            "th": {
                "class": "table-tooltip",
                "data-toggle": "tooltip",
                "data-placement": "top",
                "title": _("Adjustment"),
            }
        },
    )
    grn = tables.TemplateColumn(
        verbose_name=_("GRN"),
        accessor="goodsreceivednotestockin",
        template_name="inventories/stocks/partials/datatable_column/_column_grn.html",
        attrs={
            "th": {
                "class": "table-tooltip",
                "data-toggle": "tooltip",
                "data-placement": "top",
                "title": _("Goods Received Note"),
            }
        },
    )
    warehouse_release_order = tables.TemplateColumn(
        verbose_name=_("WRO"),
        accessor="warehousereleaseorderstockout",
        template_name="inventories/stocks/partials/datatable_column/_column_warehouse_release_order.html",
        attrs={
            "th": {
                "class": "table-tooltip",
                "data-toggle": "tooltip",
                "data-placement": "top",
                "title": _("Warehouse Release Order"),
            }
        },
    )

    # @property
    # def create_url(self):
    #     try:
    #         return reverse('inventories:items:create')
    #     except NoReverseMatch:
    #         return None

    class Meta:
        model = Transaction
        order_by = "-transaction_datetime"  # Must follow the first row display in the table
        order_by_field = (
            "-transaction_datetime"  # To ensure the JSON returns consistent results based on order_by line above
        )
        template_name = "tables/table_htmx.html"
        fields = (
            "system_quantity",
            "system_uom",
            "item_code",
        )
        sequence = [
            "transaction_date",
            # "entered_date",
            "item_code",
            "customer_reference",
            "system_quantity",
            "system_uom",
            "transaction_balance",
            "xfer",
            "is_adjustment",
            "grn",
            "warehouse_release_order",
            # "actions",
        ]

    # def __init__(self, *args, **kwargs):
    #     super().__init__(*args, **kwargs)
    #     self.counter = 0

    def value_item_code(self, value, record):
        return record.stock.item.code

    def render_system_quantity(self, value, record):
        return record.get_formatted_system_quantity

    def render_system_uom(self, value, record):
        return record.system_uom.symbol

    def render_transaction_balance(self, value, record):
        return record.get_current_transaction_balance


# class StockSideTable(tables.Table):
#     """Table used on stock detail page."""

#     batch_no = tables.TemplateColumn(
#         verbose_name="Batch No",
#         accessor="batch_no",
#         template_name="inventories/stocks/partials/tables/_row_batch_no_link.html",
#     )
#     stock_on_hand = tables.Column(
#         verbose_name=_("SOH"),
#         accessor="get_formatted_balance",
#         order_by="balance",
#         attrs={
#             "th": {
#                 "class": "table-tooltip",
#                 "data-toggle": "tooltip",
#                 "data-placement": "top",
#                 "data-original-title": _("Stock On Hand"),
#             }
#         },
#     )

#     class Meta:
#         model = Stock
#         order_by = "batch_no"  # Must follow the first row display in the table
#         default = DISPLAY_EMPTY_VALUE
#         template_name = "partials/tables/_datatables_server_side.html"
#         attrs = {"class": PARTIAL_LIST_ATTRS_CLASS, "id": "stock_datatables", "style": "display: none;"}
#         row_attrs = {"data-id": lambda record: record.pk}

#         fields = [
#             "pk",
#             "batch_no",
#             "expiry_date",
#         ]
#         sequence = [
#             "pk",
#             "batch_no",
#             "expiry_date",
#             "stock_on_hand",
#         ]

#     def render_pk(self, value, record):
#         return f"highlight_id_{value}"


# class StockTransactionDataTables(tables.Table):
#     """Table used on StockDetailView's Transaction list page."""

#     item_code = tables.Column(
#         verbose_name=_("ITEM CODE"),
#         accessor="stock.item.code",
#     )
#     transaction_date = tables.TemplateColumn(
#         verbose_name=_("TRANSACTION DATE"),
#         accessor="transaction_datetime",
#         template_name="inventories/stocks/partials/tables/_row_transaction_date.html",
#     )
#     entered_date = tables.TemplateColumn(
#         verbose_name=_("ENTERED DATE"),
#         accessor="warehousereleaseorderstockout",
#         template_name="inventories/stocks/partials/tables/_row_entered_date.html",
#         attrs={
#             "th": {
#                 "class": "table-tooltip",
#                 "data-toggle": "tooltip",
#                 "data-placement": "top",
#                 "title": _("Entered Date"),
#             }
#         },
#     )
#     customer_reference = tables.TemplateColumn(
#         verbose_name=_("CUST REF"),
#         accessor="warehousereleaseorderstockout",
#         template_name="inventories/stocks/partials/tables/_row_customer_reference.html",
#         attrs={
#             "th": {
#                 "class": "table-tooltip",
#                 "data-toggle": "tooltip",
#                 "data-placement": "top",
#                 "title": _("Customer Reference"),
#             }
#         },
#     )
#     system_quantity = tables.Column(
#         verbose_name=_("QTY"),
#         accessor="system_quantity",
#         attrs={
#             "th": {"class": "table-tooltip", "data-toggle": "tooltip", "data-placement": "top", "title": _("Quantity")}
#         },
#     )
#     system_uom = tables.Column(
#         verbose_name=_("UOM"),
#         accessor="system_uom",
#         attrs={
#             "th": {
#                 "class": "table-tooltip",
#                 "data-toggle": "tooltip",
#                 "data-placement": "top",
#                 "title": _("Unit of Measures"),
#             }
#         },
#     )
#     balance = tables.Column(
#         verbose_name=_("BAL"),
#         accessor="balance",
#         attrs={
#             "th": {"class": "table-tooltip", "data-toggle": "tooltip", "data-placement": "top", "title": _("Balance")}
#         },
#     )
#     xfer = tables.TemplateColumn(
#         verbose_name=_("XFER"),
#         accessor="transferstockinout",
#         template_name="inventories/stocks/partials/tables/_row_xfer_boolean.html",
#         attrs={
#             "th": {"class": "table-tooltip", "data-toggle": "tooltip", "data-placement": "top", "title": _("Transfer")}
#         },
#     )
#     is_adjustment = tables.TemplateColumn(
#         verbose_name=_("ADJ"),
#         accessor="is_adjustment",
#         template_name="inventories/stocks/partials/tables/_row_adjustment.html",
#         attrs={
#             "th": {
#                 "class": "table-tooltip",
#                 "data-toggle": "tooltip",
#                 "data-placement": "top",
#                 "title": _("Adjustment"),
#             }
#         },
#     )
#     grn = tables.TemplateColumn(
#         verbose_name=_("GRN"),
#         accessor="goodsreceivednotestockin",
#         template_name="inventories/stocks/partials/tables/_row_grn.html",
#         attrs={
#             "th": {
#                 "class": "table-tooltip",
#                 "data-toggle": "tooltip",
#                 "data-placement": "top",
#                 "title": _("Goods Received Note"),
#             }
#         },
#     )
#     warehouse_release_order = tables.TemplateColumn(
#         verbose_name=_("WRO"),
#         accessor="warehousereleaseorderstockout",
#         template_name="inventories/stocks/partials/tables/_row_warehouse_release_order.html",
#         attrs={
#             "th": {
#                 "class": "table-tooltip",
#                 "data-toggle": "tooltip",
#                 "data-placement": "top",
#                 "title": _("Warehouse Release Order"),
#             }
#         },
#     )
#     actions = tables.Column(
#         verbose_name=_("Actions"),
#         orderable=False,
#         accessor="created_by",
#         attrs={"th": {"class": "text-right"}, "td": {"class": "text-right"}},
#         exclude_from_export=True,
#     )

#     class Meta:
#         model = Transaction
#         order_by = "-transaction_datetime"  # Must follow the first row display in the table
#         order_by_field = (
#             "-transaction_datetime"  # To ensure the JSON returns consistent results based on order_by line above
#         )
#         default = DISPLAY_EMPTY_VALUE
#         template_name = "partials/tables/_datatables_server_side.html"
#         attrs = {
#             "class": TABLE_ATTRS_CLASS,
#             "id": "stock_transactions_datatables",
#             "style": "display: none;",
#             "thead": {"class": ""},
#         }
#         row_attrs = {"data-id": lambda record: record.pk}

#         fields = [
#             "balance",
#             "is_adjustment",
#         ]
#         sequence = [
#             "transaction_date",
#             "entered_date",
#             "item_code",
#             "customer_reference",
#             "system_quantity",
#             "system_uom",
#             "balance",
#             "xfer",
#             "is_adjustment",
#             "grn",
#             "warehouse_release_order",
#             "actions",
#         ]

#     # proper fix should use system quantity,
#     # it should inclusive of correct unit_precision already instead of applying defauly UOM unit_precision over here.
#     def render_system_quantity(self, value, record):
#         return record.get_formatted_system_quantity

#     def render_system_uom(self, value, record):
#         return record.system_uom.symbol

#     def render_balance(self, value, record):
#         return record.get_formatted_balance

#     def value_item_code(self, value, record):
#         return record.stock.item.code

# import itertools
# from decimal import Decimal
import itertools
from decimal import Decimal

from django.conf import settings
from django.contrib.humanize.templatetags.humanize import intcomma
from django.template.defaultfilters import floatformat
# from django.urls import reverse
from django.utils.translation import gettext_lazy as _

# import django_tables2 as tables
# from actstream.models import Action

# from wss.cores.models import DISPLAY_EMPTY_VALUE
# from wss.cores.tables import (
#     HTMX_LIST_SM_ATTRS_CLASS,
#     PARTIAL_LIST_ATTRS_CLASS,
#     TABLE_ATTRS_CLASS,
#     AbstractHistoryDataTables,
# )
# from wss.cores.utils import convert_camel_case_to_space, localtime_now, normalize_decimal

# from wss.apps.rackings.models import Rack, RackStorage

# from ..models import Item, Stock


import django_tables2 as tables
from django.urls import reverse, NoReverseMatch

from wms.apps.inventories.models import Item, Stock
from wms.cores.columns import HTMXColumn


class ItemDetailTable(tables.Table):
    code = HTMXColumn(
        url_name="inventories:items:detail_home",
        target_id="detail-panel",
        verbose_name=_("Code"),
        push_url=True,
        push_url_name="inventories:items:panel",
    )

    name = HTMXColumn(
        url_name="inventories:items:detail_home",
        target_id="detail-panel",
        verbose_name=_("Name"),
        push_url=True,
        push_url_name="inventories:items:panel",
    )

    stock_on_hand = tables.Column(
        verbose_name=_("SOH"),
        accessor="db_balance",
        orderable=True,
    )

    class Meta:
        model = Item
        fields = ("code", "name", "stock_on_hand")
        order_by = 'code'
        template_name = "tables/table_htmx.html"

    def __init__(self, *args, object_pk=None, **kwargs):
        super().__init__(*args, **kwargs)
        self.object_pk = object_pk

    def render_stock_on_hand(self, value):
        """
        Formats the 'stock_on_hand' value to display with a thousand separators.
        """
        return intcomma(value)

    def order_stock_on_hand(self, queryset, is_descending):
        """
        Custom ordering for stock_on_hand column.
        This uses a subquery to annotate the queryset with the sum of transactions.
        """
        from django.db.models import Sum, OuterRef, Subquery, F, Value, DecimalField
        from django.db.models.functions import Coalesce
        from wms.apps.inventories.models import Transaction

        # Create a subquery to get the sum of system_quantity for each item
        stock_subquery = Transaction.objects.filter(
            stock__item=OuterRef('pk')
        ).values('stock__item').annotate(
            total=Sum('system_quantity')
        ).values('total')

        # Annotate the queryset with the result of the subquery
        # Use Coalesce to handle NULL values (items with no stock)
        queryset = queryset.annotate(
            total_balance=Coalesce(
                Subquery(stock_subquery),
                Value(0),
                output_field=DecimalField()
            )
        )

        # Order by the annotated field
        return queryset.order_by('-total_balance' if is_descending else 'total_balance'), True


class ItemTable(tables.Table):
    section_title = "Item Lists"
    section_name = "Item"

    code = tables.LinkColumn("inventories:items:panel", args=[tables.utils.A("pk")])
    name = tables.LinkColumn("inventories:items:panel", args=[tables.utils.A("pk")])
    stock_on_hand = tables.Column(
        verbose_name=_("SOH"),
        accessor="db_balance",
        orderable=True,
    )

    actions = tables.TemplateColumn(
        verbose_name="Actions",
        template_name="tables/table_actions_column.html", orderable=False,
        extra_context={
            "view_url": "inventories:items:panel",
            "edit_url": "inventories:items:update",
            # "delete_url": "inventories:items:delete"
        }
    )

    @property
    def create_url(self):
        try:
            return reverse('inventories:items:create')
        except NoReverseMatch:
            return None

    class Meta:
        model = Item
        order_by = 'code'
        template_name = "tables/table_htmx.html"
        fields = (
            "code",
            "name",
            "brand",
            "consignor",
            "stock_on_hand",
            # "actions"
        )

    def render_stock_on_hand(self, value):
        """
        Formats the 'stock_on_hand' value to display with a thousand separators.
        """
        return intcomma(value)

    def order_stock_on_hand(self, queryset, is_descending):
        """
        Custom ordering for stock_on_hand column.
        This uses a subquery to annotate the queryset with the sum of transactions.
        """
        from django.db.models import Sum, OuterRef, Subquery, F, Value, DecimalField
        from django.db.models.functions import Coalesce
        from wms.apps.inventories.models import Transaction

        # Create a subquery to get the sum of system_quantity for each item
        stock_subquery = Transaction.objects.filter(
            stock__item=OuterRef('pk')
        ).values('stock__item').annotate(
            total=Sum('system_quantity')
        ).values('total')

        # Annotate the queryset with the result of the subquery
        # Use Coalesce to handle NULL values (items with no stock)
        queryset = queryset.annotate(
            total_balance=Coalesce(
                Subquery(stock_subquery),
                Value(0),
                output_field=DecimalField()
            )
        )

        # Order by the annotated field
        return queryset.order_by('-total_balance' if is_descending else 'total_balance'), True


class ItemStockTable(tables.Table):
    selectable = False

    # row_number = tables.Column(verbose_name="#", empty_values=(), orderable=False)
    item_code = tables.Column(
        verbose_name=_("code"),
        accessor="item",
        orderable=False,
        linkify=("inventories:stocks:panel", {"pk": tables.A("pk")})
    )
    item_name = tables.Column(verbose_name=_("name"), accessor="item", orderable=False)
    batch_no = tables.Column(verbose_name=_("batch"), attrs={
        "td": {"class": "font-bold"},
    })
    warehouse = tables.Column(verbose_name=_("warehouse"), attrs={
        "td": {
            "class": "underline whitespace-nowrap",
            "title": lambda record: record.warehouse.full_name if record.warehouse else ""
        },
    })
    expiry_date = tables.DateTimeColumn(
        verbose_name=_("expiry"),
        format=settings.DATE_FORMAT,
        attrs={
            "td": {
                "class": "whitespace-nowrap",
            },
        }
    )
    total_in = tables.Column(verbose_name=_("in"), accessor="get_total_in")
    total_out = tables.Column(verbose_name=_("out"), accessor="get_total_out")
    quantity = tables.Column(verbose_name=_("qty"), accessor="db_balance", orderable=False)
    uom = tables.Column(verbose_name=_("UOM"), accessor="item", orderable=False)

    # actions = tables.TemplateColumn(
    #     verbose_name="Actions",
    #     template_name="tables/table_actions_column.html", orderable=False,
    #     extra_context={
    #         # "view_url": "inventories:items:stock",
    #     }
    # )

    @property
    def create_url(self):
        try:
            return reverse('inventories:items:create')
        except NoReverseMatch:
            return None

    class Meta:
        model = Stock
        order_by = 'batch_no'
        template_name = "tables/table_htmx.html"
        fields = (
            # "row_number",
            "item_code",
            "item_name",
            "batch_no",
            "warehouse",
            "expiry_date",
            "total_in",
            "total_out",
            "quantity",
            "uom",
            # "actions"
        )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.counter = 0

    def render_row_number(self, value, record):
        # Get the starting position based on pagination
        starting_position = self.page.start_index() if hasattr(self, "page") else 1
        # Create or get the counter, starting from the pagination index
        self.row_counter = getattr(self, "row_counter", itertools.count(start=starting_position))
        # Return the next value from the counter
        return f"{next(self.row_counter)}"

    def render_item_code(self, value: Item) -> str:
        return f"{value.code}"

    def render_item_name(self, value: Item) -> str:
        return f"{value.name}"

    def render_warehouse(self, record: Stock) -> str:
        return f"{record.warehouse.name}"

    def render_total_in(self, value: Decimal) -> str:
        return f"{floatformat(value.normalize(), 0)}"

    def render_total_out(self, value: Decimal) -> str:
        return f"{floatformat(value.normalize(), 0)}"

    def render_quantity(self, value: Decimal, record: Stock) -> str:
        return f"{round(value, record.item.uom.unit_precision)}"

    def render_uom(self, value: Item) -> str:
        return value.uom.symbol


# from typing import Any

from django.utils.translation import gettext_lazy as _

import django_tables2 as tables
from django.urls import reverse, NoReverseMatch
from django.utils.html import format_html

# from wms.cores.models import DISPLAY_EMPTY_VALUE
# from wms.cores.tables import PARTIAL_LIST_ATTRS_CLASS, TABLE_ATTRS_CLASS

# from wms.apps.rackings.models import Rack
from wms.cores.columns import HTMXColumn

from wms.apps.inventories.models import Item, Stock
from wms.apps.settings.models import Warehouse


class WarehouseTable(tables.Table):
    section_title = "Warehouse Lists"
    section_name = "Warehouse"
    selectable = False

    # name = tables.LinkColumn("settings:warehouses:panel", args=[tables.utils.A("pk")])

    full_name = tables.LinkColumn(
        "inventories:warehouses:panel",
        args=[tables.utils.A("pk")],  # Pass primary key
        accessor="full_name"  # Refers to the @property method
    )
    items_count = tables.Column(verbose_name=_("No. of Items"), accessor="items")
    consignors_count = tables.Column(verbose_name=_("No. of Consignors"), accessor="consignors")
    leaf_node_racks_count = tables.Column(verbose_name=_("No. of Racks"), accessor="leaf_node_racks")

    # @property
    # def create_url(self):
    #     try:
    #         return reverse('inventories:warehouses:create')
    #     except NoReverseMatch:
    #         return None

    class Meta:
        model = Warehouse
        order_by = 'path'
        template_name = "tables/table_htmx.html"
        fields = (
            "full_name",
            "is_storage",
            "depth",
            "numchild",
        )

    def render_items_count(self, value, record):
        return f"{value.count()}"

    def render_consignors_count(self, value, record):
        return f"{value.count()}"

    def render_leaf_node_racks_count(self, value, record):
        return f"{value.count()}"


class WarehouseDetailTable(tables.Table):
    full_name = HTMXColumn(
        url_name="inventories:warehouses:detail_home",
        target_id="detail-panel",
        verbose_name=_("Full Name"),
        push_url=True,
        push_url_name="inventories:warehouses:panel",
        accessor="full_name",
    )

    class Meta:
        model = Warehouse
        order_by = 'path'
        template_name = "tables/table_htmx.html"
        fields = ("full_name",)

    def __init__(self, *args, object_pk=None, **kwargs):
        super().__init__(*args, **kwargs)
        self.object_pk = object_pk


#####################################################################
# Inner Datatable
#####################################################################

class WarehouseItemsTable(tables.Table):
    selectable = False

    code = tables.Column(verbose_name=_("Code"), accessor="code")
    # code = tables.LinkColumn(
    #     "inventories:warehouses:panel",
    #     args=[tables.utils.A("pk")],  # Pass primary key
    #     accessor="code"  # Refers to the @property method
    # )
    name = tables.Column(verbose_name=_("name"), accessor="name", orderable=False)
    balance = tables.Column(verbose_name=_("Balance"), accessor="db_balance")
    total_batch_no = tables.Column(
        verbose_name=_("Σ BN"),
        accessor="db_balance"
    )

    class Meta:
        model = Item
        order_by = 'code'
        template_name = "tables/table_htmx.html"
        fields = [
            "code",
            "name",
            "consignor",
        ]
        sequence = [
            "code",
            "name",
            "consignor",
            "balance",
            "total_batch_no",
            # "actions",
        ]

    def __init__(self, *args, warehouse=None, **kwargs):
        self.warehouse = warehouse
        super().__init__(*args, **kwargs)


    def render_code(self, value, record):

        latest_stock = Stock.objects.filter(
            warehouse=self.warehouse, item=record
        ).order_by("created").last()

        link = reverse(
            "inventories:stocks:panel",
            kwargs={"pk": latest_stock.pk},
        )
        return format_html(f'<a href="{link}">{value}</a>')

    # def render_name(self, value, record) -> str:
    #     return f"{record.name}"

    def render_balance(self, value, record):
        return f"{round(record.balance_by_warehouse(self.warehouse), record.uom.unit_precision)} {record.uom.symbol}"

    def render_total_batch_no(self, value, record):
        return f"{record.warehouse_total_number_of_batch_no(self.warehouse)}"


# class WarehouseSideTable(tables.Table):
#     """Table used on warehouse detail page."""

#     name = tables.TemplateColumn(
#         verbose_name="Name",
#         accessor="name",
#         template_name="inventories/warehouses/partials/tables/warehousetable/_name.html",
#     )

#     class Meta:
#         model = Warehouse
#         order_by = "path"  # Must follow the first row display in the table
#         default = DISPLAY_EMPTY_VALUE
#         template_name = "partials/tables/_datatables_server_side.html"
#         attrs = {
#             "class": PARTIAL_LIST_ATTRS_CLASS + " table-wrap",
#             "id": "warehouse_datatables",
#             "style": "display: none;",
#         }
#         row_attrs = {"data-id": lambda record: record.pk}

#         fields = [
#             "pk",
#             "path",
#             "name",
#         ]
#         sequence = [
#             "pk",
#             "path",
#             "name",
#         ]

#     def render_pk(self, value, record):
#         return f"highlight_id_{value}"


# class WarehouseItemsTable(tables.Table):
#     """Table used on warehouse's items list page."""

#     code = tables.Column(verbose_name=_("Code"))
#     name = tables.Column(verbose_name=_("Name"))
#     balance = tables.Column(verbose_name=_("Balance"), accessor="total_balance")
#     total_batch_no = tables.Column(
#         verbose_name=_("Σ BN"),
#         accessor="total_balance",
#         attrs={
#             "th": {
#                 "class": "table-tooltip",
#                 "data-toggle": "tooltip",
#                 "data-placement": "top",
#                 "title": _("Total Batch No."),
#             }
#         },
#     )
#     actions = tables.TemplateColumn(
#         verbose_name=_("Actions"),
#         orderable=False,
#         template_name="inventories/warehouses/partials/tables/warehouseitemstable/_actions.html",
#         attrs={"th": {"class": "text-right"}, "td": {"class": "text-right"}},
#         exclude_from_export=True,
#     )

#     class Meta:
#         model = Item
#         order_by = "code"  # Must follow the first row display in the table
#         default = DISPLAY_EMPTY_VALUE
#         template_name = "partials/tables/_datatables_server_side.html"
#         attrs = {
#             "class": TABLE_ATTRS_CLASS,
#             "id": "item_datatables",
#             "style": "display: none;",
#             "thead": {"class": "bg-teal"},
#         }
#         row_attrs = {"data-id": lambda record: record.pk}

#         fields = [
#             "code",
#             "name",
#             "consignor",
#         ]
#         sequence = [
#             "code",
#             "name",
#             "consignor",
#             "balance",
#             "total_batch_no",
#             "actions",
#         ]

#     def __init__(self, *args, **kwargs):
#         self.warehouse = kwargs.pop("warehouse")
#         super().__init__(*args, **kwargs)

#     def render_code(self, value, record):
#         link = reverse(
#             "inventories:warehouses:stock_detail",
#             kwargs={"warehouse_slug": self.warehouse.slug, "item_slug": record.slug},
#         )
#         return f'<a href="{link}">{value}</a>'

#     # def render_name(self, value, record):
#     #     new_name = (value[:68] + "...") if len(value) > 68 else value
#     #     return f"{new_name}"

#     def render_balance(self, value, record):
#         return f"{round(record.balance_by_warehouse(self.warehouse), record.uom.unit_precision)} {record.uom.symbol}"

#     def render_total_batch_no(self, value, record):
#         return f"{record.warehouse_total_number_of_batch_no(self.warehouse)}"

#     def render_actions(self, column, record, table, value, bound_column, bound_row):
#         """Pass warehouse into actions column."""
#         column.extra_context = {
#             "warehouse": self.warehouse,
#         }
#         return column.render(record, table, value, bound_column, **{"bound_row": bound_row})


# class WarehouseRacksTable(tables.Table):
#     """Table used on warehouse's rack list page."""

#     full_name = tables.TemplateColumn(
#         verbose_name="Full Name",
#         accessor="full_name",
#         template_name="inventories/warehouses/partials/tables/warehouserackstable/_full_name.html",
#     )
#     stock_count = tables.Column(verbose_name=_("Stock Count"), accessor="stock_count", order_by="stock_count")
#     actions = tables.TemplateColumn(
#         verbose_name=_("Actions"),
#         orderable=False,
#         template_name="inventories/warehouses/partials/tables/warehouserackstable/_actions.html",
#         attrs={"th": {"class": "text-right"}, "td": {"class": "text-right"}},
#         exclude_from_export=True,
#     )

#     class Meta:
#         model = Rack
#         order_by = "path"  # Must follow the first row display in the table
#         # orderable = False  # Known issue that MP_Node ordering on path is different with DataTables
#         default = DISPLAY_EMPTY_VALUE
#         template_name = "partials/tables/_datatables_server_side.html"
#         attrs = {"class": TABLE_ATTRS_CLASS, "id": "rack_datatables", "style": "display: none;"}
#         row_attrs = {"data-id": lambda record: record.pk}

#         fields = [
#             "path",
#             "rack_type",
#             "full_name",
#         ]
#         sequence = [
#             "path",
#             "rack_type",
#             "full_name",
#             "stock_count",
#             "actions",
#         ]

#     def render_actions(self, column, record: Rack, table, value, bound_column, bound_row) -> Any:
#         """Add permission checking into actions column."""

#         change_perms = self.request.user.has_perm("rackings.change_rack")
#         delete_perms = self.request.user.has_perm("rackings.delete_rack")
#         column.extra_context = {
#             "change_perms": change_perms,
#             "delete_perms": delete_perms,
#         }
#         return column.render(record, table, value, bound_column, **{"bound_row": bound_row})


# class WarehouseRacksSideTable(tables.Table):
#     """Table used on warehouse's rack detail page."""

#     full_name = tables.TemplateColumn(
#         verbose_name="Full Name",
#         accessor="full_name",
#         template_name="inventories/warehouses/partials/tables/warehouserackstable/_full_name.html",
#     )
#     stock_count = tables.Column(
#         verbose_name="Σ Stk.",
#         accessor="stock_count",
#         order_by="stock_count",
#         attrs={
#             "th": {
#                 "class": "table-tooltip",
#                 "data-toggle": "tooltip",
#                 "data-placement": "top",
#                 "title": _("Stock Count"),
#             }
#         },
#     )

#     class Meta:
#         model = Rack
#         order_by = "path"  # Must follow the first row display in the table
#         default = DISPLAY_EMPTY_VALUE
#         template_name = "partials/tables/_datatables_server_side.html"
#         attrs = {
#             "class": PARTIAL_LIST_ATTRS_CLASS + " table-wrap",
#             "id": "warehouse_rack_datatables",
#             "style": "display: none;",
#         }
#         row_attrs = {"data-id": lambda record: record.pk}

#         fields = [
#             "pk",
#             "path",
#             "full_name",
#         ]
#         sequence = [
#             "pk",
#             "path",
#             "full_name",
#             "stock_count",
#         ]

#     def render_pk(self, value, record):
#         return f"highlight_id_{value}"

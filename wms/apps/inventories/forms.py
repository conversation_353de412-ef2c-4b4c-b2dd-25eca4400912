from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext as _
# from django.forms import <PERSON>Field

from wms.cores.utils import get_rackstorage_choices, get_item_choices
from wms.cores.forms.fields import CoreModelForm, CoreCharField, SizedFormField, FormFieldSize, CoreChoiceField
from wms.cores.forms.widget import CoreSelectWidget, CoreNumberWidget, CoreTextWidget, CoreDateWidget

from wms.apps.settings.models import Warehouse
from wms.apps.rackings.models import Rack, RackStorage, RackTransaction


class OuterRackTransactionAdjustmentForm(CoreModelForm):
    """
    Form that supposed to use at listing of RackStorage table,
    - since it's having Rack pk from URL already, rack field should pre-filled.
    - then will need to manually select on other fields: item, batch_no, expiry_date, warehouse to add RackTransaction
    """
    rack = forms.ChoiceField(
        disabled=True,
        required=False,
        widget=CoreSelectWidget()
    )

    item = forms.ChoiceField(
        # disabled=True,
        required=True,
        widget=CoreSelectWidget()
    )
    batch_no = forms.CharField(
        widget=CoreTextWidget()
    )
    expiry_date = forms.DateField(
        widget=CoreDateWidget()
    )
    warehouse = forms.ChoiceField(
        # disabled=True,
        required=True,
        widget=CoreSelectWidget()
    )

    class Meta:
        model = RackTransaction
        fields = [
            "type",
            "rack",
            "item",
            "batch_no",
            "expiry_date",
            "warehouse",
            "transaction_datetime",
            "quantity",
            "is_reserved",
            "remark",
        ]

    def __init__(self, *args, **kwargs):
        rack_pk = kwargs.pop("pk", None)
        super().__init__(*args, **kwargs)
        self.fields["rack"].queryset = Rack.objects.filter(pk=rack_pk)
        self.fields["item"].queryset = get_item_choices()
        self.fields["warehouse"].queryset = Warehouse.objects.all()

        self.fields["transaction_datetime"].label = 'Time'
        # Explicitly set the datetime input format
        self.fields['transaction_datetime'].input_formats = ['%Y-%m-%d %I:%M:%S %p']


class RackTransactionAdjustmentForm(CoreModelForm):

    class Meta:
        model = RackTransaction
        fields = [
            "type",
            "rackstorage",
            "transaction_datetime",
            "quantity",
            "is_reserved",
            "remark",
        ]

    def __init__(self, *args, **kwargs):
        rackstorage_pk = kwargs.pop("pk", None)
        super().__init__(*args, **kwargs)
        self.fields["rackstorage"].queryset = RackStorage.objects.filter(pk=rackstorage_pk)

        self.fields["transaction_datetime"].label = 'Time'
        # Explicitly set the datetime input format
        self.fields['transaction_datetime'].input_formats = ['%Y-%m-%d %I:%M:%S %p']


class RackTransactionTransferForm(CoreModelForm):
    transfer_to = forms.ModelChoiceField(
        queryset=Rack.objects.filter(numchild=0),  # You’ll set this dynamically
        required=True,
        label="Transfer To Rack",
        widget=CoreSelectWidget()
    )

    class Meta:
        model = RackTransaction
        fields = [
            "type",
            "rackstorage",
            "transfer_to",
            "transaction_datetime",
            "quantity",
            "is_reserved",
            "remark",
        ]

    def __init__(self, *args, **kwargs):
        rackstorage_pk = kwargs.pop("pk", None)
        super().__init__(*args, **kwargs)

        self.fields["rackstorage"].queryset = RackStorage.objects.filter(pk=rackstorage_pk)
        # Post-fetch filtering to only include leaf nodes
        self.fields["transfer_to"].queryset = self.fields['transfer_to'].queryset.exclude(
            rackstorage__pk=rackstorage_pk
        )
        self.fields["transaction_datetime"].label = 'Time'
        # Explicitly set the datetime input format
        self.fields['transaction_datetime'].input_formats = ['%Y-%m-%d %I:%M:%S %p']

    def clean(self):
        cleaned_data = super().clean()

        # this is to make sure the transfer_out transaction from form submit is minus
        # then the addition of quantity to transfer_to will be handle in CBV's form_valid()
        quantity = cleaned_data.get('quantity')
        cleaned_data['quantity'] = -abs(quantity)

        return cleaned_data


#############################################################
# Old Code, to be remove after everything is stabled.
#############################################################


# from decimal import Decimal
# from typing import Any

# from django import forms
# from django.core.exceptions import ValidationError

# from wss.cores.forms import CoreHtmxModelForm, CoreModelForm, FormUOMSymbolMixin
# from wss.cores.utils import format_decimal_values, uom_choices_symbol

# from wss.apps.inventories.models.stock import Stock
# from wss.apps.rackings.models.rack import RackBalance, RackTransaction

# from .models import Item

# # from django.utils.translation import gettext_lazy as _



# class ItemForm(CoreModelForm):
#     """Form for item."""

#     class Meta:
#         model = Item
#         fields = [
#             "code",
#             "name",
#             "brand",
#             "uom",
#             "item_type",
#             "consignor",
#             "length",
#             "width",
#             "height",
#             "dimension_unit",
#             "weight",
#             "weight_unit",
#             "default_stock_in_uom",
#             "default_stock_out_uom",
#             "outbound_uom_conversion",
#         ]

#     def __init__(self, *args, **kwargs):
#         super().__init__(*args, **kwargs)

#         self.fields["uom"].choices = uom_choices_symbol()


# class ItemUpdateForm(CoreModelForm):
#     """Form for item."""

#     class Meta:
#         model = Item
#         fields = [
#             # "code",
#             "name",
#             "brand",
#             "item_type",
#             # "uom",
#             "consignor",
#             "length",
#             "width",
#             "height",
#             "dimension_unit",
#             "weight",
#             "weight_unit",
#             "default_stock_in_uom",
#             "default_stock_out_uom",
#             "outbound_uom_conversion",
#         ]


# ############
# # FOR HTMX #
# ############


# class ItemInfoUpdateForm(CoreHtmxModelForm, FormUOMSymbolMixin):
#     """Form for updating Item's Basic Info."""

#     # uom = forms.ChoiceField(label=_("UOM"), choices=uom_choices_symbol)

#     class Meta:
#         model = Item
#         fields = [
#             # "code",
#             "name",
#             "brand",
#             "item_type",
#             # "uom",
#             "consignor",
#             "length",
#             "width",
#             "height",
#             "dimension_unit",
#             "weight",
#             "weight_unit",
#         ]


# class SettingUpdateForm(CoreHtmxModelForm):
#     class Meta:
#         model = Item
#         fields = [
#             "default_stock_in_uom",
#             "default_stock_out_uom",
#             "outbound_uom_conversion",
#         ]


# class ItemBarcodeUpdateForm(CoreHtmxModelForm):
#     """HTMX form for updating Item's barcode field."""

#     class Meta:
#         model = Item
#         fields = [
#             "barcode",
#         ]


# class RackStockInOutForm(CoreModelForm):
#     """ModelForm for Rack's Stock In/Out within Inventories > Warehouses > Rack."""

#     stock = forms.CharField(widget=forms.Select())

#     class Meta:
#         model = RackTransaction
#         fields = [
#             "transaction_datetime",
#             "stock",
#             "rackstorage",
#             "quantity",
#             "remark",
#             "created_by",
#         ]
#         widgets = {
#             "remark": forms.Textarea(attrs={"cols": 120, "rows": 2}),
#             "rackstorage": forms.HiddenInput(),
#         }

#     def __init__(self, *args, **kwargs) -> None:
#         super().__init__(*args, **kwargs)
#         self.fields["stock"].widget.attrs["class"] = "form-control core-select2"
#         self.fields["created_by"].disabled = True

#     def clean_stock(self):
#         pk = self.cleaned_data["stock"]
#         try:
#             data = Stock.objects.get(pk=pk)
#         except Stock.DoesNotExist:
#             raise ValidationError("Stock does not exist.")
#         return data

#     def clean(self) -> dict[str, Any]:
#         """Override parent's class clean() method for validating StockCard quantity."""

#         cleaned_data = super().clean()
#         quantity = cleaned_data.get("quantity", None)

#         # Validate input quantity
#         if quantity is not None and quantity <= Decimal("0"):
#             msg = "The input Quantity should not be lesser than or equals to zero."
#             self.add_error("quantity", msg)

#         # Validation for stock out > current quantity in Racking
#         rackstorage = cleaned_data.get("rackstorage", None)
#         if rackstorage:
#             stock_type = self.request.POST.get("stock_type", "")
#             insufficient_balance = False

#             rack_balance, _ = RackBalance.objects.get_or_create(
#                 rack=rackstorage.rack,
#                 stock=rackstorage.stock,
#             )
#             insufficient_balance = quantity > rack_balance.balance

#             if stock_type == "stock_out":
#                 if insufficient_balance:
#                     msg = (
#                         "Cannot STOCK OUT - Insufficient Racking quantity: "
#                         + f"{format_decimal_values(rack_balance.balance)} in rack {rackstorage.rack}"
#                     )
#                     self.add_error("quantity", msg)

#         return cleaned_data

#     def save(self, commit=True) -> Any:
#         """Override ModelForm's save() method for pre-processing before saving the instance."""

#         if self.errors:
#             raise ValueError(
#                 "The %s could not be %s because the data didn't validate."
#                 % (
#                     self.instance._meta.object_name,
#                     "created" if self.instance._state.adding else "changed",
#                 )
#             )

#         if self.request.POST:
#             stock_type = self.request.POST.get("stock_type", "")
#             quantity = Decimal(self.request.POST.get("quantity", Decimal("0")))

#             # for differentiating StockIn vs StockOut quantity
#             if stock_type == "stock_out":
#                 quantity *= -1
#                 self.instance.quantity = quantity

#         if commit:
#             self.instance.save()

#         return self.instance

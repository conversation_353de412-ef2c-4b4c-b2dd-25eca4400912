from django.urls import path
# from wms.apps.reports.views.stock_report.views import (
#     StockReportListView
#     # export_stock_report_to_xlsx,
#     # report_datatables_view,
#     # report_list_view
# )
from wms.apps.reports.views import (
    GRNReportListView,
    StockReportListView,
    export_grn_report_to_xlsx,
#     grn_report_datatables_view,
#     grn_report_list_view,
)
# from wms.apps.reports.views.wro_report import (
#     export_wro_report_to_xlsx,
#     wro_report_datatables_view,
#     wro_report_list_view,
# )

app_name = "reports"


urlpatterns = [
    path("grn_list/", GRNReportListView.as_view(), name="grn_list"),
    path("stock_list/", StockReportListView.as_view(), name="stock_list"),
#     path("", view=report_list_view, name="list"),
#     path("", view=report_list_view, name="list"),
#     path("grn_list/", view=grn_report_list_view, name="grn_list"),
#     path("wro_list/", view=wro_report_list_view, name="wro_list"),
#     path("datatables/", view=report_datatables_view, name="datatables"),
#     path("grn-datatables/", view=grn_report_datatables_view, name="grn_datatables"),
#     path("wro-datatables/", view=wro_report_datatables_view, name="wro_datatables"),
#     path(
#         "export/",
#         export_stock_report_to_xlsx,
#         name="export_stock_report_xlsx",
#     ),
    path(
        "export-grn-report/",
        export_grn_report_to_xlsx,
        name="export_grn_report_xlsx",
    ),
#     path(
#         "export-wro-report/",
#         export_wro_report_to_xlsx,
#         name="export_wro_report_xlsx",
#     ),
]

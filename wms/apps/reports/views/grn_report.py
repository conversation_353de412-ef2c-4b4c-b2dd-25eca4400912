import datetime

# from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
from django.http import HttpRequest, HttpResponse

# from django_filters.views import FilterView
# from django_tables2 import SingleTableMixin
from openpyxl import Workbook
from openpyxl.styles import Alignment, Font

from wms.cores.utils import localtime_now
# from wms.cores.views import CoreDataTablesView, CoreListView

from wms.cores.mixins import ExportTableMixin
from wms.cores.views import CoreSingleTableView, CoreDataTableDetailView, CoreCreateView, CoreDetailView, CoreUpdateView

from wms.apps.receives.models import GoodsReceivedNote, GoodsReceivedNoteItem
from wms.apps.reports.filters import GRNReportFilter
from wms.apps.reports.tables import GRNReportTable  # , GRNReportDataTables


# class GRNReportListView(LoginRequiredMixin, PermissionRequiredMixin, <PERSON><PERSON>View, SingleTableMixin, CoreListView):
#     """Page to show all GRN Report. This page use DataTables server side."""

#     model = GoodsReceivedNote
#     template_name = "reports/grn_list.html"
#     table_class = GRNReportDataTables
#     filterset_class = GRNReportFilter

#     # To prevent query all object as it will use ajax call in template
#     object_list = GoodsReceivedNote.objects.none()

#     header_title = "Reports - Goods Received Notes"
#     selected_page = "reports"
#     selected_subpage = "Goods Received Notes"

#     permission_required = (
#         "receives.view_goodsreceivednote",
#         "consignors.view_consignor",
#     )

#     def get_queryset(self):
#         """To prevent query all object."""
#         return self.object_list


# grn_report_list_view = GRNReportListView.as_view()


class GRNReportListView(ExportTableMixin, CoreSingleTableView):
    """
    View for displaying and managing the list of Items.
    Supports:
    - Pagination
    - Column visibility
    - Searching
    - Export functionality
    - HTMX partial updates
    """
    model = GoodsReceivedNote
    table_class = GRNReportTable
    template_name = "cores/list_table.html"
    partial_template_name = "cores/table_partial.html"
    queryset = GoodsReceivedNote.objects.all()
    filterset_class = GRNReportFilter

    # Search configuration
    search_fields = ["customer_reference", "remark"]

    # Export configuration
    export_name = "grn_report"
    export_permission = []  # Empty list means no specific permissions required

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["customize_export_excel_url"] = True
        return context


# ##################
# # FOR DataTables #
# ##################


# class GRNReportDataTablesView(LoginRequiredMixin, PermissionRequiredMixin, CoreDataTablesView):
#     """JSON for DataTables in list page."""

#     model = GoodsReceivedNote
#     table_class = GRNReportDataTables
#     filterset_class = GRNReportFilter

#     permission_required = (
#         "receives.view_goodsreceivednote",
#         "consignors.view_consignor",
#     )

#     def get_queryset(self):
#         if self.request.user.is_superuser is True:
#             return GoodsReceivedNote.objects.select_related("consignor", "deliver_to").all()
#         else:
#             return GoodsReceivedNote.objects.select_related("consignor", "deliver_to").filter(
#                 deliver_to__in=self.request.user.warehouses.all()
#             )


# grn_report_datatables_view = GRNReportDataTablesView.as_view()


# ##################
# # FOR Excel      #
# ##################


def export_grn_report_to_xlsx(request: HttpRequest) -> HttpResponse:
    """
    Downloads all stocks with desired filtered result as Excel file with a single worksheet
    """

    response = HttpResponse(
        content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    )
    response["Content-Disposition"] = f'attachment; filename={datetime.datetime.now().strftime("%Y-%m-%d")}-grn.xlsx'

    workbook = Workbook()

    # Get active worksheet/tab
    worksheet = workbook.active
    worksheet.title = "Goods Received Notes Items"

    excel_display_start_date = "Beginning"
    excel_display_end_date = localtime_now().date()

    # Define the titles for columns
    balance_columns = [
        "Location",
        "Consignor",
        "Arrival Date",
        "GRN No.",
        "Product Code",
        "Product Brand",
        "Product Name",
        "Batch No.",
        "Expiry Date",
        "Customer Reference",
        "Quantity",
        "UOM",
        "Status",
    ]
    balance_row_num = 4

    # Assign the titles for each cell of the header
    for col_num, column_title in enumerate(balance_columns, 1):
        cell = worksheet.cell(row=balance_row_num, column=col_num)
        cell.value = column_title
        cell.font = Font(bold=True)
        cell.alignment = Alignment(horizontal="center")

    balance_row_num += 1

    # Sample of query_string as below:
    # {{url}}/?date_range_after=2024-06-01&date_range_before=&warehouses=3&warehouses=25&status=Obsolete&status=Completed&keyword_search=018&consignor=5
    query_string = request.META.get("QUERY_STRING", "")

    temp_dict = {
        "date_range_after": "",
        "date_range_before": "",
        "keyword_search": "",
        "warehouses": [],
        "consignor": "",
        "status": [],
    }

    temp_list = query_string.split("&")

    # temp_list = [
    #     'date_range_after=2024-06-01',
    #     'date_range_before=',
    #     'keyword_search=018',
    #     'warehouse=3',
    #     'warehouse=25',
    #     'consignor=5',
    #     'status=Obsolete',
    #     'status=Completed',
    # ]

    # loop the list and split it by "=" again and update the temp_dict
    for element in temp_list:
        element_list = element.split("=")

        if element_list[0] == "warehouses":
            temp_dict["warehouses"].append(int(element_list[1]))
        elif element_list[0] == "keyword_search" and element_list[1]:
            temp_dict["keyword_search"] = element_list[1]
        elif element_list[0] == "date_range_after" and element_list[1]:
            temp_dict["date_range_after"] = element_list[1]
            excel_display_start_date = element_list[1]
        elif element_list[0] == "date_range_before" and element_list[1]:
            temp_dict["date_range_before"] = element_list[1]
            excel_display_end_date = element_list[1]
        elif element_list[0] == "consignor" and element_list[1]:
            temp_dict["consignor"] = int(element_list[1])
        elif element_list[0] == "status":
            temp_dict["status"].append(element_list[1].replace("+", " "))

    # Starts filtering the GRNItems queryset
    grn_items_qs = GoodsReceivedNoteItem.objects.select_related("goods_received_note", "item", "uom").all()
    start_date = temp_dict["date_range_after"]
    end_date = temp_dict["date_range_before"]

    if temp_dict["warehouses"]:
        grn_items_qs = grn_items_qs.filter(goods_received_note__deliver_to__in=temp_dict["warehouses"])
    if temp_dict["date_range_after"]:
        start_date = datetime.datetime.strptime(temp_dict["date_range_after"], "%Y-%m-%d").date()
    if temp_dict["date_range_before"]:
        end_date = datetime.datetime.strptime(temp_dict["date_range_before"], "%Y-%m-%d").date()
    if temp_dict["consignor"]:
        grn_items_qs = grn_items_qs.filter(goods_received_note__consignor__id=temp_dict["consignor"])
    if temp_dict["keyword_search"]:
        # Based on Customer Reference
        grn_items_qs = grn_items_qs.filter(
            goods_received_note__customer_reference__icontains=temp_dict["keyword_search"]
        )
    if temp_dict["status"]:
        print("temp_dict['status']: ", temp_dict["status"])
        grn_items_qs = grn_items_qs.filter(goods_received_note__status__in=temp_dict["status"])

    # Arrival Date range filter
    if start_date and end_date:
        added_one_day_end_date = end_date + datetime.timedelta(days=1)
        grn_items_qs = grn_items_qs.filter(
            goods_received_note__arrival_datetime__gte=start_date,
            goods_received_note__arrival_datetime__lte=added_one_day_end_date,
        )
    elif start_date and not end_date:
        grn_items_qs = grn_items_qs.filter(
            goods_received_note__arrival_datetime__gte=start_date,
        )
    elif end_date and not start_date:
        added_one_day_end_date = end_date + datetime.timedelta(days=1)
        grn_items_qs = grn_items_qs.filter(
            goods_received_note__arrival_datetime__lte=added_one_day_end_date,
        )

    grn_items_qs = grn_items_qs.order_by("goods_received_note__system_number")

    # To set the header
    cell = worksheet.cell(row=1, column=1)
    if isinstance(excel_display_end_date, str):
        header_end_year = excel_display_end_date.split("-")[0]
        header_end_month = excel_display_end_date.split("-")[1]
        header_end_day = excel_display_end_date.split("-")[2]
    else:
        header_end_day = excel_display_end_date.day
        header_end_month = excel_display_end_date.month
        header_end_year = excel_display_end_date.year
    cell.value = f"Incoming Report as of {header_end_day}.{header_end_month}.{header_end_year}"
    cell.font = Font(bold=True)

    # to set excel date range display
    cell = worksheet.cell(row=2, column=1)
    cell.value = "START DATE:"
    cell = worksheet.cell(row=2, column=2)
    cell.value = excel_display_start_date
    cell = worksheet.cell(row=2, column=4)
    cell.value = "END DATE:"
    cell = worksheet.cell(row=2, column=5)
    cell.value = excel_display_end_date

    previous_grn_no = ""
    # Iterate through all filtered grn_items_qs
    for item in grn_items_qs:
        current_grn_no = item.goods_received_note.system_number

        # Right after each different GRN system_number, leave 2 blank spaces
        if previous_grn_no != "" and previous_grn_no != current_grn_no:
            worksheet.append([""])
            worksheet.append([""])

        # Define the data for each cell in the row
        row = [
            str(item.goods_received_note.deliver_to.name),
            item.goods_received_note.consignor.display_name,
            item.goods_received_note.arrival_datetime.strftime("%Y-%m-%d"),
            current_grn_no,
            item.item.code,
            item.item.brand,
            item.item.name,
            item.batch_no,
            str(item.expiry_date),
            item.goods_received_note.customer_reference,
            item.quantity,
            item.item.uom.symbol,
            item.goods_received_note.status,
        ]

        # Directly append row-by-row instead of assigning data for each cell within the row via nested for-loops
        worksheet.append(row)
        previous_grn_no = current_grn_no

    workbook.save(response)

    return response

# import datetime

# from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
# from django.db.models import Q
# from django.http import HttpRequest, HttpResponse

# from django_filters.views import FilterView
# from django_tables2 import SingleTableMixin
# from openpyxl import Workbook
# from openpyxl.styles import Alignment, Font

# from wss.cores.utils import localtime_now
# from wss.cores.views import CoreDataTablesView, CoreListView

# from wss.apps.releases.models import WarehouseReleaseOrder
# from wss.apps.releases.models.warehouse_release_order import WarehouseReleaseOrderItem

# from ..filters import WROReportFilter
# from ..tables import WROReportDataTables


# class WROReportListView(LoginRequiredMixin, PermissionRequiredMixin, FilterView, SingleTableMixin, CoreListView):
#     """Page to show all WRO Report. This page use DataTables server side."""

#     model = WarehouseReleaseOrder
#     template_name = "reports/wro_list.html"
#     table_class = WROReportDataTables
#     filterset_class = WROReportFilter

#     # To prevent query all object as it will use ajax call in template
#     object_list = WarehouseReleaseOrder.objects.none()

#     header_title = "Reports - Warehouse Release Orders"
#     selected_page = "reports"
#     selected_subpage = "Warehouse Release Orders"

#     permission_required = (
#         "releases.view_warehousereleaseorder",
#         "consignees.view_consignee",
#     )

#     def get_queryset(self):
#         """To prevent query all object."""
#         return self.object_list


# wro_report_list_view = WROReportListView.as_view()


# ##################
# # FOR DataTables #
# ##################


# class WROReportDataTablesView(LoginRequiredMixin, PermissionRequiredMixin, CoreDataTablesView):
#     """JSON for DataTables in list page."""

#     model = WarehouseReleaseOrder
#     table_class = WROReportDataTables
#     filterset_class = WROReportFilter

#     permission_required = (
#         "releases.view_warehousereleaseorder",
#         "consignees.view_consignee",
#     )

#     def get_queryset(self):
#         if self.request.user.is_superuser is True:
#             return WarehouseReleaseOrder.objects.select_related("consignee").prefetch_related("warehouses").all()
#         else:
#             return (
#                 WarehouseReleaseOrder.objects.select_related("consignee")
#                 .prefetch_related("warehouses")
#                 .filter(warehouses__in=self.request.user.warehouses.all())
#             )


# wro_report_datatables_view = WROReportDataTablesView.as_view()


# ##################
# # FOR Excel      #
# ##################


# def export_wro_report_to_xlsx(request: HttpRequest) -> HttpResponse:
#     """
#     Downloads all wro with desired filtered result as Excel file with a single worksheet
#     """

#     response = HttpResponse(
#         content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
#     )
#     response["Content-Disposition"] = f'attachment; filename={datetime.datetime.now().strftime("%Y-%m-%d")}-wro.xlsx'

#     workbook = Workbook()

#     # Get active worksheet/tab
#     worksheet = workbook.active
#     worksheet.title = "Warehouse Release Orders Items"

#     excel_display_start_date = "Beginning"
#     excel_display_end_date = localtime_now().date()

#     # Define the titles for columns
#     balance_columns = [
#         "Location",
#         "Consignor",
#         "Release Date",
#         "WRO No.",
#         "DO No.",
#         "Product Code",
#         "Product Brand",
#         "Product Name",
#         "Batch No.",
#         "Expiry Date",
#         "Customer Reference",
#         "Quantity",
#         "UOM",
#         "Status",
#     ]
#     balance_row_num = 4

#     # Assign the titles for each cell of the header
#     for col_num, column_title in enumerate(balance_columns, 1):
#         cell = worksheet.cell(row=balance_row_num, column=col_num)
#         cell.value = column_title
#         cell.font = Font(bold=True)
#         cell.alignment = Alignment(horizontal="center")

#     balance_row_num += 1

#     # query_string = ??date_range_after=&date_range_before=2023-03-06&keyword_search=F00002387&warehouse=9
#     # Sample of query_string as below:
#     # {{url}}/?date_range_after=2024-07-01&date_range_before=&warehouses=3&warehouses=12&status=New&status=Completed&keyword_search=24-020&is_delivery_order=true&consignee__consignor=5
#     query_string = request.META.get("QUERY_STRING", "")

#     temp_dict = {
#         "date_range_after": "",
#         "date_range_before": "",
#         "keyword_search": "",
#         "warehouses": [],
#         "consignee__consignor": "",
#         "status": [],
#     }

#     temp_list = query_string.split("&")

#     # temp_list = [
#     #     'date_range_after=2024-07-01',
#     #     'date_range_before=2024-03-06',
#     #     'keyword_search=24-020',
#     #     'warehouses=3',
#     #     'warehouses=12',
#     #     'status=New',
#     #     'status=Completed',
#     #     'is_delivery_order=true',
#     #     'consignee__consignor=5',
#     # ]

#     # loop the list and split it by "=" again and update the temp_dict
#     for element in temp_list:
#         element_list = element.split("=")

#         if element_list[0] == "warehouses":
#             temp_dict["warehouses"].append(int(element_list[1]))
#         elif element_list[0] == "keyword_search" and element_list[1]:
#             temp_dict["keyword_search"] = element_list[1]
#         elif element_list[0] == "date_range_after" and element_list[1]:
#             temp_dict["date_range_after"] = element_list[1]
#             excel_display_start_date = element_list[1]
#         elif element_list[0] == "date_range_before" and element_list[1]:
#             temp_dict["date_range_before"] = element_list[1]
#             excel_display_end_date = element_list[1]
#         elif element_list[0] == "consignee__consignor" and element_list[1]:
#             temp_dict["consignee__consignor"] = int(element_list[1])
#         elif element_list[0] == "status":
#             temp_dict["status"].append(element_list[1])

#     # using the updated temp_dict to start filtering the wro_items_qs
#     wro_items_qs = WarehouseReleaseOrderItem.objects.select_related("release_order", "item", "uom").all()
#     start_date = temp_dict["date_range_after"]
#     end_date = temp_dict["date_range_before"]

#     if temp_dict["warehouses"]:
#         wro_items_qs = wro_items_qs.filter(release_order__warehouses__id__in=temp_dict["warehouses"])
#     if temp_dict["date_range_after"]:
#         start_date = datetime.datetime.strptime(temp_dict["date_range_after"], "%Y-%m-%d").date()
#     if temp_dict["date_range_before"]:
#         end_date = datetime.datetime.strptime(temp_dict["date_range_before"], "%Y-%m-%d").date()
#     if temp_dict["consignee__consignor"]:
#         wro_items_qs = wro_items_qs.filter(release_order__consignee__consignor__id=temp_dict["consignee__consignor"])
#     if temp_dict["keyword_search"]:
#         # Based on Customer Reference or DO number
#         wro_items_qs = wro_items_qs.filter(
#             Q(release_order__customer_reference__icontains=temp_dict["keyword_search"])
#             | Q(release_order__deliveryorder__numbering__icontains=temp_dict["keyword_search"])
#         )
#     if temp_dict["status"]:
#         wro_items_qs = wro_items_qs.filter(release_order__status__in=temp_dict["status"])

#     # Release Date range filter
#     if start_date and end_date:
#         added_one_day_end_date = end_date + datetime.timedelta(days=1)
#         wro_items_qs = wro_items_qs.filter(
#             release_order__release_datetime__gte=start_date,
#             release_order__release_datetime__lte=added_one_day_end_date,
#         )
#     elif start_date and not end_date:
#         wro_items_qs = wro_items_qs.filter(
#             release_order__release_datetime__gte=start_date,
#         )
#     elif end_date and not start_date:
#         added_one_day_end_date = end_date + datetime.timedelta(days=1)
#         wro_items_qs = wro_items_qs.filter(
#             release_order__release_datetime__lte=added_one_day_end_date,
#         )

#     wro_items_qs = wro_items_qs.order_by("release_order__numbering")

#     # To set the header
#     cell = worksheet.cell(row=1, column=1)
#     if isinstance(excel_display_end_date, str):
#         header_end_year = excel_display_end_date.split("-")[0]
#         header_end_month = excel_display_end_date.split("-")[1]
#         header_end_day = excel_display_end_date.split("-")[2]
#     else:
#         header_end_day = excel_display_end_date.day
#         header_end_month = excel_display_end_date.month
#         header_end_year = excel_display_end_date.year
#     cell.value = f"Incoming Report as of {header_end_day}.{header_end_month}.{header_end_year}"
#     cell.font = Font(bold=True)

#     # to set excel date range display
#     cell = worksheet.cell(row=2, column=1)
#     cell.value = "START DATE:"
#     cell = worksheet.cell(row=2, column=2)
#     cell.value = excel_display_start_date
#     cell = worksheet.cell(row=2, column=4)
#     cell.value = "END DATE:"
#     cell = worksheet.cell(row=2, column=5)
#     cell.value = excel_display_end_date

#     previous_wro_no = ""
#     do_numbering = ""
#     # Iterate through all filtered wro_items_qs
#     for item in wro_items_qs:
#         current_wro_no = item.release_order.numbering
#         do_numbering = (
#             item.release_order.deliveryorder.numbering
#             if hasattr(item.release_order, "deliveryorder") is True
#             else do_numbering
#         )

#         # Right after each different WRO numbering, leave 2 blank spaces
#         if previous_wro_no != "" and previous_wro_no != current_wro_no:
#             worksheet.append([""])
#             worksheet.append([""])

#         # Define the data for each cell in the row
#         row = [
#             ", ".join(warehouse.name for warehouse in item.release_order.warehouses.all()),
#             item.release_order.consignee.consignor.display_name,
#             item.release_order.release_datetime.strftime("%Y-%m-%d"),
#             current_wro_no,
#             do_numbering,
#             item.item.code,
#             item.item.brand,
#             item.item.name,
#             item.batch_no,
#             str(item.expiry_date),
#             item.release_order.customer_reference,
#             item.quantity,
#             item.item.uom.symbol,
#             item.release_order.status,
#         ]

#         # Directly append row-by-row instead of assigning data for each cell within the row via nested for-loops
#         worksheet.append(row)
#         previous_wro_no = current_wro_no

#     workbook.save(response)

#     return response

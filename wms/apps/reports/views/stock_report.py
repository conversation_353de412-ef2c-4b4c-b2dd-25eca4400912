# import datetime
# import urllib

# from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
# from django.db.models import OuterRef, Q, Sum
# from django.http import HttpResponse

# from django_filters.views import FilterView
# from django_tables2 import SingleTableMixin
# from openpyxl import Workbook

# from wms.cores.utils import localtime_now
# from wms.cores.views import CoreDataTablesView, CoreListView

from wms.cores.mixins import ExportTableMixin
from wms.cores.views import CoreSingleTableView, CoreDataTableDetailView, CoreCreateView, CoreDetailView, CoreUpdateView

from wms.apps.inventories.models import Stock

from wms.apps.reports.filters import StockReportFilter
from wms.apps.reports.tables import StockReportTable


# class ReportListView(LoginRequiredMixin, PermissionRequiredMixin, <PERSON>lterView, SingleTableMixin, CoreListView):
#     """Page to show all Report. This page use DataTables server side."""

#     model = Stock
#     template_name = "reports/list.html"
#     table_class = ReportDataTables
#     filterset_class = ReportFilter

#     # To prevent query all object as it will use ajax call in template
#     object_list = Stock.objects.none()

#     header_title = "Reports - Stocks"
#     selected_page = "reports"
#     selected_subpage = "Stocks"

#     permission_required = (
#         "settings.view_warehouse",
#         "inventories.view_item",
#         "inventories.view_stock",
#     )

#     def get_queryset(self):
#         """To prevent query all object."""
#         return self.object_list


# report_list_view = ReportListView.as_view()


class StockReportListView(ExportTableMixin, CoreSingleTableView):
    """
    View for displaying and managing the list of Items.
    Supports:
    - Pagination
    - Column visibility
    - Searching
    - Export functionality
    - HTMX partial updates
    """
    model = Stock
    table_class = StockReportTable
    template_name = "cores/list_table.html"
    partial_template_name = "cores/table_partial.html"
    queryset = Stock.objects.all()
    filterset_class = StockReportFilter

    # Search configuration
    search_fields = ["item__code", "item__name", "item__brand", "batch_no"]

    # Export configuration
    export_name = "stock_report"
    export_permission = []  # Empty list means no specific permissions required



# ##################
# # FOR DataTables #
# ##################


# class ReportDataTablesView(LoginRequiredMixin, PermissionRequiredMixin, CoreDataTablesView):
#     """JSON for DataTables in list page."""

#     model = Stock
#     table_class = ReportDataTables
#     filterset_class = ReportFilter

#     permission_required = (
#         "settings.view_warehouse",
#         "inventories.view_item",
#         "inventories.view_stock",
#     )

#     def get_queryset(self):
#         if self.request.user.is_superuser is True:
#             return Stock.objects.select_related("warehouse", "item").prefetch_related("transaction_set").all()
#         else:
#             return (
#                 Stock.objects.select_related("warehouse", "item")
#                 .prefetch_related("transaction_set")
#                 .filter(warehouse__in=self.request.user.warehouses.all())
#             )


# report_datatables_view = ReportDataTablesView.as_view()


# ##################
# # FOR Excel      #
# ##################


# def export_stock_report_to_xlsx(request):
#     """
#     Downloads all stocks with desired filtered result as Excel file with a single worksheet
#     """

#     response = HttpResponse(
#         content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
#     )
#     response["Content-Disposition"] = f'attachment; filename={datetime.datetime.now().strftime("%Y-%m-%d")}-stocks.xlsx'

#     workbook = Workbook()

#     # Get active worksheet/tab
#     worksheet = workbook.active
#     worksheet.title = "Stock Items"

#     excel_display_start_date = "Beginning"
#     excel_display_end_date = localtime_now().date()

#     # Define the titles for columns
#     balance_columns = [
#         "LOCATION",
#         "CONSIGNOR",
#         "PRODUCT CODE",
#         "PRODUCT BRAND",
#         "PRODUCT NAME",
#         "BATCH",
#         "EXPIRY DATE",
#         "QUANTITY",
#         "UOM",
#     ]
#     balance_row_num = 3

#     # Assign the titles for each cell of the header
#     for col_num, column_title in enumerate(balance_columns, 1):
#         cell = worksheet.cell(row=balance_row_num, column=col_num)
#         cell.value = column_title

#     balance_row_num += 1

#     # query_string = ??date_range_after=&date_range_before=2023-03-06&keyword_search=F00002387&warehouse=9
#     query_string = request.META.get("QUERY_STRING", "")

#     temp_dict = {
#         "date_range_after": "",
#         "date_range_before": "",
#         "keyword_search": "",
#         "warehouse": [],
#         "item__consignor": "",
#         "item__consignor__consignee": [],
#         "item__brand": [],
#     }

#     temp_list = query_string.split("&")

#     # temp_list = [
#     #     'date_range_after=2023-01-16',
#     #     'date_range_before=2023-03-06',
#     #     'keyword_search=',
#     #     'warehouse=9',
#     #     'warehouse=17',
#     #     'item__consignor=5',
#     #     'item__consignor__consignee=5'
#     #     'item__brand=['FRESENIUS MEDICAL CARE', 'MITSUBISHI MOTOR MALAYSIA SDN BHD']
#     # ]

#     # loop the list and split it by "=" again and update the temp_dict
#     for element in temp_list:
#         element_list = element.split("=")

#         if element_list[0] == "warehouse":
#             temp_dict["warehouse"].append(int(element_list[1]))
#         elif element_list[0] == "item__consignor__consignee":
#             temp_dict["item__consignor__consignee"].append(int(element_list[1]))
#         elif element_list[0] == "item__consignor" and element_list[1]:
#             temp_dict["item__consignor"] = int(element_list[1])
#         elif element_list[0] == "date_range_after" and element_list[1]:
#             temp_dict["date_range_after"] = element_list[1]
#             excel_display_start_date = element_list[1]
#         elif element_list[0] == "date_range_before" and element_list[1]:
#             temp_dict["date_range_before"] = element_list[1]
#             excel_display_end_date = element_list[1]
#         elif element_list[0] == "keyword_search" and element_list[1]:
#             # special handling to escape space which is encoded to plus + sign in URL
#             temp_dict["keyword_search"] = urllib.parse.unquote_plus(element_list[1])
#         elif element_list[0] == "item__brand" and element_list[1]:
#             temp_dict["item__brand"] = request.GET.getlist("item__brand")

#     # using the updated temp_dict to start filtering the stock_qs
#     stock_qs = Stock.objects.all().order_by("item__code")
#     start_date = temp_dict["date_range_after"]
#     end_date = temp_dict["date_range_before"]

#     if temp_dict["warehouse"]:
#         stock_qs = stock_qs.filter(warehouse__id__in=temp_dict["warehouse"])
#     if temp_dict["date_range_after"]:
#         start_date = datetime.datetime.strptime(temp_dict["date_range_after"], "%Y-%m-%d").date()
#     if temp_dict["date_range_before"]:
#         end_date = datetime.datetime.strptime(temp_dict["date_range_before"], "%Y-%m-%d").date()
#     if temp_dict["item__consignor"]:
#         stock_qs = stock_qs.filter(item__consignor__id=temp_dict["item__consignor"])
#     if temp_dict["item__consignor__consignee"]:
#         stock_qs = stock_qs.filter(item__consignor__consignee__id__in=temp_dict["item__consignor__consignee"])
#     if temp_dict["keyword_search"]:
#         stock_qs = stock_qs.filter(
#             Q(item__code__icontains=temp_dict["keyword_search"]) | Q(item__name__icontains=temp_dict["keyword_search"])
#         )
#     if temp_dict["item__brand"]:
#         item_brand_list = temp_dict["item__brand"]
#         query = Q(item__brand__icontains=item_brand_list[0])
#         for brand in item_brand_list[1:]:
#             query |= Q(item__brand__icontains=brand)

#         stock_qs = stock_qs.filter(query)

#     # date range filter
#     if start_date and end_date:
#         added_one_day_end_date = end_date + datetime.timedelta(days=1)
#         subquery .objects.filter(
#             transaction_datetime__gte=start_date,
#             transaction_datetime__lte=added_one_day_end_date,
#             id=OuterRef("transaction__id"),
#         )
#         stock_qs = stock_qs.filter(
#             transaction__id__in=subquery,
#         )
#     elif start_date and not end_date:
#         subquery = Transaction.objects.filter(
#             transaction_datetime__gte=start_date,
#             id=OuterRef("transaction__id"),
#         )
#         stock_qs = stock_qs.filter(
#             transaction__id__in=subquery,
#         )
#     elif end_date and not start_date:
#         added_one_day_end_date = end_date + datetime.timedelta(days=1)
#         subquery = Transaction.objects.filter(
#             transaction_datetime__lte=added_one_day_end_date,
#             id=OuterRef("transaction__id"),
#         )
#         stock_qs = stock_qs.filter(
#             transaction__id__in=subquery,
#         )

#     stock_qs = stock_qs.annotate(transaction_system_quantity=Sum("transaction__system_quantity"))

#     # to set excel date range display
#     cell = worksheet.cell(row=1, column=1)
#     cell.value = "START DATE:"
#     cell = worksheet.cell(row=1, column=2)
#     cell.value = excel_display_start_date
#     cell = worksheet.cell(row=1, column=4)
#     cell.value = "END DATE:"
#     cell = worksheet.cell(row=1, column=5)
#     cell.value = excel_display_end_date

#     row_num = 3
#     # Iterate through all filtered stock_qs
#     for stock in stock_qs:
#         row_num += 1

#         # Define the data for each cell in the row
#         row = [
#             stock.warehouse.name,
#             stock.item.consignor.display_name,
#             stock.item.code,
#             stock.item.brand,
#             stock.item.name,
#             stock.batch_no,
#             stock.expiry_date,
#             stock.transaction_system_quantity,
#             stock.item.uom.symbol,
#         ]

#         # Assign the data for each cell of the row
#         for col_num, cell_value in enumerate(row, 1):
#             cell = worksheet.cell(row=row_num, column=col_num)
#             cell.value = cell_value

#     workbook.save(response)

#     return response

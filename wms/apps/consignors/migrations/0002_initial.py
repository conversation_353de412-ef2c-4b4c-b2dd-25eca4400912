# Generated by Django 5.1 on 2025-03-15 13:08

import django.contrib.postgres.indexes
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('consignors', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='billingaddress',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Created by'),
        ),
        migrations.AddField(
            model_name='billingaddress',
            name='modified_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Modified by'),
        ),
        migrations.AddField(
            model_name='consignor',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Created by'),
        ),
        migrations.AddField(
            model_name='consignor',
            name='modified_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Modified by'),
        ),
        migrations.AddField(
            model_name='billingaddress',
            name='consignor',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='consignors.consignor'),
        ),
        migrations.AddField(
            model_name='consignorcontact',
            name='consignor',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='consignors.consignor'),
        ),
        migrations.AddField(
            model_name='consignorcontact',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Created by'),
        ),
        migrations.AddField(
            model_name='consignorcontact',
            name='modified_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Modified by'),
        ),
        migrations.AddField(
            model_name='shippingaddress',
            name='consignor',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='consignors.consignor'),
        ),
        migrations.AddField(
            model_name='shippingaddress',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Created by'),
        ),
        migrations.AddField(
            model_name='shippingaddress',
            name='modified_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Modified by'),
        ),
        migrations.AddIndex(
            model_name='consignor',
            index=django.contrib.postgres.indexes.GinIndex(fields=['code'], name='consignor_code_gin', opclasses=['gin_trgm_ops']),
        ),
        migrations.AddIndex(
            model_name='consignor',
            index=models.Index(fields=['code'], name='consignors__code_9b3a89_idx'),
        ),
    ]

# Generated by Django 5.1 on 2025-03-15 13:08

import django.utils.timezone
import django_extensions.db.fields
import model_utils.fields
import phonenumber_field.modelfields
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('cores', '0001_create_extensions'),
    ]

    operations = [
        migrations.CreateModel(
            name='BillingAddress',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('address_attention', models.CharField(blank=True, max_length=256, verbose_name='Attention')),
                ('address_street_1', models.Char<PERSON>ield(blank=True, max_length=128, verbose_name='Street 1')),
                ('address_street_2', models.CharField(blank=True, max_length=128, verbose_name='Street 2')),
                ('address_postal_code', models.CharField(blank=True, max_length=32, verbose_name='Postal Code')),
                ('address_district', models.CharField(blank=True, max_length=128, verbose_name='District')),
                ('address_city', models.CharField(blank=True, max_length=128, verbose_name='City')),
                ('address_state', models.CharField(blank=True, max_length=128, verbose_name='State')),
                ('address_country', models.CharField(blank=True, max_length=128, verbose_name='Country')),
                ('address_phone', phonenumber_field.modelfields.PhoneNumberField(blank=True, help_text='Phone number (e.g. +60128585299).', max_length=128, region=None, verbose_name='Phone')),
                ('is_primary', models.BooleanField(default=False, verbose_name='Is primary?')),
            ],
            options={
                'ordering': ['-created'],
                'get_latest_by': 'created',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='Consignor',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('system_number', models.CharField(blank=True, max_length=32, verbose_name='System Number')),
                ('company_name', models.CharField(blank=True, max_length=128, verbose_name='Company Name')),
                ('display_name', models.CharField(max_length=255, unique=True, verbose_name='Display Name')),
                ('slug', django_extensions.db.fields.AutoSlugField(blank=True, editable=False, max_length=255, populate_from=['display_name'], unique=True, verbose_name='Slug')),
                ('code', models.CharField(max_length=128, unique=True, verbose_name='Code')),
                ('website', models.URLField(blank=True, verbose_name='Website')),
                ('remark', models.TextField(blank=True, max_length=512, verbose_name='Remark')),
            ],
            options={
                'ordering': ['display_name'],
                'get_latest_by': 'created',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ConsignorContact',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('sort_order', models.PositiveIntegerField(default=0, verbose_name='Ordering')),
                ('is_primary', models.BooleanField(default=False, verbose_name='Is primary?')),
                ('salutation', models.CharField(blank=True, choices=[('', '---'), ('MR', 'Mr.'), ('MRS', 'Mrs.'), ('MS', 'Ms.'), ('MISS', 'Miss.'), ('DR', 'Dr.')], default='', max_length=5, verbose_name='Salutation')),
                ('first_name', models.CharField(max_length=128, verbose_name='First Name')),
                ('last_name', models.CharField(blank=True, max_length=128, verbose_name='Last Name')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='Consignor Email')),
                ('phone', phonenumber_field.modelfields.PhoneNumberField(blank=True, max_length=128, region=None, verbose_name='Phone No.')),
                ('mobile', phonenumber_field.modelfields.PhoneNumberField(blank=True, max_length=128, region=None, verbose_name='Mobile No.')),
                ('designation', models.CharField(blank=True, max_length=128, verbose_name='Designation')),
                ('department', models.CharField(blank=True, max_length=128, verbose_name='Department')),
            ],
            options={
                'ordering': ['sort_order'],
                'get_latest_by': 'created',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ShippingAddress',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('address_attention', models.CharField(blank=True, max_length=256, verbose_name='Attention')),
                ('address_street_1', models.CharField(blank=True, max_length=128, verbose_name='Street 1')),
                ('address_street_2', models.CharField(blank=True, max_length=128, verbose_name='Street 2')),
                ('address_postal_code', models.CharField(blank=True, max_length=32, verbose_name='Postal Code')),
                ('address_district', models.CharField(blank=True, max_length=128, verbose_name='District')),
                ('address_city', models.CharField(blank=True, max_length=128, verbose_name='City')),
                ('address_state', models.CharField(blank=True, max_length=128, verbose_name='State')),
                ('address_country', models.CharField(blank=True, max_length=128, verbose_name='Country')),
                ('address_phone', phonenumber_field.modelfields.PhoneNumberField(blank=True, help_text='Phone number (e.g. +60128585299).', max_length=128, region=None, verbose_name='Phone')),
                ('is_primary', models.BooleanField(default=False, verbose_name='Is primary?')),
            ],
            options={
                'ordering': ['-created'],
                'get_latest_by': 'created',
                'abstract': False,
            },
        ),
    ]

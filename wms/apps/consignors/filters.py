# from wss.cores.filters import AbstractHistoryFilter


# class ConsignorHistoryFilter(AbstractHistoryFilter):
#     """Filter class for Consignor's History DataTables."""

#     pass

# from django.db.models import Sum, OuterRef, Subquery, Value, DecimalField
# from django.db.models.functions import Coalesce
# from django.utils.translation import gettext_lazy as _
# from django_filters import filters, FilterSet, ChoiceFilter
# from django.db.models import Q

# from wms.apps.consignors.models import Consignor
# from wms.cores.forms.fields import FormFieldSize
# from wms.cores.forms.widget import CoreSelectWidget, CoreSelectMultipleWidget, CoreTextWidget



# class ConsignorFilter(FilterSet):
#     """
#     Filter class for Consignor list view.
#     Provides filtering capabilities for Consignor attributes.
#     """

#     consignor = filters.MultipleChoiceFilter(
#         choices=[],  # Will be populated dynamically
#         field_name='consignor__code',
#         label="Consignor",
#         widget=CoreSelectMultipleWidget()
#     )

#     brand = filters.MultipleChoiceFilter(
#         choices=[],  # Will be populated dynamically
#         label="Brand",
#         widget=CoreSelectMultipleWidget()
#     )

#     stock_on_hand = StockOnHandFilter(
#         choices=[
#             ('all', _('All')),
#             ('gt_zero', _('Greater than 0')),
#             ('lt_zero', _('Less than 0')),
#         ],
#         empty_label=None,
#         label=_("SOH"),
#         widget=CoreSelectWidget()
#     )

#     # status = filters.ChoiceFilter(
#     #     choices=[
#     #         ('active', 'Active'),
#     #         ('inactive', 'Inactive'),
#     #     ],
#     #     empty_label="All Status",
#     #     label="Status",
#     #     widget=CoreSelectWidget()
#     # )

#     class Meta:
#         model = Consignor
#         fields = [
#             'consignor',
#             'brand',
#             'stock_on_hand'
#         ]

#     def __init__(self, data=None, queryset=None, *, request=None, prefix=None):
#         super().__init__(data=data, queryset=queryset, request=request, prefix=prefix)

#         # Dynamically populate location choices from existing data
#         if queryset is not None:
#             # Update field choices after initialization
#             self.form.fields['consignor'].choices = get_item_consignor_choices()
#             self.form.fields['brand'].choices = get_item_brand_choices()

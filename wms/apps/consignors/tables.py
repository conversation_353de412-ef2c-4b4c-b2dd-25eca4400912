# from django.urls import reverse
from django.utils.translation import gettext_lazy as _

# import django_tables2 as tables
# from actstream.models import Action

# from wms.cores.models import DISPLAY_EMPTY_VALUE
# from wms.cores.tables import (
#     HTMX_LIST_SM_ATTRS_CLASS,
#     PARTIAL_LIST_ATTRS_CLASS,
#     TABLE_ATTRS_CLASS,
#     AbstractHistoryDataTables,
# )
# from wms.cores.utils import convert_camel_case_to_space

# from .models import Consignor

import django_tables2 as tables
from django.urls import reverse, NoReverseMatch

from .models import Consignor
from wms.cores.columns import HTMXColumn


class ConsignorDetailTable(tables.Table):
    code = HTMXColumn(
        url_name="consignors:detail_home",
        target_id="detail-panel",
        verbose_name="Code",
        push_url=True,
        push_url_name="consignors:panel"
    )

    class Meta:
        model = Consignor
        fields = ("display_name",)
        order_by = 'display_name'
        template_name = "tables/table_htmx.html"

    def __init__(self, *args, object_pk=None, **kwargs):
        super().__init__(*args, **kwargs)
        self.object_pk = object_pk


class ConsignorTable(tables.Table):
    section_title = "Consignor Lists"
    section_name = "Consignor"
    selectable = True
    display_name = tables.LinkColumn("consignors:panel", args=[tables.utils.A("pk")])
    # actions = tables.TemplateColumn(
    #     verbose_name="Actions",
    #     template_name="tables/table_actions_column.html", orderable=False,
    #     extra_context={
    #         "edit_url": "inventories:items:update",
    #         "delete_url": "inventories:items:delete"
    #     }
    # )

    @property
    def create_url(self):
        try:
            return reverse('consignors:create')
        except NoReverseMatch:
            return None

    class Meta:
        model = Consignor
        order_by = 'system_number'
        template_name = "tables/table_htmx.html"
        fields = (
            "system_number",
            "display_name",
            "company_name",
            "code",
            # "actions"
        )

# class ConsignorTable(tables.Table):
#     """Table used on consignor list page."""

#     display_name = tables.Column(verbose_name=_("Display Name"), linkify=True)
#     primary_contact_formal_full_name = tables.Column(
#         verbose_name=_("Primary Contact"), accessor="get_primary_contact_formal_full_name"
#     )
#     primary_contact_phone = tables.Column(verbose_name=_("Phone"), accessor="get_primary_contact_phone")
#     primary_contact_email = tables.TemplateColumn(
#         verbose_name=_("Email"),
#         accessor="get_primary_contact_email",
#         template_name="consignors/partials/tables/_primary_contact_email.html",
#     )
#     grn_count = tables.Column(verbose_name=_("GRN Count"), accessor="goods_received_notes")
#     wro_count = tables.Column(verbose_name=_("WRO Count"), accessor="warehouse_release_orders")
#     actions = tables.TemplateColumn(
#         verbose_name=_("Actions"),
#         orderable=False,
#         template_name="consignors/partials/tables/_actions.html",
#         attrs={"th": {"class": "text-right"}, "td": {"class": "text-right"}},
#         exclude_from_export=True,
#     )

#     class Meta:
#         model = Consignor
#         order_by = "numbering"  # Must follow the first row display in the table
#         default = DISPLAY_EMPTY_VALUE
#         template_name = "partials/tables/_datatables.html"
#         attrs = {"class": TABLE_ATTRS_CLASS, "id": "consignor_list_table", "style": "display: none;"}
#         row_attrs = {"data-id": lambda record: record.pk}

#         fields = [
#             "numbering",
#             "display_name",
#             "company_name",
#             "code",
#         ]
#         sequence = [
#             "numbering",
#             "display_name",
#             "company_name",
#             "code",
#             "primary_contact_formal_full_name",
#             "primary_contact_phone",
#             "primary_contact_email",
#             "grn_count",
#             "wro_count",
#             "actions",
#         ]

#     def render_grn_count(self, value):
#         return value.count()

#     def render_wro_count(self, value):
#         return value.count()


# class ConsignorPartialListTable(tables.Table):
#     """Table used on consignor detail page."""

#     display_name = tables.Column(verbose_name=_("Display Name"), linkify=True)

#     class Meta:
#         model = Consignor
#         order_by = "numbering"  # Must follow the first row display in the table
#         default = DISPLAY_EMPTY_VALUE
#         template_name = "partials/tables/_datatables.html"
#         attrs = {
#             "class": PARTIAL_LIST_ATTRS_CLASS + " table-wrap",
#             "id": "consignor_list_table",
#             "style": "display: none;",
#         }
#         row_attrs = {"data-id": lambda record: record.pk}

#         fields = [
#             "numbering",
#             "display_name",
#         ]


# class ConsignorHistoryDataTables(AbstractHistoryDataTables):
#     """Table used on Consignor's history tab."""

#     class Meta(AbstractHistoryDataTables.Meta):
#         attrs = {
#             "class": HTMX_LIST_SM_ATTRS_CLASS,
#             "id": "consignor_history_datatables",
#             "style": "display: none;",
#             "thead": {"class": "bg-secondary"},
#         }

#     def render_verb(self, value: str, record: Action) -> str:
#         if value == "created BillingAddress":
#             return "Created Billing Address"
#         elif value == "created ShippingAddress":
#             return "Created Shipping Address"
#         elif value == "created ConsignorContact":
#             return "Created Consignor Contact"

#         value_string = convert_camel_case_to_space(value)
#         value_string = value_string[0].upper() + value_string[1:]

#         if value.startswith("modified"):
#             link = reverse("consignors:history-modified", kwargs={"pk": record.pk})
#             htmx_modal_attributes = (
#                 'href="#" style="text-decoration: underline dotted" '
#                 'data-toggle="modal" data-target="#modalXl" '
#                 'hx-target="#modalXlBody" hx-swap="innerHTML"'
#             )
#             return f'<a {htmx_modal_attributes} hx-get="{link}">{value_string}</a>'
#         else:
#             return value_string

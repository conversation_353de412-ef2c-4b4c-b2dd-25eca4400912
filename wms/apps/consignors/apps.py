from django.apps import AppConfig
from django.utils.translation import gettext_lazy as _


class SalesConfig(AppConfig):
    name = "wms.apps.consignors"
    verbose_name = _("Consignors")

    def ready(self):
        """Enabled signals and actstream."""
        from actstream import registry

        import wms.apps.consignors.signals  # noqa F401
        from wms.apps.consignors.models import <PERSON>ing<PERSON><PERSON><PERSON>, Consignor, ConsignorContact, ShippingAddress

        registry.register(Consignor, ConsignorContact, ShippingAddress, BillingAddress)

        # To prevent related activity stream being removed when object is deleted.

        def not_target_actions(field):
            return field.name not in ["target_actions", "action_object_actions"]

        # consignors apps
        Consignor._meta.private_fields = list(filter(not_target_actions, Consignor._meta.private_fields))
        ConsignorContact._meta.private_fields = list(filter(not_target_actions, ConsignorContact._meta.private_fields))
        ShippingAddress._meta.private_fields = list(filter(not_target_actions, ShippingAddress._meta.private_fields))
        BillingAddress._meta.private_fields = list(filter(not_target_actions, BillingAddress._meta.private_fields))

from django.contrib import admin
from django.contrib.postgres.indexes import GinIndex
from django.db import models
from django.urls import reverse
from django.utils.functional import cached_property
from django.utils.translation import gettext_lazy as _

from django_extensions.db.fields import AutoSlugField
from phonenumber_field.modelfields import PhoneNumberField

from wms.cores.models import (
    DISPLAY_EMPTY_VALUE,
    AbstractAddressModel,
    AbstractBaseModel,
    AbstractSystemNumberModel,
    AbstractSortableModel,
)

# from wms.apps.releases.models import WarehouseReleaseOrder


class BillingAddress(AbstractAddressModel, AbstractBaseModel):
    """BillingAddress model for Warehouse Management System.

    Available fields:

    * created             (AbstractBaseModel => TimeStampedModel)
    * modified            (AbstractBaseModel => TimeStampedModel)
    * created_by          (AbstractBaseModel)
    * modified_by         (AbstractBaseModel)
    * address_attention   (AbstractAddressModel)
    * address_street_1    (AbstractAddressModel)
    * address_street_2    (AbstractAddressModel)
    * address_postal_code (AbstractAddressModel)
    * address_district    (AbstractAddressModel)
    * address_city        (AbstractAddressModel)
    * address_state       (AbstractAddressModel)
    * address_country     (AbstractAddressModel)
    * address_phone       (AbstractAddressModel)
    * is_primary          (AbstractAddressModel)
    * consignor

    """

    consignor = models.ForeignKey("consignors.Consignor", on_delete=models.CASCADE)

    class Meta(AbstractBaseModel.Meta):
        pass

    def __str__(self):
        return self.get_address

    def save(self, *args, **kwargs):
        model = self.__class__

        # Only 1 billing address can set as primary for each consignor
        if self.pk and self.is_primary:
            model.objects.exclude(pk=self.pk).filter(consignor=self.consignor).update(is_primary=False)

        super().save(*args, **kwargs)


class ShippingAddress(AbstractAddressModel, AbstractBaseModel):
    """ShippingAddress model for Warehouse Management System.

    Available fields:

    * created             (AbstractBaseModel => TimeStampedModel)
    * modified            (AbstractBaseModel => TimeStampedModel)
    * created_by          (AbstractBaseModel)
    * modified_by         (AbstractBaseModel)
    * address_attention   (AbstractAddressModel)
    * address_street_1    (AbstractAddressModel)
    * address_street_2    (AbstractAddressModel)
    * address_postal_code (AbstractAddressModel)
    * address_district    (AbstractAddressModel)
    * address_city        (AbstractAddressModel)
    * address_state       (AbstractAddressModel)
    * address_country     (AbstractAddressModel)
    * address_phone       (AbstractAddressModel)
    * is_primary          (AbstractAddressModel)
    * consignor

    """

    consignor = models.ForeignKey("consignors.Consignor", on_delete=models.CASCADE)

    class Meta(AbstractBaseModel.Meta):
        pass

    def __str__(self):
        return self.get_address

    def save(self, *args, **kwargs):
        model = self.__class__

        # Only 1 shipping address can set as primary for each consignor
        if self.pk and self.is_primary:
            model.objects.exclude(pk=self.pk).filter(consignor=self.consignor).update(is_primary=False)

        super().save(*args, **kwargs)


class Consignor(AbstractSystemNumberModel, AbstractBaseModel):
    """Consignor model for Warehouse Management System.

    Available fields:

    * created           (AbstractBaseModel => TimeStampedModel)
    * modified          (AbstractBaseModel => TimeStampedModel)
    * created_by        (AbstractBaseModel)
    * modified_by       (AbstractBaseModel)
    * numbering         (AbstractSystemNumberModel)
    * company_name
    * display_name
    * slug
    * code
    * website
    * remark

    """

    company_name = models.CharField(verbose_name=_("Company Name"), max_length=128, blank=True)
    display_name = models.CharField(verbose_name=_("Display Name"), max_length=255, unique=True)
    slug = AutoSlugField(
        verbose_name=_("Slug"), populate_from=["display_name"], max_length=255, editable=True, unique=True
    )
    code = models.CharField(verbose_name=_("Code"), max_length=128, unique=True)
    website = models.URLField(verbose_name=_("Website"), blank=True)
    remark = models.TextField(verbose_name=_("Remark"), max_length=512, blank=True)

    class Meta(AbstractBaseModel.Meta):
        ordering = ["display_name"]
        indexes = [
            # GinIndex for `icontains` searches
            GinIndex(name="consignor_code_gin", fields=["code"], opclasses=["gin_trgm_ops"]),
            # single index
            models.Index(fields=["code"]),
        ]

    def __str__(self):
        return self.display_name

    def get_absolute_url(self):
        return reverse("consignors:detail", kwargs={"slug": self.slug})

    @cached_property
    def primary_contact(self):
        return self.consignorcontact_set.filter(is_primary=True).first()

    @cached_property
    def billing_address(self):
        return self.billingaddress_set.filter(is_primary=True).first()

    @cached_property
    def shipping_address(self):
        return self.shippingaddress_set.filter(is_primary=True).first()

    @admin.display(description=_("Primary contact"))
    @cached_property
    def get_primary_contact_formal_full_name(self):
        if self.primary_contact:
            return self.primary_contact.get_formal_full_name
        else:
            return DISPLAY_EMPTY_VALUE

    @admin.display(description=_("Phone"))
    @cached_property
    def get_primary_contact_phone(self):
        if self.primary_contact:
            return self.primary_contact.phone
        else:
            return DISPLAY_EMPTY_VALUE

    @admin.display(description=_("Email"))
    @cached_property
    def get_primary_contact_email(self):
        if self.primary_contact:
            return self.primary_contact.email
        else:
            return DISPLAY_EMPTY_VALUE

    @cached_property
    def items(self):
        return self.item_set.all()

    @cached_property
    def consignees(self):
        return self.consignee_set.all()

    @cached_property
    def goods_received_notes(self):
        return self.goodsreceivednote_set.all()

    @cached_property
    def warehouse_release_orders(self):
        return WarehouseReleaseOrder.objects.filter(consignee__consignor=self)

    @cached_property
    def delivery_orders(self):
        return WarehouseReleaseOrder.objects.filter(is_delivery_order=True, consignee__consignor=self)

    def save(self, *args, **kwargs):

        # Convert to uppercase
        self.company_name = self.company_name.upper()
        self.display_name = self.display_name.upper()
        self.code = self.code.upper()

        super().save(*args, **kwargs)


class ConsignorContact(AbstractBaseModel, AbstractSortableModel):
    """ConsignorContact model for Warehouse Management System.

    Available fields:

    * created           (AbstractBaseModel => TimeStampedModel)
    * modified          (AbstractBaseModel => TimeStampedModel)
    * created_by        (AbstractBaseModel)
    * modified_by       (AbstractBaseModel)
    * sort_order        (AbstractSortableModel)
    * consignor
    * is_primary
    * salutation
    * first_name
    * last_name
    * email
    * phone
    * mobile
    * designation
    * department

    """

    consignor = models.ForeignKey("consignors.Consignor", on_delete=models.CASCADE)
    is_primary = models.BooleanField(verbose_name=_("Is primary?"), default=False)

    class Salutation(models.TextChoices):
        UNDEFINED = "", DISPLAY_EMPTY_VALUE
        MR = "MR", _("Mr.")
        MRS = "MRS", _("Mrs.")
        MS = "MS", _("Ms.")
        MISS = "MISS", _("Miss.")
        DR = "DR", _("Dr.")

    salutation = models.CharField(
        verbose_name=_("Salutation"), max_length=5, choices=Salutation.choices, default=Salutation.UNDEFINED, blank=True
    )
    first_name = models.CharField(verbose_name=_("First Name"), max_length=128)
    last_name = models.CharField(verbose_name=_("Last Name"), max_length=128, blank=True)
    email = models.EmailField(verbose_name=_("Consignor Email"), blank=True)
    phone = PhoneNumberField(verbose_name=_("Phone No."), blank=True)
    mobile = PhoneNumberField(verbose_name=_("Mobile No."), blank=True)
    designation = models.CharField(verbose_name=_("Designation"), max_length=128, blank=True)
    department = models.CharField(verbose_name=_("Department"), max_length=128, blank=True)

    class Meta(AbstractBaseModel.Meta):
        ordering = ["sort_order"]

    def __str__(self):
        return f"{self.consignor.display_name}'s contact: {self.get_formal_full_name}"

    @cached_property
    def get_full_name(self):
        return f"{self.first_name} {self.last_name}".strip() or DISPLAY_EMPTY_VALUE

    @admin.display(ordering="first_name", description=_("Contact"))
    @cached_property
    def get_formal_full_name(self):
        if self.salutation:
            return f"{self.get_salutation_display()} {self.get_full_name}".strip()
        else:
            return f"{self.get_full_name}".strip() or DISPLAY_EMPTY_VALUE

    def save(self, *args, **kwargs):

        # Convert to uppercase
        self.first_name = self.first_name.upper()
        self.last_name = self.last_name.upper()
        self.designation = self.designation.upper()
        self.department = self.department.upper()

        model = self.__class__

        # Only 1 contact can set as primary for each consignor
        if self.pk and self.is_primary:
            model.objects.exclude(pk=self.pk).filter(consignor=self.consignor).update(is_primary=False)

        super().save(*args, **kwargs)

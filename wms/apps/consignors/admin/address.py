from django.contrib import admin

from import_export import resources
from import_export.admin import ImportExportModelAdmin

from wms.cores.admin import BaseModelAdmin

from ..models import BillingAddress, ShippingAddress


class BillingAddressResource(resources.ModelResource):
    class Meta:
        model = BillingAddress
        skip_unchanged = True
        exclude = (
            "created_by",
            "created",
            "modified_by",
            "modified",
        )


class ShippingAddressResource(resources.ModelResource):
    class Meta:
        model = ShippingAddress
        skip_unchanged = True
        exclude = (
            "created_by",
            "created",
            "modified_by",
            "modified",
        )


@admin.register(BillingAddress)
class BillingAddressAdmin(BaseModelAdmin, ImportExportModelAdmin):
    """Django admin for BillingAddress."""

    resource_class = BillingAddressResource

    list_display = [
        "pk",
        "is_primary",
        "consignor",
        "address_attention",
        "address_street_1",
        "address_street_2",
        "address_postal_code",
        "address_district",
        "address_city",
        "address_state",
        "address_country",
        "address_phone",
    ]
    search_fields = [
        "address_attention",
        "address_state",
        "address_district",
        "address_street_1",
        "address_street_2",
        "address_postal_code",
    ]


@admin.register(ShippingAddress)
class ShippingAddressAdmin(BaseModelAdmin, ImportExportModelAdmin):
    """Django admin for ShippingAddress."""

    resource_class = ShippingAddressResource

    list_display = [
        "pk",
        "is_primary",
        "consignor",
        "address_attention",
        "address_street_1",
        "address_street_2",
        "address_postal_code",
        "address_district",
        "address_city",
        "address_state",
        "address_country",
        "address_phone",
    ]
    search_fields = [
        "address_attention",
        "address_state",
        "address_district",
        "address_street_1",
        "address_street_2",
        "address_postal_code",
    ]

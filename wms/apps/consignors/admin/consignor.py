from django.contrib import admin

from import_export import resources
from import_export.admin import ImportExportModelAdmin

from wms.cores.admin import BaseModelAdmin, BaseStackedInline

from ..models import BillingAddress, Consignor, ConsignorContact, ShippingAddress


class ConsignorResource(resources.ModelResource):
    class Meta:
        model = Consignor
        skip_unchanged = True
        exclude = (
            "created_by",
            "created",
            "modified_by",
            "modified",
        )


class ConsignorContactResource(resources.ModelResource):
    class Meta:
        model = ConsignorContact
        skip_unchanged = True
        exclude = (
            "created_by",
            "created",
            "modified_by",
            "modified",
        )


class ConsignorContactInline(BaseStackedInline):
    model = ConsignorContact
    extra = 1


class BillingAddressInline(BaseStackedInline):
    model = BillingAddress
    extra = 1


class ShippingAddressInline(BaseStackedInline):
    model = ShippingAddress
    extra = 1


@admin.register(Consignor)
class ConsignorAdmin(BaseModelAdmin):
    """Django admin for Consignor."""

    resource_class = ConsignorResource

    list_display = [
        "display_name",
        "system_number",
        "slug",
        "code",
        "company_name",
        "get_primary_contact_formal_full_name",
        "website",
        "remark",
    ]
    inlines = [ConsignorContactInline, BillingAddressInline, ShippingAddressInline]
    search_fields = [
        "display_name",
        "company_name",
        "remark",
    ]
    fieldsets = (
        (
            None,
            {
                "fields": (
                    "company_name",
                    "display_name",
                    "slug",
                    "code",
                    "system_number",
                    "website",
                    "remark",
                )
            },
        ),
    )


@admin.register(ConsignorContact)
class ConsignorContactAdmin(BaseModelAdmin, ImportExportModelAdmin):
    """Django admin for ConsignorContact."""

    resource_class = ConsignorContactResource

    list_display = [
        "pk",
        "consignor",
        "is_primary",
        "salutation",
        "first_name",
        "last_name",
        "email",
        "phone",
        "mobile",
        "designation",
        "department",
    ]
    search_fields = [
        "first_name",
        "last_name",
        "email",
    ]

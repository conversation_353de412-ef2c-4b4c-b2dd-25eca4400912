from django.urls import path

# from wss.apps.consignors.views import (
#     consignor_billing_address_detail_view,
#     consignor_billing_address_update_view,
#     consignor_consignee_dropdown_list_view,
#     consignor_consignee_list_view,
#     consignor_create_view,
#     consignor_delete_view,
#     consignor_detail_view,
#     consignor_history_datatables_view,
#     consignor_history_list_view,
#     consignor_history_modified_view,
#     consignor_info_detail_view,
#     consignor_info_update_view,
#     consignor_item_dropdown_list_view,
#     consignor_item_list_view,
#     consignor_list_view,
#     consignor_primary_contact_detail_view,
#     consignor_primary_contact_update_view,
#     consignor_shipping_address_detail_view,
#     consignor_shipping_address_update_view,
#     consignor_update_view,
#     consignor_user_create_view,
#     consignor_user_list_view,
#     consignor_user_update_password_view,
#     consignor_user_update_view,
# )

from wms.apps.consignors.views import (
    ConsignorListView,
    ConsignorCreateView,
    Consignor<PERSON>p<PERSON>Vie<PERSON>,
    ConsignorD<PERSON>ilView,
    ConsignorD<PERSON>ilHomeView,
    ConsignorEventView,
    ConsignorDataTableDetailView,
)


app_name = "consignors"


urlpatterns = [
    path("", view=ConsignorListView.as_view(), name="list"),
    path("create/", ConsignorCreateView.as_view(), name="create"),
    path("<int:pk>/update/", ConsignorUpdateView.as_view(), name="update"),
    path("<int:pk>/", ConsignorDataTableDetailView.as_view(), name="panel"),
    path("<int:pk>/home/", ConsignorDetailHomeView.as_view(), name="detail_home"),
    path("<int:pk>/detail/", ConsignorDetailView.as_view(), name="detail"),
    path("<int:pk>/event/", ConsignorEventView.as_view(), name="event"),
#     path("create/", view=consignor_create_view, name="create"),
#     path("update/<str:slug>/", view=consignor_update_view, name="update"),
#     path("delete/<str:slug>/", view=consignor_delete_view, name="delete"),
#     path("detail/<str:slug>/", view=consignor_detail_view, name="detail"),
#     path("detail/<str:slug>/history/", view=consignor_history_list_view, name="history"),
#     path("detail/<int:pk>/history/popup-modified", view=consignor_history_modified_view, name="history-modified"),
#     path("detail/<str:slug>/datatables-history/", view=consignor_history_datatables_view, name="datatables-history"),
#     path("detail/<str:slug>/info/", view=consignor_info_detail_view, name="info"),
#     path("detail/<str:slug>/info/update/", view=consignor_info_update_view, name="info_update"),
#     path("detail/<str:slug>/primary-contact/", view=consignor_primary_contact_detail_view, name="primary_contact"),
#     path(
#         "detail/<str:slug>/primary-contact/update/",
#         view=consignor_primary_contact_update_view,
#         name="primary_contact_update",
#     ),
#     path("detail/<str:slug>/billing-address/", view=consignor_billing_address_detail_view, name="billing_address"),
#     path(
#         "detail/<str:slug>/billing-address/update/",
#         view=consignor_billing_address_update_view,
#         name="billing_address_update",
#     ),
#     path("detail/<str:slug>/shipping-address/", view=consignor_shipping_address_detail_view, name="shipping_address"),
#     path(
#         "detail/<str:slug>/shipping-address/update/",
#         view=consignor_shipping_address_update_view,
#         name="shipping_address_update",
#     ),
#     path("detail/<str:slug>/consignees/", view=consignor_consignee_list_view, name="consignees"),
#     path("dropdown/consignees/", view=consignor_consignee_dropdown_list_view, name="consignees_dropdown"),
#     path("detail/<str:slug>/items/", view=consignor_item_list_view, name="items"),
#     path("dropdown/items/", view=consignor_item_dropdown_list_view, name="items_dropdown"),
#     path("detail/<str:slug>/user/", view=consignor_user_list_view, name="users"),
#     path("detail/<str:slug>/user/create/", view=consignor_user_create_view, name="users_create"),
#     path("detail/<str:slug>/user/<int:pk>/update/", view=consignor_user_update_view, name="users_update"),
#     path(
#         "detail/<str:slug>/user/<int:pk>/update-password/",
#         view=consignor_user_update_password_view,
#         name="users_update_password",
#     ),
]

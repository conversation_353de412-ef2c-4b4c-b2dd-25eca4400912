from rest_framework import viewsets
from rest_framework.response import Response
from rest_framework.decorators import action
from django.db.models import Q
from django.shortcuts import get_object_or_404

from wms.apps.consignors.models import Consignor
from wms.apps.inventories.models import Item
from wms.cores.views import Select2Pagination
from .serializers import ConsignorSerializer, ItemSerializer


class ConsignorViewSet(viewsets.ViewSet):
    """
    ViewSet for consignor operations.
    """
    pagination_class = Select2Pagination

    def retrieve(self, request, pk=None):
        """
        Get a single consignor by ID.

        Parameters:
        - pk: Consignor ID
        """
        # Get the consignor by ID
        consignor = get_object_or_404(Consignor, pk=pk)

        # Serialize and return the consignor
        serializer = ConsignorSerializer(consignor)
        return Response(serializer.data)

    def list(self, request):
        """
        Get a list of consignors.

        Query parameters:
        - search: Search term for filtering consignors
        - page: Page number for pagination
        - page_size: Number of items per page
        """
        # Get search term from query params
        search_term = request.query_params.get('search', '')

        # Filter consignors
        queryset = Consignor.objects.all()

        # Apply search if provided
        if search_term:
            queryset = queryset.filter(
                Q(display_name__icontains=search_term) |
                Q(code__icontains=search_term) |
                Q(company_name__icontains=search_term)
            )

        # Apply pagination
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)

        if page is not None:
            serializer = ConsignorSerializer(page, many=True)
            return paginator.get_paginated_response(serializer.data)

        serializer = ConsignorSerializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'], url_path='items/(?P<item_id>[^/.]+)')
    def item_detail(self, request, pk=None, item_id=None):
        """
        Get a specific item for a consignor.

        Parameters:
        - pk: Consignor ID
        - item_id: Item ID
        """
        # Verify consignor exists
        consignor = get_object_or_404(Consignor, pk=pk)

        # Get the item
        item = get_object_or_404(Item, pk=item_id, consignor=consignor)

        # Serialize and return the item
        serializer = ItemSerializer(item)
        return Response(serializer.data)

    @action(detail=True, methods=['get'], url_path='items')
    def items(self, request, pk=None):
        """
        Get items for a specific consignor.

        Parameters:
        - pk: Consignor ID

        Query parameters:
        - search: Search term for filtering items
        - page: Page number for pagination
        - page_size: Number of items per page
        """
        # Verify consignor exists
        consignor = get_object_or_404(Consignor, pk=pk)

        # Get search term from query params
        search_term = request.query_params.get('search', '')

        # Filter items
        queryset = Item.objects.filter(consignor=consignor).select_related('uom')

        # Apply search if provided
        if search_term:
            queryset = queryset.filter(
                Q(name__icontains=search_term) |
                Q(code__icontains=search_term)
            )

        # Apply pagination
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)

        if page is not None:
            serializer = ItemSerializer(page, many=True)
            return paginator.get_paginated_response(serializer.data)

        serializer = ItemSerializer(queryset, many=True)
        return Response(serializer.data)

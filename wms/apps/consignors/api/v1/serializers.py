from rest_framework import serializers
from wms.apps.consignors.models import Consignor
from wms.apps.inventories.models import Item


class ConsignorSerializer(serializers.ModelSerializer):
    """Serializer for Consignor model."""
    name = serializers.CharField(source='display_name')

    class Meta:
        model = Consignor
        fields = ['id', 'name']


class ItemSerializer(serializers.ModelSerializer):
    """Serializer for Item model with batch information."""
    id = serializers.IntegerField()
    name = serializers.SerializerMethodField()
    batches = serializers.SerializerMethodField()
    uom = serializers.CharField(source='uom.symbol', read_only=True)
    uom_id = serializers.IntegerField(source='uom.id')

    class Meta:
        model = Item
        fields = ['id', 'name', 'uom', 'uom_id', 'batches']

    def get_name(self, obj):
        """Return item name and code in format: "[code] name" """
        return f"[{obj.code}] {obj.name}"

    def get_batches(self, obj):
        """Return all available batches for this item"""
        # Get all stocks for this item
        stocks = obj.stock_set.all().order_by('batch_no', 'expiry_date')

        # If no stocks, return a default 'N/A' batch
        if not stocks.exists():
            return [{
                'batch_no': 'N/A',
                'expiry_dates': ['N/A']
            }]

        # Group stocks by batch_no
        batch_groups = {}
        for stock in stocks:
            batch_no = stock.batch_no if stock.batch_no else 'N/A'

            if batch_no not in batch_groups:
                batch_groups[batch_no] = {
                    'batch_no': batch_no,
                    'expiry_dates': set()
                }

            # Add expiry date to the set (will automatically handle duplicates)
            expiry_date = stock.expiry_date.strftime('%Y-%m-%d') if stock.expiry_date else 'N/A'
            batch_groups[batch_no]['expiry_dates'].add(expiry_date)

        # Convert to list format for serialization
        return [
            {
                'batch_no': batch_data['batch_no'],
                'expiry_dates': list(batch_data['expiry_dates'])
            }
            for batch_data in batch_groups.values()
        ]

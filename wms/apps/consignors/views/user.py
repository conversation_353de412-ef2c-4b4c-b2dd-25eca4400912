# from django import forms
# from django.contrib.auth import get_user_model
# from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
# from django.urls import reverse
# from django.utils.translation import gettext_lazy as _

# from wms.cores.utils import send_notification
# from wms.cores.views import CoreCreateView, CoreListView, CoreUpdateView

# from wms.apps.settings.models import Warehouse

# from ..forms import ConsignorUserForm
# from ..models import Consignor

# UserModel = get_user_model()


# class ConsignorUserCreateAndUpdateMixin:
#     """Mixin for Create and Update Consignor's User."""

#     model = UserModel
#     form_class = ConsignorUserForm
#     template_name = "consignors/users/create_or_update.html"

#     selected_page = "consignors"
#     selected_subpage = None

#     def get_consignor(self):
#         return Consignor.objects.get(slug=self.kwargs["slug"])

#     def get_form(self, form_class=None):
#         form = super().get_form(form_class)

#         available_warehouse_qs = Warehouse.objects.filter(stock__item__consignor=self.consignor).distinct()
#         form.fields["warehouses"].queryset = available_warehouse_qs

#         return form

#     def get_initial(self):
#         initial = super().get_initial()

#         self.consignor = self.get_consignor()
#         initial["consignor"] = self.consignor

#         return initial

#     def form_valid(self, form):
#         self.object = form.save()
#         return super().form_valid(form)

#     def get_success_url(self):
#         return reverse("consignors:detail", kwargs={"slug": self.object.consignor.slug})

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         context["consignor"] = self.consignor
#         return context


# class ConsignorUserCreateView(
#     ConsignorUserCreateAndUpdateMixin, LoginRequiredMixin, PermissionRequiredMixin, CoreCreateView
# ):
#     """Page to create Consignor's User."""

#     success_message = _("Consignor's user %(email)s successfully created")

#     header_title = "New Consignor User"

#     permission_required = ("users.add_user",)

#     def handle_send_notification(self):
#         send_notification(
#             instance=self.object,
#             message=f"Consignor {self.consignor}'s user ({self.object.email}) has been created.",
#             user_role_list=["Superadmin"],
#             level="info",
#         )

#     def form_valid(self, form):
#         """If the form is valid, save the associated model."""
#         self.object = form.save()
#         form.instance.warehouses.set(form.cleaned_data["warehouses"])

#         # handle notification
#         self.handle_send_notification()

#         return super().form_valid(form)


# consignor_user_create_view = ConsignorUserCreateView.as_view()


# class ConsignorUserUpdateView(
#     ConsignorUserCreateAndUpdateMixin, LoginRequiredMixin, PermissionRequiredMixin, CoreUpdateView
# ):
#     """Page to update selected Consignor user based on given Consignor's slug and user pk."""

#     success_message = _("Consignor's user %(email)s successfully updated")

#     header_title = "Update Consignor User"

#     permission_required = ("users.change_user",)

#     def get_form_kwargs(self):
#         kwargs = super().get_form_kwargs()
#         # Example condition to pop fields
#         kwargs["pop_fields"] = ["password1", "password2"]

#         return kwargs

#     def handle_send_notification(self):
#         send_notification(
#             instance=self.object,
#             message=f"Consignor {self.consignor}'s user ({self.object.email}) profile has been changed.",
#             user_role_list=["Superadmin"],
#             level="info",
#         )

#     def form_valid(self, form):
#         """If the form is valid, save the associated model."""
#         self.object = form.save()
#         form.instance.warehouses.set(form.cleaned_data["warehouses"])

#         # handle notification
#         self.handle_send_notification()

#         return super().form_valid(form)


# consignor_user_update_view = ConsignorUserUpdateView.as_view()


# class ConsignorUserUpdatePasswordView(
#     ConsignorUserCreateAndUpdateMixin, LoginRequiredMixin, PermissionRequiredMixin, CoreUpdateView
# ):
#     """Page to update selected Consignor user based on given Consignor's slug and user pk."""

#     success_message = _("Consignor's user password %(email)s successfully updated")

#     header_title = "Update Consignor User Password"

#     permission_required = ("users.change_user",)

#     def get_form(self, form_class=None):
#         form = super().get_form(form_class)
#         form.fields["is_active"].widget = forms.HiddenInput()
#         form.fields["email"].disabled = True
#         form.fields["first_name"].disabled = True
#         form.fields["last_name"].disabled = True
#         form.fields["consignor"].disabled = True
#         form.fields["warehouses"].disabled = True
#         return form

#     def handle_send_notification(self):
#         send_notification(
#             instance=self.object,
#             message=f"Consignor {self.consignor}'s user ({self.object.email}) password has been changed.",
#             user_role_list=["Superadmin"],
#             level="info",
#         )

#     def form_valid(self, form):
#         """If the form is valid, save the associated model."""
#         self.object = form.save()

#         # handle notification
#         self.handle_send_notification()

#         return super().form_valid(form)


# consignor_user_update_password_view = ConsignorUserUpdatePasswordView.as_view()


# # ############
# # # FOR HTMX #
# # ############


# class ConsignorUserListView(LoginRequiredMixin, PermissionRequiredMixin, CoreListView):
#     """Page to show all Consignor User."""

#     model = UserModel
#     template_name = "consignors/partials/htmx/_users.html"

#     permission_required = (
#         "consignors.view_consignor",
#         "users.view_user",
#     )

#     def get_consignor(self):
#         return Consignor.objects.get(slug=self.kwargs["slug"])

#     def get_queryset(self):
#         return self.model.objects.filter(consignor__slug=self.kwargs["slug"])

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         context["consignor"] = self.get_consignor()
#         return context


# consignor_user_list_view = ConsignorUserListView.as_view()

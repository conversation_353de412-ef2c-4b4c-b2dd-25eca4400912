# from typing import Any

# from django.conf import settings
# from django.contrib.auth import get_user_model
# from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
# from django.core.paginator import Paginator
# from django.db.models import BLANK_CHOICE_DASH, Q
# from django.db.models.query import QuerySet
# from django.http import HttpRequest, JsonResponse
# from django.urls import reverse, reverse_lazy
# from django.utils.translation import gettext_lazy as _
from django.views.generic import DeleteView, UpdateView, DetailView

# from actstream.models import Action
# from django_tables2 import SingleTableMixin

# from wms.cores.actstream import query_actstream
# from wms.cores.utils import send_notification
# from wms.cores.views import (
#     CoreBaseHistoryModifiedView,
#     CoreCreateView,
#     CoreDataTablesView,
#     CoreDeleteView,
#     CoreDetailView,
#     CoreListView,
#     CoreUpdateView,
# )

# from wms.apps.consignees.models import Consignee
# from wms.apps.inventories.models import Item

# from ..filters import ConsignorHistoryFilter
# from ..forms import (
#     ConsignorBillingAddressUpdateForm,
#     ConsignorContactUpdateForm,
#     ConsignorForm,
#     ConsignorInfoUpdateForm,
#     ConsignorShippingAddressUpdateForm,
# )
# from ..models import BillingAddress, Consignor, ConsignorContact, ShippingAddress
# from ..tables import ConsignorHistoryDataTables, ConsignorPartialListTable, ConsignorTable

# UserModel = get_user_model()


from wms.cores.mixins import ExportTableMixin
from wms.cores.views import CoreSingleTableView, CoreDataTableDetailView, CoreCreateView, CoreDetailView, CoreUpdateView

from ..models import BillingAddress, Consignor, ConsignorContact, ShippingAddress
# from ..filters import ConsignorFilter
from ..forms import ConsignorForm, ConsignorUpdateForm
from ..tables import ConsignorTable, ConsignorDetailTable


class ConsignorCreateAndUpdateMixin:
    """Mixin for Create and Update."""

    model = Consignor
    # form_class = ConsignorForm
    template_name = "consignors/forms/create_or_update_form.html"

    # selected_page = "consignors"
    # selected_subpage = None

    def form_valid(self, form):
        self.object = form.save()

        if self.object.primary_contact:
            primary_contact = self.object.primary_contact
        else:
            primary_contact = ConsignorContact(consignor=self.object)

        primary_contact.salutation = form.cleaned_data.get("salutation", "")
        primary_contact.first_name = form.cleaned_data.get("first_name", "")
        primary_contact.last_name = form.cleaned_data.get("last_name", "")
        primary_contact.email = form.cleaned_data.get("email", "")
        primary_contact.phone = form.cleaned_data.get("phone", "")
        primary_contact.designation = form.cleaned_data.get("designation", "")
        primary_contact.department = form.cleaned_data.get("department", "")
        primary_contact.is_primary = True
        primary_contact.save()

        if self.object.billing_address:
            billing_address = self.object.billing_address
        else:
            billing_address = BillingAddress(consignor=self.object)

        billing_address.address_attention = form.cleaned_data.get("billing_address_attention", "")
        billing_address.address_street_1 = form.cleaned_data.get("billing_address_street_1", "")
        billing_address.address_street_2 = form.cleaned_data.get("billing_address_street_2", "")
        billing_address.address_postal_code = form.cleaned_data.get("billing_address_postal_code", "")
        billing_address.address_district = form.cleaned_data.get("billing_address_district", "")
        billing_address.address_city = form.cleaned_data.get("billing_address_city", "")
        billing_address.address_state = form.cleaned_data.get("billing_address_state", "")
        billing_address.address_country = form.cleaned_data.get("billing_address_country", "")
        billing_address.address_phone = form.cleaned_data.get("billing_address_phone", "")
        billing_address.is_primary = True
        billing_address.save()

        if self.object.shipping_address:
            shipping_address = self.object.shipping_address
        else:
            shipping_address = ShippingAddress(consignor=self.object)

        shipping_address.address_attention = form.cleaned_data.get("shipping_address_attention", "")
        shipping_address.address_street_1 = form.cleaned_data.get("shipping_address_street_1", "")
        shipping_address.address_street_2 = form.cleaned_data.get("shipping_address_street_2", "")
        shipping_address.address_postal_code = form.cleaned_data.get("shipping_address_postal_code", "")
        shipping_address.address_district = form.cleaned_data.get("shipping_address_district", "")
        shipping_address.address_city = form.cleaned_data.get("shipping_address_city", "")
        shipping_address.address_state = form.cleaned_data.get("shipping_address_state", "")
        shipping_address.address_country = form.cleaned_data.get("shipping_address_country", "")
        shipping_address.address_phone = form.cleaned_data.get("shipping_address_phone", "")
        shipping_address.is_primary = True
        shipping_address.save()

        return super().form_valid(form)

    # def get_success_url(self):
    #     return reverse("consignors:detail", kwargs={"pk": self.object.pk})


class ConsignorListView(ExportTableMixin, CoreSingleTableView):
    """
    View for displaying and managing the list of Consignors.
    Supports:
    - Pagination
    - Column visibility
    - Searching
    - Export functionality
    - HTMX partial updates
    """
    model = Consignor
    table_class = ConsignorTable
    template_name = "consignors/mains/list.html"
    partial_template_name = "consignors/partials/table.html"  # Optional, for HTMX
    queryset = Consignor.objects.all()
    # filterset_class = ConsignorFilter

    # Search configuration
    search_fields = ["code", "company_name", "display_name"]

    # Export configuration
    export_name = "consignors"
    export_permission = []  # Empty list means no specific permissions required


class ConsignorCreateView(ConsignorCreateAndUpdateMixin, CoreCreateView):
    # model = Consignor
    form_class = ConsignorForm
    template_name = "consignors/forms/create_or_update_form.html"
    success_url = "consignors:panel"
    section_title = "Create Consignor"
    section_desc = ""
    submit_text = "Save"
    cancel_url = "consignors:list"


class ConsignorUpdateView(ConsignorCreateAndUpdateMixin, CoreUpdateView):
    # model = Consignor
    form_class = ConsignorUpdateForm
    template_name = "consignors/forms/create_or_update_form.html"
    success_url = "consignors:panel"
    section_title = "Update Consignor"
    section_desc = ""
    submit_text = "Update"
    cancel_url = "consignors:list"

    def get_initial(self):
        initial = super().get_initial()

        primary_contact = self.object.primary_contact
        if primary_contact:
            initial["salutation"] = primary_contact.salutation
            initial["first_name"] = primary_contact.first_name
            initial["last_name"] = primary_contact.last_name
            initial["email"] = primary_contact.email
            initial["phone"] = primary_contact.phone
            initial["designation"] = primary_contact.designation
            initial["department"] = primary_contact.department

        billing_address = self.object.billing_address
        if billing_address:
            initial["billing_address_attention"] = billing_address.address_attention
            initial["billing_address_street_1"] = billing_address.address_street_1
            initial["billing_address_street_2"] = billing_address.address_street_2
            initial["billing_address_postal_code"] = billing_address.address_postal_code
            initial["billing_address_district"] = billing_address.address_district
            initial["billing_address_city"] = billing_address.address_city
            initial["billing_address_state"] = billing_address.address_state
            initial["billing_address_country"] = billing_address.address_country
            initial["billing_address_phone"] = billing_address.address_phone

        shipping_address = self.object.shipping_address
        if shipping_address:
            initial["shipping_address_attention"] = shipping_address.address_attention
            initial["shipping_address_street_1"] = shipping_address.address_street_1
            initial["shipping_address_street_2"] = shipping_address.address_street_2
            initial["shipping_address_postal_code"] = shipping_address.address_postal_code
            initial["shipping_address_district"] = shipping_address.address_district
            initial["shipping_address_city"] = shipping_address.address_city
            initial["shipping_address_state"] = shipping_address.address_state
            initial["shipping_address_country"] = shipping_address.address_country
            initial["shipping_address_phone"] = shipping_address.address_phone

        return initial


class ConsignorDetailHomeView(CoreDetailView):
    model = Consignor
    template_name = 'consignors/mains/home.html'


class ConsignorDetailView(CoreDetailView):
    model = Consignor
    template_name = 'consignors/partials/detail.html'


class ConsignorEventView(CoreDetailView):
    model = Consignor
    template_name = 'consignors/partials/event.html'


class ConsignorDataTableDetailView(CoreDataTableDetailView):
    model = Consignor
    table_class = ConsignorDetailTable
    context_object_name = "consignor"  # this is the default, but making it explicit
    partial_view = ConsignorDetailHomeView
    search_fields = ["code"]


# class ConsignorListView(LoginRequiredMixin, PermissionRequiredMixin, SingleTableMixin, CoreListView):
#     """Page to show all Consignor."""

#     model = Consignor
#     template_name = "consignors/list.html"

#     table_class = ConsignorTable
#     table_pagination = False

#     header_title = "Consignors"
#     selected_page = "consignors"
#     selected_subpage = None

#     permission_required = ("consignors.view_consignor",)


# consignor_list_view = ConsignorListView.as_view()


# class ConsignorDetailView(LoginRequiredMixin, PermissionRequiredMixin, SingleTableMixin, CoreDetailView):
#     """Page to display selected Consignor based on given Consignor's slug."""

#     model = Consignor
#     template_name = "consignors/detail.html"

#     table_class = ConsignorPartialListTable
#     table_pagination = False

#     header_title = "Consignors"
#     selected_page = "consignors"
#     selected_subpage = None

#     permission_required = ("consignors.view_consignor",)

#     def get_total_consignees(self):
#         return Consignee.objects.filter(consignor__slug=self.kwargs["slug"]).count()

#     def get_total_items(self):
#         return Item.objects.filter(consignor__slug=self.kwargs["slug"]).count()

#     def get_total_users(self):
#         return UserModel.objects.filter(consignor__slug=self.kwargs["slug"]).count()

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         context["total_consignees"] = self.get_total_consignees()
#         context["total_items"] = self.get_total_items()
#         context["total_users"] = self.get_total_users()
#         return context


# consignor_detail_view = ConsignorDetailView.as_view()


# class ConsignorCreateAndUpdateMixin:
#     """Mixin for Create and Update."""

#     model = Consignor
#     form_class = ConsignorForm
#     template_name = "consignors/create_or_update.html"

#     selected_page = "consignors"
#     selected_subpage = None

#     def form_valid(self, form):
#         self.object = form.save()

#         if self.object.primary_contact:
#             primary_contact = self.object.primary_contact
#         else:
#             primary_contact = ConsignorContact(consignor=self.object)

#         primary_contact.salutation = form.cleaned_data.get("salutation", "")
#         primary_contact.first_name = form.cleaned_data.get("first_name", "")
#         primary_contact.last_name = form.cleaned_data.get("last_name", "")
#         primary_contact.email = form.cleaned_data.get("email", "")
#         primary_contact.phone = form.cleaned_data.get("phone", "")
#         primary_contact.designation = form.cleaned_data.get("designation", "")
#         primary_contact.department = form.cleaned_data.get("department", "")
#         primary_contact.is_primary = True
#         primary_contact.save()

#         if self.object.billing_address:
#             billing_address = self.object.billing_address
#         else:
#             billing_address = BillingAddress(consignor=self.object)

#         billing_address.address_attention = form.cleaned_data.get("billing_address_attention", "")
#         billing_address.address_street_1 = form.cleaned_data.get("billing_address_street_1", "")
#         billing_address.address_street_2 = form.cleaned_data.get("billing_address_street_2", "")
#         billing_address.address_postal_code = form.cleaned_data.get("billing_address_postal_code", "")
#         billing_address.address_district = form.cleaned_data.get("billing_address_district", "")
#         billing_address.address_city = form.cleaned_data.get("billing_address_city", "")
#         billing_address.address_state = form.cleaned_data.get("billing_address_state", "")
#         billing_address.address_country = form.cleaned_data.get("billing_address_country", "")
#         billing_address.address_phone = form.cleaned_data.get("billing_address_phone", "")
#         billing_address.is_primary = True
#         billing_address.save()

#         if self.object.shipping_address:
#             shipping_address = self.object.shipping_address
#         else:
#             shipping_address = ShippingAddress(consignor=self.object)

#         shipping_address.address_attention = form.cleaned_data.get("shipping_address_attention", "")
#         shipping_address.address_street_1 = form.cleaned_data.get("shipping_address_street_1", "")
#         shipping_address.address_street_2 = form.cleaned_data.get("shipping_address_street_2", "")
#         shipping_address.address_postal_code = form.cleaned_data.get("shipping_address_postal_code", "")
#         shipping_address.address_district = form.cleaned_data.get("shipping_address_district", "")
#         shipping_address.address_city = form.cleaned_data.get("shipping_address_city", "")
#         shipping_address.address_state = form.cleaned_data.get("shipping_address_state", "")
#         shipping_address.address_country = form.cleaned_data.get("shipping_address_country", "")
#         shipping_address.address_phone = form.cleaned_data.get("shipping_address_phone", "")
#         shipping_address.is_primary = True
#         shipping_address.save()

#         return super().form_valid(form)

#     def get_success_url(self):
#         return reverse("consignors:detail", kwargs={"slug": self.object.slug})


# class ConsignorCreateView(ConsignorCreateAndUpdateMixin, LoginRequiredMixin, PermissionRequiredMixin, CoreCreateView):
#     """Page to create Consignor."""

#     success_message = _("Consignor %(display_name)s successfully created")

#     header_title = "New Consignor"

#     permission_required = ("consignors.add_consignor",)

#     def handle_send_notification(self):
#         send_notification(
#             instance=self.object,
#             message=f"NEW Consignor ({self.object.numbering}) had been added.",
#             user_role_list=["Superadmin", "Admin"],
#             level="info",
#         )

#     def form_valid(self, form):
#         """If the form is valid, save the associated model."""
#         self.object = form.save()
#         # handle notification
#         self.handle_send_notification()
#         return super().form_valid(form)


# consignor_create_view = ConsignorCreateView.as_view()


# class ConsignorUpdateView(ConsignorCreateAndUpdateMixin, LoginRequiredMixin, PermissionRequiredMixin, CoreUpdateView):
#     """Page to update selected Consignor based on given Consignor's slug."""

#     success_message = _("Consignor %(display_name)s successfully updated")

#     header_title = "Update Consignor"

#     permission_required = ("consignors.change_consignor",)

#     def get_initial(self):
#         initial = super().get_initial()

#         primary_contact = self.object.primary_contact
#         if primary_contact:
#             initial["salutation"] = primary_contact.salutation
#             initial["first_name"] = primary_contact.first_name
#             initial["last_name"] = primary_contact.last_name
#             initial["email"] = primary_contact.email
#             initial["phone"] = primary_contact.phone
#             initial["designation"] = primary_contact.designation
#             initial["department"] = primary_contact.department

#         billing_address = self.object.billing_address
#         if billing_address:
#             initial["billing_address_attention"] = billing_address.address_attention
#             initial["billing_address_street_1"] = billing_address.address_street_1
#             initial["billing_address_street_2"] = billing_address.address_street_2
#             initial["billing_address_postal_code"] = billing_address.address_postal_code
#             initial["billing_address_district"] = billing_address.address_district
#             initial["billing_address_city"] = billing_address.address_city
#             initial["billing_address_state"] = billing_address.address_state
#             initial["billing_address_country"] = billing_address.address_country
#             initial["billing_address_phone"] = billing_address.address_phone

#         shipping_address = self.object.shipping_address
#         if shipping_address:
#             initial["shipping_address_attention"] = shipping_address.address_attention
#             initial["shipping_address_street_1"] = shipping_address.address_street_1
#             initial["shipping_address_street_2"] = shipping_address.address_street_2
#             initial["shipping_address_postal_code"] = shipping_address.address_postal_code
#             initial["shipping_address_district"] = shipping_address.address_district
#             initial["shipping_address_city"] = shipping_address.address_city
#             initial["shipping_address_state"] = shipping_address.address_state
#             initial["shipping_address_country"] = shipping_address.address_country
#             initial["shipping_address_phone"] = shipping_address.address_phone

#         return initial


# consignor_update_view = ConsignorUpdateView.as_view()


# class ConsignorDeleteView(LoginRequiredMixin, PermissionRequiredMixin, CoreDeleteView):
#     """Page to delete selected Consignor based on given Consignor's slug."""

#     model = Consignor
#     success_url = reverse_lazy("consignors:list")
#     success_message = _("Consignor %(display_name)s successfully deleted")

#     permission_required = ("consignors.delete_consignor",)


# consignor_delete_view = ConsignorDeleteView.as_view()


# ##################
# # FOR DataTables #
# ##################


# class ConsignorHistoryDataTablesView(LoginRequiredMixin, PermissionRequiredMixin, CoreDataTablesView):
#     """JSON for DataTables in Consignor History's page."""

#     model = Action
#     table_class = ConsignorHistoryDataTables
#     filterset_class = ConsignorHistoryFilter

#     permission_required = "consignors.view_consignor"

#     def get_consignor_information(self):
#         self.consignor = Consignor.objects.prefetch_related(
#             "billingaddress_set", "shippingaddress_set", "consignorcontact_set"
#         ).get(slug=self.kwargs["slug"])
#         self.consignor_billingaddress = self.consignor.billingaddress_set.all()
#         self.consignor_shippingaddress = self.consignor.shippingaddress_set.all()
#         self.consignor_consignorcontact = self.consignor.consignorcontact_set.all()

#     def get_queryset(self) -> QuerySet[Action]:
#         self.get_consignor_information()

#         action_consignor_qs = query_actstream(target=self.consignor)

#         action_qs = action_consignor_qs

#         return action_qs


# consignor_history_datatables_view = ConsignorHistoryDataTablesView.as_view()


# ############
# # FOR HTMX #
# ############


# class ConsignorInfoDetailView(LoginRequiredMixin, PermissionRequiredMixin, CoreDetailView):
#     """Partial page to display selected Consignor's info page based on given Consignor's slug."""

#     model = Consignor
#     template_name = "consignors/partials/htmx/_info.html"

#     permission_required = ("consignors.view_consignor",)


# consignor_info_detail_view = ConsignorInfoDetailView.as_view()


# class ConsignorInfoUpdateView(LoginRequiredMixin, PermissionRequiredMixin, CoreUpdateView):
#     """Partial page to update selected Consignor's info page based on given Consignor's slug."""

#     model = Consignor
#     form_class = ConsignorInfoUpdateForm
#     template_name = "consignors/partials/htmx/_info_form.html"
#     success_message = _("Consignor %(display_name)s basic information successfully updated")

#     permission_required = ("consignors.change_consignor",)

#     def get_success_url(self):
#         return reverse("consignors:info", kwargs={"slug": self.object.slug})


# consignor_info_update_view = ConsignorInfoUpdateView.as_view()


# class ConsignorPrimaryContactDetailView(LoginRequiredMixin, PermissionRequiredMixin, CoreDetailView):
#     """Partial page to display selected Consignor's primary contact page based on given Consignor's slug."""

#     model = Consignor
#     template_name = "consignors/partials/htmx/_primary_contact.html"

#     permission_required = ("consignors.view_consignor",)


# consignor_primary_contact_detail_view = ConsignorPrimaryContactDetailView.as_view()


# class ConsignorPrimaryContactUpdateView(LoginRequiredMixin, PermissionRequiredMixin, CoreUpdateView):
#     """Partial page to update selected Consignor's primary contact page based on given Consignor's slug."""

#     model = ConsignorContact
#     form_class = ConsignorContactUpdateForm
#     template_name = "consignors/partials/htmx/_primary_contact_form.html"
#     success_message = _("Primary contact successfully updated")

#     permission_required = ("consignors.change_consignor",)

#     def get_object(self):
#         contact = ConsignorContact.objects.filter(consignor__slug=self.kwargs["slug"], is_primary=True).first()
#         if not contact:
#             consignor = Consignor.objects.get(slug=self.kwargs["slug"])
#             contact = ConsignorContact.objects.create(consignor=consignor, is_primary=True)
#         return contact

#     def get_success_url(self):
#         return reverse("consignors:primary_contact", kwargs={"slug": self.object.consignor.slug})


# consignor_primary_contact_update_view = ConsignorPrimaryContactUpdateView.as_view()


# class ConsignorBillingAddressDetailView(LoginRequiredMixin, PermissionRequiredMixin, CoreDetailView):
#     """Partial page to display selected Consignor's billing address page based on given Consignor's slug."""

#     model = Consignor
#     template_name = "consignors/partials/htmx/_billing_address.html"

#     permission_required = ("consignors.view_consignor",)


# consignor_billing_address_detail_view = ConsignorBillingAddressDetailView.as_view()


# class ConsignorBillingAddressUpdateView(LoginRequiredMixin, PermissionRequiredMixin, CoreUpdateView):
#     """Partial page to update selected Consignor's billing address page based on given Consignor's slug."""

#     model = BillingAddress
#     form_class = ConsignorBillingAddressUpdateForm
#     template_name = "consignors/partials/htmx/_billing_address_form.html"
#     success_message = _("Billing address successfully updated")

#     permission_required = ("consignors.change_consignor",)

#     def get_object(self):
#         address = BillingAddress.objects.filter(consignor__slug=self.kwargs["slug"], is_primary=True).first()
#         if not address:
#             consignor = Consignor.objects.get(slug=self.kwargs["slug"])
#             address = BillingAddress.objects.create(consignor=consignor, is_primary=True)
#         return address

#     def get_success_url(self):
#         return reverse("consignors:billing_address", kwargs={"slug": self.object.consignor.slug})


# consignor_billing_address_update_view = ConsignorBillingAddressUpdateView.as_view()


# class ConsignorShippingAddressDetailView(LoginRequiredMixin, PermissionRequiredMixin, CoreDetailView):
#     """Partial page to display selected Consignor's shipping address page based on given Consignor's slug."""

#     model = Consignor
#     template_name = "consignors/partials/htmx/_shipping_address.html"

#     permission_required = ("consignors.view_consignor",)


# consignor_shipping_address_detail_view = ConsignorShippingAddressDetailView.as_view()


# class ConsignorShippingAddressUpdateView(LoginRequiredMixin, PermissionRequiredMixin, CoreUpdateView):
#     """Partial page to update selected Consignor's shipping address page based on given Consignor's slug."""

#     model = ShippingAddress
#     form_class = ConsignorShippingAddressUpdateForm
#     template_name = "consignors/partials/htmx/_shipping_address_form.html"
#     success_message = _("Shipping address successfully updated")

#     permission_required = ("consignors.change_consignor",)

#     def get_object(self):
#         address = ShippingAddress.objects.filter(consignor__slug=self.kwargs["slug"], is_primary=True).first()
#         if not address:
#             consignor = Consignor.objects.get(slug=self.kwargs["slug"])
#             address = ShippingAddress.objects.create(consignor=consignor, is_primary=True)
#         return address

#     def get_success_url(self):
#         return reverse("consignors:shipping_address", kwargs={"slug": self.object.consignor.slug})


# consignor_shipping_address_update_view = ConsignorShippingAddressUpdateView.as_view()


# class ConsignorConsigneeListView(LoginRequiredMixin, PermissionRequiredMixin, CoreListView):
#     """Partial page to show all Consignee based on given Consignor's slug."""

#     model = Consignee
#     template_name = "consignors/partials/htmx/_consignees.html"

#     permission_required = (
#         "consignors.view_consignor",
#         "consignees.view_consignee",
#     )

#     def get_queryset(self):
#         return self.model.objects.filter(consignor__slug=self.kwargs["slug"])


# consignor_consignee_list_view = ConsignorConsigneeListView.as_view()


# class ConsignorItemListView(LoginRequiredMixin, PermissionRequiredMixin, CoreListView):
#     """Partial page to show all Item based on given Consignor's slug."""

#     model = Item
#     template_name = "consignors/partials/htmx/_items.html"

#     permission_required = (
#         "consignors.view_consignor",
#         "inventories.view_item",
#     )

#     def get_queryset(self):
#         return self.model.objects.filter(consignor__slug=self.kwargs["slug"])


# consignor_item_list_view = ConsignorItemListView.as_view()


# class ConsignorConsigneeDropdownListView(LoginRequiredMixin, PermissionRequiredMixin, CoreListView):
#     """Partial page to show all Consignee based on given Consignor's pk."""

#     model = Consignee
#     template_name = "consignors/partials/htmx/_dropdown.html"

#     permission_required = (
#         "consignors.view_consignor",
#         "consignees.view_consignee",
#     )

#     def get_queryset(self):
#         return self.model.objects.filter(consignor__pk=self.request.GET.get("consignor", None))

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         context["blank_choice_dash"] = BLANK_CHOICE_DASH[0][1]
#         return context


# consignor_consignee_dropdown_list_view = ConsignorConsigneeDropdownListView.as_view()


# class ConsignorHistoryListView(LoginRequiredMixin, PermissionRequiredMixin, SingleTableMixin, CoreListView):
#     """
#     Partial page to show all Actstreams based on given Consignor's slug as target_object_id.
#     This page use DataTables server side.
#     """

#     model = Action
#     table_class = ConsignorHistoryDataTables
#     template_name = "consignors/partials/htmx/_history.html"

#     # To prevent query all object as it will use ajax call in template
#     object_list = Action.objects.none()

#     permission_required = ("consignors.view_consignor",)

#     def get_queryset(self):
#         """To prevent query all object."""
#         return self.object_list

#     def get_consignor(self):
#         return Consignor.objects.get(slug=self.kwargs["slug"])

#     def get_context_data(self, **kwargs) -> Any:
#         context = super().get_context_data(**kwargs)
#         context["object"] = self.get_consignor()
#         return context


# consignor_history_list_view = ConsignorHistoryListView.as_view()


# class ConsignorHistoryModifiedView(CoreBaseHistoryModifiedView):
#     """
#     Partial pop up view to show the differences in Consignor history view.
#     """

#     permission_required = ("consignors.view_consignor",)


# consignor_history_modified_view = ConsignorHistoryModifiedView.as_view()


# def consignor_item_dropdown_list_view(request: HttpRequest) -> JsonResponse:
#     """
#     FBV to return paginated Item queryset in the form of JSONResponse, based on query params.
#     * q = Refers to search term when user keys in select2 input field
#     * page = Refers to which "page"/scroll based on the paginated queryset

#     Example:

#     - Sample output based on API call for endpoint:
#     {{baseUrl}}/consignors/dropdown/items/?consignor=9&q=pearl&page=2

#     - Returns:
#     {
#       "results": [
#         {
#           "id": 20019,
#           "text": "MORPHO 9300 :: GOLD PEARL (25KG/CTN)"
#         },
#         {
#           "id": 20017,
#           "text": "MORPHO 225** :: BLUE PEARL (5KG/PACK)"
#         },
#         {
#           "id": 20015,
#           "text": "MORPHO 9219** :: PURPLE PEARL (5KG/PACK)"
#         },
#         ...
#       ],
#       "pagination": {
#         "more": true
#       }
#     }
#     """

#     page = request.GET.get("page", 1)

#     # At least need a consignor's PK to return Item query response
#     if request.GET.get("consignor"):
#         if request.GET.get("q", None) is not None:
#             items = Item.objects.filter(
#                 Q(code__icontains=request.GET.get("q")) | Q(name__icontains=request.GET.get("q")),
#                 consignor__pk=request.GET.get("consignor"),
#             )
#         else:
#             items = Item.objects.filter(consignor__pk=request.GET.get("consignor"))

#         results = [{"id": item.pk, "text": str(item)} for item in items]
#         paginator = Paginator(results, settings.LIMIT_AUTOCOMPLETE_RESULTS_PER_SCROLL)
#         results = paginator.get_page(page).object_list

#         # Determine whether to end the "infinite scroll"
#         pagination = True if len(results) >= settings.LIMIT_AUTOCOMPLETE_RESULTS_PER_SCROLL else False
#         return JsonResponse(
#             {
#                 "results": results,
#                 "pagination": {"more": pagination},
#             }
#         )

#     return JsonResponse({"results": []})

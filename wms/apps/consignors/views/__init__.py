from .consignor import (
    Consignor<PERSON><PERSON><PERSON>iew,
    Consignor<PERSON>reateView,
    ConsignorUpdateV<PERSON>w,
    Consignor<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    ConsignorDetailHomeView,
    ConsignorEventView,
    ConsignorDataTableDetailView,
#     consignor_billing_address_detail_view,
#     consignor_billing_address_update_view,
#     consignor_consignee_dropdown_list_view,
#     consignor_consignee_list_view,
#     consignor_create_view,
#     consignor_delete_view,
#     consignor_detail_view,
#     consignor_history_datatables_view,
#     consignor_history_list_view,
#     consignor_history_modified_view,
#     consignor_info_detail_view,
#     consignor_info_update_view,
#     consignor_item_dropdown_list_view,
#     consignor_item_list_view,
#     consignor_list_view,
#     consignor_primary_contact_detail_view,
#     consignor_primary_contact_update_view,
#     consignor_shipping_address_detail_view,
#     consignor_shipping_address_update_view,
#     consignor_update_view,
)
# from .user import (
#     consignor_user_create_view,
#     consignor_user_list_view,
#     consignor_user_update_password_view,
#     consignor_user_update_view,
# )

# __all__ = [
#     "consignor_billing_address_detail_view",
#     "consignor_billing_address_update_view",
#     "consignor_consignee_dropdown_list_view",
#     "consignor_consignee_list_view",
#     "consignor_create_view",
#     "consignor_delete_view",
#     "consignor_detail_view",
#     "consignor_history_list_view",
#     "consignor_history_modified_view",
#     "consignor_history_datatables_view",
#     "consignor_info_detail_view",
#     "consignor_info_update_view",
#     "consignor_item_list_view",
#     "consignor_item_dropdown_list_view",
#     "consignor_list_view",
#     "consignor_primary_contact_detail_view",
#     "consignor_primary_contact_update_view",
#     "consignor_shipping_address_detail_view",
#     "consignor_shipping_address_update_view",
#     "consignor_update_view",
#     "consignor_user_list_view",
#     "consignor_user_create_view",
#     "consignor_user_update_view",
#     "consignor_user_update_password_view",
# ]

import logging
from datetime import <PERSON><PERSON><PERSON>
from decimal import Decimal
from functools import cached_property, reduce

from django.contrib import admin
from django.core.exceptions import ValidationError
from django.db import models, transaction
from django.db.models import F, Q, QuerySet, Sum
from django.urls import reverse
from django.utils import timezone
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _

from treebeard.mp_tree import MP_Node

from wms.cores.models import DISPLAY_EMPTY_VALUE, AbstractBaseModel
from wms.cores.utils import normalize_decimal

from ..managers import RackTransactionManager

logger = logging.getLogger(__name__)


class Rack(AbstractBaseModel, MP_Node):
    """Rack model for Warehouse Smart System.

    Available fields:

    * created           (AbstractBaseModel => TimeStampedModel)
    * modified          (AbstractBaseModel => TimeStampedModel)
    * created_by        (AbstractBaseModel)
    * modified_by       (AbstractBaseModel)
    * path              (MP_Node)
    * depth             (MP_Node)
    * numchild          (MP_Node)
    * warehouse
    * name
    * full_name
    * rack_type

    """

    class RackType(models.TextChoices):
        FLOOR = "Floor", _("Floor")
        CHAMBER = "Chamber", _("Chamber")
        RACK = "Rack", _("Rack")
        BAY = "Bay", _("Bay")
        LEVEL = "Level", _("Level")
        PALLET = "Pallet", _("Pallet")

    warehouse = models.ForeignKey(
        "settings.Warehouse", limit_choices_to={"is_storage": True}, on_delete=models.CASCADE, blank=True, null=True
    )
    name = models.CharField(verbose_name=_("Name"), max_length=32)
    full_name = models.CharField(verbose_name=_("Full Name"), max_length=256, blank=True, null=True)
    rack_type = models.CharField(
        verbose_name=_("Rack Type"), max_length=128, choices=RackType.choices, default=RackType.FLOOR
    )

    _full_name_separator = "-"

    class Meta(AbstractBaseModel.Meta):
        ordering = [
            "path",
        ]
        indexes = [
            # single index
            models.Index(fields=["warehouse"]),
            models.Index(fields=["rack_type"]),
            # composite index
            models.Index(fields=["warehouse", "rack_type", "full_name"]),  # Composite Index
            models.Index(fields=["rack_type", "warehouse", "full_name"]),  # Composite Index
        ]

    def __str__(self) -> str:
        return f"{self.full_name}"

    def get_absolute_url(self):
        return reverse("rackings:racks:detail", kwargs={"pk": self.pk})

    def get_dash_name(self, name="", with_tree=False):
        """Return name with dash based on depth."""
        if with_tree is True:
            space = ""
            icon = "└"
            if self.depth > 2:
                space = (self.depth - 2) * 6 * "\xa0"
            if self.depth == 1:
                icon = ""
            if icon:
                icon = f"{icon}\xa0"
            return format_html(f"{space}{icon} {name}")
        else:
            space = "—" * (self.depth - 1)
            return f"{space} {name}"

    @property
    def dash_name(self):
        """Return name with dash based on depth."""
        return self.get_dash_name(name=self.name)

    @property
    def tree_name(self):
        """Return name with dash based on depth."""
        return self.get_dash_name(name=self.name, with_tree=True)

    @property
    def dash_full_name(self):
        """Return full name with dash based on depth."""
        return self.get_dash_name(name=self.full_name)

    @property
    def tree_full_name(self):
        """Return full name with dash based on depth."""
        return self.get_dash_name(name=self.full_name, with_tree=True)

    def get_ancestors_and_self(self):
        """
        Gets ancestors and includes itself. Use treebeard's get_ancestors
        if you don't want to include the category itself. It's a separate
        function as it's commonly used in templates.
        """
        if self.is_root():
            return [self]

        return list(self.get_ancestors()) + [self]

    def get_descendants_and_self(self):
        """
        Gets ancestors and includes itself. Use treebeard's get_ancestors
        if you don't want to include the category itself. It's a separate
        function as it's commonly used in templates.
        """
        return [self] + list(self.get_descendants())

    # @cached_property
    # def total_balance_across_all_stocks(self) -> Decimal:
    #     """
    #     Returns total balance across every stocks based on self/Rack.

    #     If its leaf node (numchild=0), it will return the total balance
    #     from its own node only.

    #     If it's non-leaf node/ancestor nodes, then it will sum up the
    #     total balance across all of its children/descendant nodes.
    #     """

    #     return RackBalance.objects.select_related("stock").filter(rack=self).aggregate(
    #         total_balance=Sum("balance")
    #     ).get("total_balance", Decimal("0")) or Decimal("0")

    # @cached_property
    # def stock_count(self):
    #     """
    #     Get total type of stock with balance > 0 and put on the rack.
    #     """

    #     return self.rackbalance_set.filter(balance__gt=0).count()

    def save(self, *args, **kwargs):
        """
        Perform some pre-processing before saving data into model.

        Saves the string representation of a Rack object and it's ancestors
        into the full_name field, i.e.: 'A-1-2-1'.
        """

        names = [node.name for node in self.get_ancestors_and_self()]
        self.full_name = self._full_name_separator.join(names)

        super().save(*args, **kwargs)

        # Once a Rack node is being updated, any of it's descendant nodes full name should reflect the same.
        descendants = self.get_descendants()
        if descendants.exists():
            for descendant in descendants:
                original_name = descendant.full_name.split("-")[self.depth - 1]
                descendant.full_name = descendant.full_name.replace(f"{original_name}-", f"{self.name}-")
                descendant.save(update_fields=["full_name"])


class RackStorage(AbstractBaseModel):
    """RackStorage model for Warehouse Smart System.

    Available fields:

    * created           (AbstractBaseModel => TimeStampedModel)
    * modified          (AbstractBaseModel => TimeStampedModel)
    * created_by        (AbstractBaseModel)
    * modified_by       (AbstractBaseModel)
    * rack
    * stock

    Rules:
    - Rack must be a leaf node (i.e., has no children)
    - Rack's root warehouse must be same with the stock's warehouse
    """

    rack = models.ForeignKey("rackings.Rack", on_delete=models.CASCADE)
    stock = models.ForeignKey("inventories.Stock", on_delete=models.CASCADE)

    class Meta(AbstractBaseModel.Meta):
        ordering = ["rack"]
        indexes = [
            # single index
            models.Index(fields=["rack"]),
            models.Index(fields=["stock"]),
            # composite index
            models.Index(fields=["rack", "stock"]),  # Composite Index
            models.Index(fields=["stock", "rack"]),  # Composite Index
        ]

    def __str__(self) -> str:
        return f"{self.rack.full_name} :: {self.stock}"

    def get_absolute_url(self):
        return reverse("rackings:storage:detail", kwargs={"pk": self.pk})

    @cached_property
    def get_current_physical_rack_transaction_balance(self):
        from wms.apps.rackings.models import RackTransaction
        return RackTransaction.objects.balance_by_stock(
            rack=self.rack,
            stock=self.stock,
            balance_type="physical",
        )

    @cached_property
    def get_current_available_rack_transaction_balance(self):
        from wms.apps.rackings.models import RackTransaction
        return RackTransaction.objects.balance_by_stock(
            rack=self.rack,
            stock=self.stock,
            balance_type="available",
        )

    @cached_property
    def get_current_reserved_rack_transaction_balance(self):
        from wms.apps.rackings.models import RackTransaction
        return RackTransaction.objects.balance_by_stock(
            rack=self.rack,
            stock=self.stock,
            balance_type="reserved",
        )

    def clean(self) -> None:
        """
        Validation on object prior to saving it.
        """

        super().clean()

        # Rule 1: Ensure the rack is a leaf node (i.e., has no children)
        if not self.rack.is_leaf():
            raise ValidationError({"rack": f"Rack {self.rack} is not at the most bottom level (Pallet)."})

        # Rule 2: Ensure that the rack's root warehouse matches the stock's warehouse
        if self.stock.warehouse != self.rack.get_root().warehouse:
            raise ValidationError(
                {
                    "rack": (
                        f"Rack({self.rack})'s root (uppermost Floor) must be linked "
                        "to same warehouse as selected Stock's warehouse."
                    )
                }
            )

        # Rule 3: Ensure that Stock has sufficient SOH balance
        # TODO: Based on latest requirement: We omit this logic temporarily
        # if self.stock.balance <= Decimal("0"):
        #     item = self.stock.item
        #     raise ValidationError(
        #         {"stock": f"Stock {item.code} :: {item.name} has insufficient stock balance: {self.stock.balance}"}
        #     )

    def save(self, *args, **kwargs):
        # Call the clean method to validate before saving
        self.clean()
        super().save(*args, **kwargs)


class RackTransaction(AbstractBaseModel):
    """RackTransaction model for Warehouse Smart System.

    Available fields:
    * created           (AbstractBaseModel => TimeStampedModel)
    * modified          (AbstractBaseModel => TimeStampedModel)
    * created_by        (AbstractBaseModel)
    * modified_by       (AbstractBaseModel)
    * type
    * rackstorage
    * transaction_datetime
    * quantity
    * is_reserved
    * goods_received_note_item
    * warehouse_release_order_item
    * remark

    To cater for the scenarios of:
    - Able to Transfer from Rack 5-1-A-1-1-1 To 5-1-B-1-1-1
    - Able to do adjustment of Addition/Substraction on 5-1-A-1-1-1
    - Able to PutAway from GRN to Rack 5-1-A-1-1-1 (specifying which STOCK it is putting-away)
    - Able to auto-assign/pick stock from rack 5-1-A-1-1-1 linked to a WRO
        - The auto-assigned stock to WRO should be reserved.

    """

    class Type(models.TextChoices):
        GRN = "GRN", _("GRN")
        WRO = "WRO", _("WRO")
        TRANSFER_IN = "Transfer in", _("Transfer in")
        TRANSFER_OUT = "Transfer out", _("Transfer out")
        ADJUSTMENT = "Adjustment", _("Adjustment")

    type = models.CharField(verbose_name=_("Type"), max_length=32, choices=Type.choices, default=Type.ADJUSTMENT)
    rackstorage = models.ForeignKey("rackings.RackStorage", on_delete=models.CASCADE)
    transaction_datetime = models.DateTimeField(default=timezone.now)
    quantity = models.DecimalField(verbose_name=_("Quantity"), max_digits=19, decimal_places=6, default=Decimal("0"))
    is_reserved = models.BooleanField(verbose_name=_("Is Reserved?"), default=False)
    goods_received_note_item = models.ForeignKey(
        "receives.GoodsReceivedNoteItem",
        verbose_name=_("Goods Received Note Item"),
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )
    warehouse_release_order_item = models.ForeignKey(
        "releases.WarehouseReleaseOrderItem",
        verbose_name=_("Warehouse Release Order Item"),
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )
    remark = models.TextField(verbose_name=_("Remark"), max_length=512, blank=True)

    objects = RackTransactionManager()

    class Meta(AbstractBaseModel.Meta):
        ordering = ["-transaction_datetime", "-pk"]
        indexes = [
            # single index
            models.Index(fields=["rackstorage"]),
            models.Index(fields=["transaction_datetime"]),
            # composite index
            models.Index(fields=["rackstorage", "transaction_datetime"]),  # Composite Index
            models.Index(fields=["type", "rackstorage", "is_reserved", "transaction_datetime"]),  # Composite Index
            models.Index(fields=["rackstorage", "is_reserved", "transaction_datetime"]),  # Composite Index
        ]

    def __str__(self) -> str:
        return f"{self.rackstorage.rack.full_name} :: {self.rackstorage.stock.item.name} :: {self.quantity}"

    # @cached_property
    # def system_stock_in(self):
    #     if self.quantity >= Decimal("0"):
    #         return self.quantity
    #     else:
    #         return None

    # @cached_property
    # def system_stock_out(self):
    #     if self.quantity < Decimal("0"):
    #         return abs(self.quantity)
    #     else:
    #         return None

    # @property
    # def reserved_quantity(self) -> Decimal:
    #     if self.type == RackTransaction.Type.RESERVED:
    #         return abs(self.quantity)

    #     return Decimal("0")

    # @cached_property
    # def html_type_display(self):
    #     html_class = ""

    #     if self.type == RackTransaction.Type.STOCK_IN:
    #         html_class = "badge bg-success"
    #     elif self.type == RackTransaction.Type.STOCK_OUT:
    #         html_class = "badge bg-danger"
    #     elif self.type == RackTransaction.Type.TRANSFER:
    #         html_class = "badge bg-info"
    #     elif self.type == RackTransaction.Type.RESERVED:
    #         html_class = "badge bg-primary"
    #     elif self.type == RackTransaction.Type.OBSOLETE:
    #         html_class = "badge bg-secondary"

    #     return format_html(f'<span class="{html_class}">{self.get_type_display()}</span>') or DISPLAY_EMPTY_VALUE

    # def get_balance_by_rack(self, rack: Rack) -> Decimal:
    #     """
    #     Return formatted balance by given rack.
    #     """
    #     from wms.cores.utils import normalize_decimal

    #     rack_balance = self.balance_lookup.get(str(rack.pk), 0)
    #     return normalize_decimal(Decimal(str(rack_balance)))

    # def get_physical_balance_by_rack(self, rack: Rack) -> Decimal:
    #     """
    #     Return formatted physical balance by given rack.
    #     """
    #     from wms.cores.utils import normalize_decimal

    #     rack_balance = self.balance_lookup.get(str(rack.pk), 0) + self.reserved_balance_lookup.get(str(rack.pk), 0)
    #     return normalize_decimal(Decimal(str(rack_balance)))

    # def get_stock_rack_transaction_url(self):
    #     """
    #     Return the URL to the Rack Transactions view of the given Stock.
    #     """
    #     from wms.cores.utils import localtime_now

    #     year, month = self.transaction_datetime.strftime("%Y-%m-%d").split("-")[:2]

    #     return reverse(
    #         "inventories:racks:transaction_detail",
    #         kwargs={
    #             "rack_pk": self.rackstorage.rack.pk,
    #             "pk": self.rackstorage.stock.pk,
    #             "year": int(year) or localtime_now().year,
    #             "month": int(month) or localtime_now().month,
    #         },
    #     )

    def get_previous_rack_transactions(self) -> QuerySet:
        """
        Retrieve all RackTransactions before the current RackTransaction, sorted by the following order:
        1) Rack's depth (from the deepest child first)
        2) Descending transaction datetime (from the latest `transaction_datetime` first)
        3) Descending created datetime (from the latest `created` first)

        Example:
        - Suppose transactions are created in this order: [tx1, tx2, tx3, self, tx5]
        - Output: [tx3, tx2, tx1]

        Notes:
        - If transaction datetime is same as current, transactions created after the current one,
          including itself, will be excluded.
        - When querying for previous RackTransactions, also need to take into account of:
            - What - sharing similar stock
            - When - having earlier/lesser transaction_datetime than self
        """

        ancestors_pk_list = list(self.rackstorage.rack.get_ancestors().values_list("pk", flat=True))
        exclude_query = Q(transaction_datetime=self.transaction_datetime, created__gte=self.created)

        if not self._state.adding:
            # In case of update, always exclude the current RackTransaction from the query
            exclude_query |= Q(pk=self.pk)

        previous_transactions = (
            RackTransaction.objects.filter(
                rackstorage__stock=self.rackstorage.stock,
                transaction_datetime__lte=self.transaction_datetime,
            )
            .exclude(exclude_query)
            .order_by("-rackstorage__rack__depth", "-transaction_datetime", "-created")
        )

        return previous_transactions

    def get_self_and_upcoming_rack_transactions(self) -> QuerySet:
        """
        Retrieve all RackTransactions that comes after the current RackTransaction (include self),
        sorted by the following order:
        1) Ascending transaction datexwtime (from the earliest `transaction_datetime` first)
        2) Ascending created datetime (from the earliest `created` first)

        Example:
        - Suppose transactions are created in this order: [tx1, tx2, tx3, self, tx5]
        - Output: [self, tx5]

        Notes:
        - If transaction datetime is same as current, transactions created before the current one will be excluded.
        """
        ancestors_pk_list = list(self.rackstorage.rack.get_ancestors().values_list("pk", flat=True))

        upcoming_transactions = (
            RackTransaction.objects.filter(
                rackstorage__stock=self.rackstorage.stock,
                transaction_datetime__gte=self.transaction_datetime,
            )
            .exclude(transaction_datetime=self.transaction_datetime, created__lt=self.created)
            .order_by("transaction_datetime", "created")
        )

        return upcoming_transactions

    # @admin.display(description=_("Balance"))
    @cached_property
    def get_current_rack_transaction_balance(self):
        """
        return the current rack transaction's balance with on the fly calculation

        """
        total_quantity_to_add = RackTransaction.objects.filter(
            rackstorage=self.rackstorage,
            transaction_datetime__lte=self.transaction_datetime + timedelta(microseconds=1),
        ).aggregate(
            total_quantity=Sum("quantity")
        )["total_quantity"] or 0

        balance = total_quantity_to_add

        return normalize_decimal(balance)

    # def initialise_lookups(self, previous_rack_transactions: QuerySet, racks: list) -> None:
    #     """
    #     Initialise the JSONField values for each lookup based on the latest balance
    #     for each rack from the previous RackTransactions.
    #     """
    #     # Initialise lookups for all racks with 0
    #     balance_lookup = dict({f"{rack.pk}": float(0) for rack in racks})
    #     reserved_balance_lookup = balance_lookup.copy()
    #     if previous_rack_transactions.exists():
    #         rack_pks = [f"{rack.pk}" for rack in racks]
    #         previous_balance_lookups = previous_rack_transactions.values_list("balance_lookup", flat=True)
    #         previous_reserved_balance_lookups = previous_rack_transactions.values_list(
    #             "reserved_balance_lookup", flat=True
    #         )
    #         # Loop through the previous balance_lookup records and construct a dict with the
    #         # latest balance (up until the current transaction) for each rack
    #         balance_lookup = reduce(
    #             lambda acc, curr: {
    #                 **acc,
    #                 **{pk: value for pk, value in curr.items() if pk in rack_pks},
    #             },
    #             reversed(previous_balance_lookups),
    #             balance_lookup,
    #         )
    #         # Similar but for reserved_balance_lookup
    #         reserved_balance_lookup = reduce(
    #             lambda acc, curr: {
    #                 **acc,
    #                 **{pk: value for pk, value in curr.items() if pk in rack_pks},
    #             },
    #             reversed(previous_reserved_balance_lookups),
    #             reserved_balance_lookup,
    #         )

    #     self.balance_lookup = balance_lookup
    #     self.reserved_balance_lookup = reserved_balance_lookup

    # def update_lookups(
    #     self,
    #     rack_transactions: QuerySet,
    #     racks: list,
    #     quantity: float | Decimal,
    #     reserved_quantity: float | Decimal,
    # ) -> None:
    #     """
    #     Update the JSONField values for each lookup in all the provided RackTransactions
    #     by summing the corresponding quantity with the respective rack's lookup balance.
    #     """
    #     for rack_transaction in rack_transactions:
    #         balance_lookup = rack_transaction.balance_lookup
    #         quantity_added_to_rack = dict({f"{rack.pk}": float(quantity) for rack in racks})
    #         # Update the balance_lookup with the new quantity added to the racks
    #         updated_balance_lookup = {
    #             pk: value + float(quantity_added_to_rack.get(pk, 0)) for pk, value in balance_lookup.items()
    #         }

    #         reserved_balance_lookup = rack_transaction.reserved_balance_lookup
    #         quantity_reserved_on_rack = dict({f"{rack.pk}": float(reserved_quantity) for rack in racks})
    #         # Update the reserved_balance_lookup with the quantity to be reserved on the racks
    #         updated_reserved_balance_lookup = {
    #             pk: value + float(quantity_reserved_on_rack.get(pk, 0)) for pk, value in reserved_balance_lookup.items()
    #         }

    #         rack_transaction.balance_lookup = updated_balance_lookup
    #         rack_transaction.reserved_balance_lookup = updated_reserved_balance_lookup

    #     RackTransaction.objects.bulk_update(rack_transactions, ["balance_lookup", "reserved_balance_lookup"])

    @transaction.atomic
    def save(self, *args, **kwargs):
        # # Dynamically update the transaction type based on the quantity (only for STOCK_IN and STOCK_OUT)
        # if self.type == RackTransaction.Type.STOCK_IN and self.quantity < Decimal("0"):
        #     self.type = RackTransaction.Type.STOCK_OUT
        # elif self.type == RackTransaction.Type.STOCK_OUT and self.quantity >= Decimal("0"):
        #     self.type = RackTransaction.Type.STOCK_IN

        # if self.type == RackTransaction.Type.RESERVED:
        #     # Ensure quantity is negative for RESERVED transactions, indicating a pending stock-out.
        #     if self.quantity > Decimal("0"):
        #         self.quantity = -self.quantity

        # if self.type == RackTransaction.Type.OBSOLETE:
        #     # Ensure quantity is set to 0 for OBSOLETE transactions.
        #     if self.quantity != Decimal("0"):
        #         self.quantity = Decimal("0")

        # # Ensure information are saved through a bottom-up approach
        # racks = self.rackstorage.rack.get_ancestors_and_self()[::-1]
        # previous_rack_transactions = self.get_previous_rack_transactions()
        # quantity = self.quantity
        # reserved_quantity = self.reserved_quantity
        # skip_balance_update = False

        # # Handle update case
        # if not self._state.adding:
        #     old_obj = RackTransaction.objects.get(pk=self.pk)
        #     old_quantity = old_obj.quantity
        #     old_reserved_quantity = old_obj.reserved_quantity

        #     # Calculate the difference in quantity between the old and new RackTransaction
        #     quantity -= old_quantity
        #     # Calculate the difference in reserved quantity between the old and new RESERVED RackTransaction
        #     reserved_quantity -= old_reserved_quantity

        #     # Skip balance update if the delta is 0
        #     if quantity == 0 and reserved_quantity == 0:
        #         skip_balance_update = True

        #     # When performing an update, we need to subtract the old quantity from the balance_lookup of
        #     # all the RackTransactions that occured after the old object (as if it is being deleted),
        #     # and update new quantity later on (as if a new transaction is being created).
        #     # Similar applies to the reserved_balance_lookup.
        #     old_upcoming_rack_transactions = old_obj.get_self_and_upcoming_rack_transactions()
        #     self.update_lookups(old_upcoming_rack_transactions, racks, -old_quantity, -old_reserved_quantity)

        # self.initialise_lookups(previous_rack_transactions, racks)

        super().save(*args, **kwargs)

        # if not skip_balance_update:
        #     # RackBalance updates are performed independently and separately from RackTransaction changes
        #     # to maintain an accurate balance across all RackBalance objects.
        #     for rack in racks:
        #         rack_balance, created = RackBalance.objects.get_or_create(
        #             rack=rack,
        #             stock=self.rackstorage.stock,
        #         )
        #         rack_balance.balance = F("balance") + quantity if not created else quantity
        #         rack_balance.reserved_balance = (
        #             F("reserved_balance") + reserved_quantity if not created else reserved_quantity
        #         )
        #         transaction_lookup = rack_balance.transaction_lookup or {}
        #         transaction_lookup[f"{self.pk}"] = float(self.quantity)
        #         rack_balance.transaction_lookup = transaction_lookup
        #         rack_balance.save(update_fields=["balance", "reserved_balance", "transaction_lookup"])

        # # Update lookup fields for all RackTransactions that are affected
        # # by the current balance changes (self and upcoming)
        # self_and_upcoming_rack_transactions = self.get_self_and_upcoming_rack_transactions()
        # self.update_lookups(self_and_upcoming_rack_transactions, racks, self.quantity, self.reserved_quantity)

        # # Update item's no of racks
        # item = self.rackstorage.stock.item
        # no_of_racks = Rack.objects.filter(numchild=0, rackbalance__stock__item=item, rackbalance__balance__gt=0).count()
        # if item.no_of_racks != no_of_racks:
        #     item.no_of_racks = no_of_racks
        #     item.save(update_fields=["no_of_racks"])

        # # Update GRN's is_racking_completed status
        # goods_received_note = self.goods_received_note
        # # Check only if there is GRN and is_racking_completed is False
        # if goods_received_note and goods_received_note.is_racking_completed is False:
        #     grn_items = goods_received_note.goodsreceivednoteitem_set.all()
        #     percentages_list = []
        #     for item in grn_items:
        #         stock_in_percentage = item.racking_quantity_status_dict.get("stock_in_percentage", Decimal("0"))
        #         percentages_list.append(stock_in_percentage)

        #     is_racking_completed = all(percentage >= Decimal("100") for percentage in percentages_list)
        #     if is_racking_completed is True:
        #         goods_received_note.is_racking_completed = is_racking_completed
        #         goods_received_note.save(update_fields=["is_racking_completed"])

        # if self.type == RackTransaction.Type.STOCK_IN:
        #     from wms.apps.rackings.services import fulfil_incomplete_reservations

        #     fulfil_incomplete_reservations(self.rackstorage.rack.warehouse, self.rackstorage.stock, self.quantity)

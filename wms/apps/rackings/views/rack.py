# from decimal import Decimal
# from typing import Any, Union

# from django.conf import settings
# from django.contrib import messages
# from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
# from django.core.paginator import Paginator
# from django.db.models import Q
# from django.http import HttpRequest, HttpResponse, HttpResponseRedirect, JsonResponse
# from django.shortcuts import redirect
# from django.urls import reverse, reverse_lazy
# from django.utils.translation import gettext_lazy as _

# from django_filters.views import FilterView
# from django_tables2 import SingleTableMixin
# from openpyxl import Workbook
# from openpyxl.styles import Alignment, Font

# from wss.cores.utils import format_decimal_values, localtime_now
# from wss.cores.views import (
#     CoreCreateView,
#     CoreDataTablesView,
#     CoreDeleteView,
#     CoreDetailDataTablesView,
#     CoreDetailView,
#     CoreFormView,
#     CoreListView,
#     CoreTemplateView,
#     CoreUpdateView,
# )

# from wss.apps.rackings.models.rack import RackTransaction

# from ..filters import RackFilter
# from ..forms import RackForm, RackImportExcelForm, RackInfoUpdateForm
# from ..models import Rack
# from ..tables import RackDataTables, RackDetailDataTables


from wms.cores.mixins import ExportTableMixin
from wms.cores.views import CoreSingleTableView, CoreDataTableDetailView, CoreCreateView, CoreDetailView, CoreUpdateView

from wms.apps.rackings.models import Rack
from wms.apps.rackings.forms import RackForm  # , RackUpdateForm
from wms.apps.rackings.filters import RackFilter, RackDataTableFilter
from wms.apps.rackings.tables import RackTable, RackDetailTable


class RackListView(ExportTableMixin, CoreSingleTableView):
    """
    View for displaying and managing the list of Racks.
    Supports:
    - Pagination
    - Column visibility
    - Searching
    - Export functionality
    - HTMX partial updates
    """
    model = Rack
    table_class = RackTable
    template_name = "rackings/racks/mains/list.html"
    partial_template_name = "rackings/racks/partials/table.html"  # Optional, for HTMX
    queryset = Rack.objects.all()
    filterset_class = RackFilter

    # Search configuration
    search_fields = ["full_name"]

    # Export configuration
    export_name = "racks"
    export_permission = []  # Empty list means no specific permissions required


class RackCreateView(CoreCreateView):
    model = Rack
    form_class = RackForm
    template_name = "rackings/racks/forms/create_or_update_form.html"
    success_url = "rackings:racks:panel"
    section_title = "Create Rack"
    section_desc = ""
    submit_text = "Save"
    cancel_url = "rackings:racks:list"


class RackUpdateView(CoreUpdateView):
    model = Rack
    form_class = RackForm
    template_name = "rackings/racks/forms/create_or_update_form.html"
    success_url = "rackings:racks:panel"
    section_title = "Update Rack"
    section_desc = ""
    submit_text = "Update"
    cancel_url = "rackings:racks:list"


class RackDetailHomeView(CoreDetailView):
    model = Rack
    template_name = 'rackings/racks/mains/home.html'
    context_object_name = "rack"


class RackDetailView(CoreDetailView):
    model = Rack
    template_name = 'rackings/racks/partials/detail.html'
    context_object_name = "rack"


class RackDataTableDetailView(CoreDataTableDetailView):
    model = Rack
    table_class = RackDetailTable
    context_object_name = "rack"
    partial_view = RackDetailHomeView
    search_fields = ["full_name"]
    filterset_class = RackDataTableFilter


# class RackListView(LoginRequiredMixin, PermissionRequiredMixin, FilterView, SingleTableMixin, CoreListView):
#     """Page to show all Racks. This page use DataTables server side."""

#     model = Rack
#     template_name = "rackings/racks/list.html"

#     table_class = RackDataTables
#     filterset_class = RackFilter

#     # To prevent query all object as it will use ajax call in template
#     object_list = Rack.objects.none()

#     header_title = "Racks"
#     selected_page = "rackings"
#     selected_subpage = "racks"

#     permission_required = ("rackings.view_rack",)

#     def get_queryset(self):
#         """To prevent query all object."""
#         return self.object_list


# rack_list_view = RackListView.as_view()


# class RackDetailView(LoginRequiredMixin, PermissionRequiredMixin, CoreDetailDataTablesView):
#     """
#     Page to display selected Rack based on given Rack's pk.

#     This page use DataTables server side.
#     """

#     model = Rack
#     template_name = "rackings/racks/detail.html"

#     table_class = RackDetailDataTables

#     header_title = "Racks"
#     selected_page = "rackings"
#     selected_subpage = "racks"

#     permission_required = ("rackings.view_rack",)


# rack_detail_view = RackDetailView.as_view()


# class RackCreateAndUpdateMixin:
#     """Mixin for Create and Update."""

#     model = Rack
#     form_class = RackForm
#     template_name = "rackings/racks/create_or_update.html"

#     selected_page = "rackings"
#     selected_subpage = "racks"

#     def post(self, request: HttpRequest, *args: Any, **kwargs: Any) -> Union[HttpResponse, HttpResponseRedirect]:
#         """
#         Function to check if there's existing Rack name based on request's submitted data (after trimming)
#         at root-level.
#         """

#         name = request.POST.get("name", "").strip()
#         warehouse_pk = request.POST.get("warehouse", "")
#         relative_to = request.POST.getlist("_ref_node_id", [""])[0]

#         # When user chooses dropdown "--root--" option
#         if relative_to == "":

#             # Redirect to existing Rack's DetailView if user submits duplicated Rack
#             # - with same Rack name at root
#             # - and pointing to same warehouse.
#             if warehouse_pk != "":
#                 check_rack = self.model.objects.filter(name=name, warehouse__pk=warehouse_pk, depth=1)
#             else:
#                 check_rack = self.model.objects.filter(name=name, warehouse__isnull=True, depth=1)

#             if check_rack.exists():
#                 rack = check_rack.first()
#                 rack_type = rack.rack_type
#                 messages.info(self.request, f"{rack_type} {name} with warehouse {rack.warehouse} has already existed.")
#                 return redirect(rack.get_absolute_url())
#             else:
#                 post_data = request.POST.copy()
#                 post_data["_ref_node_id"] = "0"
#                 request.POST = post_data

#         return super().post(request, *args, **kwargs)

#     def get_context_data(self, **kwargs):
#         form = self.get_form()
#         context = super().get_context_data(**kwargs)

#         # If form is invalid, we need to pre-populate the "Relative To" field based on previous request
#         if not form.is_valid():
#             relative_to_id = form.data.get("_ref_node_id", "0")

#             if relative_to_id != "0" and relative_to_id != "":
#                 relative_to_value = self.model.objects.get(pk=relative_to_id).tree_full_name
#             else:
#                 relative_to_value = "--root--"

#             context["relative_to_id"] = relative_to_id
#             context["relative_to_value"] = relative_to_value
#         return context

#     def get_success_url(self) -> Any:
#         return reverse("rackings:racks:detail", kwargs={"pk": self.object.pk})


# class RackCreateView(RackCreateAndUpdateMixin, LoginRequiredMixin, PermissionRequiredMixin, CoreCreateView):
#     """Page to create Rack."""

#     success_message = _("Rack %(name)s successfully created")

#     header_title = "New Rack"

#     permission_required = ("rackings.add_rack",)


# rack_create_view = RackCreateView.as_view()


# class RackUpdateView(RackCreateAndUpdateMixin, LoginRequiredMixin, PermissionRequiredMixin, CoreUpdateView):
#     """Page to update selected Rack based on given Rack's pk."""

#     success_message = _("Rack %(name)s successfully updated")

#     header_title = "Update Rack"

#     permission_required = ("rackings.change_rack",)

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)

#         # To pre-populate selected "relative_to" values when retrieve form via GET request
#         if self.request.method == "GET" and self.object is not None:
#             relative_to_id = self.get_form()._get_position_ref_node(instance=self.object).get("_ref_node_id", "0")

#             # In case if previously it points to root node already
#             if relative_to_id != "0" and relative_to_id != "":
#                 relative_to_value = self.model.objects.get(pk=relative_to_id).tree_full_name
#             else:
#                 relative_to_value = "--root--"
#             context["relative_to_id"] = relative_to_id
#             context["relative_to_value"] = relative_to_value

#         return context


# rack_update_view = RackUpdateView.as_view()


# class RackDeleteView(LoginRequiredMixin, PermissionRequiredMixin, CoreDeleteView):
#     """Page to delete selected Rack based on given Rack's pk."""

#     model = Rack
#     success_url = reverse_lazy("rackings:racks:list")
#     success_message = _("Rack %(name)s successfully deleted")

#     permission_required = ("rackings.delete_rack",)


# rack_delete_view = RackDeleteView.as_view()


# ##################
# # FOR DataTables #
# ##################


# class RackDataTablesView(LoginRequiredMixin, PermissionRequiredMixin, CoreDataTablesView):
#     """JSON for DataTables in Rack's list page."""

#     model = Rack
#     table_class = RackDataTables
#     filterset_class = RackFilter

#     permission_required = ("rackings.view_rack",)


# rack_datatables_view = RackDataTablesView.as_view()


# class RackDetailDataTablesView(RackDataTablesView):
#     """JSON for DataTables in Rack's detail page."""

#     table_class = RackDetailDataTables


# rack_detail_datatables_view = RackDetailDataTablesView.as_view()


# ############
# # FOR HTMX #
# ############


# class RackInfoDetailView(LoginRequiredMixin, PermissionRequiredMixin, CoreDetailView):
#     """Partial page to display selected Rack's info based on given Rack's pk."""

#     model = Rack
#     template_name = "rackings/racks/partials/htmx/_info.html"

#     permission_required = ("rackings.view_rack",)


# rack_info_detail_view = RackInfoDetailView.as_view()


# class RackInfoUpdateView(LoginRequiredMixin, PermissionRequiredMixin, CoreUpdateView):
#     """Partial page to update selected Rack's info based on given Rack's pk."""

#     model = Rack
#     form_class = RackInfoUpdateForm
#     template_name = "rackings/racks/partials/htmx/_info_form.html"
#     success_message = _("Rack %(name)s basic information successfully updated")

#     permission_required = ("rackings.change_rack",)

#     def _prepare_relative_to_fields(self, relative_to_id: str, context: dict[str, Any]) -> dict[str, Any]:
#         """
#         Private function to update the "Relative To" fields for HTMX Basic info.
#         """
#         if relative_to_id not in ["0", "", None]:
#             relative_to_value = self.model.objects.get(pk=relative_to_id).tree_full_name
#         else:
#             relative_to_value = "--root--"

#         context["relative_to_id"] = relative_to_id
#         context["relative_to_value"] = relative_to_value

#         return context

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)

#         relative_to_id = None

#         # If form is invalid, we need to pre-populate the "Relative To" field based on previous request
#         form = self.get_form()
#         if not form.is_valid():
#             relative_to_id = form.data.get("_ref_node_id", "0")
#             context = self._prepare_relative_to_fields(relative_to_id, context)

#         # To pre-populate selected "relative_to" values when retrieve form via GET request
#         if self.request.method == "GET" and self.object is not None:
#             relative_to_id = (
#                 self.get_form()._get_position_ref_node(instance=self.object).get("_ref_node_id", "0") or "0"
#             )
#             context = self._prepare_relative_to_fields(relative_to_id, context)

#         return context

#     def get_success_url(self) -> Any:
#         return reverse("rackings:racks:info", kwargs={"pk": self.object.pk})


# rack_info_update_view = RackInfoUpdateView.as_view()


# class RackImportExcelView(LoginRequiredMixin, PermissionRequiredMixin, CoreFormView):
#     """Page to import Excel for Rack(s)."""

#     header_title = "Import Racks data"
#     selected_page = "rackings"
#     selected_subpage = "racks"

#     permission_required = ("rackings.add_rack",)

#     form_class = RackImportExcelForm
#     template_name = "rackings/racks/import.html"
#     success_url = reverse_lazy("rackings:racks:summary")
#     success_message = _("Successfully imported Excel data")

#     def post(self, request, *args, **kwargs):
#         """
#         Handle POST requests: instantiate a form instance with the passed
#         POST variables and then check if it's valid.
#         """
#         form = self.get_form()

#         if form.is_valid():
#             # To keep track of summary via the form object and pass it to the next view
#             request.session["summary"] = form.imported_object.summary

#             form_valid = self.form_valid(form)
#             return form_valid
#         else:
#             return self.form_invalid(form)


# rack_import_excel_view = RackImportExcelView.as_view()


# class RackImportSummaryView(LoginRequiredMixin, PermissionRequiredMixin, CoreTemplateView):
#     """Page to display Summary of imported Excel for Rack(s) after user clicks Submit button."""

#     header_title = "Import Excel Summary"
#     selected_page = "rackings"
#     selected_subpage = "racks"

#     template_name = "rackings/racks/summary.html"
#     permission_required = ("rackings.view_rack",)

#     def get_context_data(self, **kwargs) -> dict[str, Any]:
#         context = super().get_context_data(**kwargs)

#         # Retrieve data/values from previous view
#         context["summary"] = self.request.session["summary"]
#         return context


# rack_import_summary_view = RackImportSummaryView.as_view()


# def racks_dropdown_list_view(request: HttpRequest) -> JsonResponse:
#     """
#     FBV to return paginated Item queryset in the form of JSONResponse, based on query params.
#     * q = Refers to search term when user keys in select2 input field
#     * page = Refers to which "page"/scroll based on the paginated queryset

#     Example:

#     - Sample output based on API call for endpoint:
#     {{baseUrl}}/rackings/racks/dropdown/?&q=a-1-1&page=2
#     """

#     page = request.GET.get("page", 1)

#     if request.GET.get("q", None) is not None:
#         racks = Rack.objects.filter(Q(full_name__icontains=request.GET.get("q")))
#     else:
#         racks = Rack.objects.all()

#     results = [{"id": rack.pk, "text": rack.tree_full_name} for rack in racks]
#     # Add 1 extra item for "--root--"
#     results.insert(0, {"id": "", "text": "--root--"})

#     paginator = Paginator(results, settings.LIMIT_AUTOCOMPLETE_RESULTS_PER_SCROLL)
#     results = paginator.get_page(page).object_list

#     # Determine whether to end the "infinite scroll"
#     pagination = True if len(results) >= settings.LIMIT_AUTOCOMPLETE_RESULTS_PER_SCROLL else False
#     return JsonResponse(
#         {
#             "results": results,
#             "pagination": {"more": pagination},
#         }
#     )


# ##################
# # FOR Export Excel #
# ##################


# def rack_detail_view_export_to_xlsx(request: HttpRequest, pk: int) -> HttpResponse:
#     """
#     Downloads all rack's storage (stocks) including the descendant rack nodes based on selected
#     Rack detail view as an Excel file.
#     """

#     rack = Rack.objects.get(pk=pk)

#     response = HttpResponse(
#         content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
#     )
#     today_date = localtime_now().strftime("%Y.%m.%d")
#     response["Content-Disposition"] = f"attachment; filename={rack.rack_type.upper()} {rack.name}-{today_date}.xlsx"

#     workbook = Workbook()

#     # Get active worksheet/tab
#     worksheet = workbook.active
#     # worksheet.title = "Stock Items"

#     # Define the titles for columns
#     balance_columns = [
#         "Index",
#         "Floor",
#         "Chamber",
#         "Rack",
#         "Bay",
#         "Level",
#         "Pallet",
#         "Location",
#         "ProductCode",
#         "Batch",
#         "Qty",
#         "ExpiryDate",
#     ]
#     balance_row_num = 1

#     # Assign the titles for each cell of the header
#     for col_num, column_title in enumerate(balance_columns, 1):
#         cell = worksheet.cell(row=balance_row_num, column=col_num)
#         cell.value = column_title
#         cell.font = Font(bold=True)
#         cell.alignment = Alignment(horizontal="center")

#     balance_row_num += 1

#     if rack.numchild != 0:
#         rack_nodes = rack.get_descendants().filter(numchild=0).order_by("full_name")
#     else:
#         rack_nodes = Rack.objects.filter(pk=pk)

#     index = 1

#     for node in rack_nodes:
#         full_name_list = node.full_name.split("-")
#         rack_storages = node.rackstorage_set.all()

#         max_depth = 6
#         temp = ["" for _ in range(max_depth - len(full_name_list))]
#         full_name_list.extend(temp)

#         if rack_storages.count() > 0:
#             for rack_storage in rack_storages:
#                 index += 1
#                 stock = rack_storage.stock

#                 rack_transaction = (
#                     RackTransaction.objects.filter(rackstorage=rack_storage).order_by("transaction_datetime").last()
#                 )
#                 quantity = rack_transaction.quantity if rack_transaction is not None else Decimal("0")
#                 expiry_date = stock.expiry_date.strftime("%Y/%m/%d") if stock.expiry_date is not None else ""

#                 # Define the data for each cell in the row
#                 row = [
#                     index - 1,
#                     str(full_name_list[0]),
#                     str(full_name_list[1]),
#                     str(full_name_list[2]),
#                     str(full_name_list[3]),
#                     str(full_name_list[4]),
#                     str(full_name_list[5]),
#                     node.full_name,
#                     stock.item.code,
#                     stock.batch_no,
#                     format_decimal_values(quantity),
#                     expiry_date,
#                 ]

#                 # Assign the data for each cell of the row
#                 for col_num, cell_value in enumerate(row, 1):
#                     cell = worksheet.cell(row=index, column=col_num)
#                     cell.value = cell_value
#                     cell.alignment = Alignment(horizontal="center")
#         else:
#             index += 1
#             row = [
#                 index - 1,
#                 str(full_name_list[0]),
#                 str(full_name_list[1]),
#                 str(full_name_list[2]),
#                 str(full_name_list[3]),
#                 str(full_name_list[4]),
#                 str(full_name_list[5]),
#                 node.full_name,
#                 "",
#                 "",
#                 "",
#                 "",
#             ]

#             # Assign the data for each cell of the row
#             for col_num, cell_value in enumerate(row, 1):
#                 cell = worksheet.cell(row=index, column=col_num)
#                 cell.value = cell_value
#                 cell.alignment = Alignment(horizontal="center")

#     workbook.save(response)

#     return response

# from typing import Any

# from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
# from django.urls import reverse
# from django.utils.translation import gettext_lazy as _

# from wss.cores.utils import localtime_now
# from wss.cores.views import CoreCreateView

# from wss.apps.inventories.models import Stock
# from wss.apps.rackings.forms import RackTransactionForm, RackTransactionTransferForm
# from wss.apps.rackings.models import Rack, RackStorage, RackTransaction


# class RackTransactionPopupCreateView(LoginRequiredMixin, PermissionRequiredMixin, CoreCreateView):
#     """Partial page to create a new RackTransaction object based on selected Rack and Stock."""

#     model = RackTransaction
#     form_class = RackTransactionForm
#     template_name = "rackings/rack_transactions/partials/htmx/_popup_create.html"

#     permission_required = ("rackings.add_racktransaction",)

#     success_message = _("New Stock Card successfully created!")

#     def get_rack(self) -> Rack:
#         return Rack.objects.get(pk=self.kwargs["rack_pk"])

#     def get_stock(self) -> Stock:
#         return Stock.objects.get(pk=self.kwargs["stock_pk"])

#     def get_rack_storage(self) -> RackStorage:
#         return RackStorage.objects.get(rack=self.rack, stock=self.stock)

#     def get_initial(self) -> dict[str, Any]:
#         self.rack = self.get_rack()
#         self.stock = self.get_stock()
#         self.rack_storage = self.get_rack_storage()

#         initial = super().get_initial()
#         initial["rackstorage"] = self.rack_storage
#         initial["created_by"] = self.request.user

#         return initial

#     def get_context_data(self, **kwargs) -> dict[str, Any]:
#         context = super().get_context_data(**kwargs)
#         context["rack"] = self.rack
#         context["stock"] = self.stock
#         return context

#     def get_success_url(self) -> str:
#         # Redirect to Rack's Stock's transactions view
#         year, month = self.object.transaction_datetime.strftime("%Y-%m-%d").split("-")[:2]

#         return reverse(
#             "inventories:racks:transaction_detail",
#             kwargs={
#                 "rack_pk": self.rack.pk,
#                 "pk": self.stock.pk,
#                 "year": int(year) or localtime_now().year,
#                 "month": int(month) or localtime_now().month,
#             },
#         )


# rack_transaction_popup_create_view = RackTransactionPopupCreateView.as_view()


# class RackTransactionTransferView(LoginRequiredMixin, PermissionRequiredMixin, CoreCreateView):
#     """"""

#     model = RackTransaction
#     form_class = RackTransactionTransferForm
#     template_name = "rackings/rack_transactions/partials/htmx/_transfer.html"

#     permission_required = ("rackings.add_racktransaction",)

#     success_message = _("Stock successfully transferred!")

#     def get_rack(self) -> Rack:
#         if not hasattr(self, "rack"):
#             self.rack = Rack.objects.get(pk=self.kwargs["rack_pk"])
#         return self.rack

#     def get_stock(self) -> Stock:
#         if not hasattr(self, "stock"):
#             self.stock = Stock.objects.get(pk=self.kwargs["stock_pk"])
#         return self.stock

#     def get_rack_storage(self) -> RackStorage:
#         if not hasattr(self, "rack_storage"):
#             self.rack_storage = RackStorage.objects.get(rack=self.get_rack(), stock=self.get_stock())
#         return self.rack_storage

#     def get_initial(self) -> dict[str, Any]:
#         initial = super().get_initial()
#         initial["type"] = RackTransaction.Type.TRANSFER
#         initial["transfer_from"] = self.get_rack_storage()
#         initial["created_by"] = self.request.user

#         return initial

#     def get_context_data(self, **kwargs) -> dict[str, Any]:
#         context = super().get_context_data(**kwargs)
#         context["rack"] = self.get_rack()
#         context["stock"] = self.get_stock()
#         return context

#     def get_success_url(self) -> str:
#         return self.object.get_stock_rack_transaction_url()


# rack_transaction_transfer_view = RackTransactionTransferView.as_view()

import logging
import os
from datetime import datetime

# from decimal import Decimal
from typing import Any, Union

from django.core.exceptions import ValidationError
from django.db.models import signals

import openpyxl

from wms.cores.actstream import register_stream

from wms.apps.inventories.models.item import Item
from wms.apps.inventories.models.stock import Stock
from wms.apps.rackings.models import Rack
from wms.apps.rackings.models.rack import RackStorage, RackTransaction
from wms.apps.settings.models.warehouse import Warehouse

logger = logging.getLogger(__name__)


class ImportRacksExcel:
    """
    Base class for handling imported Excel sheet for managing Racks data.
    """

    def __init__(self, data=None) -> None:
        self.data = data
        self.rack_cells = ()
        self._valid = False
        self.import_line_list = []
        self.summary = {
            "floor_count": 0,
            "chamber_count": 0,
            "rack_count": 0,
            "bay_count": 0,
            "level_count": 0,
            "pallet_count": 0,
            "skipped_existing_racks_count": 0,
            # Those keys below that ends with "*_info" stores list of line numbers from Excel
            # for tracking purposes in Summary page later.
            "skip_rows_with_empty_location_name": 0,
            "skip_rows_with_empty_location_name_info": [],
            "skip_stock_invalid_exp_date": 0,
            "skip_stock_invalid_exp_date_info": [],
            "skip_stock_zero_balance": 0,
            "skip_stock_zero_balance_info": [],
            "skip_invalid_non_numeric_quantity": 0,
            "skip_invalid_non_numeric_quantity_info": [],
            "skip_invalid_item_batch": 0,
            "skip_invalid_item_batch_info": [],
            "skip_invalid_item_code_quantity": 0,
            "skip_invalid_item_code_quantity_info": [],
        }

    def _check_file_extension(self):
        ext = os.path.splitext(self.data.name)[1]  # [0] returns path+filename
        valid_extensions = [".xlsx"]
        if not ext.lower() in valid_extensions:
            raise ValidationError(f"Unsupported file extension for '{ext}'.")

    def _check_valid_expiry_date(self, date_obj, date_format="%Y/%m/%d"):
        try:
            # Try to parse the string into a date
            date_obj.strftime(date_format)
            return True
        except (Exception, ValueError):
            # If parsing fails, it's not in the correct format
            return False

    def _update_racks_warehouse(self, rack_node: Union[Rack, Any], warehouse: Union[Warehouse, None]) -> Rack:
        """
        Only update the rack_node's warehouse if it's different from input argument above.
        """
        if rack_node is not None and rack_node.warehouse != warehouse:
            rack_node.warehouse = warehouse
            rack_node.save(update_fields=["warehouse"])

        return rack_node

    def _check_header_row(self, header_cells: tuple) -> None:
        """
        Gracefully returns error messages if the imported header row does not fulfill
        the minimum criteria of labelling from Floor to Pallet at header columns.
        """
        extracted_header_cells = header_cells[0]
        check_header_cells = {
            0: "Floor",
            1: "Chamber",
            2: "Rack",
            3: "Bay",
            4: "Level",
            5: "Pallet",
        }

        for idx in list(check_header_cells.keys()):
            if (
                extracted_header_cells[idx].value is not None
                and isinstance(extracted_header_cells[idx].value, str)
                and extracted_header_cells[idx].value.upper() != check_header_cells[idx].upper()
            ):
                raise ValidationError(
                    f"Invalid data format at Line 2's Header: Expected {check_header_cells[idx]} "
                    + f"but instead gotten {extracted_header_cells[idx].value}"
                )

    def cleaned(self):
        """
        Function to build cleaned data from uploaded file and track unsual values based on expected format.

        Make this function public, so that it can also be reused and called by external places whenever
        we instantiate this class.
        """

        if self._valid is True:
            # Call private method
            self._check_rack_cells()

    def _check_naming_values(self, value) -> str:
        """
        Check the incoming value for all the formats, from column B to column G,
        consisting of:
        - Floor
        - Chamber
        - Rack
        - Bay
        - Level
        - Pallet.
        """
        output = ""

        if value is not None:
            output = str(int(value)) if isinstance(value, float) else str(value)

        # To handle empty/blank cells for Pallet column
        if output in ["None", ""]:
            output = "1"

        return output

    def _clean_batch_no(self, value) -> str:
        """
        Clean/Handle the batch no values.

        We follow MM/yyyy format if input argument `value` is datetime object
        and not string.
        """
        if isinstance(value, datetime):
            batch_no = f"{value.month:02}/{value.year}"
        else:
            batch_no = str(value).strip()
            if batch_no in ["None", ""]:
                batch_no = "N/A"

        return batch_no

    def _check_rack_cells(self):
        """
        * Go through each line, if there's already an exisiting Rack object that matches the
        naming provided, then it will just retrieve it instead of creating a duplicated node
        with same depth & same naming. Similar behavior to get_or_create()

        * Update new row/entries within self.import_line_list to be further processed later.

        * Since there's too many rows (current sample file contains 8.5k++ lines), its for the best to
        keep track of the inconsistent/unexpected data into self.summary and display it
        there in the Summary Page view once user has clicked Submit button and it redirects to that
        summary view.
        """

        for floor, chamber, rack, bay, level, pallet, _, item_code, batch_no, quantity, expiry_date in self.rack_cells:
            line_number = floor.row
            floor = self._check_naming_values(floor.value)
            chamber = self._check_naming_values(chamber.value)
            rack = self._check_naming_values(rack.value)
            bay = self._check_naming_values(bay.value)
            level = self._check_naming_values(level.value)
            pallet = self._check_naming_values(pallet.value)
            item_code = str(item_code.value).strip()
            batch_no = self._clean_batch_no(batch_no.value)
            quantity = quantity.value
            expiry_date = expiry_date.value

            location = f"{floor}-{chamber}-{rack}-{bay}-{level}-{pallet}"

            # Skips current row if there's empty row in all of the rack naming (column B to column G)
            if all(value == "" for value in [floor, chamber, rack, bay, level, pallet]):
                self.summary["skip_rows_with_empty_location_name"] += 1
                self.summary["skip_rows_with_empty_location_name_info"].append({f"Line {line_number}": ""})
                continue

            if Rack.objects.filter(full_name=location).exists():
                self.summary["skipped_existing_racks_count"] += 1

            self.import_line_list.append(
                [floor, chamber, rack, bay, level, pallet, item_code, batch_no, quantity, expiry_date, line_number]
            )

    def process_create_racks(self) -> None:
        """
        To create Racks, RackStorage & RackTransactions with RackBalance based on validated self.import_line_list.

        Example of self.import_line_list:

        self.import_line_list = [
            # Corresponds to Rack 5-1-A-1-1-1 at line 1
            ["5", "1", "A", "1", "1", "1", "AP00245", "F2ZH053", 72, datetime.datetime(2027, 7, 31, 0, 0), 1],
            # Corresponds to Rack 5-1-A-1-2-1 at line 2
            ["5", "1", "A", "1", "2", "1", "AP00243", "F2ZG154", 28, datetime.datetime(2026, 6, 30, 0, 0), 2],
            ...
            # Corresponds to Rack 5-1-AQ-4-2-1 at line 1810
            ["5", "1", "AQ", "4", "2", "1", "AP00243", "F2ZG154", 28, datetime.datetime(2026, 6, 30, 0, 0), 1810],
        ]
        """
        # Disable signals for create_stream and update_stream.
        skip_signals_models = [
            Rack,
            RackStorage,
            RackTransaction,
        ]

        for model in skip_signals_models:
            signals.post_save.disconnect(sender=model, dispatch_uid=f"{model.__name__}_create_stream")
            signals.pre_save.disconnect(sender=model, dispatch_uid=f"{model.__name__}_update_stream")

        # Disable signals for core's create_stream and update_stream.
        skip_core_signals_models = [
            Item,
        ]

        for model in skip_core_signals_models:
            signals.post_save.disconnect(sender=model, dispatch_uid=f"{model.__name__}_create_stream")
            signals.pre_save.disconnect(sender=model, dispatch_uid=f"{model.__name__}_update_stream")

        # By default, all the warehouse FK within each Rack points to SELANGOR > Warehouse SA02 > 3PL2
        warehouse = Warehouse.objects.get(slug="3pl2")

        # to create Rack nodes from top-down approach
        for (
            floor,
            chamber,
            rack,
            bay,
            level,
            pallet,
            item_code,
            batch_no,
            quantity,
            expiry_date,
            line_number,
        ) in self.import_line_list:
            location = f"{floor}-{chamber}-{rack}-{bay}-{level}-{pallet}"

            # Create/Retrieve floor (root node)
            query_floor_type = Rack.objects.filter(name=floor, depth=1, rack_type=Rack.RackType.FLOOR)
            if query_floor_type.exists():
                floor_node = query_floor_type.first()
            else:
                floor_node = Rack.add_root(name=floor, rack_type=Rack.RackType.FLOOR)
                self.summary["floor_count"] += 1

            floor_node = self._update_racks_warehouse(floor_node, warehouse)

            # Create/Retrieve chamber
            chamber_full_name = f"{floor}-{chamber}"
            query_chamber_type = Rack.objects.filter(
                name=chamber, full_name=chamber_full_name, depth=2, rack_type=Rack.RackType.CHAMBER
            )
            if query_chamber_type.exists():
                chamber_node = query_chamber_type.first()
            else:
                new_node = Rack(name=chamber, rack_type=Rack.RackType.CHAMBER)
                chamber_node = floor_node.add_child(instance=new_node)
                self.summary["chamber_count"] += 1

            chamber_node = self._update_racks_warehouse(chamber_node, warehouse)

            # Create/Retrieve rack
            rack_full_name = f"{chamber_full_name}-{rack}"
            query_rack_type = Rack.objects.filter(
                name=rack, full_name=rack_full_name, depth=3, rack_type=Rack.RackType.RACK
            )
            if query_rack_type.exists():
                rack_node = query_rack_type.first()
            else:
                new_node = Rack(name=rack, rack_type=Rack.RackType.RACK)
                rack_node = chamber_node.add_child(instance=new_node)
                self.summary["rack_count"] += 1

            rack_node = self._update_racks_warehouse(rack_node, warehouse)

            # Create/Retrieve bay
            bay_full_name = f"{rack_full_name}-{bay}"
            query_bay_type = Rack.objects.filter(
                name=bay, full_name=bay_full_name, depth=4, rack_type=Rack.RackType.BAY
            )
            if query_bay_type.exists():
                bay_node = query_bay_type.first()
            else:
                new_node = Rack(name=bay, rack_type=Rack.RackType.BAY)
                bay_node = rack_node.add_child(instance=new_node)
                self.summary["bay_count"] += 1

            bay_node = self._update_racks_warehouse(bay_node, warehouse)

            # Create/Retrieve level
            level_full_name = f"{bay_full_name}-{level}"
            query_level_type = Rack.objects.filter(
                name=level, full_name=level_full_name, depth=5, rack_type=Rack.RackType.LEVEL
            )
            if query_level_type.exists():
                level_node = query_level_type.first()
            else:
                new_node = Rack(name=level, rack_type=Rack.RackType.LEVEL)
                level_node = bay_node.add_child(instance=new_node)
                self.summary["level_count"] += 1

            level_node = self._update_racks_warehouse(level_node, warehouse)

            # Create/Retrieve pallet
            pallet_full_name = f"{level_full_name}-{pallet}"
            query_pallet_type = Rack.objects.filter(
                name=pallet, full_name=pallet_full_name, depth=6, rack_type=Rack.RackType.PALLET
            )
            if query_pallet_type.exists():
                pallet_node = query_pallet_type.first()
            else:
                new_node = Rack(name=pallet, rack_type=Rack.RackType.PALLET)
                pallet_node = level_node.add_child(instance=new_node)
                self.summary["pallet_count"] += 1

            pallet_node = self._update_racks_warehouse(pallet_node, warehouse)

            if expiry_date is not None:
                if self._check_valid_expiry_date(expiry_date) is True:
                    query_stock = Stock.objects.filter(
                        warehouse=warehouse,
                        item__code=item_code,
                        batch_no=batch_no,
                        expiry_date=expiry_date.date(),
                    )
                else:
                    # error_msg = (
                    #     "Error when reading Expiry Date field, expected yyyy/MM/dd "
                    #     + f"at column L but instead got {expiry_date}. "
                    #     + "Skipped creating Transaction/Balance for current line."
                    # )
                    # logger.error(error_msg)
                    # query_stock = Stock.objects.none()
                    # self.summary["skip_stock_invalid_exp_date"] += 1
                    # self.summary["skip_stock_invalid_exp_date_info"].append(
                    #     {f"Line {line_number}, Column L": expiry_date}
                    # )
                    # continue

                    # TODO: Based on latest requirement:
                    # If invalid Expiry Date, simply query by excluding Expiry Date field
                    query_stock = Stock.objects.filter(warehouse=warehouse, item__code=item_code, batch_no=batch_no)
            else:
                query_stock = Stock.objects.filter(warehouse=warehouse, item__code=item_code, batch_no=batch_no)

            # To handle Stock Out scenario, where previously there's RackTransaction but in latest import, there's none
            stock_reset_to_none = all(i in ["None", None] for i in [item_code, batch_no, quantity, expiry_date])
            query_rack_transactions = RackTransaction.objects.filter(rackstorage__rack=pallet_node)

            # TODO: Based on latest requirement:
            # if there's no queried stock exists from inventory based on Excel rows, simply create new Stock
            # from scratch with balance qty = 0 as long as it has valid Item code.
            if not query_stock.exists() and item_code not in ["", "None"]:
                item = Item.objects.filter(code=item_code).first()
                if item is not None:
                    # quantity = quantity or 1
                    # expiry_date = expiry_date.date() if isinstance(expiry_date, datetime) else None

                    # # Check for existing stocks based on unique_together, right before creating new Stock
                    # query_stock = Stock.objects.filter(
                    #     warehouse=warehouse,
                    #     item=item,
                    #     batch_no=batch_no,
                    #     expiry_date=expiry_date,
                    # )

                    # if not query_stock.exists():
                    #     # Set newly-created Stock's balance to 0 since there's no (inventory)
                    #     # Transaction linked to it yet.
                    #     new_stock = Stock.objects.create(
                    #         warehouse=warehouse,
                    #         item=item,
                    #         batch_no=batch_no,
                    #         expiry_date=expiry_date,
                    #         balance=Decimal("0"),
                    #     )
                    #     query_stock = Stock.objects.filter(pk=new_stock.pk)
                    self.summary["skip_invalid_item_batch"] += 1
                    self.summary["skip_invalid_item_batch_info"].append({f"Line {line_number}": item_code})

                else:
                    self.summary["skip_invalid_item_code_quantity"] += 1
                    self.summary["skip_invalid_item_code_quantity_info"].append({f"Line {line_number}": item_code})

            # Two main conditions:
            # 1) If there's valid Stock in Excel row, then perform create/update on RackTransaction.
            # 2) If there's blank on Stock's unique_together in Excel row and there exists RackTransactions,
            #    then we perform Stock Out to deduct the quantity back to 0.

            if query_stock.exists():
                stock = query_stock.first()
                try:
                    rack_storage, _ = RackStorage.objects.get_or_create(rack=pallet_node, stock=stock)
                except ValidationError as e:
                    self.summary["skip_stock_zero_balance"] += 1
                    self.summary["skip_stock_zero_balance_info"].append({f"Line {line_number}": str(stock)})
                    logger.error(e)
                    continue
                else:
                    quantity = quantity or 1
                    if isinstance(quantity, str) and not quantity.isnumeric():
                        self.summary["skip_invalid_non_numeric_quantity"] += 1
                        self.summary["skip_invalid_non_numeric_quantity_info"].append(
                            {f"Line {line_number}, Column K": quantity}
                        )
                        continue

                    # Create/Update RackTransactions along with RackBalance
                    if RackTransaction.objects.filter(rackstorage=rack_storage).count() <= 1:
                        RackTransaction.objects.update_or_create(
                            rackstorage=rack_storage,
                            defaults={"quantity": quantity},
                        )
                    else:
                        rack_transaction = (
                            RackTransaction.objects.filter(rackstorage=rack_storage)
                            .order_by("transaction_datetime")
                            .last()
                        )
                        rack_transaction.quantity = quantity
                        rack_transaction.save()

            elif stock_reset_to_none and query_rack_transactions.exists():
                # In case there's multiple Transactions with same RackStorage, we just create
                # Stock Out transaction based on the latest RackTransaction, only if it's not zero.
                latest_transaction = query_rack_transactions.order_by("transaction_datetime").last()

                if latest_transaction.balance_lookup.get(f"{pallet_node.pk}", 0) <= 0:
                    continue
                else:
                    stock_out_qty = latest_transaction.quantity * -1
                    RackTransaction.objects.create(rackstorage=latest_transaction.rackstorage, quantity=stock_out_qty)

            try:
                assert pallet_node.full_name == location
            except AssertionError:
                error_msg = (
                    f"Naming for node generated {pallet_node.full_name} does NOT match expected format: {location}"
                )
                logger.error(error_msg)

        # Enable signals for create_stream and update_stream.
        for model in skip_signals_models:
            register_stream(model, delete=False)

        # Enable signals for core's create_stream and update_stream.
        for model in skip_core_signals_models:
            register_stream(model, delete=False)

        return None

    def is_valid(self):
        """Function to check if the uploaded file is valid."""

        valid = True

        self._check_file_extension()

        # read_only argument uses much less memory & faster but not all
        # features are available (charts, images, etc.)
        # data_only argument ensures that we only retrieve values, not formulae for Qty column
        book = openpyxl.load_workbook(self.data, read_only=True, data_only=True)
        sheet = book.active

        try:

            # To determine the starting row
            starting_row = 2 if sheet["B1"].value == "Floor" else 3

            # To determine the final row
            final_row = sheet.max_row

            header_cells = sheet[f"B{starting_row-1}":f"L{starting_row-1}"]
            self._check_header_row(header_cells)

            rack_cells = sheet[f"B{starting_row}":f"L{final_row}"]
            self.rack_cells = rack_cells
        except (ValueError, TypeError) as ex:
            logger.error(ex)
            valid = False
            raise ValidationError(ex)

        self._valid = valid

        return self._valid

from django.urls import include, path

# from wss.apps.rackings.views import (
#     rack_create_view,
#     rack_datatables_view,
#     rack_delete_view,
#     rack_detail_datatables_view,
#     rack_detail_view,
#     rack_detail_view_export_to_xlsx,
#     rack_import_excel_view,
#     rack_import_summary_view,
#     rack_info_detail_view,
#     rack_info_update_view,
#     rack_list_view,
#     rack_transaction_popup_create_view,
#     rack_transaction_transfer_view,
#     rack_update_view,
#     racks_dropdown_list_view,
# )
from wms.apps.rackings.views import (
    RackListView,
    RackCreateView,
    RackUpdateView,
    RackDetailHomeView,
    RackDetailView,
    RackDataTableDetailView,
)

app_name = "rackings"


racks_urlpatterns = [
    path("", RackListView.as_view(), name="list"),
    path("create/", RackCreateView.as_view(), name="create"),
    path("<int:pk>/update/", RackUpdateView.as_view(), name="update"),
    path("<int:pk>/", RackDataTableDetailView.as_view(), name="panel"),
    path("<int:pk>/home/", RackDetailHomeView.as_view(), name="detail_home"),
    path("<int:pk>/detail/", RackDetailView.as_view(), name="detail"),
#     path("", view=rack_list_view, name="list"),
#     path("create/", view=rack_create_view, name="create"),
#     path("delete/<int:pk>/", view=rack_delete_view, name="delete"),
#     path("detail/<int:pk>/", view=rack_detail_view, name="detail"),
#     path("detail/<int:pk>/update/", view=rack_update_view, name="update"),
#     path("detail/<int:pk>/info/", view=rack_info_detail_view, name="info"),
#     path("detail/<int:pk>/info/update/", view=rack_info_update_view, name="info_update"),
#     path("datatables/", view=rack_datatables_view, name="datatables"),
#     path("datatables-detail/", view=rack_detail_datatables_view, name="datatables-detail"),
#     path("dropdown/", view=racks_dropdown_list_view, name="racks_dropdown"),
#     path("import/", view=rack_import_excel_view, name="import"),
#     path("summary/", view=rack_import_summary_view, name="summary"),
#     path("detail/<int:pk>/export/", view=rack_detail_view_export_to_xlsx, name="detail-export"),
#     # path("detail/<int:pk>/history/", view=rack_history_list_view, name="history"),
#     # path("detail/<int:pk>/history/popup-modified", view=rack_history_modified_view, name="history-modified"),
#     # path("detail/<int:pk>/datatables-history/", view=rack_history_list_datatables_view, name="datatables-history"),
]

# rack_transactions_urlpatterns = [
#     path(
#         "rack/<int:rack_pk>/stock/<int:stock_pk>/popup-create/",
#         view=rack_transaction_popup_create_view,
#         name="popup_create",
#     ),
#     path(
#         "rack/<int:rack_pk>/stock/<int:stock_pk>/transfer/",
#         view=rack_transaction_transfer_view,
#         name="transfer",
#     ),
# ]

urlpatterns = [
    path(
        "racks/",
        include((racks_urlpatterns, "rackings.racks"), namespace="racks"),
    ),
#     path(
#         "rack-transactions/",
#         include((rack_transactions_urlpatterns, "rackings.rack_transactions"), namespace="rack_transactions"),
#     ),
]

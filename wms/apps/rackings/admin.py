from django.contrib import admin

from treebeard.admin import TreeAdmin
from treebeard.forms import movenodeform_factory

from wms.cores.admin import BaseModelAdmin

from .models import Rack, RackStorage, RackTransaction


@admin.register(Rack)
class RackAdmin(BaseModelAdmin, TreeAdmin):
    """Django admin for Rack."""

    list_display = [
        "warehouse",
        "name",
        "full_name",
        "rack_type",
        "path",
        "depth",
        "numchild",
    ]
    readonly_fields = [
        "full_name",
    ]
    search_fields = [
        "name",
        "warehouse__name",
    ]
    list_filter = [
        "warehouse__name",
        "rack_type",
    ]
    exclude = BaseModelAdmin.exclude + [
        "path",
        "depth",
        "numchild",
    ]
    form = movenodeform_factory(Rack)


@admin.register(RackStorage)
class RackStorageAdmin(BaseModelAdmin):
    """Django admin for RackStorage."""

    list_display = [
        "pk",
        "rack",
        "stock",
    ]
    raw_id_fields = ("stock",)
    search_fields = [
        "stock__item__code",
        "stock__item__name",
        "stock__batch_no",
    ]
    list_filter = [
        # "rack__name",
        "stock__item__item_type",
        "stock__item__manage_type",
    ]

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        if db_field.name == "rack":
            # Filter the queryset to only include leaf nodes (numchild == 0)
            kwargs["queryset"] = Rack.objects.filter(numchild=0)

        return super().formfield_for_foreignkey(db_field, request, **kwargs)


@admin.register(RackTransaction)
class RackTransactionAdmin(BaseModelAdmin):
    """Django admin for RackTransaction."""

    list_display = (
        "transaction_datetime",
        "rackstorage",
        "quantity",
        "is_reserved",
        "goods_received_note_item",
        "warehouse_release_order_item",
    )
    search_fields = (
        "rackstorage__rack__full_name",
        "rackstorage__stock__item__code",
        "rackstorage__stock__item__name",
    )
    list_filter = [
        "is_reserved",
        "rackstorage__rack",
    ]
    date_hierarchy = "transaction_datetime"

# Generated by Django 5.1.7 on 2025-04-12 10:53

import django.db.models.deletion
import django.utils.timezone
import model_utils.fields
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('inventories', '0001_initial'),
        ('receives', '0001_initial'),
        ('releases', '0001_initial'),
        ('settings', '0002_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Rack',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('path', models.CharField(max_length=255, unique=True)),
                ('depth', models.PositiveIntegerField()),
                ('numchild', models.PositiveIntegerField(default=0)),
                ('name', models.CharField(max_length=32, verbose_name='Name')),
                ('full_name', models.CharField(blank=True, max_length=256, null=True, verbose_name='Full Name')),
                ('rack_type', models.CharField(choices=[('Floor', 'Floor'), ('Chamber', 'Chamber'), ('Rack', 'Rack'), ('Bay', 'Bay'), ('Level', 'Level'), ('Pallet', 'Pallet')], default='Floor', max_length=128, verbose_name='Rack Type')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('modified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Modified by')),
                ('warehouse', models.ForeignKey(blank=True, limit_choices_to={'is_storage': True}, null=True, on_delete=django.db.models.deletion.CASCADE, to='settings.warehouse')),
            ],
            options={
                'ordering': ['path'],
                'get_latest_by': 'created',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RackStorage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('modified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Modified by')),
                ('rack', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='rackings.rack')),
                ('stock', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventories.stock')),
            ],
            options={
                'ordering': ['rack'],
                'get_latest_by': 'created',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RackTransaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('type', models.CharField(choices=[('GRN', 'GRN'), ('WRO', 'WRO'), ('Transfer in', 'Transfer in'), ('Transfer out', 'Transfer out'), ('Adjustment', 'Adjustment')], default='Adjustment', max_length=32, verbose_name='Type')),
                ('transaction_datetime', models.DateTimeField(default=django.utils.timezone.now)),
                ('quantity', models.DecimalField(decimal_places=6, default=Decimal('0'), max_digits=19, verbose_name='Quantity')),
                ('is_reserved', models.BooleanField(default=False, verbose_name='Is Reserved?')),
                ('remark', models.TextField(blank=True, max_length=512, verbose_name='Remark')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('goods_received_note_item', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='receives.goodsreceivednoteitem', verbose_name='Goods Received Note Item')),
                ('modified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Modified by')),
                ('rackstorage', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='rackings.rackstorage')),
                ('warehouse_release_order_item', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='releases.warehousereleaseorderitem', verbose_name='Warehouse Release Order Item')),
            ],
            options={
                'ordering': ['-transaction_datetime', '-pk'],
                'get_latest_by': 'created',
                'abstract': False,
            },
        ),
        migrations.AddIndex(
            model_name='rack',
            index=models.Index(fields=['warehouse'], name='rackings_ra_warehou_e8956b_idx'),
        ),
        migrations.AddIndex(
            model_name='rack',
            index=models.Index(fields=['rack_type'], name='rackings_ra_rack_ty_cc3e64_idx'),
        ),
        migrations.AddIndex(
            model_name='rack',
            index=models.Index(fields=['warehouse', 'rack_type', 'full_name'], name='rackings_ra_warehou_a0c627_idx'),
        ),
        migrations.AddIndex(
            model_name='rack',
            index=models.Index(fields=['rack_type', 'warehouse', 'full_name'], name='rackings_ra_rack_ty_270e0a_idx'),
        ),
        migrations.AddIndex(
            model_name='rackstorage',
            index=models.Index(fields=['rack'], name='rackings_ra_rack_id_ec8371_idx'),
        ),
        migrations.AddIndex(
            model_name='rackstorage',
            index=models.Index(fields=['stock'], name='rackings_ra_stock_i_2c985a_idx'),
        ),
        migrations.AddIndex(
            model_name='rackstorage',
            index=models.Index(fields=['rack', 'stock'], name='rackings_ra_rack_id_032b23_idx'),
        ),
        migrations.AddIndex(
            model_name='rackstorage',
            index=models.Index(fields=['stock', 'rack'], name='rackings_ra_stock_i_e739a1_idx'),
        ),
        migrations.AddIndex(
            model_name='racktransaction',
            index=models.Index(fields=['rackstorage'], name='rackings_ra_racksto_aa760c_idx'),
        ),
        migrations.AddIndex(
            model_name='racktransaction',
            index=models.Index(fields=['transaction_datetime'], name='rackings_ra_transac_8de910_idx'),
        ),
        migrations.AddIndex(
            model_name='racktransaction',
            index=models.Index(fields=['rackstorage', 'transaction_datetime'], name='rackings_ra_racksto_be3ac0_idx'),
        ),
        migrations.AddIndex(
            model_name='racktransaction',
            index=models.Index(fields=['type', 'rackstorage', 'is_reserved', 'transaction_datetime'], name='rackings_ra_type_946b9a_idx'),
        ),
        migrations.AddIndex(
            model_name='racktransaction',
            index=models.Index(fields=['rackstorage', 'is_reserved', 'transaction_datetime'], name='rackings_ra_racksto_f32f66_idx'),
        ),
    ]

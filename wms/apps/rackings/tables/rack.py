from django.utils.translation import gettext_lazy as _

import django_tables2 as tables
from django.urls import reverse, NoReverseMatch

# from wms.cores.models import DISPLAY_EMPTY_VALUE
# from wms.cores.tables import PARTIAL_LIST_ATTRS_CLASS, TABLE_ATTRS_CLASS, CoreBooleanColumn

from ..models import Rack
from wms.cores.columns import HTMXColumn


class RackDetailTable(tables.Table):
    full_name = HTMXColumn(
        url_name="rackings:racks:detail_home",
        target_id="detail-panel",
        verbose_name=_("Name"),
        push_url=True,
        push_url_name="rackings:racks:panel",
    )

    class Meta:
        model = Rack
        order_by = 'name'
        template_name = "tables/table_htmx.html"
        fields = [
            "rack_type",
            "full_name",
        ]

    def __init__(self, *args, object_pk=None, **kwargs):
        super().__init__(*args, **kwargs)
        self.object_pk = object_pk


class RackTable(tables.Table):
    section_title = "Rack Lists"
    section_name = "Rack"
    selectable = True

    # name = tables.LinkColumn("rackings:racks:panel", args=[tables.utils.A("pk")])

    name = tables.Column(verbose_name=_("Name"), accessor="tree_name")
    full_name = tables.LinkColumn(
        "rackings:racks:panel",
        args=[tables.utils.A("pk")],  # Pass primary key
        accessor="full_name"  # Refers to the @property method
    )
    warehouse = tables.Column(verbose_name=_("Warehouse"), accessor="warehouse", linkify=True)

    @property
    def create_url(self):
        try:
            return reverse('rackings:racks:create')
        except NoReverseMatch:
            return None

    class Meta:
        model = Rack
        order_by = 'path'
        template_name = "tables/table_htmx.html"
        fields = [
            "rack_type",
            "name",
            "full_name",
            "depth",
            "numchild",
            "warehouse",
        ]
        sequence = [
            "rack_type",
            "name",
            "full_name",
            "depth",
            "numchild",
            "warehouse",
        ]


# class RackDataTables(tables.Table):
#     """Table used on Rack's list page."""

#     name = tables.Column(verbose_name=_("Name"), accessor="tree_name")
#     full_name = tables.Column(verbose_name=_("Full Name"), accessor="full_name", linkify=True)
#     warehouse = tables.Column(verbose_name=_("Warehouse"), accessor="warehouse", linkify=True)
#     actions = tables.TemplateColumn(
#         verbose_name=_("Actions"),
#         orderable=False,
#         template_name="rackings/racks/partials/tables/server_side/_actions.html",
#         attrs={"th": {"class": "text-right"}, "td": {"class": "text-right"}},
#         exclude_from_export=True,
#     )

#     class Meta:
#         model = Rack
#         orderable = False  # Known issue that MP_Node ordering on path is different with DataTables
#         default = DISPLAY_EMPTY_VALUE
#         template_name = "partials/tables/_datatables_server_side.html"
#         attrs = {"class": TABLE_ATTRS_CLASS, "id": "rack_datatables", "style": "display: none;"}
#         row_attrs = {"data-id": lambda record: record.pk}

#         fields = [
#             "rack_type",
#             "name",
#             "full_name",
#             "depth",
#             "numchild",
#             "warehouse",
#         ]
#         sequence = [
#             "rack_type",
#             "name",
#             "full_name",
#             "depth",
#             "numchild",
#             "warehouse",
#             "actions",
#         ]

#     def render_actions(self, column, record: Rack, table, value, bound_column, bound_row) -> Any:
#         """Add permission checking into actions column."""

#         change_perms = self.request.user.has_perm("rackings.change_rack")
#         delete_perms = self.request.user.has_perm("rackings.delete_rack")
#         column.extra_context = {
#             "change_perms": change_perms,
#             "delete_perms": delete_perms,
#         }
#         return column.render(record, table, value, bound_column, **{"bound_row": bound_row})


# class RackDetailDataTables(tables.Table):
#     """Table used on Rack's detail page."""

#     full_name = tables.Column(verbose_name=_("Full Name"), accessor="full_name", order_by="-path", linkify=True)

#     class Meta:
#         model = Rack
#         order_by = "path"  # Must follow the first row display in the table
#         default = DISPLAY_EMPTY_VALUE
#         template_name = "partials/tables/_datatables_server_side.html"
#         attrs = {
#             "class": PARTIAL_LIST_ATTRS_CLASS + " table-wrap",
#             "id": "rack_detail_datatables",
#             "style": "display: none;",
#         }
#         row_attrs = {"data-id": lambda record: record.pk}

#         fields = [
#             "pk",
#             "path",
#             "rack_type",
#             "full_name",
#         ]
#         sequence = [
#             "pk",
#             "path",
#             "rack_type",
#             "full_name",
#         ]

#     def render_pk(self, value, record: Rack) -> str:
#         return f"highlight_id_{value}"

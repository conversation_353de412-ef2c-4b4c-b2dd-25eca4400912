import logging

from wms.cores.actstream import register_stream

from .models import <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, RackTransaction

logger = logging.getLogger(__name__)


#################
# FOR ACTSTREAM #
#################


register_stream(Rack, logger=logger)
register_stream(RackStorage, logger=logger, parent_field="rack")
register_stream(RackTransaction, logger=logger)


###################
# FOR APP SIGNALS #
###################

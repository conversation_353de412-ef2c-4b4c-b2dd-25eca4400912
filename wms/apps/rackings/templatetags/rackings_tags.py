# from django import template

# register = template.Library()


# @register.filter
# def get_racktransaction_physical_balance_by_rack(transaction, rack):
#     """To get the physical balance of transaction based on given rack."""
#     return transaction.get_physical_balance_by_rack(rack)


# @register.filter
# def get_racktransaction_available_balance_by_rack(transaction, rack):
#     """To get the balance of transaction based on given rack."""
#     return transaction.get_balance_by_rack(rack)

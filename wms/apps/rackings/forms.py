# from decimal import Decimal
from typing import Any

from django import forms
from django.core.exceptions import ValidationError
from django.db import transaction
from django.db.models import BLANK_CHOICE_DASH
from django.utils.translation import gettext_lazy as _

from treebeard.forms import MoveNodeForm

from wms.cores.mixins import MPNodeFormMixin
from wms.cores.forms.fields import CoreModelForm, CoreCharField, SizedFormField, FormFieldSize, CoreChoiceField

# from wms.cores.utils import get_all_leaf_rack_choices

# from wms.apps.rackings.imports.racks import ImportRacksExcel

from .models import Rack, RackStorage, RackTransaction


class RackForm(MPNodeFormMixin, MoveNodeForm, CoreModelForm):
    """ModelForm for Rack model."""

    # _ref_node_id = CoreCharField(label=_("Relative to"), widget=forms.Select())

    class Meta:
        model = Rack
        fields = [
            "name",
            "warehouse",
            "rack_type",
        ]

    # def __init__(self, *args, **kwargs):
    #     super().__init__(*args, **kwargs)

    #     self.fields["_ref_node_id"].widget.attrs["class"] = "form-control core-select2 relative-to"

    @transaction.atomic
    def clean(self) -> dict[str, Any]:
        """
        Function to perform some pre-processing/validation right before directly saving the form values
        into DB via transaction.atomic feature.

        Explanation: In model's def clean() & def save(), there's no way to find out whether the incoming
        data is a root node, because the MPNode object hasn't been created.

        I.e.: In model's def clean()
        -> self.path fields will always return empty string instead of "00010001".
        -> self.is_root() will always return False even if submitted request sets Rack to root.

        Similarly, there's no way to gracefully return the proper ValidationError from model's def save() or
        even signal's pre_save() method, at max it will just return a yellow error page in local (server error 500).

        Hence this is the only way to determine if request submitted is at root node and do some validation
        prior to saving into model/DB.
        """

        cleaned_data = super().clean()

        # Set a savepoint prior to create a Rack object & save the form
        savepoint = transaction.savepoint()
        rack = self.save()

        # Rule 1: Ensure that updated root's node warehouse is reflected throughout all of its descendant nodes
        if rack.is_root():
            for descendant in rack.get_descendants():
                descendant.warehouse = cleaned_data["warehouse"]
                descendant.save(update_fields=["warehouse"])

        else:
            # Rule 2: Ensure that root's node warehouse is same with current node's warehouse
            if rack.get_root().warehouse != cleaned_data["warehouse"]:
                root_node_warehouse = rack.get_root().warehouse or BLANK_CHOICE_DASH[0][1]
                error_msg = ValidationError(
                    _(
                        f"This warehouse is different from warehouse ({root_node_warehouse}) at the "
                        f"topmost Floor ({rack.get_root().full_name})"
                    )
                )
                self.add_error("warehouse", error_msg)

        # Rule here is simple, as long as there's errors in form fields, we don't save the Rack object into DB
        if self.errors:
            transaction.savepoint_rollback(savepoint)
        else:
            transaction.savepoint_commit(savepoint)

        return cleaned_data


# class FilterRackForm(CoreModelForm):
#     """ModelForm for Rack model that use in Warehouse's rack detail view sie menu."""

#     class Meta:
#         model = Rack
#         fields = [
#             "full_name",
#             "rack_type",
#         ]

#     def __init__(self, *args, **kwargs):
#         super().__init__(*args, **kwargs)

#         # Add a blank choice at the top of the choices
#         self.fields["rack_type"].choices = [("", EMPTY_LABEL)] + list(self.fields["rack_type"].choices)
#         self.fields["rack_type"].initial = ""
#         self.fields["rack_type"].widget.attrs.update({"class": "form-control datatables-select2"})


# class RackImportExcelForm(CoreForm):
#     """Form to import Rack(s) via Excel."""

#     upload_excel_file = forms.FileField(label=_("Upload Excel file"), required=True)

#     def clean_upload_excel_file(self):
#         data = self.cleaned_data["upload_excel_file"]

#         self.imported_object = ImportRacksExcel(data=data)

#         if self.imported_object.is_valid():
#             # To build cleaned data in import class
#             self.imported_object.cleaned()
#             self.imported_object.process_create_racks()

#         return data


# class RackTransactionForm(CoreModelForm):
#     """ModelForm for RackTransaction model."""

#     class Meta:
#         model = RackTransaction
#         fields = [
#             "transaction_datetime",
#             "rackstorage",
#             "quantity",
#             "remark",
#             "created_by",
#         ]
#         widgets = {
#             "remark": forms.Textarea(attrs={"cols": 120, "rows": 2}),
#             "rackstorage": forms.HiddenInput(),
#             "created_by": forms.HiddenInput(),
#         }

#     def clean(self) -> dict[str, Any]:
#         """Override parent's class clean() method for validating StockCard quantity."""

#         cleaned_data = super().clean()
#         quantity = cleaned_data.get("quantity", None)

#         # Validate input quantity
#         if quantity is not None and quantity <= Decimal("0"):
#             msg = _("The input Quantity should not be lesser than or equals to zero.")
#             self.add_error("quantity", msg)

#         # Validation for stock out
#         rackstorage = cleaned_data.get("rackstorage", None)
#         stock_type = self.request.POST.get("stock_type", "")
#         insufficient_balance = False

#         rack_balance, created = RackBalance.objects.get_or_create(
#             rack=rackstorage.rack,
#             stock=rackstorage.stock,
#         )
#         insufficient_balance = quantity > rack_balance.balance

#         if stock_type == "stock_out":
#             if insufficient_balance:
#                 msg = _(f"Cannot STOCK OUT - Insufficient stock quantity: {rack_balance.balance}")
#                 self.add_error("quantity", msg)

#         return cleaned_data

#     def save(self, commit=True) -> Any:
#         """Override ModelForm's save() method for pre-processing before saving the instance."""

#         if self.errors:
#             raise ValueError(
#                 "The %s could not be %s because the data didn't validate."
#                 % (
#                     self.instance._meta.object_name,
#                     "created" if self.instance._state.adding else "changed",
#                 )
#             )

#         if self.request.POST:
#             stock_type = self.request.POST.get("stock_type", "")
#             quantity = Decimal(self.request.POST.get("quantity", Decimal("0")))

#             # for differentiating StockIn vs StockOut quantity
#             if stock_type == "stock_out":
#                 quantity *= -1
#                 self.instance.quantity = quantity

#         if commit:
#             self.instance.save()

#         return self.instance


# class RackTransactionStockForm(CoreModelForm):
#     """ModelForm for RackTransaction model."""

#     item = forms.CharField(widget=forms.Select())
#     consignor_pk = forms.CharField(required=False, widget=forms.HiddenInput())

#     class Meta:
#         model = RackTransaction
#         fields = [
#             "transaction_datetime",
#             "rackstorage",
#             "quantity",
#             "remark",
#             "created_by",
#         ]
#         widgets = {
#             "remark": forms.Textarea(attrs={"cols": 120, "rows": 2}),
#             "rackstorage": forms.HiddenInput(),
#             "created_by": forms.HiddenInput(),
#         }

#     def clean(self) -> dict[str, Any]:
#         """Override parent's class clean() method for validating StockCard quantity."""

#         cleaned_data = super().clean()
#         quantity = cleaned_data.get("quantity", None)

#         # Validate input quantity
#         if quantity is not None and quantity <= Decimal("0"):
#             msg = _("The input Quantity should not be lesser than or equals to zero.")
#             self.add_error("quantity", msg)

#         # Validation for stock out
#         rackstorage = cleaned_data.get("rackstorage", None)
#         stock_type = self.request.POST.get("stock_type", "")
#         insufficient_balance = False

#         rack_balance, created = RackBalance.objects.get_or_create(
#             rack=rackstorage.rack,
#             stock=rackstorage.stock,
#         )
#         insufficient_balance = quantity > rack_balance.balance

#         if stock_type == "stock_out":
#             if insufficient_balance:
#                 msg = _(f"Cannot STOCK OUT - Insufficient stock quantity: {rack_balance.balance}")
#                 self.add_error("quantity", msg)

#         return cleaned_data

#     def save(self, commit=True) -> Any:
#         """Override ModelForm's save() method for pre-processing before saving the instance."""

#         if self.errors:
#             raise ValueError(
#                 "The %s could not be %s because the data didn't validate."
#                 % (
#                     self.instance._meta.object_name,
#                     "created" if self.instance._state.adding else "changed",
#                 )
#             )

#         if self.request.POST:
#             stock_type = self.request.POST.get("stock_type", "")
#             quantity = Decimal(self.request.POST.get("quantity", Decimal("0")))

#             # for differentiating StockIn vs StockOut quantity
#             if stock_type == "stock_out":
#                 quantity *= -1
#                 self.instance.quantity = quantity

#         if commit:
#             self.instance.save()

#         return self.instance


# class RackTransactionTransferForm(CoreModelForm):
#     """"""

#     transfer_from = forms.ModelChoiceField(queryset=RackStorage.objects.all(), initial=0, disabled=True)
#     transfer_to_rack = forms.ChoiceField(label=_("Transfer to"), choices=[])

#     class Meta:
#         model = RackTransaction
#         fields = [
#             "type",
#             "transaction_datetime",
#             "quantity",
#             "remark",
#             "created_by",
#         ]
#         widgets = {
#             "type": forms.HiddenInput(),
#             "remark": forms.Textarea(attrs={"cols": 120, "rows": 2}),
#             "created_by": forms.HiddenInput(),
#         }

#     def __init__(self, *args, **kwargs):
#         super().__init__(*args, **kwargs)

#         self.fields["transfer_from"].label_from_instance = lambda obj: obj.rack.full_name

#         rackstorage_obj = self.initial.get("transfer_from", None)
#         if rackstorage_obj:
#             self.fields["transfer_from"].queryset = RackStorage.objects.filter(pk=rackstorage_obj.pk)

#         # Override choices
#         self.fields["transfer_to_rack"].choices = get_all_leaf_rack_choices()

#     def clean(self) -> dict[str, Any]:
#         """Override parent's class clean() method for validating StockCard quantity."""

#         cleaned_data = super().clean()
#         quantity = cleaned_data.get("quantity", None)

#         # Validate input quantity
#         if quantity is not None and quantity <= Decimal("0"):
#             msg = _("The input Quantity should not be lesser than or equals to zero.")
#             self.add_error("quantity", msg)

#         # Validate whether transfer_from rack has sufficient balance
#         transfer_from = cleaned_data.get("transfer_from", None)
#         rack_balance, created = RackBalance.objects.get_or_create(
#             rack=transfer_from.rack,
#             stock=transfer_from.stock,
#         )
#         if quantity > rack_balance.balance:
#             msg = _(f"Cannot TRANSFER - Insufficient stock quantity: {rack_balance.balance}")
#             self.add_error("quantity", msg)

#         return cleaned_data

#     def save(self, commit=True) -> Any:
#         """Override ModelForm's save() method for pre-processing before saving the instance."""
#         if self.errors:
#             raise ValueError(
#                 "The %s could not be %s because the data didn't validate."
#                 % (
#                     self.instance._meta.object_name,
#                     "created" if self.instance._state.adding else "changed",
#                 )
#             )

#         if commit:
#             destination_rack_storage, created = RackStorage.objects.get_or_create(
#                 rack_id=self.cleaned_data["transfer_to_rack"],
#                 stock_id=self.cleaned_data["transfer_from"].stock_id,
#             )

#             # Perform Stock Out on current rackstorage
#             self.instance.rackstorage = self.cleaned_data["transfer_from"]
#             self.instance.quantity *= -1
#             self.instance.save()

#             # Perform Stock In on destination rackstorage
#             current_rack = self.instance.rackstorage.rack.full_name
#             current_rack_url = self.instance.get_stock_rack_transaction_url()
#             destination_instance = RackTransaction.objects.create(
#                 type=RackTransaction.Type.TRANSFER,
#                 transaction_datetime=self.cleaned_data["transaction_datetime"],
#                 rackstorage_id=destination_rack_storage.pk,
#                 quantity=self.cleaned_data["quantity"],
#                 remark=self.cleaned_data["remark"] + f" [From <a href='{current_rack_url}'>{current_rack}</a>]",
#                 created_by=self.cleaned_data["created_by"],
#             )

#             # Update current instance's remark with destination rack's URL
#             destination_rack = destination_instance.rackstorage.rack.full_name
#             destination_rack_url = destination_instance.get_stock_rack_transaction_url()
#             self.instance.remark = (
#                 self.instance.remark + f" [To <a href='{destination_rack_url}'>{destination_rack}</a>]"
#             )
#             RackTransaction.objects.bulk_update([self.instance], ["remark"])

#         return self.instance


# ############
# # FOR HTMX #
# ############


# class RackInfoUpdateForm(RackForm, CoreHtmxModelForm):
#     """HTMX form to update Rack's basic info."""

#     pass

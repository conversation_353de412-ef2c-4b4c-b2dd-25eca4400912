from django.apps import AppConfig
from django.utils.translation import gettext_lazy as _


class RackingsConfig(AppConfig):
    """Config class for Rackings app."""

    name = "wms.apps.rackings"
    verbose_name = _("Rackings")

    def ready(self):
        """Enabled signals and actstream."""

        from actstream import registry

        import wms.apps.rackings.signals  # noqa F401
        from wms.apps.rackings.models import Rack, RackStorage, RackTransaction

        registry.register(
            Ra<PERSON>,
            RackStorage,
            RackTransaction,
        )

        # To prevent related activity stream being removed when object is deleted.
        def not_target_actions(field):
            return field.name not in ["target_actions", "action_object_actions"]

        # Racking apps
        Rack._meta.private_fields = list(filter(not_target_actions, Rack._meta.private_fields))
        RackStorage._meta.private_fields = list(filter(not_target_actions, RackStorage._meta.private_fields))
        RackTransaction._meta.private_fields = list(filter(not_target_actions, RackTransaction._meta.private_fields))

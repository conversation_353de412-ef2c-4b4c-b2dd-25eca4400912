from rest_framework import serializers
from django.db.models import Q
from wms.apps.rackings.models import RackStorage


class RackStorageSerializer(serializers.ModelSerializer):
    """
    Serializer for rackings model only.
    """
    id = serializers.SerializerMethodField()
    # rack = serializers.SerializerMethodField()
    # stock = serializers.SerializerMethodField()
    rackstorage = serializers.SerializerMethodField()
    # uom = serializers.CharField(source='uom.symbol', read_only=True)
    # uom_id = serializers.IntegerField(source='uom.id')

    class Meta:
        model = RackStorage
        fields = [
            'id',
            # 'rack',
            # 'stock',
            'rackstorage'
        ]

    def get_id(self, obj):
        """Return the item ID instead of the stock ID"""
        return obj.id

    # def get_rack(self, obj):
    #     """Return item rack and code in format: "[code] rack" """
    #     return f"[{obj.rack}]"

    # def get_stock(self, obj):
    #     """Return item rack and code in format: "[code] rack" """
    #     return f"[{obj.stock}]"

    def get_rackstorage(self, obj):
        """Return item rack and code in format: "[code] rack" """
        return f"[{obj.rack}] {obj.stock}"

# from collections import defaultdict

from rest_framework import viewsets, filters
# from rest_framework.decorators import action
# from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
# from django.shortcuts import get_object_or_404
from wms.cores.views import Select2Pagination

from wms.apps.rackings.models import RackStorage
from .serializers import RackStorageSerializer


class RackStorageViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing RackStorage objects in the Warehouse Management System.
    """
    pagination_class = Select2Pagination
    queryset = RackStorage.objects.all().select_related('rack', 'stock')
    serializer_class = RackStorageSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['rack__full_name', 'stock__item__name', 'stock__item__code']
    search_fields = ['rack__full_name', 'stock__item__name', 'stock__item__code']
    ordering_fields = ['rack__full_name', 'stock__item__code']
    ordering = ['rack__full_name']

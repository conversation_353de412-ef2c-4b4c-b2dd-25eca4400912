# from decimal import Decimal

# from django.db.models import DecimalField, OuterRef, QuerySet, Subquery, Value
# from django.db.models.functions import Coalesce

# from wss.apps.inventories.models import Stock
# from wss.apps.rackings.models import Rack<PERSON>alance, RackStorage, RackTransaction
# from wss.apps.releases.models import WarehouseReleaseOrder, WarehouseReleaseOrderItem
# from wss.apps.settings.models import Warehouse


# def _calculate_reserved_quantity(
#     quantity: float | Decimal, available_balance_dict: dict, reserved_quantity_dict: dict
# ) -> dict:
#     """
#     Calculate and return a dictionary containing the final quantity to be reserved for each RackStorage.
#     The result will be used to create or update the RackTransaction object for the reservation.
#     """
#     remaining_quantity = quantity
#     total_available_balance = sum(available_balance_dict.values())
#     rack_storage_reserved_quantity = reserved_quantity_dict

#     # Allocate all available balance if it is less than or just enough to fulfil the quantity requested
#     if total_available_balance <= remaining_quantity:
#         rack_storage_reserved_quantity.update(
#             {k: v + rack_storage_reserved_quantity.get(k, 0) for k, v in available_balance_dict.items()}
#         )
#         return rack_storage_reserved_quantity

#     # Check if there's any available balance left on RackStorage where stock has already been reserved prior
#     if reserved_quantity_dict:
#         for rack_storage, available_balance in available_balance_dict.items():
#             if remaining_quantity <= 0:
#                 break

#             if rack_storage in reserved_quantity_dict:
#                 if available_balance >= remaining_quantity:
#                     quantity_to_be_deducted = remaining_quantity
#                 else:
#                     quantity_to_be_deducted = available_balance

#                 available_balance_dict[rack_storage] -= quantity_to_be_deducted
#                 rack_storage_reserved_quantity[rack_storage] += quantity_to_be_deducted
#                 remaining_quantity -= quantity_to_be_deducted

#         # Filter out the RackStorage with 0 available balance
#         available_balance_dict = {k: v for k, v in available_balance_dict.items() if v > 0}

#     if remaining_quantity > 0:
#         # Currently the following only does reservation based on Rack sequence.
#         # TODO: Improve RackStorage allocation algorithm
#         for rack_storage, available_balance in available_balance_dict.items():
#             if remaining_quantity <= 0:
#                 break

#             if available_balance >= remaining_quantity:
#                 rack_storage_reserved_quantity[rack_storage] = remaining_quantity
#                 break

#             rack_storage_reserved_quantity[rack_storage] = available_balance
#             remaining_quantity -= available_balance

#     return rack_storage_reserved_quantity


# def get_available_rack_storages(warehouse: Warehouse, stock: Stock) -> QuerySet[RackStorage]:
#     """
#     Retrieve all RackStorage objects that contains the specified Stock and has balanace available for reservation.
#     """
#     available_balance_subquery = Subquery(
#         RackBalance.objects.filter(rack=OuterRef("rack"), stock=OuterRef("stock")).values("balance")[:1],
#         output_field=DecimalField(),
#     )
#     rack_storage_qs = (
#         RackStorage.objects.select_related("rack", "stock")
#         .filter(rack__warehouse=warehouse, stock=stock)
#         .annotate(available_balance=Coalesce(available_balance_subquery, Value(Decimal("0"))))
#         .filter(available_balance__gt=0)
#     )

#     return rack_storage_qs


# def reserve_release_order_item(release_order_item: WarehouseReleaseOrderItem) -> None:
#     """
#     Reserve Stock on available Racks for the specified WarehouseReleaseOrderItem.
#     """
#     release_order = release_order_item.release_order
#     quantity = release_order_item.quantity
#     reserved_quantity_dict = {}

#     # Retrieve existing reserved transactions
#     reserved_rack_transaction_qs = RackTransaction.objects.filter(
#         type=RackTransaction.Type.RESERVED,
#         warehouse_release_order=release_order,
#         warehouse_release_order_item=release_order_item,
#     )
#     if reserved_rack_transaction_qs.exists():
#         reserved_quantity_dict = {
#             reserved.rackstorage.pk: abs(reserved.quantity) for reserved in reserved_rack_transaction_qs
#         }
#         total_reserved_quantity = sum(reserved_quantity_dict.values())
#         if total_reserved_quantity >= quantity:
#             return
#         # Update value to the remaining quantity that needs to be reserved
#         quantity -= total_reserved_quantity

#     # Retrieve the first Warehouse from the Release Order
#     warehouse = release_order.warehouses.first()
#     stock = Stock.objects.filter(
#         warehouse=warehouse,
#         item=release_order_item.item,
#         batch_no=release_order_item.batch_no,
#         expiry_date=release_order_item.expiry_date,
#         item__consignor=release_order.consignee.consignor,
#     ).first()
#     # Calculate the balance available for reservation for each RackStorage
#     rack_storage_qs = get_available_rack_storages(warehouse, stock)
#     available_balance_dict = {rack_storage.pk: rack_storage.available_balance for rack_storage in rack_storage_qs}
#     total_available_balance = sum(available_balance_dict.values())

#     # The following we calculate whether our available balance throughout all RackStorage is sufficient to fulfil the
#     # quantity requested. If not, we will perform partial reservation (allocate all available balance)
#     # and set the reservation status of the WRO item to QUEUED.
#     final_reserved_quantity_dict = _calculate_reserved_quantity(
#         quantity, available_balance_dict, reserved_quantity_dict
#     )
#     # Also removes item from incomplete reservation queue once the WRO is obsolete or fully picked (manual picking)
#     if (
#         total_available_balance >= quantity
#         or release_order_item.release_order.status == WarehouseReleaseOrder.Status.OBSOLETE
#         or release_order_item.total_picker_system_quantity >= release_order_item.quantity
#     ):
#         release_order_item.reservation_status = WarehouseReleaseOrderItem.ReservationStatus.RESERVED_COMPLETE
#     else:
#         release_order_item.reservation_status = WarehouseReleaseOrderItem.ReservationStatus.RESERVED_INCOMPLETE
#     release_order_item.save(skip_reserve=True, update_fields=["reservation_status"])

#     for rack_storage_pk, reserved_quantity in final_reserved_quantity_dict.items():
#         RackTransaction.objects.update_or_create(
#             type=RackTransaction.Type.RESERVED,
#             rackstorage_id=rack_storage_pk,
#             warehouse_release_order=release_order,
#             warehouse_release_order_item=release_order_item,
#             defaults={"quantity": -reserved_quantity},
#         )


# def fulfil_incomplete_reservations(
#     warehouse: Warehouse,
#     stock: Stock,
#     quantity: float | Decimal,
# ) -> None:
#     """
#     Check and reserve Stock for WarehouseReleaseOrderItems that are partially reserved and
#     placed in the incomplete reservation queue.
#     """
#     release_order_item_qs = WarehouseReleaseOrderItem.objects.filter(
#         reservation_status=WarehouseReleaseOrderItem.ReservationStatus.RESERVED_INCOMPLETE,
#         release_order__warehouses=warehouse,
#         item=stock.item,
#         batch_no=stock.batch_no,
#         expiry_date=stock.expiry_date,
#     ).order_by("created")

#     if not release_order_item_qs.exists():
#         return

#     remaining_quantity = quantity
#     for release_order_item in release_order_item_qs:
#         # Determine whether Stock In quantity is sufficient to fulfil the quantity pending to be reserved
#         if remaining_quantity <= 0:
#             break

#         pending_quantity = release_order_item.quantity - release_order_item.total_reserved_quantity
#         remaining_quantity -= pending_quantity

#         reserve_release_order_item(release_order_item)

# from django.db.models import Q, QuerySet

from wms.cores.utils import get_warehouse_choices

from django.db.models import Q, Sum, OuterRef, Subquery, Value, DecimalField
from django.db.models.functions import Coalesce
from django.utils.translation import gettext_lazy as _
from django_filters import filters, FilterSet, ChoiceFilter

from wms.cores.forms.fields import FormFieldSize
from wms.cores.forms.widget import CoreSelectWidget, CoreSelectMultipleWidget, CoreTextWidget

from wms.apps.settings.models import Warehouse
from wms.apps.rackings.models import Rack


class RackFilter(FilterSet):
    """
    Filter class for Rack list view.
    Provides filtering capabilities for Rack attributes.
    """

    rack_type = filters.MultipleChoiceFilter(
        choices=Rack.RackType,
        label="Rack Type",
        widget=CoreSelectMultipleWidget()
    )

    warehouse = filters.MultipleChoiceFilter(
        choices=[],  # Will be populated dynamically
        label="Warehouse",
        widget=CoreSelectMultipleWidget()
    )


    class Meta:
        model = Rack
        fields = ['rack_type', "warehouse"]

    def __init__(self, data=None, queryset=None, *, request=None, prefix=None):
        super().__init__(data=data, queryset=queryset, request=request, prefix=prefix)

        # Dynamically populate location choices from existing data
        if queryset is not None:
            # Update field choices after initialization
            self.form.fields['warehouse'].choices = get_warehouse_choices()


class RackDataTableFilter(FilterSet):
    """
    Filter class for Rack list view.
    Provides filtering capabilities for Rack attributes.

    Widget attrs:
        'data-auto-submit' only work with filter_auto.html
    """

    rack_type = filters.MultipleChoiceFilter(
        choices=Rack.RackType,  # Will be populated dynamically
        # field_name='consignor__code',
        label="Rack Type",
        widget=CoreSelectMultipleWidget(attrs={
            'data-auto-submit': 'true'
        })
    )

    class Meta:
        model = Rack
        fields = ['rack_type']


# class RackFilter(filters.FilterSet):
#     """Filterset class for Rack model."""

#     search_keyword = filters.CharFilter(
#         label=_("Full Name contains"),
#         method="custom_keyword_filter",
#     )

#     class Meta:
#         model = Rack
#         fields = [
#             "rack_type",
#             "numchild",
#         ]

#     def __init__(self, data=None, queryset=None, *, request=None, prefix=None):
#         super().__init__(data=data, queryset=queryset, request=request, prefix=prefix)

#         self.filters["rack_type"].field.widget.attrs.update({"class": "form-control core-select2"})
#         self.filters["search_keyword"].field.widget.attrs.update({"class": "form-control"})

#     def custom_keyword_filter(self, queryset: QuerySet[Rack], name: str, value: str) -> QuerySet[Rack]:
#         qs = queryset

#         for keyword in value.split():
#             qs = qs.filter(Q(full_name__icontains=keyword))

#         return qs.distinct()

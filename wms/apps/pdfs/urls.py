from django.urls import include, path

from wms.apps.pdfs.views import (
    arrival_notice_pdf_view,
    count_sheet_detail_pdf_view,
    count_sheet_index_pdf_view,
    do_pdf_view,
    wro_pdf_view,
    wro_print_info_view,
)

app_name = "pdfs"


receives_pdf_urlpatterns = [
    # example: http://localhost:8000/pdfs/receives/arrival-notice/3000/
    path("arrival-notice/<int:pk>/", view=arrival_notice_pdf_view, name="arrival_notice_pdf"),
]

releases_pdf_urlpatterns = [
    # example: http://localhost:8000/pdfs/releases/do/55783/
    path("do/<int:pk>/", view=do_pdf_view, name="do_pdf"),
    # example: http://localhost:8000/pdfs/releases/wro/55783/
    path("wro/<int:pk>/", view=wro_pdf_view, name="wro_pdf"),
    # example[WITH COPY]: http://localhost:8000/pdfs/releases/print-info/54815/
    # example[WITHOUT COPY]: http://localhost:8000/pdfs/releases/print-info/55796/
    path("print-info/<int:pk>/", view=wro_print_info_view, name="print_info"),
]

reports_pdf_urlpatterns = [
    # example: http://localhost:8000/pdfs/reports/count_sheet/10/
    path("count_sheet/<int:pk>/", view=count_sheet_detail_pdf_view, name="count_sheet_detail_pdf"),
    # example: http://localhost:8000/pdfs/reports/count_sheet/index/
    path("count_sheet/index/", view=count_sheet_index_pdf_view, name="count_sheet_index_pdf"),
]

urlpatterns = [
    path(
        "receives/",
        include((receives_pdf_urlpatterns, "pdfs.receives"), namespace="receives"),
    ),
    path(
        "releases/",
        include((releases_pdf_urlpatterns, "pdfs.releases"), namespace="releases"),
    ),
    path(
        "reports/",
        include((reports_pdf_urlpatterns, "pdfs.reports"), namespace="reports"),
    ),
]

from django_weasyprint import WeasyTemplateResponseMixin

from wms.cores.utils import localtime_now
from wms.cores.views import CoreDetailView

from wms.apps.receives.models import GoodsReceivedNote


class ArrivalNoticePDFView(WeasyTemplateResponseMixin, CoreDetailView):
    """Function to generate PDF for Warehouse Arrival Notice."""

    template_name = "pdfs/receives/arrival_notice.html"
    model = GoodsReceivedNote

    pdf_attachment = False

    def get_pdf_filename(self):
        return f"{self.object.system_number}-{localtime_now().strftime('%Y%m%d-%H%M')}.pdf"


arrival_notice_pdf_view = ArrivalNoticePDFView.as_view()

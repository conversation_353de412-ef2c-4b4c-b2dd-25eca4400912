from django_weasyprint import WeasyTemplateResponseMixin

from wms.cores.utils import localtime_now
from wms.cores.views import CoreDetailView, CoreFormView, CoreTemplateView

from wms.apps.releases.models import WarehouseReleaseOrder


class DoPDFView(WeasyTemplateResponseMixin, CoreDetailView):
    """Function to generate PDF for DO."""

    template_name = "pdfs/releases/do.html"
    model = WarehouseReleaseOrder

    permission_required = "releases.view_warehousereleaseorder"

    pdf_attachment = False

    def get_pdf_filename(self):
        return f"{self.object.system_number}-{localtime_now().strftime('%Y%m%d-%H%M')}.pdf"


do_pdf_view = DoPDFView.as_view()


class WroPDFView(WeasyTemplateResponseMixin, CoreDetailView):
    """Function to generate PDF for WRO."""

    template_name = "pdfs/releases/wro.html"
    model = WarehouseReleaseOrder

    permission_required = "releases.view_warehousereleaseorder"

    pdf_attachment = False

    def get_pdf_filename(self):
        return f"{self.object.system_number}-{localtime_now().strftime('%Y%m%d-%H%M')}.pdf"


wro_pdf_view = WroPDFView.as_view()


class WarehouseReleaseOrderPrintInfoView(
    WeasyTemplateResponseMixin, CoreDetailView
):
    """Partial page to print info and all WRO item in A4 paper with selected WarehouseReleaseOrder's pk detail view."""

    model = WarehouseReleaseOrder
    template_name = "pdfs/releases/printinfo.html"

    permission_required = "releases.view_warehousereleaseorder"

    pdf_attachment = False

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        warehouse_release_order = self.get_object()
        context["wro_items"] = warehouse_release_order.warehouse_release_order_items.all()
        return context


wro_print_info_view = WarehouseReleaseOrderPrintInfoView.as_view()

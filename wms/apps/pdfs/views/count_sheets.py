from django.db.models import Q, QuerySet

from django_weasyprint import WeasyTemplateResponseMixin

from wms.cores.utils import localtime_now
from wms.cores.views import CoreDetailView, CoreListView

from wms.apps.count_sheets.models import CountSheet


class CountSheetDetailPDFView(WeasyTemplateResponseMixin, CoreDetailView):
    """Function to generate PDF for CountSheet based on its doc_id."""

    template_name = "pdfs/reports/count_sheet_detail.html"
    model = CountSheet

    pdf_attachment = False

    def get_pdf_filename(self) -> str:
        return f"{self.object.doc_id}-{localtime_now().strftime('%Y%m%d-%H%M')}.pdf"


count_sheet_detail_pdf_view = CountSheetDetailPDFView.as_view()


class CountSheetIndexPDFView(WeasyTemplateResponseMixin, CoreListView):
    """Function to generate PDF for CountSheet Index."""

    template_name = "pdfs/reports/count_sheet_index.html"
    model = CountSheet

    pdf_attachment = False

    def get_pdf_filename(self) -> str:
        return f"CountSheetIndex-{localtime_now().strftime('%Y%m%d-%H%M')}.pdf"

    def get_queryset(self) -> QuerySet[CountSheet]:
        qs = self.model.objects.all().order_by("doc_id")

        issued_by_pk = self.request.GET.get("issued_by", None)
        if issued_by_pk not in [None, ""]:
            qs = qs.filter(issued_by__pk=issued_by_pk)

        floor_chamber_rack_list = self.request.GET.getlist("floor_chamber_rack", [])
        if len(floor_chamber_rack_list) > 0:
            qs = qs.filter(Q(floor_chamber_rack__in=floor_chamber_rack_list))

        return qs


count_sheet_index_pdf_view = CountSheetIndexPDFView.as_view()

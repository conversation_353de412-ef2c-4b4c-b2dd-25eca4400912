from django.utils.translation import gettext_lazy as _

import django_tables2 as tables
from django.urls import reverse, NoReverseMatch

# from wss.cores.models import DISPLAY_EMPTY_VALUE
# from wss.cores.tables import PARTIAL_LIST_ATTRS_CLASS, TABLE_ATTRS_CLASS, CoreBooleanColumn

from ..models import Warehouse
from wms.cores.columns import HTMXColumn


class WarehouseDetailTable(tables.Table):
    name = HTMXColumn(
        url_name="settings:warehouses:detail_home",
        target_id="detail-panel",
        verbose_name=_("Name"),
        push_url=True,
        push_url_name="settings:warehouses:panel",
    )

    class Meta:
        model = Warehouse
        fields = ("name",)
        order_by = 'name'
        template_name = "tables/table_htmx.html"

    def __init__(self, *args, object_pk=None, **kwargs):
        super().__init__(*args, **kwargs)
        self.object_pk = object_pk


class WarehouseTable(tables.Table):
    section_title = "Warehouse Lists"
    section_name = "Warehouse"
    selectable = True

    # name = tables.LinkColumn("settings:warehouses:panel", args=[tables.utils.A("pk")])

    full_name = tables.LinkColumn(
        "settings:warehouses:panel",
        args=[tables.utils.A("pk")],  # Pass primary key
        accessor="full_name"  # Refers to the @property method
    )

    @property
    def create_url(self):
        try:
            return reverse('settings:warehouses:create')
        except NoReverseMatch:
            return None

    class Meta:
        model = Warehouse
        order_by = 'path'
        template_name = "tables/table_htmx.html"
        fields = (
            "full_name",
            "is_storage",
            "depth",
            "numchild",
        )

    # def render_name(self, record):
    #     return record.full_name  # Calls the property method


# class WarehouseTable(tables.Table):
#     """Table used on warehouse list page."""

#     full_name = tables.Column(verbose_name=_("Name"), linkify=True)
#     is_storage = CoreBooleanColumn()
#     actions = tables.TemplateColumn(
#         verbose_name=_("Actions"),
#         orderable=False,
#         template_name="settings/warehouses/partials/tables/_actions.html",
#         attrs={"th": {"class": "text-right"}, "td": {"class": "text-right"}},
#         exclude_from_export=True,
#     )

#     class Meta:
#         model = Warehouse
#         orderable = False  # Known issue that MP_Node ordering on path is different with DataTables
#         default = DISPLAY_EMPTY_VALUE
#         template_name = "partials/tables/_datatables.html"
#         attrs = {"class": TABLE_ATTRS_CLASS, "id": "warehouse_list_table", "style": "display: none;"}
#         row_attrs = {"data-id": lambda record: record.pk}

#         fields = [
#             "full_name",
#             "is_storage",
#             "depth",
#             "numchild",
#         ]
#         sequence = [
#             "full_name",
#             "is_storage",
#             "depth",
#             "numchild",
#             "actions",
#         ]


# class WarehousePartialListTable(tables.Table):
#     """Table used on warehouse detail page."""

#     full_name = tables.Column(verbose_name=_("Name"), linkify=True)

#     class Meta:
#         model = Warehouse
#         order_by = "path"
#         default = DISPLAY_EMPTY_VALUE
#         template_name = "partials/tables/_datatables.html"
#         attrs = {"class": PARTIAL_LIST_ATTRS_CLASS, "id": "warehouse_list_table", "style": "display: none;"}
#         row_attrs = {"data-id": lambda record: record.pk}

#         fields = [
#             "path",
#             "full_name",
#         ]

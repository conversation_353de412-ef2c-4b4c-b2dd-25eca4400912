from django.utils.translation import gettext_lazy as _

import django_tables2 as tables
from django.urls import reverse, NoReverseMatch

# from wms.cores.models import DISPLAY_EMPTY_VALUE
# from wms.cores.tables import PARTIAL_LIST_ATTRS_CLASS, TABLE_ATTRS_CLASS, CoreBooleanColumn

from ..models import UnitConversion, UnitOfMeasure
from wms.cores.columns import HTMXColumn


#################################################
# UnitOfMeasure
#################################################

class UnitOfMeasureDetailTable(tables.Table):
    unit_name = HTMXColumn(
        url_name="settings:uoms:detail_home",
        target_id="detail-panel",
        verbose_name=_("Name"),
        push_url=True,
        push_url_name="settings:uoms:panel",
    )

    class Meta:
        model = UnitOfMeasure
        fields = ("unit_name", "symbol")
        order_by = 'unit_name'
        template_name = "tables/table_htmx.html"

    def __init__(self, *args, object_pk=None, **kwargs):
        super().__init__(*args, **kwargs)
        self.object_pk = object_pk


class UnitOfMeasureTable(tables.Table):
    section_title = "UOM Lists"
    section_name = "UOM"
    selectable = True

    unit_name = tables.LinkColumn("settings:uoms:panel", args=[tables.utils.A("pk")])
    # name = tables.LinkColumn("settings:uoms:panel", args=[tables.utils.A("pk")])

    @property
    def create_url(self):
        try:
            return reverse('settings:uoms:create')
        except NoReverseMatch:
            return None

    class Meta:
        model = UnitOfMeasure
        order_by = 'unit_name'
        template_name = "tables/table_htmx.html"
        fields = (
            "unit_name",
            "symbol",
            "unit_precision",
            "in_use",
        )


#################################################
# UnitConversion
#################################################


class UnitConversionDetailTable(tables.Table):
    origin = HTMXColumn(
        url_name="settings:unit_conversions:detail_home",
        target_id="detail-panel",
        verbose_name=_("Origin"),
        push_url=True,
        push_url_name="settings:unit_conversions:panel",
    )

    class Meta:
        model = UnitConversion
        fields = ("origin", "symbol")
        order_by = 'origin'
        template_name = "tables/table_htmx.html"

    def __init__(self, *args, object_pk=None, **kwargs):
        super().__init__(*args, **kwargs)
        self.object_pk = object_pk


class UnitConversionTable(tables.Table):
    section_title = "Unit Conversion Lists"
    section_name = "Unit Conversion"
    selectable = True

    origin = tables.LinkColumn("settings:unit_conversions:panel", args=[tables.utils.A("pk")])
    description = tables.Column(verbose_name=_("Description"), accessor="description")

    @property
    def create_url(self):
        try:
            return reverse('settings:unit_conversions:create')
        except NoReverseMatch:
            return None

    class Meta:
        model = UnitConversion
        order_by = 'origin'
        template_name = "tables/table_htmx.html"
        fields = (
            "origin",
            "target",
            "rate",
            "description",
        )


# class UnitOfMeasureTable(tables.Table):
#     """Table used on uom list page."""

#     unit_name = tables.Column(verbose_name=_("Name"), accessor="unit_name", linkify=True)
#     total_conversion = tables.Column(verbose_name=_("Total Conversion"), accessor="total_number_of_conversion")
#     in_use = CoreBooleanColumn()
#     actions = tables.TemplateColumn(
#         verbose_name=_("Actions"),
#         orderable=False,
#         template_name="settings/unit_of_measures/partials/tables/_actions.html",
#         attrs={"th": {"class": "text-right"}, "td": {"class": "text-right"}},
#         exclude_from_export=True,
#     )

#     class Meta:
#         model = UnitOfMeasure
#         order_by = "unit_name"  # Must follow the first row display in the table
#         default = DISPLAY_EMPTY_VALUE
#         template_name = "partials/tables/_datatables.html"
#         attrs = {"class": TABLE_ATTRS_CLASS, "id": "uom_list_table", "style": "display: none;"}
#         row_attrs = {"data-id": lambda record: record.pk}

#         fields = [
#             "unit_name",
#             "symbol",
#             "unit_precision",
#             "in_use",
#         ]
#         sequence = [
#             "unit_name",
#             "symbol",
#             "unit_precision",
#             "total_conversion",
#             "in_use",
#             "actions",
#         ]

#     def render_actions(self, column, record, table, value, bound_column, bound_row):
#         """Add permission checking into actions column."""
#         change_perms = self.request.user.has_perm("items.change_unit_of_measure") and record.in_use is False
#         delete_perms = self.request.user.has_perm("items.delete_unit_of_measure") and record.in_use is False
#         column.extra_context = {
#             "change_perms": change_perms,
#             "delete_perms": delete_perms,
#         }
#         return column.render(record, table, value, bound_column, **{"bound_row": bound_row})


# class UnitOfMeasurePartialListTable(tables.Table):
#     """Table used on uom detail page."""

#     unit_name = tables.Column(verbose_name=_("Name"), accessor="unit_name", linkify=True)

#     class Meta:
#         model = UnitOfMeasure
#         order_by = "unit_name"  # Must follow the first row display in the table
#         default = DISPLAY_EMPTY_VALUE
#         template_name = "partials/tables/_datatables.html"
#         attrs = {"class": PARTIAL_LIST_ATTRS_CLASS, "id": "uom_list_table", "style": "display: none;"}
#         row_attrs = {"data-id": lambda record: record.pk}

#         fields = [
#             "unit_name",
#             "symbol",
#         ]


# class UnitConversionTable(tables.Table):
#     """Table used on UnitConversion list page."""

#     origin = tables.Column(
#         verbose_name=_("Origin"), accessor="origin", linkify=lambda record: record.get_absolute_url()
#     )
#     conversion_rate = tables.Column(verbose_name=_("Conversion Rate"), accessor="conversion_rate")
#     description = tables.Column(verbose_name=_("Description"), accessor="description")
#     actions = tables.TemplateColumn(
#         verbose_name=_("Actions"),
#         orderable=False,
#         template_name="settings/unit_conversions/partials/tables/_actions.html",
#         attrs={"th": {"class": "text-right"}, "td": {"class": "text-right"}},
#         exclude_from_export=True,
#     )

#     class Meta:
#         model = UnitConversion
#         order_by = "origin"  # Must follow the first row display in the table
#         default = DISPLAY_EMPTY_VALUE
#         template_name = "partials/tables/_datatables.html"
#         attrs = {"class": TABLE_ATTRS_CLASS, "id": "unit_conversion_list_table", "style": "display: none;"}
#         row_attrs = {"data-id": lambda record: record.pk}

#         fields = [
#             "origin",
#             "target",
#             "conversion_rate",
#         ]
#         sequence = [
#             "origin",
#             "target",
#             "conversion_rate",
#             "description",
#             "actions",
#         ]

#     def render_actions(self, column, record, table, value, bound_column, bound_row):
#         """Add permission checking into actions column."""
#         change_perms = (
#             self.request.user.has_perm("items.change_unit_conversion")
#             and record.origin.in_use is False
#             and record.target.in_use is False
#         )
#         delete_perms = (
#             self.request.user.has_perm("items.delete_unit_conversion")
#             and record.origin.in_use is False
#             and record.target.in_use is False
#         )
#         column.extra_context = {
#             "change_perms": change_perms,
#             "delete_perms": delete_perms,
#         }
#         return column.render(record, table, value, bound_column, **{"bound_row": bound_row})


# class UnitConversionPartialListTable(tables.Table):
#     """Table used on UnitConversion detail page."""

#     origin = tables.Column(verbose_name=_("Origin"), accessor="origin__unit_name", linkify=True)
#     description = tables.Column(verbose_name=_("Description"), accessor="description")

#     class Meta:
#         model = UnitConversion
#         order_by = "origin"  # Must follow the first row display in the table
#         default = DISPLAY_EMPTY_VALUE
#         template_name = "partials/tables/_datatables.html"
#         attrs = {"class": PARTIAL_LIST_ATTRS_CLASS, "id": "unit_conversion_list_table", "style": "display: none;"}
#         row_attrs = {"data-id": lambda record: record.pk}

#         fields = [
#             "origin",
#             "description",
#         ]

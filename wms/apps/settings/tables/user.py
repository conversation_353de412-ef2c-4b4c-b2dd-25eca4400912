# from typing import Any

# from django.contrib.auth import get_user_model
# from django.urls import reverse
# from django.utils.translation import gettext_lazy as _

# import django_tables2 as tables

# from wss.cores.models import DISPLAY_EMPTY_VALUE
# from wss.cores.tables import PARTIAL_LIST_ATTRS_CLASS, TABLE_ATTRS_CLASS, CoreBooleanColumn

# User = get_user_model()


# class SinoflexUserDataTables(tables.Table):
#     """Table used on Sinoflex users list page."""

#     email = tables.Column(verbose_name=_("Email"))
#     profile_image = tables.Column(verbose_name=_("Profile Image"))
#     display_name = tables.Column(
#         verbose_name=_("Display Name"),
#         accessor="get_display_name",
#     )
#     get_groups = tables.Column(verbose_name=_("Groups"))
#     is_active = CoreBooleanColumn()
#     actions = tables.TemplateColumn(
#         verbose_name=_("Actions"),
#         orderable=False,
#         template_name="settings/users/partials/tables/_actions.html",
#         attrs={"th": {"class": "text-right"}, "td": {"class": "text-right"}},
#         exclude_from_export=True,
#     )

#     class Meta:
#         model = User
#         order_by = "email"  # Must follow the first row display in the table
#         default = DISPLAY_EMPTY_VALUE
#         template_name = "partials/tables/_datatables_server_side.html"
#         attrs = {"class": TABLE_ATTRS_CLASS, "id": "sinoflex_user_datatables", "style": "display: none;"}
#         row_attrs = {"data-id": lambda record: record.pk}

#         fields = [
#             "email",
#             "profile_image",
#             "display_name",
#             "get_groups",
#             "is_active",
#         ]
#         sequence = [
#             "email",
#             "profile_image",
#             "display_name",
#             "get_groups",
#             "is_active",
#             "actions",
#         ]

#     def render_email(self, value: str, record: User) -> str:
#         link = reverse("settings:users:detail", kwargs={"username": record.username})
#         return f'<a href="{link}">{value}</a>'

#     def render_profile_image(self, value: Any, record: User) -> str:
#         img_style = 'style="height: 70px"'
#         return f'<img src="{value.url}" class="img-circle elevation-2" alt="User Image" {img_style}>'


# class SinoflexUserDetailDataTables(tables.Table):
#     """Table used on Sinoflex User detail page."""

#     pk = tables.Column(verbose_name=_("ID"))  # For highlight in table
#     email = tables.Column(verbose_name=_("Email"))
#     is_active = CoreBooleanColumn()

#     class Meta:
#         model = User
#         order_by = "email"  # Must follow the first row display in the table
#         default = DISPLAY_EMPTY_VALUE
#         template_name = "partials/tables/_datatables_server_side.html"
#         attrs = {"class": PARTIAL_LIST_ATTRS_CLASS, "id": "sinoflex_user_detail_datatables", "style": "display: none;"}
#         row_attrs = {"data-id": lambda record: record.pk}

#         fields = [
#             "pk",
#             "email",
#             "is_active",
#         ]
#         sequence = [
#             "pk",
#             "email",
#             "is_active",
#         ]

#     def render_email(self, value: str, record: User) -> str:
#         link = reverse("settings:users:detail", kwargs={"username": record.username})
#         return f'<a href="{link}">{value}</a>'

#     def render_pk(self, value, record):
#         return f"highlight_id_{value}"

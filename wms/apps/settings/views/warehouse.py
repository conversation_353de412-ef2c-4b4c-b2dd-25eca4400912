# from django.conf import settings
# from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
# from django.core.paginator import Paginator
# from django.db.models import Q
# from django.http import HttpRequest, JsonResponse
# from django.urls import reverse, reverse_lazy
# from django.utils.translation import gettext_lazy as _

# from django_tables2 import SingleTableMixin

# from wss.cores.views import (
#     CoreCreateView,
#     CoreDeleteView,
#     CoreDetailDataTablesView,
#     CoreDetailView,
#     CoreListView,
#     CoreUpdateView,
# )

# from wss.apps.inventories.models import Stock

# from ..forms import WarehouseForm, WarehouseInfoUpdateForm
# from ..models import Warehouse
# from ..tables import WarehousePartialListTable, WarehouseTable


from wms.cores.mixins import ExportTableMixin
from wms.cores.views import CoreSingleTableView, CoreDataTableDetailView, <PERSON><PERSON><PERSON>View, <PERSON>D<PERSON>ilView, CoreUpdateView

from wms.apps.settings.models import Warehouse
from wms.apps.settings.forms import WarehouseForm  # , WarehouseUpdateForm
# from wms.apps.settings.filters import WarehouseDataTableFilter
from wms.apps.settings.tables import WarehouseTable, WarehouseDetailTable


class WarehouseListView(ExportTableMixin, CoreSingleTableView):
    """
    View for displaying and managing the list of Warehouses.
    Supports:
    - Pagination
    - Column visibility
    - Searching
    - Export functionality
    - HTMX partial updates
    """
    model = Warehouse
    table_class = WarehouseTable
    template_name = "settings/warehouses/mains/list.html"
    partial_template_name = "settings/warehouses/partials/table.html"  # Optional, for HTMX
    queryset = Warehouse.objects.all()
    # filterset_class = WarehouseFilter

    # Search configuration
    search_fields = ["name"]

    # Export configuration
    export_name = "warehouses"
    export_permission = []  # Empty list means no specific permissions required


class WarehouseCreateView(CoreCreateView):
    model = Warehouse
    form_class = WarehouseForm
    template_name = "settings/warehouses/forms/create_or_update_form.html"
    success_url = "settings:warehouses:panel"
    section_title = "Create Warehouse"
    section_desc = ""
    submit_text = "Save"
    cancel_url = "settings:warehouses:list"


class WarehouseUpdateView(CoreUpdateView):
    model = Warehouse
    form_class = WarehouseForm
    template_name = "settings/warehouses/forms/create_or_update_form.html"
    success_url = "settings:warehouses:panel"
    section_title = "Update Warehouse"
    section_desc = ""
    submit_text = "Update"
    cancel_url = "settings:warehouses:list"


class WarehouseDetailHomeView(CoreDetailView):
    model = Warehouse
    template_name = 'settings/warehouses/mains/home.html'
    context_object_name = "warehouse"


class WarehouseDetailView(CoreDetailView):
    model = Warehouse
    template_name = 'settings/warehouses/partials/detail.html'
    context_object_name = "warehouse"


class WarehouseDataTableDetailView(CoreDataTableDetailView):
    model = Warehouse
    table_class = WarehouseDetailTable
    context_object_name = "warehouse"
    partial_view = WarehouseDetailHomeView
    search_fields = ["name"]
    # filterset_class = WarehouseDataTableFilter

# class WarehouseListView(LoginRequiredMixin, PermissionRequiredMixin, SingleTableMixin, CoreListView):
#     """Page to show all Warehouse."""

#     model = Warehouse
#     template_name = "settings/warehouses/list.html"

#     table_class = WarehouseTable
#     table_pagination = False

#     header_title = "Warehouses"
#     selected_page = "settings"
#     selected_subpage = "warehouses"

#     permission_required = ("settings.view_warehouse",)

#     def get_queryset(self):
#         if self.request.user.is_superuser is True:
#             return self.model.objects.all()
#         else:
#             request_user_warehouse_pk_list = list(self.request.user.warehouses.values_list("pk", flat=True))
#             return self.model.objects.filter(pk__in=request_user_warehouse_pk_list)


# warehouse_list_view = WarehouseListView.as_view()


# class WarehouseDetailView(LoginRequiredMixin, PermissionRequiredMixin, CoreDetailDataTablesView):
#     """Page to display selected Warehouse based on given Warehouse's slug."""

#     model = Warehouse
#     template_name = "settings/warehouses/detail.html"

#     table_class = WarehousePartialListTable
#     table_pagination = False

#     header_title = "Warehouses"
#     selected_page = "settings"
#     selected_subpage = "warehouses"

#     permission_required = ("settings.view_warehouse",)

#     def get_queryset(self):
#         if self.request.user.is_superuser is True:
#             return self.model.objects.all()
#         else:
#             request_user_warehouse_pk_list = list(self.request.user.warehouses.values_list("pk", flat=True))
#             return self.model.objects.filter(pk__in=request_user_warehouse_pk_list)


# warehouse_detail_view = WarehouseDetailView.as_view()


# class WarehouseCreateAndUpdateMixin:
#     """Mixin for Create and Update."""

#     model = Warehouse
#     form_class = WarehouseForm
#     template_name = "settings/warehouses/create_or_update.html"

#     selected_page = "settings"
#     selected_subpage = "warehouses"

#     def get_success_url(self):
#         return reverse("settings:warehouses:detail", kwargs={"slug": self.object.slug})


# class WarehouseCreateView(WarehouseCreateAndUpdateMixin, LoginRequiredMixin, PermissionRequiredMixin, CoreCreateView):
#     """Page to create Warehouse."""

#     success_message = _("Warehouse %(name)s successfully created")

#     header_title = "New Warehouse"

#     permission_required = ("settings.add_warehouse",)


# warehouse_create_view = WarehouseCreateView.as_view()


# class WarehouseUpdateView(WarehouseCreateAndUpdateMixin, LoginRequiredMixin, PermissionRequiredMixin, CoreUpdateView):
#     """Page to update selected Warehouse based on given Warehouse's slug."""

#     success_message = _("Warehouse %(name)s successfully updated")

#     header_title = "Update Warehouse"

#     permission_required = ("settings.change_warehouse",)


# warehouse_update_view = WarehouseUpdateView.as_view()


# class WarehouseDeleteView(LoginRequiredMixin, PermissionRequiredMixin, CoreDeleteView):
#     """Page to delete selected Warehouse based on given Warehouse's slug."""

#     model = Warehouse
#     success_url = reverse_lazy("settings:warehouses:list")
#     success_message = _("Warehouse %(name)s successfully deleted")

#     permission_required = ("settings.delete_warehouse",)


# warehouse_delete_view = WarehouseDeleteView.as_view()


# ############
# # FOR HTMX #
# ############


# class WarehouseInfoDetailView(LoginRequiredMixin, PermissionRequiredMixin, CoreDetailView):
#     """Partial page to display selected Warehouse's info based on given Warehouse's slug."""

#     model = Warehouse
#     template_name = "settings/warehouses/partials/htmx/_info.html"

#     permission_required = ("settings.view_warehouse",)


# warehouse_info_detail_view = WarehouseInfoDetailView.as_view()


# class WarehouseInfoUpdateView(LoginRequiredMixin, PermissionRequiredMixin, CoreUpdateView):
#     """Partial page to update selected Warehouse's info based on given Warehouse's slug."""

#     model = Warehouse
#     form_class = WarehouseInfoUpdateForm
#     template_name = "settings/warehouses/partials/htmx/_info_form.html"
#     success_message = _("Warehouse %(name)s basic information successfully updated")

#     permission_required = ("settings.change_warehouse",)

#     def get_success_url(self):
#         return reverse("settings:warehouses:info", kwargs={"slug": self.object.slug})


# warehouse_info_update_view = WarehouseInfoUpdateView.as_view()


# def warehouse_stock_dropdown_list_view(request: HttpRequest) -> JsonResponse:
#     """
#     FBV to return paginated Stock queryset in the form of JSONResponse, based on query params.
#     * q = Refers to search term when user keys in select2 input field
#     * page = Refers to which "page"/scroll based on the paginated queryset

#     Example:

#     - Sample output based on API call for endpoint:
#     {{baseUrl}}/settings/warehouses/dropdown/stocks/?warehouse=2&&q=pearl

#     - Returns:
#     {
#       "results": [
#         {
#           "id": 2252,
#           "text": "MORPHO 153 :: GLITTER PEARL (153) [20210962] :: None (FG) :: Warehouse SA01 :: 1"
#         },
#         {
#           "id": 2247,
#           "text": "MORPHO 100 :: SILVER PEARL [20170217] :: None (FG) :: Warehouse SA01 :: 0"
#         },
#         {
#           "id": 3402,
#           "text": "INCM158 :: KC 224 RUTILE VIALACEOUS PEARL [20190113-34A] :: None (FG) :: Warehouse SA01 :: 3"
#         },
#         ...
#       ],
#       "pagination": {
#         "more": true
#       }
#     }
#     """

#     page = request.GET.get("page", 1)

#     # At least need a warehouse's PK to return Item query response
#     if request.GET.get("warehouse"):
#         if request.GET.get("q", None) is not None:
#             stocks = Stock.objects.filter(
#                 Q(item__code__icontains=request.GET.get("q"))
#                 | Q(item__name__icontains=request.GET.get("q"))
#                 | Q(batch_no__icontains=request.GET.get("q")),
#                 warehouse__pk=request.GET.get("warehouse"),
#             )
#         else:
#             stocks = Stock.objects.filter(warehouse__pk=request.GET.get("warehouse"))

#         results = [{"id": stock.pk, "text": str(stock)} for stock in stocks]

#         # Shows 10 results "per page" or per scroll.
#         paginator = Paginator(results, settings.LIMIT_AUTOCOMPLETE_RESULTS_PER_SCROLL)
#         results = paginator.get_page(page).object_list

#         # Determine whether to end the "infinite scroll"
#         pagination = True if len(results) >= settings.LIMIT_AUTOCOMPLETE_RESULTS_PER_SCROLL else False
#         return JsonResponse(
#             {
#                 "results": results,
#                 "pagination": {"more": pagination},
#             }
#         )

#     return JsonResponse({"results": []})

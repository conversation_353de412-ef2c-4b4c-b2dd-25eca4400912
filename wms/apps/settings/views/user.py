# from typing import Any

# from django.contrib.auth import get_user_model
# from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
# from django.contrib.auth.models import Group
# from django.db.models.query import QuerySet
# from django.urls import reverse
# from django.utils.translation import gettext_lazy as _

# from django_filters.views import FilterView
# from django_tables2 import SingleTableMixin

# from wss.cores.views import CoreDataTablesView, CoreDetailDataTablesView, CoreDetailView, CoreListView, CoreUpdateView

# from ..filters import SinoflexUserFilter
# from ..forms import SinoflexUserInfoUpdateForm
# from ..tables.user import SinoflexUserDataTables, SinoflexUserDetailDataTables

# User = get_user_model()


# class SinoflexUserListView(LoginRequiredMixin, PermissionRequiredMixin, FilterView, SingleTableMixin, CoreListView):
#     """Page to show all Sinoflex users."""

#     model = User
#     template_name = "settings/users/list.html"

#     table_class = SinoflexUserDataTables
#     filterset_class = SinoflexUserFilter

#     # To prevent query all object as it will use ajax call in template
#     object_list = User.objects.none()

#     header_title = "Users"
#     selected_page = "settings"
#     selected_subpage = "users"

#     permission_required = ("settings.view_user",)

#     def get_queryset(self) -> QuerySet:
#         users_qs = self.model.objects.filter(groups=Group.objects.get(name="SINOFLEX"))
#         return users_qs


# sinoflex_user_list_view = SinoflexUserListView.as_view()


# class SinoflexUserDetailView(LoginRequiredMixin, PermissionRequiredMixin, CoreDetailDataTablesView):
#     """Page to display selected Sinoflex user."""

#     model = User
#     slug_field = "username"
#     slug_url_kwarg = "username"
#     template_name = "settings/users/detail.html"

#     table_class = SinoflexUserDetailDataTables

#     header_title = "Users"
#     selected_page = "settings"
#     selected_subpage = "users"

#     permission_required = ("settings.view_user",)

#     def get_queryset(self) -> QuerySet:
#         users_qs = self.model.objects.filter(groups=Group.objects.get(name="SINOFLEX"))
#         return users_qs


# sinoflex_user_detail_view = SinoflexUserDetailView.as_view()


# ##################
# # FOR DataTables #
# ##################


# class SinoflexUserDataTablesView(LoginRequiredMixin, PermissionRequiredMixin, CoreDataTablesView):
#     """JSON for DataTables in list page."""

#     model = User
#     slug_field = "username"
#     slug_url_kwarg = "username"
#     table_class = SinoflexUserDataTables
#     filterset_class = SinoflexUserFilter

#     permission_required = ("settings.view_user",)

#     def get_queryset(self) -> QuerySet:
#         users_qs = self.model.objects.filter(groups=Group.objects.get(name="SINOFLEX"))
#         return users_qs


# sinoflex_user_datatables_view = SinoflexUserDataTablesView.as_view()


# class SinoflexUserDetailDataTablesView(SinoflexUserDataTablesView):
#     """JSON for DataTables in detail page."""

#     table_class = SinoflexUserDetailDataTables


# sinoflex_user_detail_datatables_view = SinoflexUserDetailDataTablesView.as_view()


# ############
# # FOR HTMX #
# ############


# class SinoflexUserInfoDetailView(LoginRequiredMixin, PermissionRequiredMixin, CoreDetailView):
#     """Partial page to display selected Sinoflex User's info page based on given user's username."""

#     model = User
#     slug_field = "username"
#     slug_url_kwarg = "username"
#     template_name = "settings/users/partials/htmx/_info.html"

#     permission_required = ("settings.view_user",)

#     def get_queryset(self) -> QuerySet:
#         users_qs = self.model.objects.filter(groups=Group.objects.get(name="SINOFLEX"))
#         return users_qs


# sinoflex_user_info_detail_view = SinoflexUserInfoDetailView.as_view()


# class SinoflexUserInfoUpdateView(LoginRequiredMixin, PermissionRequiredMixin, CoreUpdateView):
#     """Partial page to update selected Sinoflex User's info page based on given user's username."""

#     model = User
#     slug_field = "username"
#     slug_url_kwarg = "username"
#     form_class = SinoflexUserInfoUpdateForm
#     template_name = "settings/users/partials/htmx/_info_form.html"
#     success_message = _("User's basic information successfully updated")

#     permission_required = ("settings.change_user",)

#     def get_queryset(self) -> QuerySet:
#         users_qs = self.model.objects.filter(groups=Group.objects.get(name="SINOFLEX"))
#         return users_qs

#     def get_success_url(self) -> Any:
#         return reverse("settings:users:info", kwargs={"username": self.object.username})


# sinoflex_user_info_update_view = SinoflexUserInfoUpdateView.as_view()

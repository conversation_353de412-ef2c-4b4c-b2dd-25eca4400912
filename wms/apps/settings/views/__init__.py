# from .branch import branch_create_view, branch_delete_view, branch_detail_view, branch_update_view
# from .organization import organization_detail_view, organization_info_detail_view, organization_info_update_view
from .unit_conversion import (
    UnitConversionListView,
    UnitConversionCreateView,
    UnitConversionUpdateView,
    UnitConversionDetailHomeView,
    UnitConversionDetailView,
    UnitConversionDataTableDetailView,
#     unit_conversion_create_view,
#     unit_conversion_delete_view,
#     unit_conversion_detail_view,
#     unit_conversion_list_view,
#     unit_conversion_update_view,
)
from .unit_of_measure import (
    UnitOfMeasureListView,
    UnitOfMeasureCreateView,
    UnitOfMeasureUpdateView,
    UnitOfMeasureDetailHomeView,
    UnitOfMeasureDetailView,
    UnitOfMeasureDataTableDetailView,
    # unit_of_measure_create_view,
    # unit_of_measure_delete_view,
    # unit_of_measure_detail_view,
    # unit_of_measure_list_view,
    # unit_of_measure_unit_conversion_list_view,
    # unit_of_measure_update_view,
)
# from .user import (
#     sinoflex_user_datatables_view,
#     sinoflex_user_detail_datatables_view,
#     sinoflex_user_detail_view,
#     sinoflex_user_info_detail_view,
#     sinoflex_user_info_update_view,
#     sinoflex_user_list_view,
# )
from .warehouse import (
    WarehouseListView,
    WarehouseCreateView,
    WarehouseUpdateView,
    WarehouseDetailHomeView,
    WarehouseDetailView,
    WarehouseDataTableDetailView,
#     warehouse_create_view,
#     warehouse_delete_view,
#     warehouse_detail_view,
#     warehouse_info_detail_view,
#     warehouse_info_update_view,
#     warehouse_list_view,
#     warehouse_stock_dropdown_list_view,
#     warehouse_update_view,
)

# __all__ = [
#     "branch_create_view",
#     "branch_detail_view",
#     "branch_update_view",
#     "branch_delete_view",
#     "organization_detail_view",
#     "organization_info_detail_view",
#     "organization_info_update_view",
#     "unit_conversion_create_view",
#     "unit_conversion_delete_view",
#     "unit_conversion_detail_view",
#     "unit_conversion_list_view",
#     "unit_conversion_update_view",
#     "unit_of_measure_create_view",
#     "unit_of_measure_delete_view",
#     "unit_of_measure_detail_view",
#     "unit_of_measure_list_view",
#     "unit_of_measure_update_view",
#     "unit_of_measure_unit_conversion_list_view",
#     "warehouse_create_view",
#     "warehouse_delete_view",
#     "warehouse_detail_view",
#     "warehouse_info_detail_view",
#     "warehouse_info_update_view",
#     "warehouse_list_view",
#     "warehouse_update_view",
#     "warehouse_stock_dropdown_list_view",
#     "sinoflex_user_list_view",
#     "sinoflex_user_detail_view",
#     "sinoflex_user_info_detail_view",
#     "sinoflex_user_info_update_view",
#     "sinoflex_user_datatables_view",
#     "sinoflex_user_detail_datatables_view",
# ]

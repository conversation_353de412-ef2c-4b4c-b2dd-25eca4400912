# from django.contrib import messages
# from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
# from django.http import HttpResponse, HttpResponsePermanentRedirect, HttpResponseRedirect
# from django.shortcuts import redirect
# from django.urls import reverse, reverse_lazy
# from django.utils.translation import gettext_lazy as _

# from django_tables2 import SingleTableMixin

# from wss.cores.utils import send_notification
# from wss.cores.views import (
#     CoreCreateView,
#     CoreDeleteView,
#     CoreDetailView,
#     CoreListView,
#     CoreUpdateView,
#     HttpResponseHXRedirect,
# )

from wms.cores.mixins import ExportTableMixin
from wms.cores.views import CoreSingleTableView, CoreDataTableDetailView, CoreCreateView, CoreDetailView, CoreUpdateView

from wms.apps.settings.models import UnitConversion, UnitOfMeasure
from wms.apps.settings.forms import UnitOfMeasureForm, UnitOfMeasureUpdateForm
# from wms.apps.settings.filters import UnitOfMeasureDataTableFilter
from wms.apps.settings.tables import UnitOfMeasureTable, UnitOfMeasureDetailTable


class UnitOfMeasureListView(ExportTableMixin, CoreSingleTableView):
    """
    View for displaying and managing the list of UnitOfMeasures.
    Supports:
    - Pagination
    - Column visibility
    - Searching
    - Export functionality
    - HTMX partial updates
    """
    model = UnitOfMeasure
    table_class = UnitOfMeasureTable
    template_name = "settings/uoms/mains/list.html"
    partial_template_name = "settings/uoms/partials/table.html"  # Optional, for HTMX
    queryset = UnitOfMeasure.objects.all()
    # filterset_class = UnitOfMeasureFilter

    # Search configuration
    search_fields = ["unit_name", "symbol"]

    # Export configuration
    export_name = "uoms"
    export_permission = []  # Empty list means no specific permissions required


class UnitOfMeasureCreateView(CoreCreateView):
    model = UnitOfMeasure
    form_class = UnitOfMeasureForm
    template_name = "settings/uoms/forms/create_or_update_form.html"
    success_url = "settings:uoms:panel"
    section_title = "Create UnitOfMeasure"
    section_desc = ""
    submit_text = "Save"
    cancel_url = "settings:uoms:list"


class UnitOfMeasureUpdateView(CoreUpdateView):
    model = UnitOfMeasure
    form_class = UnitOfMeasureUpdateForm
    template_name = "settings/uoms/forms/create_or_update_form.html"
    success_url = "settings:uoms:panel"
    section_title = "Update UnitOfMeasure"
    section_desc = ""
    submit_text = "Update"
    cancel_url = "settings:uoms:list"


class UnitOfMeasureDetailHomeView(CoreDetailView):
    model = UnitOfMeasure
    template_name = 'settings/uoms/mains/home.html'
    context_object_name = "uom"


class UnitOfMeasureDetailView(CoreDetailView):
    model = UnitOfMeasure
    template_name = 'settings/uoms/partials/detail.html'
    context_object_name = "uom"


class UnitOfMeasureDataTableDetailView(CoreDataTableDetailView):
    model = UnitOfMeasure
    table_class = UnitOfMeasureDetailTable
    context_object_name = "uom"
    partial_view = UnitOfMeasureDetailHomeView
    search_fields = ["unit_name", "symbol"]
    # filterset_class = UnitOfMeasureDataTableFilter


# class UnitOfMeasureListView(LoginRequiredMixin, PermissionRequiredMixin, SingleTableMixin, CoreListView):
#     """Page to show all UnitOfMeasure."""

#     model = UnitOfMeasure
#     template_name = "settings/unit_of_measures/list.html"

#     table_class = UnitOfMeasureTable
#     table_pagination = False

#     header_title = "Unit Of Measures"
#     selected_page = "settings"
#     selected_subpage = "unit_of_measures"

#     permission_required = ("settings.view_unitofmeasure",)


# unit_of_measure_list_view = UnitOfMeasureListView.as_view()


# class UnitOfMeasureDetailView(LoginRequiredMixin, PermissionRequiredMixin, SingleTableMixin, CoreDetailView):
#     """Page to display selected UnitOfMeasure based on given UnitOfMeasure's pk."""

#     model = UnitOfMeasure
#     template_name = "settings/unit_of_measures/detail.html"

#     table_class = UnitOfMeasurePartialListTable
#     table_pagination = False

#     header_title = "Unit Of Measures"
#     selected_page = "settings"
#     selected_subpage = "unit_of_measures"

#     permission_required = ("settings.view_unitofmeasure",)

#     def get_total_unit_conversions(self):
#         return UnitConversion.objects.filter(origin__pk=self.kwargs["pk"]).count()

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         context["total_unit_conversions"] = self.get_total_unit_conversions()
#         return context


# unit_of_measure_detail_view = UnitOfMeasureDetailView.as_view()


# class UnitOfMeasureCreateAndUpdateMixin:
#     """Mixin for Create and Update."""

#     model = UnitOfMeasure
#     form_class = UnitOfMeasureForm
#     template_name = "settings/unit_of_measures/create_or_update.html"

#     selected_page = "settings"
#     selected_subpage = "unit_of_measures"

#     def get_success_url(self):
#         return reverse("settings:unit_of_measures:detail", kwargs={"pk": self.object.pk})


# class UnitOfMeasureCreateView(
#     UnitOfMeasureCreateAndUpdateMixin, LoginRequiredMixin, PermissionRequiredMixin, CoreCreateView
# ):
#     """Page to create UnitOfMeasure."""

#     success_message = _("Unit Of Measure %(unit_name)s successfully created")

#     header_title = "New Unit Of Measure"

#     permission_required = ("settings.add_unitofmeasure",)

#     def handle_send_notification(self):
#         send_notification(
#             instance=self.object,
#             message=f"NEW UOM ({self.object.unit_name}) had been added.",
#             user_role_list=["Superadmin", "Admin"],
#             level="info",
#         )

#     def form_valid(self, form):
#         """If the form is valid, save the associated model."""
#         self.object = form.save()
#         # handle notification
#         self.handle_send_notification()
#         return super().form_valid(form)


# unit_of_measure_create_view = UnitOfMeasureCreateView.as_view()


# class UnitOfMeasureUpdateView(
#     UnitOfMeasureCreateAndUpdateMixin, LoginRequiredMixin, PermissionRequiredMixin, CoreUpdateView
# ):
#     """Page to update selected UnitOfMeasure based on given UnitOfMeasure's pk."""

#     success_message = _("Unit Of Measure %(unit_name)s successfully updated")

#     header_title = "Update Unit Of Measure"

#     permission_required = ("settings.change_unitofmeasure",)

#     def get(self, request, *args, **kwargs) -> (HttpResponseRedirect, HttpResponsePermanentRedirect, HttpResponse):
#         self.object = self.get_object()
#         if self.object.in_use is True:
#             messages.error(self.request, "Unit Of Measure cannot be edited")
#             return redirect(reverse("settings:unit_of_measures:list"))
#         return super().get(request, *args, **kwargs)


# unit_of_measure_update_view = UnitOfMeasureUpdateView.as_view()


# class UnitOfMeasureDeleteView(LoginRequiredMixin, PermissionRequiredMixin, CoreDeleteView):
#     """Page to delete selected UnitOfMeasure based on given UnitOfMeasure's pk."""

#     model = UnitOfMeasure
#     success_url = reverse_lazy("settings:unit_of_measures:list")
#     success_message = _("Unit Of Measure %(unit_name)s successfully deleted")

#     permission_required = ("settings.delete_unitofmeasure",)

#     def delete(
#         self, request, *args, **kwargs
#     ) -> (HttpResponseHXRedirect, HttpResponseRedirect, HttpResponsePermanentRedirect, HttpResponse):
#         self.object = self.get_object()
#         if self.object.in_use is True:
#             messages.error(self.request, "Unit Of Measure cannot be deleted")

#             # Special handle for HTMX
#             if request.headers.get("hx-request") == "true":
#                 return HttpResponseHXRedirect(request.headers.get("referer"))

#             return redirect(reverse("settings:unit_of_measures:list"))
#         return super().delete(request, *args, **kwargs)


# unit_of_measure_delete_view = UnitOfMeasureDeleteView.as_view()


# ############
# # FOR HTMX #
# ############


# class UnitOfMeasureUnitConversionListView(LoginRequiredMixin, PermissionRequiredMixin, CoreListView):
#     """Partial page to show all UnitConversion based on given UnitOfMeasure's pk."""

#     model = UnitConversion
#     template_name = "settings/unit_of_measures/partials/htmx/_unit_conversions.html"

#     permission_required = (
#         "settings.view_unitofmeasure",
#         "settings.view_unitconversion",
#     )

#     def get_queryset(self):
#         return self.model.objects.filter(origin__pk=self.kwargs["pk"])


# unit_of_measure_unit_conversion_list_view = UnitOfMeasureUnitConversionListView.as_view()

# from django.conf import settings
# from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
# from django.urls import reverse
# from django.utils.translation import gettext_lazy as _

# from wss.cores.views import CoreDetailView, CoreUpdateView

# from ..forms import OrganizationForm
# from ..models import Organization


# class OrganizationDetailView(LoginRequiredMixin, PermissionRequiredMixin, CoreDetailView):
#     """Page to display selected Organization based on given Organization's slug."""

#     model = Organization
#     template_name = "settings/organizations/detail.html"

#     header_title = "Organizations"
#     selected_page = "settings"
#     selected_subpage = "organizations"

#     permission_required = ("settings.view_organization",)

#     def get_object(self):
#         organization = Organization.objects.all().first()
#         if not organization:
#             organization = Organization.objects.create(name=settings.PROJECT_NAME)

#         return organization

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)

#         branches = self.object.branch_set.all()
#         context["branches"] = branches

#         return context


# organization_detail_view = OrganizationDetailView.as_view()


# ############
# # FOR HTMX #
# ############


# class OrganizationInfoDetailView(LoginRequiredMixin, PermissionRequiredMixin, CoreDetailView):
#     """Partial page to display selected Organization's info based on given Organization's slug."""

#     model = Organization
#     template_name = "settings/organizations/partials/htmx/_info.html"

#     permission_required = ("settings.view_organization",)


# organization_info_detail_view = OrganizationInfoDetailView.as_view()


# class OrganizationInfoUpdateView(LoginRequiredMixin, PermissionRequiredMixin, CoreUpdateView):
#     """Partial page to update selected Organization's info based on given Organization's slug."""

#     model = Organization
#     form_class = OrganizationForm
#     template_name = "settings/organizations/partials/htmx/_info_form.html"
#     success_message = _("Organization %(name)s basic information successfully updated")

#     permission_required = ("settings.change_organization",)

#     def get_success_url(self):
#         return reverse("settings:organizations:info", kwargs={"slug": self.object.slug})


# organization_info_update_view = OrganizationInfoUpdateView.as_view()

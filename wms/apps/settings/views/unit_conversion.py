# from typing import Union

# from django.contrib import messages
# from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
# from django.db.models.query import QuerySet
# from django.http import HttpResponse, HttpResponsePermanentRedirect, HttpResponseRedirect
# from django.shortcuts import redirect
# from django.urls import reverse, reverse_lazy
# from django.utils.translation import gettext_lazy as _

# from django_tables2 import SingleTableMixin

# from wss.cores.views import (
#     CoreCreateView,
#     CoreDeleteView,
#     CoreDetailView,
#     CoreListView,
#     CoreUpdateView,
#     HttpResponseHXRedirect,
# )

# from ..forms import UnitConversionForm
# from ..models import UnitConversion
# from ..tables import UnitConversionPartialListTable, UnitConversionTable


from wms.cores.mixins import ExportTableMixin
from wms.cores.views import CoreSingleTableView, CoreDataTableDetailView, CoreCreateView, CoreDetailView, CoreUpdateView

from wms.apps.settings.models import UnitConversion
from wms.apps.settings.forms import UnitConversionForm, UnitConversionUpdateForm
# from wms.apps.settings.filters import UnitConversionDataTableFilter
from wms.apps.settings.tables import UnitConversionTable, UnitConversionDetailTable


class UnitConversionListView(ExportTableMixin, CoreSingleTableView):
    """
    View for displaying and managing the list of UnitConversions.
    Supports:
    - Pagination
    - Column visibility
    - Searching
    - Export functionality
    - HTMX partial updates
    """
    model = UnitConversion
    table_class = UnitConversionTable
    template_name = "settings/unit_conversions/mains/list.html"
    partial_template_name = "settings/unit_conversions/partials/table.html"  # Optional, for HTMX
    queryset = UnitConversion.objects.all()
    # filterset_class = UnitConversionFilter

    # Search configuration
    search_fields = ["origin", "target"]

    # Export configuration
    export_name = "unit_conversions"
    export_permission = []  # Empty list means no specific permissions required


class UnitConversionCreateView(CoreCreateView):
    model = UnitConversion
    form_class = UnitConversionForm
    template_name = "settings/unit_conversions/forms/create_or_update_form.html"
    success_url = "settings:unit_conversions:panel"
    section_title = "Create UnitConversion"
    section_desc = ""
    submit_text = "Save"
    cancel_url = "settings:unit_conversions:list"


class UnitConversionUpdateView(CoreUpdateView):
    model = UnitConversion
    form_class = UnitConversionUpdateForm
    template_name = "settings/unit_conversions/forms/create_or_update_form.html"
    success_url = "settings:unit_conversions:panel"
    section_title = "Update UnitConversion"
    section_desc = ""
    submit_text = "Update"
    cancel_url = "settings:unit_conversions:list"


class UnitConversionDetailHomeView(CoreDetailView):
    model = UnitConversion
    template_name = 'settings/unit_conversions/mains/home.html'
    context_object_name = "unit_conversion"


class UnitConversionDetailView(CoreDetailView):
    model = UnitConversion
    template_name = 'settings/unit_conversions/partials/detail.html'
    context_object_name = "unit_conversion"


class UnitConversionDataTableDetailView(CoreDataTableDetailView):
    model = UnitConversion
    table_class = UnitConversionDetailTable
    context_object_name = "unit_conversion"
    partial_view = UnitConversionDetailHomeView
    search_fields = ["origin", "target"]
    # filterset_class = UnitConversionDataTableFilter


# class UnitConversionListView(LoginRequiredMixin, PermissionRequiredMixin, SingleTableMixin, CoreListView):
#     """Page to show all UnitConversion."""

#     model = UnitConversion
#     template_name = "settings/unit_conversions/list.html"

#     table_class = UnitConversionTable
#     table_pagination = False

#     header_title = "Unit Conversions"
#     selected_page = "settings"
#     selected_subpage = "unit_conversions"

#     permission_required = ("settings.view_unitconversion",)

#     def get_queryset(self) -> QuerySet:
#         qs = self.model.objects.prefetch_related("origin", "target").all()
#         return qs


# unit_conversion_list_view = UnitConversionListView.as_view()


# class UnitConversionDetailView(LoginRequiredMixin, PermissionRequiredMixin, SingleTableMixin, CoreDetailView):
#     """Page to display selected UnitConversion based on given UnitConversion's pk."""

#     model = UnitConversion
#     template_name = "settings/unit_conversions/detail.html"

#     table_class = UnitConversionPartialListTable
#     table_pagination = False

#     header_title = "Unit Conversions"
#     selected_page = "settings"
#     selected_subpage = "unit_conversions"

#     permission_required = ("settings.view_unitconversion",)

#     def get_queryset(self) -> QuerySet:
#         qs = self.model.objects.prefetch_related("origin", "target").all()
#         return qs


# unit_conversion_detail_view = UnitConversionDetailView.as_view()


# class UnitConversionCreateAndUpdateMixin:
#     """Mixin for Create and Update."""

#     model = UnitConversion
#     form_class = UnitConversionForm
#     template_name = "settings/unit_conversions/create_or_update.html"

#     selected_page = "settings"
#     selected_subpage = "unit_conversions"

#     def get_success_url(self):
#         return reverse("settings:unit_conversions:detail", kwargs={"pk": self.object.pk})


# class UnitConversionCreateView(
#     UnitConversionCreateAndUpdateMixin, LoginRequiredMixin, PermissionRequiredMixin, CoreCreateView
# ):
#     """Page to create UnitConversion."""

#     success_message = _("Unit Conversion %(origin)s -> %(target)s successfully created")

#     header_title = "New Unit Conversion"

#     permission_required = ("settings.add_unitconversion",)


# unit_conversion_create_view = UnitConversionCreateView.as_view()


# class UnitConversionUpdateView(
#     UnitConversionCreateAndUpdateMixin, LoginRequiredMixin, PermissionRequiredMixin, CoreUpdateView
# ):
#     """Page to update selected UnitConversion based on given UnitConversion's pk."""

#     success_message = _("Unit Conversion %(origin)s -> %(target)s successfully updated")

#     header_title = "Update Unit Conversion"

#     permission_required = ("settings.change_unitconversion",)

#     def get(self, request, *args, **kwargs) -> Union[HttpResponseRedirect, HttpResponsePermanentRedirect, HttpResponse]:
#         self.object = self.get_object()
#         if self.object.origin.in_use is True or self.object.target.in_use is True:
#             messages.error(self.request, "Unit Conversion cannot be edited")
#             return redirect(reverse("settings:unit_conversions:list"))
#         return super().get(request, *args, **kwargs)


# unit_conversion_update_view = UnitConversionUpdateView.as_view()


# class UnitConversionDeleteView(LoginRequiredMixin, PermissionRequiredMixin, CoreDeleteView):
#     """Page to delete selected UnitConversion based on given UnitConversion's pk."""

#     model = UnitConversion
#     success_url = reverse_lazy("settings:unit_conversions:list")
#     success_message = _("Unit Conversion %(origin)s -> %(target)s successfully deleted")

#     permission_required = ("settings.delete_unitconversion",)

#     def delete(
#         self, request, *args, **kwargs
#     ) -> Union[HttpResponseHXRedirect, HttpResponseRedirect, HttpResponsePermanentRedirect, HttpResponse]:
#         self.object = self.get_object()
#         if self.object.origin.in_use is True or self.object.target.in_use is True:
#             messages.error(self.request, "Unit Conversion cannot be deleted")

#             # Special handle for HTMX
#             if request.headers.get("hx-request") == "true":
#                 return HttpResponseHXRedirect(request.headers.get("referer"))

#             return redirect(reverse("settings:unit_conversions:list"))
#         return super().delete(request, *args, **kwargs)

#     def get_success_message(self):
#         """Override success message due to foreign key"""
#         return self.success_message % {"origin": self.object.origin, "target": self.object.target}


# unit_conversion_delete_view = UnitConversionDeleteView.as_view()

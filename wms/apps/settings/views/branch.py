# from django.conf import settings
# from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
# from django.urls import reverse, reverse_lazy
# from django.utils.translation import gettext_lazy as _

# from wms.cores.views import CoreCreateView, CoreDeleteView, CoreDetailView, CoreUpdateView

# from ..forms import BranchUpdateForm
# from ..models import Branch, Organization


# class BranchCreateAndUpdateMixin:
#     """Mixin for Create and Update."""

#     model = Branch
#     form_class = BranchUpdateForm
#     template_name = "settings/branches/create_or_update.html"

#     selected_page = "settings"
#     selected_subpage = "organizations"

#     def get_organization(self):
#         organization = Organization.objects.all().first()
#         if not organization:
#             organization = Organization.objects.create(name=settings.PROJECT_NAME)

#         return organization

#     def get_initial(self):
#         initial = super().get_initial()

#         initial["organization"] = self.get_organization()

#         return initial

#     def get_success_url(self):
#         return reverse("settings:organizations:detail")


# class BranchCreateView(BranchCreateAndUpdateMixin, LoginRequiredMixin, PermissionRequiredMixin, CoreCreateView):
#     """Page to create Branch."""

#     success_message = _("Branch %(name)s successfully created")

#     header_title = "New Branch"

#     permission_required = ("settings.add_branch",)


# branch_create_view = BranchCreateView.as_view()


# class BranchDeleteView(LoginRequiredMixin, PermissionRequiredMixin, CoreDeleteView):
#     """Page to delete selected Branch based on given Branch's slug."""

#     model = Branch
#     success_url = reverse_lazy("settings:organizations:detail")
#     success_message = _("Branch %(name)s successfully deleted")

#     permission_required = ("settings.delete_branch",)


# branch_delete_view = BranchDeleteView.as_view()


# ############
# # FOR HTMX #
# ############


# class BranchDetailView(LoginRequiredMixin, PermissionRequiredMixin, CoreDetailView):
#     """Partial page to display selected Branch based on given Branch's slug."""

#     model = Branch
#     template_name = "settings/branches/partials/htmx/_detail.html"

#     permission_required = ("settings.view_branch",)


# branch_detail_view = BranchDetailView.as_view()


# class BranchUpdateView(LoginRequiredMixin, PermissionRequiredMixin, CoreUpdateView):
#     """Partial page to update selected Branch based on given Branch's slug."""

#     model = Branch
#     form_class = BranchUpdateForm
#     template_name = "settings/branches/partials/htmx/_form.html"
#     success_message = _("Branch %(name)s information successfully updated")

#     permission_required = ("settings.change_branch",)

#     def get_success_url(self):
#         return reverse("settings:branches:detail", kwargs={"slug": self.object.slug})


# branch_update_view = BranchUpdateView.as_view()

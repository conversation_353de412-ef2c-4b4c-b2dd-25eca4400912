# Generated by Django 5.1 on 2025-03-15 13:08

import django.utils.timezone
import django_extensions.db.fields
import model_utils.fields
import phonenumber_field.modelfields
import sorl.thumbnail.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('cores', '0001_create_extensions'),
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
            ],
            options={
                'permissions': (('view_user', 'Can view user'), ('change_user', 'Can change user')),
                'managed': False,
                'default_permissions': (),
            },
        ),
        migrations.CreateModel(
            name='Branch',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('address_attention', models.CharField(blank=True, max_length=256, verbose_name='Attention')),
                ('address_street_1', models.CharField(blank=True, max_length=128, verbose_name='Street 1')),
                ('address_street_2', models.CharField(blank=True, max_length=128, verbose_name='Street 2')),
                ('address_postal_code', models.CharField(blank=True, max_length=32, verbose_name='Postal Code')),
                ('address_district', models.CharField(blank=True, max_length=128, verbose_name='District')),
                ('address_city', models.CharField(blank=True, max_length=128, verbose_name='City')),
                ('address_state', models.CharField(blank=True, max_length=128, verbose_name='State')),
                ('address_country', models.CharField(blank=True, max_length=128, verbose_name='Country')),
                ('address_phone', phonenumber_field.modelfields.PhoneNumberField(blank=True, help_text='Phone number (e.g. +60128585299).', max_length=128, region=None, verbose_name='Phone')),
                ('is_primary', models.BooleanField(default=False, verbose_name='Is primary?')),
                ('name', models.CharField(max_length=255, verbose_name='Branch Name')),
                ('slug', django_extensions.db.fields.AutoSlugField(blank=True, editable=False, max_length=255, populate_from=['name'], unique=True, verbose_name='Slug')),
            ],
            options={
                'ordering': ['-is_primary', 'name'],
                'get_latest_by': 'created',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='Organization',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('name', models.CharField(max_length=255, verbose_name='Name')),
                ('slug', django_extensions.db.fields.AutoSlugField(blank=True, editable=False, max_length=255, populate_from=['name'], unique=True, verbose_name='Slug')),
                ('registration_no', models.CharField(blank=True, max_length=24, verbose_name='Registration No.')),
                ('logo', sorl.thumbnail.fields.ImageField(blank=True, upload_to='settings/organization', verbose_name='Logo')),
                ('phone', phonenumber_field.modelfields.PhoneNumberField(blank=True, max_length=128, region=None, verbose_name='Phone')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='Email')),
                ('website', models.URLField(blank=True, max_length=255, verbose_name='Website')),
            ],
            options={
                'ordering': ['-created'],
                'get_latest_by': 'created',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='UnitConversion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('rate', models.DecimalField(decimal_places=6, help_text='1 [Origin] = Conversion Rate [Target]. Example: 1 CM = 0.01 M', max_digits=19, verbose_name='Conversion Rate')),
            ],
            options={
                'ordering': ['-created'],
                'get_latest_by': 'created',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='UnitOfMeasure',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('unit_name', models.CharField(max_length=256, verbose_name='Unit Name')),
                ('symbol', models.CharField(max_length=256, unique=True, verbose_name='Symbol')),
                ('unit_precision', models.IntegerField(choices=[(0, 0), (1, 1), (2, 2), (3, 3), (4, 4), (5, 5), (6, 6)], verbose_name='Unit Precision')),
                ('in_use', models.BooleanField(default=False, help_text='Has this UOM been used once Transaction created.', verbose_name='In use?')),
            ],
            options={
                'ordering': ['-created'],
                'get_latest_by': 'created',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='Warehouse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('path', models.CharField(max_length=255, unique=True)),
                ('depth', models.PositiveIntegerField()),
                ('numchild', models.PositiveIntegerField(default=0)),
                ('name', models.CharField(max_length=256, verbose_name='Warehouse Name')),
                ('slug', django_extensions.db.fields.AutoSlugField(blank=True, editable=False, max_length=255, populate_from=['name'], unique=True, verbose_name='slug')),
                ('is_storage', models.BooleanField(default=False, verbose_name='Is Storage?')),
                ('photo', sorl.thumbnail.fields.ImageField(blank=True, null=True, upload_to='settings/warehouse', verbose_name='Warehouse Photo')),
            ],
            options={
                'ordering': ['path'],
                'get_latest_by': 'created',
                'abstract': False,
            },
        ),
    ]

# from typing import Any

# from django import forms
# from django.contrib.auth import get_user_model
from django.db.models.query import QuerySet
from django.core.exceptions import ValidationError
from django.utils.translation import gettext as _

from treebeard.forms import MoveNodeForm

# from wms.cores.forms import CoreHtmxModelForm, CoreModelForm
# from wms.cores.forms.mixins import FormUOMSymbolMixin
from wms.cores.forms.fields import CoreModelForm, CoreCharField, SizedFormField, FormFieldSize, CoreChoiceField
from wms.cores.utils import uom_choices_symbol
# from wms.cores.widgets import CoreGroupCheckboxSelectMultiple

from wms.apps.settings.models import Branch, Organization, UnitConversion, UnitOfMeasure, Warehouse

# User = get_user_model()


# class OrganizationForm(CoreModelForm):
#     class Meta:
#         model = Organization
#         fields = [
#             "name",
#             "registration_no",
#             "logo",
#             "phone",
#             "email",
#             "website",
#         ]


class WarehouseForm(MoveNodeForm, CoreModelForm):
    class Meta:
        model = Warehouse
        fields = [
            "name",
            "branch",
            "is_storage",
        ]

    def _clean_cleaned_data(self):
        """delete auxilary fields not belonging to node model"""
        reference_node_id = None

        if "_ref_node_id" in self.cleaned_data:
            if self.cleaned_data["_ref_node_id"] != "0":
                reference_node_id = self.cleaned_data["_ref_node_id"]
                if reference_node_id.isdigit():
                    reference_node_id = int(reference_node_id)
            del self.cleaned_data["_ref_node_id"]

        position_type = self.cleaned_data.get("_position")
        if position_type:
            del self.cleaned_data["_position"]

        return position_type, reference_node_id

    def save(self):
        position_type, reference_node_id = self._clean_cleaned_data()

        # handled the created_by/modified_by logic, (CoreModelForm's save function being overrided here,
        # therefore need to reinitialize this logic)
        if hasattr(self, "request"):
            if hasattr(self.request, "user") and self.instance.pk and hasattr(self.instance, "modified_by"):
                self.instance.modified_by = self.request.user

            if hasattr(self.request, "user") and hasattr(self.instance, "created_by") and not self.instance.created_by:
                self.instance.created_by = self.request.user
                self.instance.modified_by = None

        if self.instance._state.adding:
            cl_data = {}
            for field in self.cleaned_data:
                if not isinstance(self.cleaned_data[field], (list, QuerySet)):
                    cl_data[field] = self.cleaned_data[field]
            if reference_node_id:
                reference_node = self._meta.model.objects.get(pk=reference_node_id)
                self.instance = reference_node.add_child(**cl_data)
                self.instance.move(reference_node, pos=position_type)
            else:
                self.instance = self._meta.model.add_root(**cl_data)

            # Assign warehouse to the user who created it
            if self.request.user:
                self.request.user.warehouses.add(self.instance)
        else:
            self.instance.save()
            if reference_node_id:
                reference_node = self._meta.model.objects.get(pk=reference_node_id)
                self.instance.move(reference_node, pos=position_type)

        # Reload the instance
        self.instance.refresh_from_db()

        # # CoreModelForm have handled the created_by/modified_by logic
        # super().save()

        return self.instance


class UnitOfMeasureForm(CoreModelForm):

    def clean(self):
        cleaned_data = super().clean()
        symbol = cleaned_data.get("symbol")

        if symbol:
            # Check if an item with same consignor and symbol exists
            exists = UnitOfMeasure.objects.filter(
                symbol__iexact=symbol
            ).exclude(pk=self.instance.pk if self.instance else None).exists()

            if exists:
                raise ValidationError({
                    "symbol": _("UnitOfMeasure with this Symbol already exists."),
                })
        return cleaned_data

    class Meta:
        model = UnitOfMeasure
        fields = [
            "unit_name",
            "symbol",
            "unit_precision",
        ]


class UnitOfMeasureUpdateForm(CoreModelForm):
    # # Read-only display fields
    # code = CoreCharField(disabled=True, required=False)
    # uom = CoreChoiceField(disabled=True, required=False)

    # def __init__(self, *args, **kwargs):
    #     super().__init__(*args, **kwargs)
    #     instance = kwargs.get('instance')

    #     if instance:
    #         self.fields["uom"].choices = uom_choices_symbol()

    # def clean(self):
    #     cleaned_data = super().clean()
    #     if self.instance:
    #         # Preserve original values for read-only fields
    #         cleaned_data['code'] = self.instance.code
    #         cleaned_data['uom'] = self.instance.uom
    #     return cleaned_data

    class Meta:
        model = UnitOfMeasure
        fields = [
            "unit_name",
            "symbol",
            "unit_precision",
        ]


# *NOTE: first creation of Conversion on UOM_A to UOM_B will successful,
#        but if i tried to create same Conversion on UOM_A to UOM_B,
#        it will hit error on class's model clean function.
#        Expected result: raise validation that this Origin + Target already exist
class UnitConversionForm(CoreModelForm):

    def clean(self):
        cleaned_data = super().clean()
        origin = cleaned_data.get("origin")
        target = cleaned_data.get("target")

        if origin and target:
            # Check if an item with same consignor and origin exists
            exists = UnitConversion.objects.filter(
                origin=origin,
                target=target,
            ).exclude(pk=self.instance.pk if self.instance else None).exists()

            if exists:
                raise ValidationError({
                    "origin": _("UnitConversion with this Origin + Target already exists."),
                    "target": _("UnitConversion with this Origin + Target already exists."),
                })
        return cleaned_data

    class Meta:
        model = UnitConversion
        fields = [
            "origin",
            "target",
            "rate",
        ]


class UnitConversionUpdateForm(CoreModelForm):
    class Meta:
        model = UnitConversion
        fields = [
            "origin",
            "target",
            "rate",
        ]


# class UnitOfMeasureForm(CoreModelForm):
#     class Meta:
#         model = UnitOfMeasure
#         fields = [
#             "unit_name",
#             "symbol",
#             "unit_precision",
#         ]


# class UnitConversionForm(CoreModelForm, FormUOMSymbolMixin):
#     """Unit Conversion form."""

#     origin = forms.ChoiceField(label=_("Origin"), choices=uom_choices_symbol)
#     target = forms.ChoiceField(label=_("Target"), choices=uom_choices_symbol)

#     class Meta:
#         model = UnitConversion
#         fields = [
#             "target",
#             "origin",
#             "rate",
#         ]

#     def clean(self) -> dict[str, Any]:
#         """Return UOM objects for origin & target based on selected UOM's symbol."""

#         cleaned_data = super().clean()
#         cleaned_data["origin"] = UnitOfMeasure.objects.get(pk=cleaned_data["origin"])
#         cleaned_data["target"] = UnitOfMeasure.objects.get(pk=cleaned_data["target"])
#         return cleaned_data


# ############
# # FOR HTMX #
# ############


# class WarehouseInfoUpdateForm(WarehouseForm, CoreHtmxModelForm):
#     pass


# class BranchUpdateForm(CoreHtmxModelForm):
#     class Meta:
#         model = Branch
#         fields = [
#             "name",
#             "organization",
#             "address_attention",
#             "address_street_1",
#             "address_street_2",
#             "address_postal_code",
#             "address_district",
#             "address_city",
#             "address_state",
#             "address_country",
#             "address_phone",
#             "is_primary",
#         ]

#     def __init__(self, *args, **kwargs):
#         super().__init__(*args, **kwargs)
#         self.fields["is_primary"].label = _("Is HQ?")


# class SinoflexUserInfoUpdateForm(CoreHtmxModelForm):
#     """
#     Basic info form for (Sinoflex) User Update under the Settings > User in the left navigation bar.
#     """

#     warehouses = forms.ModelMultipleChoiceField(
#         queryset=Warehouse.objects.all(),
#         widget=CoreGroupCheckboxSelectMultiple(),
#         required=False,
#     )

#     class Meta:
#         model = User
#         fields = [
#             "email",
#             "profile_image",
#             "first_name",
#             "last_name",
#             "name",
#             "groups",
#             "is_active",
#             "warehouses",
#         ]

#     def __init__(self, *args, **kwargs):
#         super().__init__(*args, **kwargs)

#         self.fields["email"].disabled = True
#         self.fields["email"].label = _("Email")

#         self.fields["first_name"].label = _("First Name")
#         self.fields["last_name"].label = _("Last Name")
#         self.fields["name"].label = _("Display Name")
#         self.fields["is_active"].label = _("Is Active")

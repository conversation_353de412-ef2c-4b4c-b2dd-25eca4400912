# from django.contrib.auth import get_user_model
# from django.db.models import Q
# from django.utils.translation import gettext_lazy as _

# import django_filters as filters

# from wms.cores.filters import CoreBooleanWidget
# from wms.cores.utils import get_group_choices

# User = get_user_model()


# class SinoflexUserFilter(filters.FilterSet):
#     """Filter class for User DataTables."""

#     keyword = filters.CharFilter(
#         label=_("Numbering or Status contains"),
#         method="custom_keyword_filter",
#     )
#     groups = filters.MultipleChoiceFilter(method="groups_filter", choices=[])
#     is_active = filters.BooleanFilter(widget=CoreBooleanWidget)

#     class Meta:
#         model = User
#         fields = [
#             "username",
#             "first_name",
#             "last_name",
#             "groups",
#             "is_active",
#         ]

#     def __init__(self, data=None, queryset=None, *, request=None, prefix=None):
#         super().__init__(data=data, queryset=queryset, request=request, prefix=prefix)

#         # Override choices within init function. Otherwise, it might trigger
#         # django.db.utils.ProgrammingError when you dropdb, re-createdb, run makemigrations & migrate.
#         self.filters["groups"].extra["choices"] = get_group_choices()
#         self.filters["is_active"].field.widget.attrs.update({"class": "form-control core-select2"})

#     def custom_keyword_filter(self, queryset, name, value):
#         qs = queryset

#         for keyword in value.split():
#             qs = qs.filter(
#                 Q(username__icontains=keyword) | Q(first_name__icontains=keyword) | Q(last_name__icontains=keyword)
#             )

#         return qs.distinct()

#     def groups_filter(self, queryset, name, value):
#         qs = queryset

#         qs = qs.filter(groups__in=value)

#         return qs.distinct()

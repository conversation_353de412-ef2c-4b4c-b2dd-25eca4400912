from django.contrib import admin
from django.db import models
from django.utils.translation import gettext_lazy as _

from django_extensions.db.fields import AutoSlugField
from phonenumber_field.modelfields import Phone<PERSON><PERSON>berField
from sorl.thumbnail import <PERSON>Field

from wms.cores.models import AbstractBaseModel
from wms.cores.utils import generate_thumbnail


class Organization(AbstractBaseModel):
    """Organization model for Warehouse Management System.

    Available fields:

    * created           (AbstractBaseModel => TimeStampedModel)
    * modified          (AbstractBaseModel => TimeStampedModel)
    * created_by        (AbstractBaseModel)
    * modified_by       (AbstractBaseModel)
    * name
    * slug
    * registration_no
    * logo
    * phone
    * email
    * website

    """

    name = models.CharField(verbose_name=_("Name"), max_length=255)
    slug = AutoSlugField(verbose_name=_("Slug"), populate_from=["name"], max_length=255, editable=True, unique=True)
    registration_no = models.CharField(verbose_name=_("Registration No."), max_length=24, blank=True)
    logo = ImageField(verbose_name=_("Logo"), upload_to="settings/organization", blank=True)
    phone = PhoneNumberField(verbose_name=_("Phone"), blank=True)
    email = models.EmailField(verbose_name=_("Email"), blank=True)
    website = models.URLField(verbose_name=_("Website"), max_length=255, blank=True)

    class Meta(AbstractBaseModel.Meta):
        pass

    def __str__(self):
        return self.name

    @admin.display(description=_("Company Logo"))
    def get_logo_thumb(self, img_size="x64"):
        return generate_thumbnail(self.logo, img_size)

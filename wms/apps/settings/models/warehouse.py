from django.contrib import admin
from django.db import models
from django.urls import reverse
from django.utils.functional import cached_property
from django.utils.translation import gettext_lazy as _

from django_extensions.db.fields import AutoSlugField
from sorl.thumbnail import <PERSON>Field
from treebeard.mp_tree import MP_Node

from wms.cores.models import AbstractBaseModel
from wms.cores.utils import generate_thumbnail

from wms.apps.consignors.models import Consignor
from wms.apps.inventories.models import Transaction
from wms.apps.rackings.models import Rack


class Warehouse(AbstractBaseModel, MP_Node):
    """Warehouse model for Warehouse Management System.

    Available fields:

    * created           (AbstractBaseModel => TimeStampedModel)
    * modified          (AbstractBaseModel => TimeStampedModel)
    * created_by        (AbstractBaseModel)
    * modified_by       (AbstractBaseModel)
    * path              (MP_Node)
    * depth             (MP_Node)
    * numchild          (MP_Node)
    * name
    * slug
    * branch
    * is_storage
    * photo

    """

    name = models.CharField(verbose_name=_("Warehouse Name"), max_length=256)
    slug = AutoSlugField(verbose_name=_("slug"), populate_from=["name"], max_length=255, editable=True, unique=True)
    branch = models.ForeignKey("settings.Branch", on_delete=models.CASCADE)
    is_storage = models.BooleanField(verbose_name=_("Is Storage?"), default=False)
    photo = ImageField(verbose_name=_("Warehouse Photo"), upload_to="settings/warehouse", blank=True, null=True)

    _full_name_separator = " > "

    class Meta(AbstractBaseModel.Meta):
        ordering = ["path"]

    def __str__(self):
        return f"{self.full_name}"

    def get_absolute_url(self):
        return reverse("settings:warehouses:panel", kwargs={"pk": self.pk})

    @property
    def dash_name(self):
        """Return name with dash based on depth."""
        space = "— " * (self.depth - 1)
        return f"{space}{self.name}"

    @property
    def full_name(self):
        """
        Returns a string representation of the category and it's ancestors,
        e.g. 'Books > Non-fiction > Essential programming'.

        It's rarely used in Oscar, but used to be stored as a CharField and is
        hence kept for backwards compatibility. It's also sufficiently useful
        to keep around.
        """
        names = [category.name for category in self.get_ancestors_and_self()]
        return self._full_name_separator.join(names)

    def get_ancestors_and_self(self):
        """
        Gets ancestors and includes itself. Use treebeard's get_ancestors
        if you don't want to include the category itself. It's a separate
        function as it's commonly used in templates.
        """
        if self.is_root():
            return [self]

        return list(self.get_ancestors()) + [self]

    @admin.display(description=_("Warehouse Photo"))
    def get_photo_thumb(self, img_size="x64"):
        return generate_thumbnail(self.photo, img_size)

    @cached_property
    def items(self):
        """Return items belong to this warehouse."""
        return self.warehouses.all().distinct()

    @cached_property
    def racks(self):
        """Return racks belong to this warehouse."""
        return Rack.objects.filter(warehouse=self)

    @cached_property
    def leaf_node_racks(self):
        """Return racks belong to this warehouse."""
        return Rack.objects.filter(warehouse=self, numchild=0)

    @cached_property
    def consignors(self):
        """Return consignors belong to this warehouse."""
        item_pks = self.items.values_list("pk", flat=True)
        consignors = Consignor.objects.filter(item__pk__in=item_pks).distinct()
        return consignors

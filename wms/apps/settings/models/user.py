from django.db import models


class User(models.Model):
    class Meta:

        # No database table creation or deletion  \
        # operations will be performed for this model.
        managed = False

        # disable "add", "change", "delete"
        # and "view" default permissions
        default_permissions = ()

        permissions = (
            ("view_user", "Can view user"),
            ("change_user", "Can change user"),
        )

from decimal import Decimal

from django.contrib import admin
from django.core.exceptions import ValidationError
from django.db import models
from django.urls import reverse
from django.utils.functional import cached_property
from django.utils.translation import gettext_lazy as _

from wms.cores.models import AbstractBaseModel


class UnitOfMeasure(AbstractBaseModel):
    """UnitOfMeasure model for Warehouse Management System.

    Available fields:

    * created           (AbstractBaseModel => TimeStampedModel)
    * modified          (AbstractBaseModel => TimeStampedModel)
    * created_by        (AbstractBaseModel)
    * modified_by       (AbstractBaseModel)
    * unit_name
    * symbol
    * unit_precision
    * in_use

    """

    CHOICES = [(i, i) for i in range(7)]

    unit_name = models.CharField(verbose_name=_("Unit Name"), max_length=256)
    symbol = models.CharField(verbose_name=_("Symbol"), max_length=256, unique=True)
    unit_precision = models.IntegerField(verbose_name=_("Unit Precision"), choices=CHOICES)
    in_use = models.BooleanField(
        verbose_name=_("In use?"), default=False, help_text=_("Has this UOM been used once Transaction created.")
    )

    class Meta(AbstractBaseModel.Meta):
        indexes = [
            # single index
            models.Index(fields=["symbol"]),
        ]

    def __str__(self):
        return f"{self.unit_name} [{self.symbol}]"

    def get_absolute_url(self):
        return reverse("settings:uoms:detail", kwargs={"pk": self.pk})

    @cached_property
    def total_number_of_conversion(self):
        """return total number of conversion"""
        return self.origin.all().count()


class UnitConversion(AbstractBaseModel):
    """UnitConversion model for Warehouse Management System.

    To handle each UnitOfMeasure's conversion rate.

    Available fields:

    * created           (AbstractBaseModel => TimeStampedModel)
    * modified          (AbstractBaseModel => TimeStampedModel)
    * created_by        (AbstractBaseModel)
    * modified_by       (AbstractBaseModel)
    * origin
    * target
    * rate

    origin is the origin unit and then base on conversion rate to the target
    E.g: 1 Meter(origin) is 100(conversion rate) Centimeter(target)
         1 Centimeter(origin) is 0.01(conversion rate) Meter(target)
    """

    origin = models.ForeignKey("settings.UnitOfMeasure", related_name="origin", on_delete=models.CASCADE)
    target = models.ForeignKey("settings.UnitOfMeasure", related_name="target", on_delete=models.CASCADE)
    rate = models.DecimalField(
        verbose_name=_("Conversion Rate"),
        max_digits=19,
        decimal_places=6,
        help_text=_("1 [Origin] = Conversion Rate [Target]. Example: 1 CM = 0.01 M"),
    )

    is_cleaned = False

    class Meta(AbstractBaseModel.Meta):
        unique_together = ["origin", "target"]

    def __str__(self):
        return f"1 {self.origin.unit_name} = {self.conversion_rate()} {self.target.unit_name}"

    def get_absolute_url(self):
        return reverse("settings:unit_conversions:detail", kwargs={"pk": self.pk})

    def description(self):
        return f"1 {self.origin.unit_name} = {self.conversion_rate()} {self.target.unit_name}"

    @admin.display(description=_("Conversion Rate"))
    def conversion_rate(self):
        """Return rate based on target.unit_precision."""
        return round(self.rate, self.target.unit_precision)

    def clean(self):
        """Validation on item and rejected quantity."""
        self.is_cleaned = True

        temp_rate = self.rate
        print("self.origin: ", self.origin)
        print("self.target: ", self.target)
        print("self.rate: ", self.rate)
        self.rate = round(self.rate, self.target.unit_precision)
        reverse_rate = round((Decimal("1") / (Decimal(f"{temp_rate}"))), self.origin.unit_precision)

        if self.rate.is_zero() is True:
            raise ValidationError(
                {
                    "rate": _(
                        f"Conversion rate cannot be less than 0 due to the target `{self.target.unit_name}`'s "
                        f"unit precision is {self.target.unit_precision}."
                    )
                }
            )
        elif reverse_rate.is_zero() is True:
            raise ValidationError(
                {
                    "rate": _(
                        f"Conversion rate cannot be more than 0 due to the origin `{self.origin.unit_name}`'s "
                        f"unit precision is {self.origin.unit_precision}."
                    )
                }
            )

        super().clean()

    def save(self, *args, **kwargs):
        # Update conversion rate based on target unit precision
        temp_rate = self.rate
        self.rate = round(self.rate, self.target.unit_precision)

        super().save(*args, **kwargs)

        # Get or create another way round of conversion
        model = self.__class__
        reverse_rate = round((Decimal("1") / (Decimal(f"{temp_rate}"))), self.origin.unit_precision)

        reverse_conversion = model.objects.filter(origin=self.target, target=self.origin).first()
        if not reverse_conversion:
            model.objects.create(created_by=self.created_by, origin=self.target, rate=reverse_rate, target=self.origin)
        elif reverse_conversion.rate != reverse_rate:
            reverse_conversion.rate = reverse_rate
            reverse_conversion.save(update_fields=["rate"])

from django.db import models
from django.utils.functional import cached_property
from django.utils.translation import gettext_lazy as _

from django_extensions.db.fields import AutoSlugField

from wms.cores.models import AbstractAddressModel, AbstractBaseModel


class Branch(AbstractAddressModel, AbstractBaseModel):
    """Branch model for Warehouse Management System.

    Available fields:

    * created                   (AbstractBaseModel => TimeStampedModel)
    * modified                  (AbstractBaseModel => TimeStampedModel)
    * created_by                (AbstractBaseModel)
    * modified_by               (AbstractBaseModel)
    * address_attention         (AbstractAddressModel)
    * address_street_1          (AbstractAddressModel)
    * address_street_2          (AbstractAddressModel)
    * address_postal_code       (AbstractAddressModel)
    * address_district          (AbstractAddressModel)
    * address_city              (AbstractAddressModel)
    * address_state             (AbstractAddressModel)
    * address_country           (AbstractAddressModel)
    * address_phone             (AbstractAddressModel)
    * is_primary                (AbstractAddressModel)
    * name
    * slug
    * organization

    """

    name = models.CharField(verbose_name=_("Branch Name"), max_length=255)
    slug = AutoSlugField(verbose_name=_("Slug"), populate_from=["name"], max_length=255, editable=True, unique=True)
    organization = models.ForeignKey("settings.Organization", on_delete=models.CASCADE)

    class Meta(AbstractBaseModel.Meta):
        ordering = ["-is_primary", "name"]

    def __str__(self):
        return self.name

    @cached_property
    def get_warehouse(self):
        return self.warehouse_set.filter(is_storage=True)

    def save(self, *args, **kwargs):
        model = self.__class__

        # Only 1 address can set as primary for each Branch
        if self.pk and self.is_primary:
            model.objects.exclude(pk=self.pk).filter(organization=self.organization).update(is_primary=False)

        super().save(*args, **kwargs)

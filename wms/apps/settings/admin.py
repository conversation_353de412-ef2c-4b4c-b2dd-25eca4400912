from django.contrib import admin

from treebeard.admin import TreeAdmin
from treebeard.forms import movenodeform_factory

from wms.cores.admin import BaseModelAdmin, BaseTabularInline

from .models import Branch, Organization, UnitConversion, UnitOfMeasure, Warehouse


@admin.register(Organization)
class OrganizationAdmin(BaseModelAdmin):
    """Django admin for Organization."""

    list_display = [
        "name",
        "registration_no",
        "get_logo_thumb",
        "phone",
        "email",
        "website",
    ]
    search_fields = [
        "name",
        "registration_no",
        "phone",
        "email",
    ]


@admin.register(Branch)
class BranchAdmin(BaseModelAdmin):
    """Django admin for Branch."""

    list_display = [
        "name",
        "organization",
        "is_primary",
    ]
    search_fields = [
        "name",
        "organization__name",
    ]


@admin.register(Warehouse)
class WarehouseAdmin(BaseModelAdmin, TreeAdmin):
    """Django admin for Warehouse."""

    list_display = [
        "name",
        "slug",
        "branch",
        "is_storage",
        "path",
        "depth",
        "numchild",
    ]
    search_fields = [
        "name",
        "branch__name",
    ]
    exclude = BaseModelAdmin.exclude + [
        "path",
        "depth",
        "numchild",
    ]
    form = movenodeform_factory(Warehouse)


class UnitConversionInline(BaseTabularInline):
    model = UnitConversion
    fk_name = "origin"
    extra = 1


@admin.register(UnitOfMeasure)
class UnitOfMeasureAdmin(BaseModelAdmin):
    """Django admin for UnitOfMeasure."""

    list_display = [
        "unit_name",
        "symbol",
        "unit_precision",
        "in_use",
    ]
    list_editable = [
        "symbol",
        "unit_precision",
        "in_use",
    ]
    inlines = [
        UnitConversionInline,
    ]
    search_fields = [
        "unit_name",
    ]

    class Media:
        js = ("admin/js/vendor/select2/select2.full.js",)
        css = {
            "all": (
                "admin/css/vendor/select2/select2.css",
                "admin/css/autocomplete.css",
            )
        }


@admin.register(UnitConversion)
class UnitConversionAdmin(BaseModelAdmin):
    """Django admin for UnitOfMeasure."""

    list_display = [
        "__str__",
        "origin",
        "conversion_rate",
        "target",
    ]
    search_fields = [
        "origin__unit_name",
        "target__unit_name",
    ]

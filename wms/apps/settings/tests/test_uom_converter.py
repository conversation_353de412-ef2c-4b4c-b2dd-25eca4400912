# from decimal import Decimal

# from django.test import TestCase

# from wss.apps.settings.models import UnitOfMeasure
# from wss.apps.settings.utils import uom_converter


# class UomConverterTest(TestCase):
#     fixtures = [
#         "auth_groups.json",
#         "test_fixtures.json",
#         "settings.organization.json",
#         "settings.branch.json",
#         "settings.warehouse.json",
#     ]

#     def test_uom_converter(self):
#         ea = UnitOfMeasure.objects.get(symbol="EA")
#         carton_6 = UnitOfMeasure.objects.get(symbol="CT-6")

#         # EA -> CT-6
#         converted_quantity = uom_converter(ea, carton_6, 12)
#         self.assertEqual(converted_quantity, Decimal("2.000004"))

#         converted_quantity = uom_converter(ea, carton_6, 12, skip_unit_precision=True)
#         self.assertEqual(converted_quantity, Decimal("2.000004"))

#         # EA -> EA
#         converted_quantity = uom_converter(ea, ea, 3)
#         self.assertEqual(converted_quantity, Decimal("3"))

#         converted_quantity = uom_converter(ea, ea, 3, skip_unit_precision=True)
#         self.assertEqual(converted_quantity, Decimal("3.000000"))

#         # CT-6 -> CT-6
#         converted_quantity = uom_converter(carton_6, carton_6, 3)
#         self.assertEqual(converted_quantity, Decimal("3.000000"))

#         converted_quantity = uom_converter(carton_6, carton_6, 3, skip_unit_precision=True)
#         self.assertEqual(converted_quantity, Decimal("3.000000"))

#         # CT-6 -> EA
#         converted_quantity = uom_converter(carton_6, ea, 3)
#         self.assertEqual(converted_quantity, Decimal("18"))

#         converted_quantity = uom_converter(carton_6, ea, 3, skip_unit_precision=True)
#         self.assertEqual(converted_quantity, Decimal("18.000000"))

#         # CT-6 -> EA (With decimal input quantity)
#         converted_quantity = uom_converter(carton_6, ea, 0.1)
#         self.assertEqual(converted_quantity, Decimal("1"))

#         converted_quantity = uom_converter(carton_6, ea, 0.1, skip_unit_precision=True)
#         self.assertEqual(converted_quantity, Decimal("0.600000"))

from decimal import Decimal

from django.core.exceptions import ImproperlyConfigured

from .models import UnitConversion


def uom_converter(origin_uom, target_uom, quantity, skip_unit_precision=False):
    """Unit of measure conversion with given origin UOM, target UOM and quantity.

    Unit conversion based on rate which is maintained in UnitConversion model.
    It's mainly use for transaction / balance calculation or to display quantity
    in different UOM in graph or so on.

    """

    converted_quantity = Decimal("0")

    if not isinstance(quantity, Decimal):
        quantity = Decimal(str(quantity))

    if origin_uom == target_uom:
        converted_quantity = quantity
        if skip_unit_precision is True:
            converted_quantity = round(quantity, 6)
        else:
            converted_quantity = round(quantity, target_uom.unit_precision)
    else:
        try:
            uc_obj = UnitConversion.objects.get(origin=origin_uom, target=target_uom)
        except UnitConversion.DoesNotExist:
            raise ImproperlyConfigured(
                f"Missing UOM conversion setting for {origin_uom} and {target_uom}. "
                "Please setup in Unit Conversion model."
            )
        else:
            if skip_unit_precision is True:
                converted_quantity = round(
                    Decimal(str(uc_obj.rate)) * Decimal(str(quantity)), 6
                )
            else:
                converted_quantity = round(
                    Decimal(str(uc_obj.rate)) * Decimal(str(quantity)), target_uom.unit_precision
                )

    return converted_quantity

from collections import defaultdict

from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from django.db.models import Q

from wms.apps.settings.models import Warehouse
from wms.apps.inventories.models import Stock
from wms.cores.views import Select2Pagination
from .serializers import ItemStockSerializer


class WarehouseViewSet(viewsets.ViewSet):
    """
    ViewSet for warehouse operations.
    """
    pagination_class = Select2Pagination

    @action(detail=True, methods=['get'], url_path='items')
    def stock(self, request, pk=None):
        """
        Get available stock items for a specific warehouse.
        Groups stocks by item ID to show each unique item only once.

        Query parameters:
        - search: Search term for filtering items
        - page: Page number for pagination
        - page_size: Number of items per page
        """
        # Verify warehouse exists
        warehouse = get_object_or_404(Warehouse, pk=pk)

        # Get search term from query params
        search_term = request.query_params.get('search', '')

        # Filter stock items
        queryset = Stock.objects.filter(
            warehouse=warehouse,
        ).select_related('item', 'item__uom')

        # Apply search if provided
        if search_term:
            queryset = queryset.filter(
                Q(item__name__icontains=search_term) |
                Q(item__code__icontains=search_term) |
                Q(batch_no__icontains=search_term)
            )

        # Group stocks by item ID
        item_groups = defaultdict(list)
        for stock in queryset:
            item_groups[stock.item.id].append(stock)

        # Create a list of representative stocks (one per item)
        representative_stocks = []
        for item_id, stocks in item_groups.items():
            if stocks:  # Make sure we have at least one stock
                representative_stocks.append(stocks[0])  # Use the first stock as representative

        # Apply pagination to the representative stocks
        paginator = self.pagination_class()
        paginated_queryset = paginator.paginate_queryset(representative_stocks, request)

        # Serialize with all stocks for each item
        serializer = ItemStockSerializer(
            paginated_queryset,
            many=True,
            context={'item_stocks': item_groups}
        )

        return paginator.get_paginated_response(serializer.data)

    @action(detail=True, methods=['get'], url_path='items/(?P<item_id>[^/.]+)')
    def item_detail(self, request, pk=None, item_id=None):
        """
        Get available stock for a specific warehouse and item combination.

        Parameters:
        - pk: Warehouse ID
        - item_id: Item ID

        Query parameters:
        - stock_id: Optional stock ID for preloading specific stock

        Returns details for the specific warehouse and item combination.
        If stock_id is provided, includes a 'selected_stock' field with details of the specified stock.
        """
        # Verify warehouse exists
        warehouse = get_object_or_404(Warehouse, pk=pk)

        # Get stock_id from query params if provided
        stock_id = request.query_params.get('stock_id')

        # Filter stock items for the specific warehouse and item
        queryset = Stock.objects.filter(
            warehouse=warehouse,
            item_id=item_id
        ).select_related('item', 'item__uom')

        if not queryset.exists():
            return Response({'detail': 'No stock found for this warehouse and item combination.'}, status=404)

        # Group stocks by item ID (will only have one item in this case)
        item_groups = defaultdict(list)
        for stock in queryset:
            item_groups[stock.item.id].append(stock)

        # Get the first stock as representative (there should be at least one)
        representative_stock = queryset.first()

        # Prepare the response data
        context = {'item_stocks': item_groups}

        # If stock_id is provided, find the specific stock
        selected_stock = None
        if stock_id:
            try:
                selected_stock = Stock.objects.get(id=stock_id, warehouse=warehouse, item_id=item_id)
                # Add the selected stock to the context
                context['selected_stock'] = selected_stock
            except Stock.DoesNotExist:
                # If the stock doesn't exist, we'll just continue without it
                pass

        # Serialize with all stocks for the specific item
        serializer = ItemStockSerializer(
            representative_stock,
            context=context
        )

        response_data = serializer.data

        # If we have a selected stock, add its details to the response
        if selected_stock:
            # Find the batch and stock details for the selected stock
            batch_no = selected_stock.batch_no
            expiry_date = selected_stock.expiry_date

            # Add selected stock information to the response
            response_data['selected_stock'] = {
                'id': selected_stock.id,
                'batch_no': batch_no,
                'expiry_date': expiry_date.isoformat() if expiry_date else None
            }

        return Response(response_data)

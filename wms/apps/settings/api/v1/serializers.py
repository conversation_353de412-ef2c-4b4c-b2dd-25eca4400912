from rest_framework import serializers
from django.db.models import Q
from wms.apps.inventories.models import Stock

class StockDetailSerializer(serializers.ModelSerializer):
    """Serializer for individual stock entries with specific expiry dates"""
    expiry_date = serializers.DateField(format="%Y-%m-%d", allow_null=True)
    available_balance = serializers.SerializerMethodField()
    uom_id = serializers.IntegerField(source='item.uom.id')

    class Meta:
        model = Stock
        fields = ['id', 'expiry_date', 'available_balance', 'uom_id']

    def get_available_balance(self, obj):
        return obj.available_balance

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        if representation['expiry_date'] is None:
            representation['expiry_date'] = "N/A"
        return representation

class BatchSerializer(serializers.Serializer):
    """Serializer for batch information grouping stocks by batch number"""
    batch_no = serializers.CharField()
    stocks = serializers.SerializerMethodField()

    def get_stocks(self, obj):
        """Return all stocks with this batch number"""
        stocks = obj.get('stocks', [])
        return StockDetailSerializer(stocks, many=True).data

class ItemStockSerializer(serializers.ModelSerializer):
    """
    Serializer for items with all their stocks grouped by batch.
    Each item appears only once, with all its batches and expiry dates.
    """
    id = serializers.SerializerMethodField()
    name = serializers.SerializerMethodField()
    uom = serializers.CharField(source='item.uom.symbol', read_only=True)
    uom_id = serializers.IntegerField(source='item.uom.id')
    batches = serializers.SerializerMethodField()

    class Meta:
        model = Stock
        fields = ['id', 'name', 'uom', 'uom_id', 'batches']

    def get_id(self, obj):
        """Return the item ID instead of the stock ID"""
        return obj.item.id

    def get_name(self, obj):
        """Return item name and code in format: "[code] name" """
        return f"[{obj.item.code}] {obj.item.name}"

    def get_batches(self, obj):
        """
        Return all available batches for this item in the warehouse,
        grouped by batch number with multiple expiry dates
        """
        # Get all stocks for this item from the context
        item_stocks = self.context.get('item_stocks', {}).get(obj.item.id, [])

        # Group stocks by batch_no
        batch_groups = {}
        for stock in item_stocks:
            if stock.batch_no not in batch_groups:
                batch_groups[stock.batch_no] = {
                    'batch_no': stock.batch_no,
                    'stocks': []
                }
            batch_groups[stock.batch_no]['stocks'].append(stock)

        # Serialize the batch information
        serializer = BatchSerializer(batch_groups.values(), many=True)
        return serializer.data

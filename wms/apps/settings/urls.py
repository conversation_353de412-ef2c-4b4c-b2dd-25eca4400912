from django.urls import include, path

# from wss.apps.settings.views import (
#     branch_create_view,
#     branch_delete_view,
#     branch_detail_view,
#     branch_update_view,
#     organization_detail_view,
#     organization_info_detail_view,
#     organization_info_update_view,
#     sinoflex_user_datatables_view,
#     sinoflex_user_detail_datatables_view,
#     sinoflex_user_detail_view,
#     sinoflex_user_info_detail_view,
#     sinoflex_user_info_update_view,
#     sinoflex_user_list_view,
#     unit_conversion_create_view,
#     unit_conversion_delete_view,
#     unit_conversion_detail_view,
#     unit_conversion_list_view,
#     unit_conversion_update_view,
#     unit_of_measure_create_view,
#     unit_of_measure_delete_view,
#     unit_of_measure_detail_view,
#     unit_of_measure_list_view,
#     unit_of_measure_unit_conversion_list_view,
#     unit_of_measure_update_view,
#     warehouse_create_view,
#     warehouse_delete_view,
#     warehouse_detail_view,
#     warehouse_info_detail_view,
#     warehouse_info_update_view,
#     warehouse_list_view,
#     warehouse_stock_dropdown_list_view,
#     warehouse_update_view,
# )

from wms.apps.settings.views import (
    WarehouseListView,
    WarehouseCreateView,
    WarehouseUpdateView,
    WarehouseDetailHomeView,
    WarehouseDetailView,
    WarehouseDataTableDetailView,
    UnitOfMeasureListView,
    UnitOfMeasureCreateView,
    UnitOfMeasureUpdateView,
    UnitOfMeasureDetailHomeView,
    UnitOfMeasureDetailView,
    UnitOfMeasureDataTableDetailView,
    UnitConversionListView,
    UnitConversionCreateView,
    UnitConversionUpdateView,
    UnitConversionDetailHomeView,
    UnitConversionDetailView,
    UnitConversionDataTableDetailView,
)

app_name = "settings"

warehouses_urlpatterns = [
    path("", WarehouseListView.as_view(), name="list"),
    path("create/", WarehouseCreateView.as_view(), name="create"),
    path("<int:pk>/update/", WarehouseUpdateView.as_view(), name="update"),
    path("<int:pk>/", WarehouseDataTableDetailView.as_view(), name="panel"),
    path("<int:pk>/home/", WarehouseDetailHomeView.as_view(), name="detail_home"),
    path("<int:pk>/detail/", WarehouseDetailView.as_view(), name="detail"),
    #     path("", view=warehouse_list_view, name="list"),
    #     path("create/", view=warehouse_create_view, name="create"),
    #     path("delete/<str:slug>/", view=warehouse_delete_view, name="delete"),
    #     path("detail/<str:slug>/", view=warehouse_detail_view, name="detail"),
    #     path("detail/<str:slug>/info/", view=warehouse_info_detail_view, name="info"),
    #     path("detail/<str:slug>/info/update/", view=warehouse_info_update_view, name="info_update"),
    #     path("detail/<str:slug>/update/", view=warehouse_update_view, name="update"),
    #     path("dropdown/stocks/", view=warehouse_stock_dropdown_list_view, name="stocks_dropdown"),
]
unit_of_measures_urlpatterns = [
    path("", UnitOfMeasureListView.as_view(), name="list"),
    path("create/", UnitOfMeasureCreateView.as_view(), name="create"),
    path("<int:pk>/update/", UnitOfMeasureUpdateView.as_view(), name="update"),
    path("<int:pk>/", UnitOfMeasureDataTableDetailView.as_view(), name="panel"),
    path("<int:pk>/home/", UnitOfMeasureDetailHomeView.as_view(), name="detail_home"),
    path("<int:pk>/detail/", UnitOfMeasureDetailView.as_view(), name="detail"),
    #     path("", view=unit_of_measure_list_view, name="list"),
    #     path("create/", view=unit_of_measure_create_view, name="create"),
    #     path("delete/<int:pk>/", view=unit_of_measure_delete_view, name="delete"),
    #     path("detail/<int:pk>/", view=unit_of_measure_detail_view, name="detail"),
    #     path("detail/<int:pk>/update/", view=unit_of_measure_update_view, name="update"),
    #     path("detail/<int:pk>/conversions/", view=unit_of_measure_unit_conversion_list_view, name="unit_conversions"),
]
unit_conversions_urlpatterns = [
    path("", UnitConversionListView.as_view(), name="list"),
    path("create/", UnitConversionCreateView.as_view(), name="create"),
    path("<int:pk>/update/", UnitConversionUpdateView.as_view(), name="update"),
    path("<int:pk>/", UnitConversionDataTableDetailView.as_view(), name="panel"),
    path("<int:pk>/home/", UnitConversionDetailHomeView.as_view(), name="detail_home"),
    path("<int:pk>/detail/", UnitConversionDetailView.as_view(), name="detail"),
    #     path("", view=unit_conversion_list_view, name="list"),
    #     path("create/", view=unit_conversion_create_view, name="create"),
    #     path("delete/<int:pk>/", view=unit_conversion_delete_view, name="delete"),
    #     path("detail/<int:pk>/", view=unit_conversion_detail_view, name="detail"),
    #     path("detail/<int:pk>/update/", view=unit_conversion_update_view, name="update"),
]
# organizations_urlpatterns = [
#     path("", view=organization_detail_view, name="detail"),
#     path("detail/<str:slug>/info/", view=organization_info_detail_view, name="info"),
#     path("detail/<str:slug>/info/update/", view=organization_info_update_view, name="info_update"),
# ]
# branches_urlpatterns = [
#     path("create/", view=branch_create_view, name="create"),
#     path("delete/<str:slug>/", view=branch_delete_view, name="delete"),
#     path("detail/<str:slug>/", view=branch_detail_view, name="detail"),
#     path("detail/<str:slug>/update/", view=branch_update_view, name="update"),
# ]
# users_urlpatterns = [
#     path("", view=sinoflex_user_list_view, name="list"),
#     path("detail/<str:username>/", view=sinoflex_user_detail_view, name="detail"),
#     path("detail/<str:username>/info/", view=sinoflex_user_info_detail_view, name="info"),
#     path("detail/<str:username>/info/update/", view=sinoflex_user_info_update_view, name="info_update"),
#     # For Datatables
#     path("datatables/", view=sinoflex_user_datatables_view, name="datatables"),
#     path("datatables-detail/", view=sinoflex_user_detail_datatables_view, name="datatables-detail"),
# ]

urlpatterns = [
    path("warehouses/", include((warehouses_urlpatterns, "settings.warehouse"), namespace="warehouses")),
    path(
        "uoms/",
        include((unit_of_measures_urlpatterns, "settings.unit_of_measure"), namespace="uoms"),
    ),
    path(
        "unit_conversions/",
        include((unit_conversions_urlpatterns, "settings.unit_conversion"), namespace="unit_conversions"),
    ),
    #     path("organizations/", include((organizations_urlpatterns, "settings.organization"), namespace="organizations")),
    #     path("organizations/branches/", include((branches_urlpatterns, "settings.branch"), namespace="branches")),
    #     path("users/", include((users_urlpatterns, "settings.users"), namespace="users")),
]

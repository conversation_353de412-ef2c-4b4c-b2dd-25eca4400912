from typing import ClassVar

from django.contrib import admin
from django.contrib.auth.models import AbstractUser
from django.db import models
from django.db.models import <PERSON><PERSON><PERSON>ield
from django.db.models import <PERSON><PERSON><PERSON>ield
from django.urls import reverse
from django.utils.functional import cached_property
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _

from phonenumber_field.modelfields import PhoneNumber<PERSON>ield
from sorl.thumbnail import <PERSON>Field
from wms.cores.models import SoftDeleteModel, DISPLAY_EMPTY_VALUE, AbstractBaseModel
from .managers import UserManager


class User(AbstractBaseModel, AbstractUser):
    """
    Enhanced custom user model with dual authentication (username/email) support.
    Includes soft delete functionality and extensive user profile fields.

    Available fields:

    * created           (AbstractBaseModel => TimeStampedModel)
    * modified          (AbstractBaseModel => TimeStampedModel)
    * created_by        (AbstractBaseModel)
    * modified_by       (AbstractBaseModel)
    * username          (AbstractUser)
    * first_name        (AbstractUser)
    * last_name         (AbstractUser)
    * is_staff          (AbstractUser)
    * is_active         (AbstractUser)
    * date_joined       (AbstractUser)
    * password          (AbstractUser => AbstractBaseUser)
    * last_login        (AbstractUser => AbstractBaseUser)
    * is_superuser      (AbstractUser => PermissionsMixin)
    * groups            (AbstractUser => PermissionsMixin)
    * user_permissions  (AbstractUser => PermissionsMixin)
    * profile_image
    * name
    * email
    * username
    * gender
    * dob
    * ic_no
    * passport_no
    * phone
    * job_title
    * warehouses
    * consignor_filter
    * notification_whatsapp
    * whatsapp_number
    * notification_email
    * notification_reorder_level

    """

    class Gender(models.TextChoices):
        MALE = 'M', _('Male')
        FEMALE = 'F', _('Female')
        UNDEFINED = 'U', _('Undefined')

    # Profile fields
    profile_image = ImageField(verbose_name=_("Profile Image"), upload_to="users/profile", blank=True, null=True)

    name = CharField(_("Name of User"), blank=True, max_length=255)
    email = EmailField(_("email address"), unique=True)
    username = CharField(_("username"), max_length=150, unique=True)
    gender = CharField(
        verbose_name=_("Gender"),
        max_length=1,
        choices=Gender.choices,
        default=Gender.UNDEFINED
    )

    # Personal Information
    dob = models.DateField(verbose_name=_("Date of birth"), blank=True, null=True)
    ic_no = models.CharField(verbose_name=_("Identity card no."), max_length=24, blank=True)
    passport_no = models.CharField(verbose_name=_("Passport no."), max_length=24, blank=True)
    phone = PhoneNumberField(verbose_name=_("Phone no."), blank=True)
    job_title = models.CharField(verbose_name=_("Job title"), max_length=24, blank=True)

    # User's settings
    warehouses = models.ManyToManyField(
        "settings.Warehouse",
        related_name="user_warehouses",
        verbose_name=_("Warehouses"),
        blank=True,
        help_text=_("Indicate the User have permission on which Warehouses."),
    )
    consignor_filter = models.ForeignKey(
        "consignors.Consignor",
        on_delete=models.CASCADE,
        blank=True,
        null=True,
        help_text=_("Filter all whole system's objects based on this consignor."),
    )

    # Notification settings
    notification_whatsapp = models.BooleanField(verbose_name=_("Notification on WhatsApp?"), default=False)
    whatsapp_number = PhoneNumberField(verbose_name=_("WhatsApp number"), blank=True)
    notification_email = models.BooleanField(verbose_name=_("Notification on email?"), default=False)
    notification_reorder_level = models.BooleanField(
        verbose_name=_("Notification on inventory reorder level?"),
        default=False
    )

    USERNAME_FIELD = "username"
    REQUIRED_FIELDS = ["email"]

    objects: ClassVar[UserManager] = UserManager()

    class Meta:
        permissions = [
            ("manage_user", "Can manage users"),
        ]
        verbose_name = _("user")
        verbose_name_plural = _("users")

    def __str__(self):
        return self.get_display_name

    @admin.display(description=_("Display Name"))
    @cached_property
    def get_display_name(self):
        """Return full name or short name or username.

        Returns:
            str: self.get_full_name()

        """
        return self.name or self.get_full_name() or self.get_short_name() or self.username

    def get_absolute_url(self) -> str:
        """
        Get URL for user's detail view.
        Returns:
            str: URL for user detail.

        """
        return reverse("users:detail", kwargs={"username": self.username})

    @admin.display(description=_("Groups"))
    @cached_property
    def get_groups(self, joiner="<br />", hide_sinoflex=True):
        """Return groups name belongs to the user.

        Returns:
            str: html format of groups joined with default <br />.

        """
        if hide_sinoflex:
            return format_html(joiner.join([x.name for x in self.groups.all() if x.name != "SINOFLEX"]))
        else:
            return format_html(joiner.join([x.name for x in self.groups.all()]))

    @admin.display(description=_("Warehouses"))
    @cached_property
    def html_warehouses_display(self):
        """Return nice HTML display for all warehouses."""
        warehouses = self.warehouses.all()
        return (
            format_html("<br />".join([warehouse.__str__().replace(" ", "&nbsp;") for warehouse in warehouses]))
            or DISPLAY_EMPTY_VALUE
        )

    def save(self, *args, **kwargs):
        # Ensure username is always lowercase
        self.username = self.username.lower()
        self.email = self.email.lower()
        super().save(*args, **kwargs)

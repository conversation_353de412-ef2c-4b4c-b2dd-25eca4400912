# Generated by Django 5.1 on 2025-03-15 13:08

import django.db.models.deletion
import django.utils.timezone
import model_utils.fields
import phonenumber_field.modelfields
import sorl.thumbnail.fields
import wms.apps.users.managers
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('cores', '0001_create_extensions'),
        ('auth', '0012_alter_user_first_name_max_length'),
        ('consignors', '0001_initial'),
        ('settings', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('profile_image', sorl.thumbnail.fields.ImageField(blank=True, null=True, upload_to='users/profile', verbose_name='Profile Image')),
                ('name', models.CharField(blank=True, max_length=255, verbose_name='Name of User')),
                ('email', models.EmailField(max_length=254, unique=True, verbose_name='email address')),
                ('username', models.CharField(max_length=150, unique=True, verbose_name='username')),
                ('gender', models.CharField(choices=[('M', 'Male'), ('F', 'Female'), ('U', 'Undefined')], default='U', max_length=1, verbose_name='Gender')),
                ('dob', models.DateField(blank=True, null=True, verbose_name='Date of birth')),
                ('ic_no', models.CharField(blank=True, max_length=24, verbose_name='Identity card no.')),
                ('passport_no', models.CharField(blank=True, max_length=24, verbose_name='Passport no.')),
                ('phone', phonenumber_field.modelfields.PhoneNumberField(blank=True, max_length=128, region=None, verbose_name='Phone no.')),
                ('job_title', models.CharField(blank=True, max_length=24, verbose_name='Job title')),
                ('notification_whatsapp', models.BooleanField(default=False, verbose_name='Notification on WhatsApp?')),
                ('whatsapp_number', phonenumber_field.modelfields.PhoneNumberField(blank=True, max_length=128, region=None, verbose_name='WhatsApp number')),
                ('notification_email', models.BooleanField(default=False, verbose_name='Notification on email?')),
                ('notification_reorder_level', models.BooleanField(default=False, verbose_name='Notification on inventory reorder level?')),
                ('consignor_filter', models.ForeignKey(blank=True, help_text="Filter all whole system's objects based on this consignor.", null=True, on_delete=django.db.models.deletion.CASCADE, to='consignors.consignor')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('modified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Modified by')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
                ('warehouses', models.ManyToManyField(blank=True, help_text='Indicate the User have permission on which Warehouses.', related_name='user_warehouses', to='settings.warehouse', verbose_name='Warehouses')),
            ],
            options={
                'verbose_name': 'user',
                'verbose_name_plural': 'users',
                'permissions': [('manage_user', 'Can manage users')],
            },
            managers=[
                ('objects', wms.apps.users.managers.UserManager()),
            ],
        ),
    ]

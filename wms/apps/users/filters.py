from django.db.models import Q
import django_filters
from django.contrib.auth import get_user_model

User = get_user_model()


class UserFilter(django_filters.FilterSet):
    query = django_filters.CharFilter(method='universal_search',
                                      label="")

    class Meta:
        model = User
        fields = ['query']

    def universal_search(self, queryset, name, value):
        return User.objects.filter(
            Q(name__icontains=value) | Q(email__icontains=value)
        )

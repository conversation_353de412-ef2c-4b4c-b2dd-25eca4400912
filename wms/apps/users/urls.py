from django.shortcuts import redirect
from django.urls import path, reverse

from wms.apps.users.menu import MENU_DATA
from wms.apps.users.views import (
    user_detail_view,
    user_update_view,
    UserManageListView, IndexView,
)

app_name = "users"

urlpatterns = [
    path("manage/", view=UserManageListView.as_view(), name="manage"),
    path("update/<int:pk>", view=user_update_view, name="update"),
    path("detail/<int:pk>", view=user_detail_view, name="detail"),
    path("redirect/", lambda request: redirect(next(
        (reverse(submenu['url_name']) for submenu in MENU_DATA['submenus'] if all(request.user.has_perm(perm) for perm in submenu.get('permissions', []))),
        '/'  # Default redirect if no permissions match (adjust as needed)
    )), name="redirect"),  # Add name='home' here
    path("index/", IndexView.as_view(), name="user_htmx"),
]

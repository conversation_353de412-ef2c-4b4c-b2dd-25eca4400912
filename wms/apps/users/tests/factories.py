import factory
from django.contrib.auth import get_user_model
from django.utils import timezone

User = get_user_model()


class UserFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = User

    username = factory.Sequence(lambda n: f'user{n}')
    email = factory.LazyAttribute(lambda obj: f'{obj.username}@example.com')
    is_active = True
    date_joined = factory.LazyFunction(timezone.now)
    password = factory.PostGenerationMethodCall('set_password', 'password123')
    created = factory.LazyFunction(timezone.now)
    modified = factory.LazyFunction(timezone.now)

    @factory.post_generation
    def groups(self, create, extracted, **kwargs):
        if not create:
            return

        if extracted:
            for group in extracted:
                self.groups.add(group)

    @classmethod
    def _create(cls, model_class, *args, **kwargs):
        """Override the default _create to handle the case of creating a superuser."""
        if kwargs.get('is_superuser'):
            manager = cls._get_manager(model_class)
            return manager.create_superuser(*args, **kwargs)
        return super()._create(model_class, *args, **kwargs)

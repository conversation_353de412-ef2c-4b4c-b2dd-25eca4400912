from django.contrib.auth import get_user_model
from django.urls import reverse, NoReverseMatch
from django_tables2 import tables

User = get_user_model()


class UserHTMxTable(tables.Table):
    section_title = "User"
    section_desc = "Manage system users"
    section_name = "User"
    selectable = True

    # Example of excluding a column from export
    actions = tables.Column(empty_values=(), orderable=False, exclude_from_export=True)

    @property
    def create_url(self):
        try:
            return reverse('users:user_htmx')
        except NoReverseMatch:
            return None

    class Meta:
        model = User
        template_name = "tables/table_htmx.html"
        fields = ('id', 'username', 'email', 'date_joined')
        exclude = ('password', 'last_login')

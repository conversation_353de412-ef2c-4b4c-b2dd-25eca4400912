from typing import TYPE_CHECKING

from django.db import models
from django.contrib.auth.hashers import make_password
from django.contrib.auth.models import UserManager as DjangoUserManager

if TYPE_CHECKING:
    from .models import User  # noqa: F401


class UserManager(DjangoUserManager["User"]):
    """Custom manager for the User model with email/username authentication."""

    def get_by_natural_key(self, username):
        """Allow login with either username or email"""
        return self.get(models.Q(username__iexact=username) |
                        models.Q(email__iexact=username))

    def _create_user(self, username: str, email: str, password: str | None, **extra_fields):
        """
        Create and save a user with the given username, email, and password.
        Ensures proper validation and normalization of inputs.
        """
        if not username:
            raise ValueError("The given username must be set")
        if not email:
            raise ValueError("The given email must be set")

        email = self.normalize_email(email)
        username = username.lower().strip()

        # Check if username exists case-insensitively
        if self.filter(username__iexact=username).exists():
            raise ValueError("Username already exists")

        # Check if email exists case-insensitively
        if self.filter(email__iexact=email).exists():
            raise ValueError("Email already exists")

        user = self.model(username=username, email=email, **extra_fields)
        user.password = make_password(password)
        user.save(using=self._db)
        return user

    # noinspection PyMethodOverriding
    def create_user(self, username: str, email: str, password: str | None = None, **extra_fields) -> "User":
        """
        Create and save a regular user.
        """
        extra_fields.setdefault("is_staff", False)
        extra_fields.setdefault("is_superuser", False)
        return self._create_user(username, email, password, **extra_fields)

    # noinspection PyMethodOverriding
    def create_superuser(self, username: str, email: str, password: str | None = None, **extra_fields) -> "User":
        """
        Create and save a superuser.
        """
        extra_fields.setdefault("is_staff", True)
        extra_fields.setdefault("is_superuser", True)

        if extra_fields.get("is_staff") is not True:
            raise ValueError("Superuser must have is_staff=True.")
        if extra_fields.get("is_superuser") is not True:
            raise ValueError("Superuser must have is_superuser=True.")

        return self._create_user(username, email, password, **extra_fields)

# Generated by Django 5.1 on 2025-03-15 13:12

import django.db.models.deletion
import django.utils.timezone
import model_utils.fields
import wms.cores.models
import wms.cores.utils
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('cores', '0001_create_extensions'),
        ('inventories', '0001_initial'),
        ('settings', '0002_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Transfer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('system_number', models.CharField(blank=True, max_length=32, verbose_name='System Number')),
                ('transfer_datetime', models.DateTimeField(default=wms.cores.utils.localtime_now, verbose_name='Transfer Date Time')),
                ('status', models.CharField(choices=[('Draft', 'Draft'), ('Processing', 'Processing'), ('Completed', 'Completed'), ('Completed with reject', 'Completed with reject')], default='Draft', max_length=32, verbose_name='Status')),
                ('remark', models.TextField(blank=True, max_length=512, verbose_name='Remark')),
                ('customer_reference', models.CharField(blank=True, max_length=64, verbose_name='Customer Reference')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('issued_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Modified by')),
                ('transfer_from', models.ForeignKey(limit_choices_to={'is_storage': True}, on_delete=django.db.models.deletion.PROTECT, related_name='transfer_from', to='settings.warehouse')),
                ('transfer_to', models.ForeignKey(limit_choices_to={'is_storage': True}, on_delete=django.db.models.deletion.PROTECT, related_name='transfer_to', to='settings.warehouse')),
            ],
            options={
                'ordering': ['-system_number'],
                'get_latest_by': 'created',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='TransferItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('sort_order', models.PositiveIntegerField(default=0, verbose_name='Ordering')),
                ('quantity', models.DecimalField(decimal_places=6, default=Decimal('0'), max_digits=19, validators=[wms.cores.models.greater_than_zero], verbose_name='Quantity')),
                ('remark', models.TextField(blank=True, max_length=512, verbose_name='Remark')),
                ('status', models.CharField(choices=[('Draft', 'Draft'), ('Approved', 'Approved'), ('Rejected', 'Rejected')], default='Draft', max_length=32, verbose_name='Status')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('modified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Modified by')),
                ('stock', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventories.stock')),
                ('transfer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='transfers.transfer')),
                ('uom', models.ForeignKey(help_text='The item will be measured in terms of this unit (e.g.: kg, pcs, box).', on_delete=django.db.models.deletion.RESTRICT, to='settings.unitofmeasure', verbose_name='UOM')),
            ],
            options={
                'ordering': ['sort_order'],
                'get_latest_by': 'created',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='TransferStockInOut',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('transfer_datetime', models.DateTimeField(verbose_name='Stock Out Date Time')),
                ('remark', models.TextField(blank=True, max_length=512, verbose_name='Remark')),
                ('approved_by', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('modified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Modified by')),
                ('transaction_in', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='transactions_in', to='inventories.transaction')),
                ('transaction_out', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='transactions_out', to='inventories.transaction')),
                ('transfer_item', models.ForeignKey(help_text="Each Transfer Item indicate a relation to item's released quantity.", on_delete=django.db.models.deletion.CASCADE, to='transfers.transferitem')),
                ('uom', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='settings.unitofmeasure', verbose_name='UOM')),
            ],
            options={
                'ordering': ['transfer_datetime', 'pk'],
                'get_latest_by': 'created',
                'abstract': False,
            },
        ),
    ]

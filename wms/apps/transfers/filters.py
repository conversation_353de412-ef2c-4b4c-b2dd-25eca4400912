import datetime

from django.db.models import Q, Sum, OuterRef, Subquery, Value, DecimalField
from django.db.models.functions import Coalesce
from django.utils.translation import gettext_lazy as _
from django_filters import filters, FilterSet, ChoiceFilter

from wms.apps.inventories.models import Item, Transaction, Stock
from wms.apps.transfers.models import Transfer
from wms.cores.forms.widget import CoreSelectWidget, CoreSelectMultipleWidget, CoreTextWidget, CoreDateWidget, \
    CoreDateRangeWidget


class TransferFilter(FilterSet):
    """
    Filter class for Item list view.
    Provides filtering capabilities for Item attributes.
    """
    date_range = filters.DateFromToRangeFilter(
        label=_("Transfer Date Range"),
        field_name="transfer_datetime",
        widget=CoreDateRangeWidget()
    )

    status = filters.ChoiceFilter(
        choices=Transfer.Status.choices,
        label=_("Status"),
        widget=CoreSelectWidget()
    )

    class Meta:
        model = Transfer
        fields = ['date_range', 'status']


class TransferDataTableFilter(FilterSet):
    """
    Filter class for Transfer DataTable list view.
    Provides filtering capabilities for Stock attributes.

    Widget attrs:
        'data-auto-submit' only work with filter_auto.html
    """
    status = filters.ChoiceFilter(
        choices=Transfer.Status.choices,
        label=_("Status"),
        widget=CoreSelectWidget(attrs={
            'data-auto-submit': 'true'
        })
    )

    class Meta:
        model = Transfer
        fields = ['status']

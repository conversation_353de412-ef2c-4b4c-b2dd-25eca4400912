from django.contrib import messages
from django.http import JsonResponse
from django.shortcuts import get_object_or_404, render
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.views.decorators.http import require_POST

from wms.apps.transfers.models import TransferItem, TransferStockInOut


def handle_htmx_response(request, transfer_item):
    """
    Helper function to handle HTMX responses for transfer item actions.
    Returns the appropriate HTML response based on the target element.
    """
    context = {'record': transfer_item, 'request': request}

    # Check which element is being targeted
    target = request.headers.get('HX-Target', '')

    if 'transfer-item-status' in target:
        # Return just the status column
        return render(request, 'transfers/transfer_item_status.html', context).content.decode('utf-8')
    elif 'transfer-item-actions' in target:
        # Return just the actions column
        return render(request, 'transfers/transfer_item_actions.html', context).content.decode('utf-8')
    elif 'transfer-item-action-by' in target:
        # Return just the action_by column
        return render(request, 'transfers/transfer_item_action_by.html', context).content.decode('utf-8')
    else:
        # Default response with all columns - using render().content.decode() to avoid JSON serialization issues with Alpine.js directives
        actions_html = render(request, 'transfers/transfer_item_actions.html', context).content.decode('utf-8')
        status_html = render(request, 'transfers/transfer_item_status.html', context).content.decode('utf-8')
        action_by_html = render(request, 'transfers/transfer_item_action_by.html', context).content.decode('utf-8')

        response_data = {
            'actions': actions_html,
            'status': status_html,
            'action_by': action_by_html
        }
        return JsonResponse(response_data)


def transfer_item_approve_form(request, pk):
    """
    Display the form for approving a transfer item.
    """
    transfer_item = get_object_or_404(TransferItem, pk=pk)

    context = {
        'transfer_item': transfer_item,
        'request': request,
    }

    return render(request, 'transfers/transfer_item_approve_form.html', context)


def transfer_item_reject_form(request, pk):
    """
    Display the form for rejecting a transfer item.
    """
    transfer_item = get_object_or_404(TransferItem, pk=pk)

    context = {
        'transfer_item': transfer_item,
        'request': request,
    }

    return render(request, 'transfers/transfer_item_reject_form.html', context)


@require_POST
def transfer_item_approve(request, pk):
    """
    Approve a transfer item and create the necessary stock in/out transactions.
    """
    transfer_item = get_object_or_404(TransferItem, pk=pk)

    if transfer_item.status != TransferItem.Status.DRAFT:
        return JsonResponse({
            'success': False,
            'message': _('This transfer item has already been processed.')
        }, status=400)

    # Get the remark from the form
    remark = request.POST.get('remark', '')

    # Update the transfer item with the remark
    transfer_item.remark = remark
    transfer_item.save(update_fields=['remark'])

    # Create TransferStockInOut record
    TransferStockInOut.objects.create(
        transfer_item=transfer_item,
        approved_by=request.user,
        transfer_datetime=timezone.now(),
        uom=transfer_item.uom,
        remark=remark
    )

    # The status update is handled in the TransferStockInOut.save() method
    # which will also update the parent Transfer status
    # messages.success(request, _('Transfer item approved successfully.'))

    # For HTMX requests, return the updated cells HTML
    if request.headers.get('HX-Request'):
        return handle_htmx_response(request, transfer_item)

    # For non-HTMX requests, return JSON
    return JsonResponse({
        'success': True,
        'message': _('Transfer item approved successfully.')
    })


@require_POST
def transfer_item_reject(request, pk):
    """
    Reject a transfer item.
    """
    transfer_item = get_object_or_404(TransferItem, pk=pk)

    if transfer_item.status != TransferItem.Status.DRAFT:
        return JsonResponse({
            'success': False,
            'message': _('This transfer item has already been processed.')
        }, status=400)

    # Update the transfer item status
    transfer_item.status = TransferItem.Status.REJECTED
    transfer_item.remark = request.POST.get('remark', '')
    transfer_item.save()

    # messages.success(request, _('Transfer item rejected successfully.'))

    # For HTMX requests, return the updated cells HTML
    if request.headers.get('HX-Request'):
        return handle_htmx_response(request, transfer_item)

    # For non-HTMX requests, return JSON
    return JsonResponse({
        'success': True,
        'message': _('Transfer item rejected successfully.')
    })

import itertools

import django_tables2 as tables
from django.conf import settings
from django.urls import reverse, NoReverseMatch

from wms.apps.transfers.models import Transfer, TransferItem
from wms.cores.columns import HTMXColumn
from django.utils.translation import gettext_lazy as _


class TransferTable(tables.Table):
    section_title = "Transfer Lists"
    section_name = "Transfer"

    system_number = tables.LinkColumn(
        "transfers:notes:panel",
        args=[tables.utils.A("pk")],
        verbose_name="Number"
    )
    status = tables.Column()
    transfer_datetime = tables.DateTimeColumn(
        verbose_name=_("Transfer Date"),
        format=settings.DATETIME_FORMAT,
        attrs={
            "td": {
                "class": "whitespace-nowrap",
            },
        }
    )

    created = tables.DateTimeColumn(
        format=settings.DATETIME_FORMAT,
        attrs={
            "td": {
                "class": "whitespace-nowrap",
            },
        }
    )

    actions = tables.TemplateColumn(
        verbose_name=_("Actions"),
        template_name="tables/table_actions_column.html",
        orderable=False
    )

    @property
    def create_url(self):
        try:
            return reverse('transfers:notes:create')
        except NoReverseMatch:
            return None

    # def get_row_class(self, record):
    #     """Returns the appropriate class based on the status value"""
    #     return record.html_status_class if record else 'bg-inherit'

    class Meta:
        model = Transfer
        order_by = '-system_number'
        template_name = "tables/table_htmx.html"
        # row_attrs = {
        #     'class': lambda record: TransferTable.get_row_class(None, record)
        # }
        fields = (
            "system_number",
            "status",
            "issued_by",
            "transfer_datetime",
            "created",
        )

    # def render_status(self, value, record):
    #     """Return nice html label display for status."""
    #     return record.html_status_display

    def render_actions(self, value, record, column):
        """
        Conditionally show edit button based on Transfer status.
        Only show edit button when Transfer status is not COMPLETED or COMPLETED_WITH_REJECT.
        """
        from django.template.loader import get_template

        # Create a context dictionary for the template
        context = {'view_url': "transfers:notes:panel"}

        # Only add edit_url to context if the transfer is not completed
        if record.status in [Transfer.Status.DRAFT]:
            context['edit_url'] = "transfers:notes:update"

        # Always add the record to the context
        context['record'] = record

        # Render the template with our custom context
        template = get_template("tables/table_actions_column.html")
        return template.render(context)

    def render_status(self, record):
        """Return nice html label display for status."""
        return record.html_status_display


class TransferDetailTable(tables.Table):
    """
    Table for displaying transfer details in the detail view.
    Used by TransferDataTableDetailView.
    """
    system_number = HTMXColumn(
        url_name="transfers:notes:detail_home",
        target_id="detail-panel",
        verbose_name=_("Number"),
        push_url=True,
        push_url_name="transfers:notes:panel",
    )
    status = tables.Column()

    class Meta:
        model = Transfer
        fields = ("system_number", "status")
        order_by = '-system_number'
        template_name = "tables/table_htmx.html"

    def __init__(self, *args, object_pk=None, **kwargs):
        super().__init__(*args, **kwargs)
        self.object_pk = object_pk

    def render_status(self, record):
        """Return nice html label display for status."""
        return record.html_status_display


class TransferItemTable(tables.Table):
    row_number = tables.Column(
        verbose_name="#",
        empty_values=(),
        orderable=True,
        order_by="sort_order"
    )
    item_code = tables.Column(
        verbose_name=_("Code"),
        accessor="stock.item.code",
    )
    item_name = tables.Column(verbose_name=_("Name"), accessor="stock.item.name")
    batch_no = tables.Column(
        verbose_name=_("Batch"),
        accessor="stock.batch_no",
        attrs={
            "td": {"class": "font-bold"},
        }
    )
    expiry_date = tables.DateTimeColumn(
        verbose_name=_("Expiry"),
        accessor="stock.expiry_date",
        format=settings.DATE_FORMAT,
        attrs={
            "td": {
                "class": "whitespace-nowrap",
            },
        }
    )
    quantity = tables.Column(verbose_name=_("Qty"), orderable=False)
    uom = tables.Column(verbose_name=_("UOM"), orderable=False)
    status = tables.Column(
        verbose_name=_("Status"),
        attrs={
            "td": {"class": "status-cell"}
        }
    )
    action_by = tables.TemplateColumn(
        verbose_name=_("Action By"),
        template_name="transfers/transfer_item_action_by.html",
        orderable=False,
        attrs={
            "td": {"class": "action-by-cell"}
        }
    )

    actions = tables.TemplateColumn(
        verbose_name=_("Actions"),
        template_name="transfers/transfer_item_actions.html",
        orderable=False
    )

    class Meta:
        model = TransferItem
        order_by = 'sort_order'
        template_name = "tables/table_htmx.html"
        fields = (
            "row_number",
            "item_code",
            "item_name",
            "batch_no",
            "expiry_date",
            "quantity",
            "uom",
            "status",
            "action_by",
            "actions"
        )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.counter = 0

    def render_row_number(self, value, record):
        # Use the get_position method from the model to get the fixed position
        # This ensures the row number stays consistent regardless of sorting
        return str(record.get_position)

    def render_quantity(self, value, record):
        """Format the quantity with appropriate precision."""
        return f"{round(record.quantity, record.uom.unit_precision)}"

    def render_uom(self, value, record):
        """Display the UOM symbol."""
        return record.uom.symbol

    def render_status(self, value, record):
        """Display the status in a readable format."""
        from django.template.loader import get_template

        # Create a context dictionary for the template
        context = {'record': record}

        # Render the template with our custom context
        template = get_template("transfers/transfer_item_status.html")
        return template.render(context)

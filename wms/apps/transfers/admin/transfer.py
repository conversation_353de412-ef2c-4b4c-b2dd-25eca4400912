from django.contrib import admin

from wms.cores.admin import BaseModelAdmin, BaseTabularInline

from ..models import Transfer, TransferItem, TransferStockInOut


class TransferItemInline(BaseTabularInline):
    model = TransferItem
    extra = 1


@admin.register(Transfer)
class TransferAdmin(BaseModelAdmin):
    """Django admin for Transfer."""

    list_display = [
        "system_number",
        "issued_by",
        "transfer_datetime",
        "transfer_from",
        "transfer_to",
        "status",
        "remark",
    ]
    inlines = [TransferItemInline]
    search_fields = ["system_number"]
    list_filter = ["status"]


@admin.register(TransferItem)
class TransferItemAdmin(BaseModelAdmin):
    """Django admin for TransferItem."""

    list_display = [
        "transfer",
        "stock",
        "uom",
        "quantity",
        "remark",
        "status",
    ]
    search_fields = ["stock__item__name", "stock__warehouse__name" "stock__batch_no"]
    list_filter = ["status"]


@admin.register(TransferStockInOut)
class TransferStockInOutAdmin(BaseModelAdmin):
    list_display = [
        "transfer_item",
        "approved_by",
        "transfer_datetime",
        "uom",
        "remark",
        "transaction_in",
        "transaction_out",
    ]
    raw_id_fields = (
        "transfer_item",
        "transaction_in",
        "transaction_out",
    )
    search_fields = (
        "transfer_item__stock__item__name",
        "transfer_item__stock__warehouse__name",
    )
    list_filter = [
        "transfer_item__stock__warehouse",
        "transfer_item__stock__item",
        "transfer_item__stock__batch_no",
    ]
    date_hierarchy = "transfer_datetime"

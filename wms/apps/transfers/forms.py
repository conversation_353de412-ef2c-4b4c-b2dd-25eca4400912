from django import forms
from django.core.exceptions import ValidationError
from django.utils.timezone import localtime

from wms.apps.settings.models import UnitOfMeasure
from wms.apps.transfers.models import Transfer, TransferItem
from wms.cores.forms.fields import CoreModelForm, CoreChoiceField, FormFieldSize, CoreCharField
from wms.apps.inventories.models import Stock
from wms.cores.forms.widget import CoreSelectWidget, CoreNumberWidget, CoreTextWidget
from django.forms import ChoiceField


class TransferForm(CoreModelForm):
    class Meta:
        model = Transfer
        fields = [
            "issued_by",
            "transfer_datetime",
            "transfer_from",
            "transfer_to",
            "remark",
        ]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.fields["issued_by"].disabled = True
        self.fields["transfer_datetime"].label = 'Time'
        self.fields["remark"].widget.attrs.update({"cols": 70, "rows": 4})
        # Add an ID to transfer_from for easier JS selection
        self.fields['transfer_from'].widget.attrs.update({'id': 'id_transfer_from'})

        # Explicitly set the datetime input format
        self.fields['transfer_datetime'].input_formats = ['%Y-%m-%d %I:%M:%S %p']


class NoValidationChoiceField(ChoiceField):
    def validate(self, value):
        pass  # Skip validation


class TransferItemForm(CoreModelForm):
    stock = forms.CharField(
        disabled=True,
        required=False,
        widget=CoreSelectWidget(
            attrs={
                'class': 'transfer-item-stock w-xl',
                'data-api-url': '/api/warehouses/{warehouse_id}/items/'
            }
        )
    )

    batch_no = NoValidationChoiceField(
        choices=[],
        required=False,
        widget=CoreSelectWidget(
            attrs={
                'class': 'transfer-item-batch w-40',
                'disabled': 'disabled',
            }
        )
    )

    expiry_date = NoValidationChoiceField(
        choices=[],
        required=False,
        widget=CoreSelectWidget(
            attrs={
                'class': 'transfer-item-expiry w-40',
                'disabled': 'disabled',
            }
        )
    )

    quantity = forms.IntegerField(
        required=True,
        widget=CoreNumberWidget(
            attrs={
                'class': 'transfer-item-quantity w-25',
            },
        )
    )
    balance = forms.CharField(
        required=False,
        widget=CoreTextWidget(
            attrs={
                'class': 'transfer-item-balance w-25',
                'disabled': 'disabled',
                'readonly': 'readonly'
            }
        )
    )
    uom = forms.CharField(
        required=False,
        widget=CoreTextWidget(
            attrs={
                'class': 'transfer-item-uom w-25',
                'disabled': 'disabled',
                'readonly': 'readonly'
            }
        )
    )

    stock_id = forms.IntegerField(
        widget=forms.HiddenInput(),
        required=False,
    )
    item_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)
    uom_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)

    class Meta:
        model = TransferItem
        fields = [
            "stock",
            "batch_no",
            "expiry_date",
            "quantity",
            "balance",
            "uom",
        ]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # If we have an instance with data, we need to populate the fields
        if self.instance and self.instance.pk:
            # If editing an existing transfer item, we need to set the initial values
            if self.instance.stock:
                warehouse_id = self.instance.transfer.transfer_from.id if self.instance.transfer.transfer_from else None

                if warehouse_id:
                    # Update the API URL with the actual warehouse ID
                    self.fields['stock'].widget.attrs['data-api-url'] = self.fields['stock'].widget.attrs[
                        'data-api-url'].format(warehouse_id=warehouse_id)

                    # Set initial values for hidden fields
                    self.initial['stock_id'] = self.instance.stock.id
                    self.initial['item_id'] = self.instance.stock.item.id
                    if self.instance.uom:
                        self.initial['uom_id'] = self.instance.uom.id

                    # Enable the stock field and set its initial value
                    self.fields['stock'].disabled = False
                    self.fields['stock'].initial = self.instance.stock

                    # Set the UOM display value
                    if self.instance.uom:
                        self.initial['uom'] = str(self.instance.uom)

    def clean(self):
        """Main form cleaning method"""
        cleaned_data = super().clean()
        stock_id = cleaned_data.get('stock_id')

        if not stock_id:
            self.add_error('stock', 'Please select a stock [Batch, Expiry, UOM]')
            self.add_error('batch_no', '')
            self.add_error('expiry_date', '')
            self.add_error('uom', '')
            raise ValidationError("Stock selection is required")
        try:
            cleaned_data.update(self.clean_stock_and_uom())
        except ValidationError as e:
            # Add the error to the stock field specifically
            self.add_error('stock', str(e))
            raise
        except Exception as e:
            self.add_error('stock', f"Error processing form: {str(e)}")
            raise ValidationError(f"Error processing form: {str(e)}")

        return cleaned_data

    def clean_stock_and_uom(self):
        """Handle stock and UOM validation with proper error handling"""
        cleaned_data = self.cleaned_data
        stock_id = cleaned_data.get('stock_id')
        item_id = cleaned_data.get('item_id')
        uom_id = cleaned_data.get('uom_id')

        def get_warehouse():
            """Get warehouse from transfer instance"""
            if not hasattr(self, 'instance') or not self.instance or \
                not hasattr(self.instance, 'transfer') or not self.instance.transfer:
                raise ValidationError("Invalid transfer instance")

            warehouse = self.instance.transfer.transfer_from
            if not warehouse:
                raise ValidationError("No warehouse selected for transfer")
            return warehouse

        def get_stock_by_id():
            """Attempt to get stock directly by ID"""
            try:
                return Stock.objects.get(id=stock_id)
            except Stock.DoesNotExist:
                return None

        def find_stock_by_item():
            """Find stock by item in warehouse"""
            if not item_id:
                raise ValidationError("Invalid stock selection")

            warehouse = get_warehouse()
            stock = Stock.objects.filter(warehouse=warehouse, item_id=item_id).first()
            if not stock:
                raise ValidationError("No stock found for this item in the selected warehouse")
            return stock

        def validate_uom():
            """Validate UOM selection"""
            if not uom_id:
                raise ValidationError("UOM selection is required when stock is selected")

            try:
                from wms.apps.settings.models import UnitOfMeasure
                return UnitOfMeasure.objects.get(id=uom_id)
            except UnitOfMeasure.DoesNotExist:
                raise ValidationError("Invalid UOM selection")

        try:
            # Try to get stock and validate
            stock = get_stock_by_id() or find_stock_by_item()
            if not stock:
                self.add_error('stock', 'Invalid stock selection')
                raise ValidationError("Invalid stock selection")

            cleaned_data['stock'] = stock
            cleaned_data['stock_id'] = stock.id

            # Validate UOM
            try:
                cleaned_data['uom'] = validate_uom()
            except ValidationError as e:
                self.add_error('uom', str(e))
                raise

            return cleaned_data

        except ValidationError as e:
            raise e
        except Exception as e:
            self.add_error('stock', f"Error processing stock: {str(e)}")
            raise ValidationError(f"Error processing stock: {str(e)}")

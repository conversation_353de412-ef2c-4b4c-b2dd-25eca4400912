import logging

from django.db.models.signals import post_save
from django.dispatch import receiver

from wms.cores.actstream import register_stream

from .models import Transfer, TransferItem, TransferStockInOut

logger = logging.getLogger(__name__)


#################
# FOR ACTSTREAM #
#################

register_stream(Transfer, logger=logger)
register_stream(TransferItem, logger=logger, parent_field="transfer")
register_stream(TransferStockInOut, logger=logger)


###################
# FOR APP SIGNALS #
###################


@receiver(post_save, sender=Transfer)
def generate_system_number(sender, instance, created=False, **kwargs):
    """To generate numbering on instance."""
    if created:
        instance.generate_system_number()
        logger.info(f"SIGNAL: {sender.__name__}: generated numbering for {instance}")

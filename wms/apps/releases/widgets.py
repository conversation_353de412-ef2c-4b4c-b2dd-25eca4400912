# from wss.cores.widgets import CoreGroupCheckboxSelectMultiple


# class WROWarehouseGroupCheckboxSelectMultipleMixin:
#     """This is a mix-in for admin to change `ManyToManyField` of Category to use CoreGroupCheckboxSelectMultiple."""

#     def formfield_for_dbfield(self, db_field, **kwargs):
#         if db_field.name == "warehouses":
#             return db_field.formfield(widget=CoreGroupCheckboxSelectMultiple())

#         return super().formfield_for_dbfield(db_field, **kwargs)

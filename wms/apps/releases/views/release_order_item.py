import json
from decimal import Decimal, InvalidOperation

from django.contrib import messages
from django.http import HttpResponse
from django.shortcuts import get_object_or_404, render
from django.template.loader import render_to_string
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.views.decorators.http import require_POST
from django.urls import reverse

from wms.cores.views import CoreCreateView
from wms.apps.releases.forms.release_order_item_pick import ReleaseOrderItemPickForm
from wms.apps.releases.forms.release_order_item_expected_quantity import ReleaseOrderItemExpectedQuantityForm
from wms.apps.releases.forms.release_order_item_add import ReleaseOrderItemAddForm
from wms.apps.releases.models import WarehouseReleaseOrderItem, WarehouseReleaseOrder, WarehouseReleaseOrderItemPicker
from wms.apps.settings.models import UnitOfMeasure
from wms.apps.inventories.models import Transaction, Item


class ReleaseOrderItemPickFormView(CoreCreateView):
    """
    Display and process the form for picking a release order item.
    """
    model = WarehouseReleaseOrderItemPicker
    form_class = ReleaseOrderItemPickForm
    template_name = 'releases/partials/release_order_item_pick_form.html'
    success_url = 'releases:orders:detail'

    def dispatch(self, request, *args, **kwargs):
        # Get the release order item
        self.release_order_item = get_object_or_404(WarehouseReleaseOrderItem, pk=self.kwargs['pk'])

        # Check if the parent order is in 'NEW' status
        if self.release_order_item.release_order.status not in [WarehouseReleaseOrder.Status.NEW,
                                                                WarehouseReleaseOrder.Status.PROCESSING]:
            if self.request.htmx:
                headers = {
                    'HX-Trigger': json.dumps({
                        "closeModalEvent": None,
                        "showNotificationEvent": {
                            "message": "Picking is only allowed for Release Orders in 'New' status.",
                            "type": "error"
                        }
                    })
                }
                return HttpResponse(status=400, headers=headers)
            messages.error(request, _("Picking is only allowed for Release Orders in 'New' status."))
            # Return an empty response or a message indicating the restriction
            return HttpResponse(status=204)  # No content, or render a message template

        return super().dispatch(request, *args, **kwargs)

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['release_order_item'] = self.release_order_item
        return kwargs

    def form_valid(self, form):
        from wms.apps.inventories.models import Stock

        try:
            # Set required fields before saving
            form.instance.release_order_item = self.release_order_item
            form.instance.uom = self.release_order_item.uom
            form.instance.picked_datetime = timezone.now()
            form.instance.picked_by = self.request.user
            form.instance.batch_no = self.release_order_item.batch_no
            form.instance.expiry_date = self.release_order_item.expiry_date

            # Find the matching stock record
            warehouse = form.cleaned_data['warehouse']
            stock_filter = {
                'item': self.release_order_item.item,
                'warehouse': warehouse,
            }

            # Add batch_no if specified
            if self.release_order_item.batch_no:
                stock_filter['batch_no'] = self.release_order_item.batch_no

            # Add expiry_date if specified
            if self.release_order_item.expiry_date:
                stock_filter['expiry_date'] = self.release_order_item.expiry_date

            # Try to find the matching stock
            stock = Stock.objects.filter(**stock_filter).first()
            if not stock:
                # If no stock found, add an error and return form_invalid
                form.add_error('warehouse',
                               _("No matching stock found in this warehouse with the specified batch and expiry date."))
                return self.form_invalid(form)

            form.instance.stock = stock

            # Set quantity and system_quantity equal to pick_quantity initially
            # system_quantity may be adjusted by the model's save method if needed
            pick_qty = Decimal(str(form.cleaned_data['pick_quantity']))
            form.instance.quantity = pick_qty
            form.instance.system_quantity = pick_qty

            # save the form data into DB
            response = super().form_valid(form)

            # Refresh the release_order_item to get updated values
            self.release_order_item.refresh_from_db()

            # Check if this is an HTMX request
            if self.request.headers.get('HX-Request'):
                # For HTMX requests, return a JSON response with updated HTML fragments

                # Prepare context for templates
                context = {'record': self.release_order_item, 'request': self.request}
                actions_id = f"release-order-item-actions-{self.release_order_item.pk}"
                status_id = f"release-order-item-status-{self.release_order_item.pk}"
                percentage_id = f"release-order-item-percentage-{self.release_order_item.pk}"
                action_by_id = f"release-order-item-action-by-{self.release_order_item.pk}"
                expected_quantity_id = f"release-order-item-expected-quantity-{self.release_order_item.pk}"

                actions_html = render_to_string('releases/partials/release_order_item_actions.html', context,
                                                request=self.request)
                status_html = render_to_string('releases/partials/release_order_item_status.html', context,
                                               request=self.request)
                percentage_html = render_to_string('releases/partials/release_order_item_percentage.html', context,
                                                   request=self.request)
                action_by_html = render_to_string('releases/partials/release_order_item_action_by.html', context,
                                                  request=self.request)
                expected_quantity_html = render_to_string('releases/partials/release_order_item_expected_quantity.html',
                                                          context,
                                                          request=self.request)

                # Return JSON response with all HTML fragments
                response_html = f"""
                <div id="{actions_id}" hx-swap-oob="true">{actions_html}</div>
                <div id="{status_id}" hx-swap-oob="true">{status_html}</div>
                <div id="{percentage_id}" hx-swap-oob="true">{percentage_html}</div>
                <div id="{action_by_id}" hx-swap-oob="true">{action_by_html}</div>
                <div id="{expected_quantity_id}" hx-swap-oob="true">{expected_quantity_html}</div>
                <div></div>
                """

                trigger_data = {
                    "closeModalEvent": None,
                    "showNotificationEvent": {
                        "message": "Item picked successfully!",
                        "type": "success"
                    }
                }

                headers = {
                    'HX-Trigger': json.dumps(trigger_data)
                }
                response = HttpResponse(response_html, headers=headers)
                return response

            # For regular form submissions, return the standard redirect response
            return response

        except Exception as e:
            # If any exception occurs during processing, add it as a form error
            form.add_error(None, str(e))
            return self.form_invalid(form)

    def get_success_url(self):
        # Redirect to the release order detail page
        return reverse('releases:orders:detail', kwargs={'pk': self.release_order_item.release_order.pk})

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get the available warehouses from the form
        available_warehouses = self.get_form().fields['warehouse'].queryset

        # Get the total stock for this item
        item_stock_total = Transaction.objects.stock_total_by_item(item=self.release_order_item.item)

        # Get stock by warehouse for this item, filtered by available warehouses and matching batch/expiry
        from wms.apps.inventories.models import Stock

        # Build filter for stocks matching the release order item's batch and expiry
        stock_filter = {
            'item': self.release_order_item.item,
            'warehouse__in': available_warehouses,
        }

        # Add batch_no if specified
        if self.release_order_item.batch_no:
            stock_filter['batch_no'] = self.release_order_item.batch_no

        # Add expiry_date if specified
        if self.release_order_item.expiry_date:
            stock_filter['expiry_date'] = self.release_order_item.expiry_date

        # Get matching stocks
        matching_stocks = Stock.objects.filter(**stock_filter)

        # Prepare warehouse stock data
        warehouses_stock = []
        for stock in matching_stocks:
            # Get the available balance
            stock_balance = Transaction.objects.balance_by_stock(stock)
            if hasattr(stock, 'available_balance'):
                stock_balance = stock.available_balance

            # Only include warehouses with available stock
            if stock_balance > 0:
                # Create a dictionary similar to what sum_group_warehouse_by_item returns
                warehouse_stock = {
                    'warehouse_id': stock.warehouse.id,
                    'warehouse_name': stock.warehouse.name,
                    'full_warehouse_path': stock.warehouse.get_full_path() if hasattr(stock.warehouse,
                                                                                      'get_full_path') else [],
                    'total_quantity': stock_balance,
                    'uom_symbol': self.release_order_item.uom.symbol,
                    'batch_no': stock.batch_no,
                    'expiry_date': stock.expiry_date,
                }
                warehouses_stock.append(warehouse_stock)

        context.update({
            'release_order_item': self.release_order_item,
            'item_stock_total': item_stock_total,
            'warehouses_stock': warehouses_stock,
        })

        return context


def release_order_item_delete_form(request, pk):
    """
    Display the confirmation form for deleting a release order item.
    """
    release_order_item = get_object_or_404(WarehouseReleaseOrderItem, pk=pk)

    # Check if the item has any picks
    if release_order_item.total_picker_system_quantity > 0:
        if request.headers.get('HX-Request'):
            headers = {
                'HX-Trigger': json.dumps({
                    "closeModalEvent": None,
                    "showNotificationEvent": {
                        "message": "Deletion is only allowed for items with no picks.",
                        "type": "error"
                    }
                })
            }
            return HttpResponse(status=400, headers=headers)
        messages.error(request, _("Deletion is only allowed for items with no picks."))
        # Return an empty response or a message indicating the restriction
        return HttpResponse(status=204)  # No content, or render a message template

    # Check if this is the last item in the release order
    item_count = release_order_item.release_order.warehouse_release_order_items.count()
    if item_count <= 1:
        if request.headers.get('HX-Request'):
            headers = {
                'HX-Trigger': json.dumps({
                    "closeModalEvent": None,
                    "showNotificationEvent": {
                        "message": "Cannot delete the last item in a release order.",
                        "type": "error"
                    }
                })
            }
            return HttpResponse(status=400, headers=headers)
        messages.error(request, _("Cannot delete the last item in a release order."))
        # Return an empty response or a message indicating the restriction
        return HttpResponse(status=204)  # No content, or render a message template

    context = {
        'release_order_item': release_order_item,
        'request': request,
    }

    return render(request, 'releases/partials/release_order_item_delete_form.html', context)


@require_POST
def release_order_item_delete(request, pk):
    """
    Delete a release order item.
    """
    release_order_item = get_object_or_404(WarehouseReleaseOrderItem, pk=pk)

    # Check if the release order is in New status
    if release_order_item.release_order.status != 'New':
        if request.headers.get('HX-Request'):
            headers = {
                'HX-Trigger': json.dumps({
                    "closeModalEvent": None,
                    "showNotificationEvent": {
                        "message": "Deletion is only allowed for Release Orders in 'New' status.",
                        "type": "error"
                    }
                })
            }
            return HttpResponse(status=400, headers=headers)
        messages.error(request, _("Deletion is only allowed for Release Orders in 'New' status."))
        # Return an empty response or a message indicating the restriction
        # Use 400 Bad Request as the action is invalid at this state
        return HttpResponse(status=400, content="Deletion not allowed for this order status.")

    # Check if the item has any picks
    if release_order_item.total_picker_system_quantity > 0:
        if request.headers.get('HX-Request'):
            headers = {
                'HX-Trigger': json.dumps({
                    "closeModalEvent": None,
                    "showNotificationEvent": {
                        "message": "Deletion is only allowed for items with no picks.",
                        "type": "error"
                    }
                })
            }
            return HttpResponse(status=400, headers=headers)
        messages.error(request, _("Deletion is only allowed for items with no picks."))
        # Return an empty response or a message indicating the restriction
        # Use 400 Bad Request as the action is invalid at this state
        return HttpResponse(status=400, content="Deletion not allowed for items with picks.")

    # Check if this is the last item in the release order
    item_count = release_order_item.release_order.warehouse_release_order_items.count()
    if item_count <= 1:
        if request.headers.get('HX-Request'):
            headers = {
                'HX-Trigger': json.dumps({
                    "closeModalEvent": None,
                    "showNotificationEvent": {
                        "message": "Cannot delete the last item in a release order.",
                        "type": "error"
                    }
                })
            }
            return HttpResponse(status=400, headers=headers)
        messages.error(request, _("Cannot delete the last item in a release order."))
        # Return an empty response or a message indicating the restriction
        # Use 400 Bad Request as the action is invalid at this state
        return HttpResponse(status=400, content="Cannot delete the last item in a release order.")

    try:
        release_order_item.delete()
        response = HttpResponse(status=200)
        if request.headers.get('HX-Request'):
            trigger_data = {
                "closeModalEvent": None,
                "showNotificationEvent": {
                    "message": "Item deleted successfully!",
                    "type": "success"
                }
            }
            headers = {
                'HX-Trigger': json.dumps(trigger_data)
            }
            response = HttpResponse(status=200, headers=headers)
        return response
    except Exception as e:
        messages.error(request, _("Failed to delete release order item: %(error)s") % {'error': str(e)})
        # Return an error response for HTMX
        return HttpResponse(status=500, content=f"Error deleting item: {str(e)}")


def release_order_item_approve_form(request, pk):
    """
    Display the form for approving a release order item.
    """
    release_order_item = get_object_or_404(WarehouseReleaseOrderItem, pk=pk)

    # Check if the item is eligible for approval (pick quantity equals expected quantity)
    if release_order_item.total_picker_system_quantity != release_order_item.expected_quantity:
        messages.error(request,
                       _("Approval is only allowed when pick quantity equals expected quantity."))
        return HttpResponse(status=400, content="Item not eligible for approval.")

    # Check if the item is already approved
    if release_order_item.status == WarehouseReleaseOrderItem.Status.APPROVED:
        messages.error(request, _("This item is already approved."))
        return HttpResponse(status=400, content="Item already approved.")

    context = {
        'release_order_item': release_order_item,
        'request': request,
    }

    return render(request, 'releases/partials/release_order_item_approve_form.html', context)


def release_order_item_expected_quantity_form(request, pk):
    """
    Display the form for updating the expected quantity of a release order item.
    """
    release_order_item = get_object_or_404(WarehouseReleaseOrderItem, pk=pk)

    # Check if the item is in a status that allows updating the expected quantity
    if release_order_item.status not in [WarehouseReleaseOrderItem.Status.NEW,
                                         WarehouseReleaseOrderItem.Status.PROCESSING]:
        messages.error(request, _("Expected quantity can only be updated for items in 'New' or 'Processing' status."))
        return HttpResponse(status=400, content="Item not eligible for expected quantity update.")

    # Initialize the form with the current item
    form = ReleaseOrderItemExpectedQuantityForm(instance=release_order_item)

    context = {
        'release_order_item': release_order_item,
        'form': form,
        'request': request,
    }

    return render(request, 'releases/partials/release_order_item_expected_quantity_form.html', context)


@require_POST
def release_order_item_expected_quantity_update(request, pk):
    """
    Process the update of a release order item's expected quantity.
    """
    release_order_item = get_object_or_404(WarehouseReleaseOrderItem, pk=pk)

    # Check if the item is in a status that allows updating the expected quantity
    if release_order_item.status not in [WarehouseReleaseOrderItem.Status.NEW,
                                         WarehouseReleaseOrderItem.Status.PROCESSING]:
        messages.error(request, _("Expected quantity can only be updated for items in 'New' or 'Processing' status."))
        return HttpResponse(status=400, content="Item not eligible for expected quantity update.")

    # Process the form
    try:
        form = ReleaseOrderItemExpectedQuantityForm(request.POST, instance=release_order_item)

        if form.is_valid():
            form.save()
        else:
            release_order_item.quantity = Decimal(request.POST.get('expected_quantity'))
            context = {
                'release_order_item': release_order_item,
                'form': form,
                'request': request,
            }
            return render(request, 'releases/partials/release_order_item_expected_quantity_form.html', context)

        # For HTMX requests, return updated HTML fragments
        if request.headers.get('HX-Request'):
            # Prepare context for templates
            context = {'record': release_order_item, 'request': request}
            actions_id = f"release-order-item-actions-{release_order_item.pk}"
            expected_quantity_id = f"release-order-item-expected-quantity-{release_order_item.pk}"
            percentage_id = f"release-order-item-percentage-{release_order_item.pk}"

            actions_html = render_to_string('releases/partials/release_order_item_actions.html', context,
                                            request=request)
            expected_quantity_html = render_to_string('releases/partials/release_order_item_expected_quantity.html',
                                                      context, request=request)
            percentage_html = render_to_string('releases/partials/release_order_item_percentage.html', context,
                                               request=request)

            # Return JSON response with all HTML fragments
            response_html = f"""
            <div id="{actions_id}" hx-swap-oob="true">{actions_html}</div>
            <div id="{expected_quantity_id}" hx-swap-oob="true">{expected_quantity_html}</div>
            <div id="{percentage_id}" hx-swap-oob="true">{percentage_html}</div>
            <div></div>
            """

            trigger_data = {
                "closeModalEvent": None,
                "showNotificationEvent": {
                    "message": "Expected quantity updated successfully!",
                    "type": "success"
                }
            }

            headers = {
                'HX-Trigger': json.dumps(trigger_data)
            }
            response = HttpResponse(response_html, headers=headers)
            return response

        # For regular form submissions, return a success message
        messages.success(request, _("Release order item expected quantity updated successfully."))
        return HttpResponse(status=200)
    except (ValueError, InvalidOperation) as e:
        messages.error(request, _("Failed to update expected quantity: %(error)s") % {'error': str(e)})
        return HttpResponse(status=400, content=f"Error updating expected quantity: {str(e)}")
    except Exception as e:
        messages.error(request, _("Failed to update expected quantity: %(error)s") % {'error': str(e)})
        return HttpResponse(status=500, content=f"Error updating expected quantity: {str(e)}")


def release_order_item_approve(request, pk):
    """
    Process the approval of a release order item.
    """
    release_order_item = get_object_or_404(WarehouseReleaseOrderItem, pk=pk)

    # Check if the item is eligible for approval (pick quantity equals expected quantity)
    if release_order_item.total_picker_system_quantity != release_order_item.expected_quantity:
        messages.error(request,
                       _("Approval is only allowed when pick quantity equals expected quantity."))
        return HttpResponse(status=400, content="Item not eligible for approval.")

    # Get the remark from the form
    remark = request.POST.get('remark', '')

    try:
        # Update the item status
        release_order_item.status = WarehouseReleaseOrderItem.Status.APPROVED
        release_order_item.approved_by = request.user
        release_order_item.remark = remark
        release_order_item.save()

        # For HTMX requests, return updated HTML fragments
        if request.headers.get('HX-Request'):
            # Prepare context for templates
            context = {'record': release_order_item, 'request': request}
            actions_id = f"release-order-item-actions-{release_order_item.pk}"
            status_id = f"release-order-item-status-{release_order_item.pk}"
            percentage_id = f"release-order-item-percentage-{release_order_item.pk}"
            action_by_id = f"release-order-item-action-by-{release_order_item.pk}"
            expected_quantity_id = f"release-order-item-expected-quantity-{release_order_item.pk}"

            actions_html = render_to_string('releases/partials/release_order_item_actions.html', context,
                                            request=request)
            status_html = render_to_string('releases/partials/release_order_item_status.html', context, request=request)
            percentage_html = render_to_string('releases/partials/release_order_item_percentage.html', context,
                                               request=request)
            action_by_html = render_to_string('releases/partials/release_order_item_action_by.html', context,
                                              request=request)
            expected_quantity_html = render_to_string('releases/partials/release_order_item_expected_quantity.html',
                                                      context, request=request)

            # Return JSON response with all HTML fragments
            response_html = f"""
            <div id="{actions_id}" hx-swap-oob="true">{actions_html}</div>
            <div id="{status_id}" hx-swap-oob="true">{status_html}</div>
            <div id="{percentage_id}" hx-swap-oob="true">{percentage_html}</div>
            <div id="{action_by_id}" hx-swap-oob="true">{action_by_html}</div>
            <div id="{expected_quantity_id}" hx-swap-oob="true">{expected_quantity_html}</div>
            <div></div>
            """

            trigger_data = {
                "closeModalEvent": None,
                "showNotificationEvent": {
                    "message": "Item approved successfully!",
                    "type": "success"
                }
            }

            # Check if all items in the release order are approved
            release_order = release_order_item.release_order
            all_items = release_order.warehouse_release_order_items.all()
            all_approved = all(item.status == WarehouseReleaseOrderItem.Status.APPROVED for item in all_items)

            # If all items are approved, add a trigger to refresh the tab content
            if all_approved:
                # Add a trigger to refresh the tab content
                detail_url = reverse('releases:orders:detail', kwargs={'pk': release_order.pk})
                trigger_data['refreshTabContent'] = {
                    'url': detail_url
                }

            headers = {
                'HX-Trigger': json.dumps(trigger_data)
            }
            response = HttpResponse(response_html, headers=headers)
            return response

        # For regular form submissions, return a success message
        messages.success(request, _("Release order item approved successfully."))
        return HttpResponse(status=200)
    except Exception as e:
        messages.error(request, _("Failed to approve release order item: %(error)s") % {'error': str(e)})
        return HttpResponse(status=500, content=f"Error approving item: {str(e)}")


class ReleaseOrderItemAddView(CoreCreateView):
    """
    Display and process the form for adding a new release order item.
    """
    model = WarehouseReleaseOrderItem
    form_class = ReleaseOrderItemAddForm
    template_name = 'releases/partials/release_order_item_add_form.html'
    success_url = 'releases:orders:detail'

    def dispatch(self, request, *args, **kwargs):
        # Get the release order
        self.release_order = get_object_or_404(WarehouseReleaseOrder, pk=self.kwargs['pk'])

        # Check if the release order is in 'NEW' status
        if self.release_order.status not in [WarehouseReleaseOrder.Status.NEW, WarehouseReleaseOrder.Status.PROCESSING]:
            if self.request.htmx:
                headers = {
                    'HX-Trigger': json.dumps({
                        "closeModalEvent": None,
                        "showNotificationEvent": {
                            "message": "Items can only be added to Release Orders in 'New' or 'Processing' status.",
                            "type": "error"
                        }
                    })
                }
                return HttpResponse(status=400, headers=headers)
            messages.error(request, _("Items can only be added to Release Orders in 'New' or 'Processing' status."))
            return HttpResponse(status=400, content="Release order not eligible for adding items.")

        return super().dispatch(request, *args, **kwargs)

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['release_order'] = self.release_order
        return kwargs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['release_order'] = self.release_order
        return context

    def form_valid(self, form):
        try:
            # Get the item and UOM from the form
            item_id = form.cleaned_data.get('item_id')
            uom_id = form.cleaned_data.get('uom_id')

            # Get the actual objects
            item = get_object_or_404(Item, pk=item_id)
            uom = get_object_or_404(UnitOfMeasure, pk=uom_id)

            # Create the release order item
            release_order_item = form.save(commit=False)
            release_order_item.item = item
            release_order_item.uom = uom
            release_order_item.release_order = self.release_order
            release_order_item.created_by = self.request.user
            release_order_item.batch_no = form.cleaned_data.get('batch_no')
            release_order_item.expiry_date = form.cleaned_data.get('expiry_date')
            release_order_item.save()

            # For HTMX requests, return a success response and trigger refreshes
            if self.request.headers.get('HX-Request'):
                # Get the detail URL for the release order
                detail_url = reverse('releases:orders:detail', kwargs={'pk': self.release_order.pk})

                # Set up HTMX triggers for success notification, modal close, and tab refresh
                trigger_data = {
                    "closeModalEvent": None,
                    "showNotificationEvent": {
                        "message": "Item added successfully!",
                        "type": "success"
                    },
                    "refreshTabContent": {
                        "url": detail_url
                    }
                }

                headers = {
                    'HX-Trigger': json.dumps(trigger_data)
                }

                # Return an empty response with headers - no need for row_html since we're refreshing the whole tab
                return HttpResponse('', headers=headers, status=200)

            # For regular form submissions, return the standard redirect response
            return super().form_valid(form)

        except Exception as e:
            # If any exception occurs during processing, add it as a form error
            form.add_error(None, str(e))
            return self.form_invalid(form)

    def form_invalid(self, form):
        # For HTMX requests, return the form with errors
        if self.request.headers.get('HX-Request'):
            return self.render_to_response(self.get_context_data(form=form))

        # For regular form submissions, return the standard response
        return super().form_invalid(form)

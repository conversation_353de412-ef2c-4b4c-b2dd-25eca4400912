from django.db import models
from django.utils.translation import gettext_lazy as _

from wms.cores.models import AbstractBaseModel, AbstractSystemNumberModel


class DeliveryOrder(AbstractSystemNumberModel, AbstractBaseModel):
    """DeliveryOrder model for Warehouse Smart System.

    Available fields:

    * created                       (AbstractBaseModel => TimeStampedModel)
    * modified                      (AbstractBaseModel => TimeStampedModel)
    * created_by                    (AbstractBaseModel)
    * modified_by                   (AbstractBaseModel)
    * system_number                 (AbstractSystemNumberModel)
    * warehouse_release_order

    """

    warehouse_release_order = models.OneToOneField(
        "releases.WarehouseReleaseOrder", verbose_name=_("Warehouse Release Order"), on_delete=models.CASCADE
    )

    class Meta(AbstractBaseModel.Meta):
        ordering = ["-system_number"]

    def __str__(self):
        return f"{self.system_number}"

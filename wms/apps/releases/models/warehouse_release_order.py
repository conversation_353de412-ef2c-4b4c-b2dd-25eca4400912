import decimal
import os
from decimal import Decimal

from django.conf import settings
from django.contrib.contenttypes.models import ContentType
from django.contrib.postgres.indexes import GinIndex
from django.core.exceptions import ValidationError
from django.core.validators import FileExtensionValidator
from django.db import models, transaction
from django.db.models import Q, QuerySet, Sum
from django.db.models.functions import Coalesce
from django.urls import reverse
from django.utils.functional import cached_property
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _

from extra_settings.models import Setting
# from notifications.models import Notification

from wms.cores.models import (
    DISPLAY_EMPTY_VALUE,
    AbstractBaseModel,
    AbstractSystemNumberModel,
    AbstractSortableModel,
    greater_than_zero,
)
from wms.cores.utils import localtime_now, calculate_base_uom_to_expected_conversion_uom

from wms.apps.inventories.models import Stock, Transaction
# from wms.apps.rackings.models import Rack
# from wms.apps.rackings.models.rack import RackTransaction
from wms.apps.settings.utils import uom_converter
from wms.integrated_clients.fmc.outbound_actions.fmc_edi_confirm import FMCEDIConfirmation
from wms.integrated_clients.mmm.outbound_actions.mmm_edi_confirm import MMMEDIConfirmation

from .delivery_order import DeliveryOrder

UPLOAD_PATH = "warehouse_release_orders"


class WarehouseReleaseOrder(AbstractSystemNumberModel, AbstractBaseModel):
    """WarehouseReleaseOrder model for Warehouse Management System.

    Available fields:

    ~ created                                   (AbstractBaseModel => TimeStampedModel)
    ~ modified                                  (AbstractBaseModel => TimeStampedModel)
    ~ created_by                                (AbstractBaseModel)
    ~ modified_by                               (AbstractBaseModel)
    ~ system_number                             (AbstractSystemNumberModel)
    ### INTERNAL INFO ###
    ~ issued_by                                 (FK: User)
    ~ consignee                                 (FK: Consignee)
    ~ release_datetime
    ~ status
    ### GENERAL INFO ###
    ~ customer_document_no
    ~ customer_reference
    ~ tag
    ### PDF INFO ###
    ~ show_shipper_info
    ~ total_cartons
    ~ total_weight
    ### SHIPPER INFO ###
    ~ shipper_address
    ~ shipper_phone
    ~ shipper_mobile
    ~ shipper_attn
    ### SHIPPING INFO ###
    ~ shipping_address
    ~ shipping_phone
    ~ shipping_mobile
    ~ shipping_attn
    ### FILEFIELD TO STORE FROM EDI FILE ###
    ~ imported_outbound_file
    ### WAREHOUSE SETTINGS ###
    ~ warehouses                                (m2m: Warehouse)
    ### WRO/DO BELONG IN WHICH PICKING LIST ###
    ~ picking_list                              (FK: PickingList)
    ### FLAG INFO ###
    ~ is_delivery_order
    ~ is_edi_confirmation_sent
    ~ is_from_edi
    ~ is_reverse_pgi
    ~ is_return
    ### CONSIGNOR'S SIDE INFORMATION ###
    ~ consignor_outbound_delivery_no
    ~ consignor_picking_list_no
    ~ consignor_sales_order_no
    ~ consignor_customer_requisition_no
    ~ consignor_ppl_date
    ~ consignor_system_delivery_date
    ### EXTRA INFO ###
    ~ remark

    """

    class Status(models.TextChoices):
        NEW = "New", _("New")
        PROCESSING = "Processing", _("Processing")
        READY_TO_PRINT = "Ready To Print", _("Print")
        READY_TO_RELEASE = "Ready To Release", _("Release")
        OBSOLETE = "Obsolete", _("Obsolete")
        COMPLETED = "Completed", _("Completed")
        COMPLETED_WITH_REJECT = "Completed with reject", _("Completed with reject")

    # internal info
    issued_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.PROTECT)
    consignee = models.ForeignKey("consignees.Consignee", on_delete=models.PROTECT)
    release_datetime = models.DateTimeField(verbose_name=_("Release Date Time"), default=localtime_now)
    status = models.CharField(verbose_name=_("Status"), max_length=32, choices=Status.choices, default=Status.NEW)

    # general info
    customer_document_no = models.CharField(verbose_name=_("Customer Document No"), max_length=32, blank=True)
    customer_reference = models.CharField(verbose_name=_("Customer Reference"), max_length=64, blank=True)
    tag = models.CharField(verbose_name=_("Tag"), max_length=256, blank=True)

    # pdf info
    show_shipper_info = models.BooleanField(verbose_name=_("Show Shipper Info?"), default=True)
    total_cartons = models.CharField(verbose_name=_("Total Cartons"), max_length=128, blank=True)
    total_weight = models.CharField(verbose_name=_("Total Weight"), max_length=128, blank=True)
    # shipper info
    shipper_address = models.TextField(verbose_name=_("Shipper Address"), max_length=512, blank=True)
    shipper_phone = models.CharField(verbose_name=_("Shipper Phone"), max_length=128, blank=True)
    shipper_mobile = models.CharField(verbose_name=_("Shipper Mobile"), max_length=128, blank=True)
    shipper_attn = models.CharField(verbose_name=_("Shipper Attn"), max_length=256, blank=True)
    # shipping info
    shipping_address = models.TextField(verbose_name=_("Shipping Address"), max_length=512, blank=True)
    shipping_phone = models.CharField(verbose_name=_("Shipping Phone"), max_length=128, blank=True)
    shipping_mobile = models.CharField(verbose_name=_("Shipping Mobile"), max_length=128, blank=True)
    shipping_attn = models.CharField(verbose_name=_("Shipping Attn"), max_length=256, blank=True)

    # file field that holds incoming file from EDI
    imported_outbound_file = models.FileField(
        verbose_name="Imported Outbound File",
        blank=True,
        null=True,
        upload_to=UPLOAD_PATH,
        validators=[
            FileExtensionValidator(
                allowed_extensions=[
                    "txt",
                    "xml",
                ]
            )
        ],
    )
    # Warehouse settings
    warehouses = models.ManyToManyField(
        "settings.Warehouse", related_name="wro_warehouses", verbose_name=_("Warehouses"), blank=True
    )

    # indicates this WRO/DO belong in which picking list
    picking_list = models.ForeignKey(
        "pickings.PickingList",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text=_("To indicate if this WRO is being release through which picking list"),
    )

    # flag info
    is_delivery_order = models.BooleanField(verbose_name=_("Is Delivery Order?"), default=False)
    is_edi_confirmation_sent = models.BooleanField(
        verbose_name=_("Is EDI Confirmation Sent?"),
        default=False,
        help_text=_("To indicate EDI confirmation sent or not"),
    )
    is_from_edi = models.BooleanField(
        verbose_name=_("Is From EDI?"),
        default=False,
        help_text=_("To differentiate WRO is manually created or from EDI"),
    )
    is_reverse_pgi = models.BooleanField(
        verbose_name=_("Is Reverse PGI?"),
        default=False,
        help_text=_("To indicate whether the WRO have been reverse transaction through Adjustment")
    )
    is_return = models.BooleanField(
        verbose_name=_("Is Return?"),
        default=False,
        help_text=_("To indicate whether the WRO have been returned transaction through GRN")
    )

    # consignor's side information
    consignor_outbound_delivery_no = models.CharField(
        verbose_name=_("Outbound Delivery No"), max_length=256, blank=True
    )
    consignor_picking_list_no = models.CharField(verbose_name=_("Picking List No"), max_length=256, blank=True)
    consignor_sales_order_no = models.CharField(verbose_name=_("Sales Order No"), max_length=256, blank=True)
    consignor_customer_requisition_no = models.CharField(
        verbose_name=_("Customer Requisition No"), max_length=256, blank=True
    )
    consignor_ppl_date = models.DateField(verbose_name=_("PPL Posted date"), blank=True, null=True)
    consignor_system_delivery_date = models.DateField(verbose_name=_("System Delivery Date"), blank=True, null=True)

    # extra info
    remark = models.TextField(verbose_name=_("Remark"), max_length=512, blank=True)

    class Meta(AbstractBaseModel.Meta):
        ordering = ["-system_number"]
        indexes = [
            # NOTE: M2M can't be index, it's auto indexed
            # GinIndex for `icontains` searches
            GinIndex(name="wro_customer_reference_gin", fields=["customer_reference"], opclasses=["gin_trgm_ops"]),
            GinIndex(name="wro_remark_gin", fields=["remark"], opclasses=["gin_trgm_ops"]),
            # single index
            models.Index(fields=["status"]),
            models.Index(fields=["consignee"]),
            models.Index(fields=["created"]),
            models.Index(fields=["is_from_edi"]),
            # composite index
            models.Index(fields=["consignor_outbound_delivery_no", "is_from_edi", "consignee"]),  # used in FMC EDI
            models.Index(fields=["consignee", "consignor_outbound_delivery_no"]),  # used in FMC EDI

        ]

    def __str__(self):
        if self.consignor_outbound_delivery_no:
            return f"{self.system_number} :: {self.consignor_outbound_delivery_no} :: {self.consignee}"
        else:
            return f"{self.system_number} :: {self.consignee}"

    def get_absolute_url(self):
        return reverse("releases:orders:detail", kwargs={"pk": self.pk})

    @cached_property
    def get_ascending_sort_wro_items(self):
        return self.warehouse_release_order_items.all().order_by("sort_order")

    def assign_shipper_address(self):
        """
        Assign consignor's branch address into field shipper_address
        """
        self.shipper_address = self.consignee.consignor.shipping_address.get_address

    def assign_shipper_phone(self):
        """
        Assign consignor's branch phone into field shipper_phone
        """
        self.shipper_phone = self.consignee.consignor.primary_contact.phone

    def assign_shipper_mobile(self):
        """
        Assign consignor's branch mobile into field shipper_mobile
        """
        self.shipper_mobile = self.consignee.consignor.primary_contact.mobile

    def assign_shipper_attn(self):
        """
        Assign consignor's PIC into field shipper_attn
        """
        self.shipper_attn = self.consignee.consignor.primary_contact.get_formal_full_name

    def assign_shipping_address(self):
        """
        Assign consignee address into field shipping_address
        """
        self.shipping_address = self.consignee.shipping_address.get_address

    def assign_shipping_phone(self):
        """
        Assign consignee primary_contact phone into field shipping_phone
        """
        self.shipping_phone = ""
        if self.consignee.primary_contact:
            self.shipping_phone = self.consignee.primary_contact.phone

    def assign_shipping_mobile(self):
        """
        Assign consignee primary_contact mobile into field shipping_mobile
        """
        self.shipping_mobile = ""
        if self.consignee.primary_contact:
            self.shipping_mobile = self.consignee.primary_contact.mobile

    def assign_shipping_attn(self):
        """
        Assign consignee primary_contact name into field shipping_attn
        """
        self.shipping_attn = ""
        if self.consignee.primary_contact:
            self.shipping_attn = self.consignee.primary_contact.get_formal_full_name

    @cached_property
    def total_all_picker_system_quantity(self):
        """Get total count of picker system quantity."""
        warehouse_release_order_items_qs = self.warehouse_release_order_items.filter(
            status=WarehouseReleaseOrderItem.Status.APPROVED
        )
        total_all_picker_system_quantity = 0

        for warehouse_release_order_item in warehouse_release_order_items_qs:
            total_picker_system_quantity = warehouse_release_order_item._get_all_pickers_log().aggregate(
                Sum("system_quantity")
            ).get("system_quantity__sum", Decimal("0")) or Decimal("0")

            total_all_picker_system_quantity += round(
                total_picker_system_quantity, warehouse_release_order_item.item.uom.unit_precision
            )

        return total_all_picker_system_quantity

    @cached_property
    def all_items_in_new_status(self):
        """Check if all release order items are in 'New' status."""
        return not self.warehouse_release_order_items.exclude(status=WarehouseReleaseOrderItem.Status.NEW).exists()

    @cached_property
    def total_all_picker_expected_quantity(self):
        """Get total count of picker expected quantity."""
        warehouse_release_order_items_qs = self.warehouse_release_order_items.all()
        total_all_picker_expected_quantity = 0

        if warehouse_release_order_items_qs.count() > 0:
            # for goods_receive_note_item in warehouse_release_order_items_qs:
            total_picker_expected_quantity = warehouse_release_order_items_qs.aggregate(Sum("quantity")).get(
                "quantity__sum", Decimal("0")
            ) or Decimal("0")

            total_all_picker_expected_quantity += round(
                total_picker_expected_quantity, warehouse_release_order_items_qs[0].item.uom.unit_precision
            )

        return total_all_picker_expected_quantity

    @cached_property
    def imported_outbound_file_filename(self):
        if self.imported_outbound_file:
            return os.path.basename(self.imported_outbound_file.name)
        else:
            return None

    def release_transaction(self, released_by=None):
        """
        function to release ALL picked item with APPROVED state transaction

        param: released_by - person who perform the release/transaction on this WRO/DO
        Return: bool
        """
        from wms.apps.inventories.models import WarehouseReleaseOrderStockOut

        if released_by is None:
            return False
        else:
            wro_items_qs = self.warehouse_release_order_items.all()
            for wro_item in wro_items_qs:

                wro_item_picker_qs = wro_item.warehouse_release_order_item_pickers.all()

                for wro_item_picker in wro_item_picker_qs:
                    WarehouseReleaseOrderStockOut.objects.create(
                        release_order_item=wro_item,
                        released_by=released_by,
                        released_quantity=wro_item_picker.system_quantity,
                        stock_out_datetime=localtime_now(),
                        stock=wro_item_picker.stock,
                        uom=wro_item.uom,
                    )

            return True

    # def clear_notifications(self) -> None:
    #     """
    #     Remove existing notifications for newly created WRO once Completed
    #     """

    #     if self.status == self.Status.COMPLETED:
    #         message = f"NEW WRO created ({self.system_number}). Ready to pick!"
    #         notifications_qs = Notification.objects.filter(
    #             actor_object_id=self.pk,
    #             actor_content_type=ContentType.objects.get_for_model(self),
    #             verb__icontains=message,
    #             level="info",
    #             public=False,
    #         )

    #         if notifications_qs.exists():
    #             notifications_qs.delete()

    # def release_reserved_stock(self) -> None:
    #     """
    #     Release reserved stock (obsolete transaction) and remove items
    #     from the incomplete reservation queue
    #     """
    #     if self.status == self.Status.OBSOLETE:
    #         item_qs = self.warehouse_release_order_items.all()
    #         for item in item_qs:
    #             if item.reservation_status == WarehouseReleaseOrderItem.ReservationStatus.RESERVED_INCOMPLETE:
    #                 item.reservation_status = WarehouseReleaseOrderItem.ReservationStatus.RESERVED_COMPLETE
    #                 item.save(update_fields=["reservation_status"])

    #         rack_transaction_qs = RackTransaction.objects.filter(
    #             type=RackTransaction.Type.RESERVED,
    #             warehouse_release_order=self,
    #         )

    #         for rack_transaction in rack_transaction_qs:
    #             rack_transaction.type = RackTransaction.Type.OBSOLETE
    #             rack_transaction.quantity = Decimal("0")
    #             rack_transaction.save(update_fields=["type", "quantity"])

    @cached_property
    def html_status_display(self):
        """Return nice HTML display for status."""
        html_class = ""

        if self.status == WarehouseReleaseOrder.Status.NEW:
            html_class = "badge bg-theme-status-warning"
        elif self.status == WarehouseReleaseOrder.Status.PROCESSING:
            html_class = "badge bg-theme-status-info"
        elif self.status == WarehouseReleaseOrder.Status.READY_TO_PRINT:
            html_class = "badge bg-theme-primary"
        elif self.status == WarehouseReleaseOrder.Status.READY_TO_RELEASE:
            html_class = "badge bg-gray-500"
        elif self.status == WarehouseReleaseOrder.Status.OBSOLETE:
            html_class = "badge bg-gray-700"
        elif self.status == WarehouseReleaseOrder.Status.COMPLETED:
            html_class = "badge bg-theme-status-success"
        elif self.status == WarehouseReleaseOrder.Status.COMPLETED_WITH_REJECT:
            html_class = "badge bg-theme-status-error"

        return format_html(f'<span class="{html_class}">{self.get_status_display()}</span>') or DISPLAY_EMPTY_VALUE

    def save(self, *args, **kwargs):
        """
        Need to check some rules before save:
        - Ensure these fields are filled from sinoflex's warehouse and consignee's info
            - shipper_address
            - shipper_phone
            - shipper_mobile
            - shipper_attn
            - shipping_address
            - shipping_phone
            - shipping_mobile
            - shipping_attn
        - If is_delivery_order is True, there must be a DeliveryOrder object with
          system_number tight with this WarehouseReleaseOrder

        """

        if not self.pk and self.consignee:
            self.assign_shipper_address()
            self.assign_shipper_phone()
            self.assign_shipper_mobile()
            self.assign_shipper_attn()
            self.assign_shipping_address()
            self.assign_shipping_phone()
            self.assign_shipping_mobile()
            self.assign_shipping_attn()

        super().save(*args, **kwargs)

        # to handle DO linkage based on WRO's is_delivery_order boolean field
        try:
            delivery_order = DeliveryOrder.objects.get(warehouse_release_order=self)
        except DeliveryOrder.DoesNotExist:
            delivery_order = None
        if self.is_delivery_order is True and delivery_order is None:
            DeliveryOrder.objects.create(warehouse_release_order=self)
        elif self.is_delivery_order is False and delivery_order:
            delivery_order.delete()

        # Setting.get("EDI_ENABLED", default=False) must be first condition.
        if Setting.get("EDI_ENABLED", default=False):
            # MMM EDI
            if (
                self.status == self.Status.COMPLETED
                and self.is_from_edi is True
                and self.imported_outbound_file
                and self.is_edi_confirmation_sent is False
                and self.consignee.consignor.code == "MMM"
            ):
                mmm_outbound_confirm = MMMEDIConfirmation(obj=self)
                mmm_outbound_confirm.upload_return_do_txt()
                self.is_edi_confirmation_sent = True

            # FMC EDI
            if (
                self.status == self.Status.COMPLETED
                and self.is_from_edi is True
                and self.imported_outbound_file
                and self.is_edi_confirmation_sent is False
                and self.consignee.consignor.code == "FMCSB"
            ):
                fmc_outbound_confirm = FMCEDIConfirmation(obj=self)
                fmc_outbound_confirm.send_xml_confirmation()
                self.is_edi_confirmation_sent = True

        # self.clear_notifications()

        # self.release_reserved_stock()


class WarehouseReleaseOrderItem(AbstractBaseModel, AbstractSortableModel):
    """WarehouseReleaseOrderItem model for Warehouse Management System.

    Available fields:

    ~ created           (AbstractBaseModel => TimeStampedModel)
    ~ modified          (AbstractBaseModel => TimeStampedModel)
    ~ created_by        (AbstractBaseModel)
    ~ modified_by       (AbstractBaseModel)
    ~ sort_order        (AbstractSortableModel)
    ~ status
    ~ picker_status
    ~ release_order     (FK)
    ~ item              (FK)
    ~ uom               (FK)
    ~ batch_no
    ~ expiry_date
    ~ is_serial_no
    ~ quantity
    ~ approved_by       (FK)
    ~ remark

    Rules:

    """

    class Status(models.TextChoices):
        NEW = "New", _("New")
        PROCESSING = "Processing", _("Processing")
        APPROVED = "Approved", _("Approved")
        REJECTED = "Rejected", _("Rejected")

    class PickerStatus(models.TextChoices):
        DRAFT = "Draft", _("Draft")
        PICKED_COMPLETE = "Picked (Complete)", _("Picked (Complete)")
        PICKED_INCOMPLETE = "Picked (Incomplete)", _("Picked (Incomplete)")

    # class ReservationStatus(models.TextChoices):
    #     DRAFT = "Draft", _("Draft")
    #     RESERVED_COMPLETE = "Reserved (Complete)", _("Reserved (Complete)")
    #     RESERVED_INCOMPLETE = "Reserved (Incomplete)", _("Reserved (Incomplete)")

    # statuses
    status = models.CharField(verbose_name=_("Status"), max_length=32, choices=Status.choices, default=Status.NEW)
    picker_status = models.CharField(
        verbose_name=_("Picker Status"), max_length=32, choices=PickerStatus.choices, default=PickerStatus.DRAFT
    )
    # parent
    release_order = models.ForeignKey(
        "releases.WarehouseReleaseOrder", related_name="warehouse_release_order_items", on_delete=models.CASCADE
    )
    # stock info
    item = models.ForeignKey("inventories.Item", on_delete=models.CASCADE)
    uom = models.ForeignKey(
        "settings.UnitOfMeasure",
        verbose_name=_("UOM"),
        on_delete=models.RESTRICT,
        help_text=_("The item will be measured in terms of this unit (e.g.: kg, pcs, box)."),
    )
    batch_no = models.CharField(verbose_name=_("Batch No."), max_length=255, default="N/A")
    expiry_date = models.DateField(verbose_name=_("Expiry Date"), blank=True, null=True)

    # scenario on FMC EDI, some item such as big machine, they are using "serialNumber" instead of using "batchNumber"
    # (but in the consideration of our structure, batch_no should serve this purpose to store this number)
    # in the inbound/outbound return confirmation we need to prepare different naming tag for them.
    # this field serves the indicator.
    is_serial_no = models.BooleanField(verbose_name=_("Is Serial Number?"), default=False)

    # expected to release quantity.
    quantity = models.DecimalField(
        verbose_name=_("Quantity"),
        max_digits=19,
        decimal_places=6,
        default=Decimal("0"),
        validators=[greater_than_zero],
    )
    # checker who approve all the picked quantity from picker.
    approved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL, related_name="+", on_delete=models.RESTRICT, null=True, blank=True
    )
    # flag to indicate if this WROItem's expected outbound rack is assigned
    is_rack_assigned = models.BooleanField(verbose_name=_("Is Rack Assigned?"), default=False)
    # extra info
    remark = models.TextField(verbose_name=_("Remark"), max_length=512, blank=True)

    # reservation_status = models.CharField(
    #     verbose_name=_("Reservation Status"),
    #     max_length=32,
    #     choices=ReservationStatus.choices,
    #     default=ReservationStatus.DRAFT,
    # )

    class Meta(AbstractBaseModel.Meta):
        ordering = ["sort_order"]
        indexes = [
            # single index
            models.Index(fields=["release_order"]),
            models.Index(fields=["picker_status"]),
            # composite index
            models.Index(fields=["release_order", "picker_status", "status"]),
            models.Index(fields=["release_order", "status", "picker_status"]),
            models.Index(fields=["status", "picker_status"]),
            models.Index(fields=["picker_status", "status"]),
        ]

    def __str__(self):
        return f"{self.release_order.system_number} :: {self.sort_order}"

    def get_absolute_url(self):
        return reverse("releases:orders:detail", kwargs={"pk": self.release_order.pk})

    def _get_all_pickers_log(self):
        return self.warehouse_release_order_item_pickers.all()

    @cached_property
    def total_picker_system_quantity(self):
        total_picker_system_quantity = self._get_all_pickers_log().aggregate(Sum("system_quantity")).get(
            "system_quantity__sum", Decimal("0")
        ) or Decimal("0")
        return round(total_picker_system_quantity, self.uom.unit_precision)

    @cached_property
    def picker_percentage(self):
        try:
            return round((self.total_picker_system_quantity / self.quantity) * Decimal("100"), 0)
        except (decimal.DivisionByZero, decimal.InvalidOperation):
            return 0

    @cached_property
    def show_or_hide_html_approve_button(self) -> bool:
        """
        Function to display or hide HTML Approve button on WROItems.

        As long as the total picked percentage is < 100% or > 100%, then it should hide the button,
        even if the picker_status yields PICKED_COMPLETE.
        """
        return self.picker_percentage == Decimal("100")

    @cached_property
    def html_status_display(self):
        """Return nice HTML display for status."""
        html_class = ""

        if self.status == WarehouseReleaseOrderItem.Status.NEW:
            html_class = "badge bg-theme-status-warning"
        elif self.status == WarehouseReleaseOrderItem.Status.PROCESSING:
            html_class = "badge bg-theme-status-info"
        elif self.status == WarehouseReleaseOrderItem.Status.APPROVED:
            html_class = "badge bg-theme-status-success"
        elif self.status == WarehouseReleaseOrderItem.Status.REJECTED:
            html_class = "badge bg-theme-status-error"

        return format_html(f'<span class="{html_class}">{self.get_status_display()}</span>') or DISPLAY_EMPTY_VALUE

    @cached_property
    def expected_quantity(self):
        return round(self.quantity, self.uom.unit_precision)

    @cached_property
    def get_display_quantity_status(self):
        """get_display_quantity_status

        Expected return e.g:
            500/1000
        """
        return f"{self.total_picker_system_quantity}/{self.expected_quantity}"

    @cached_property
    def quantity_status_dict(self):
        """Get a dictionary of quantity status.

        Expected return e.g:
            quantity_status_dict = {
                "total_percentage": 100,
                "overall_status": "warning",
            }
        """
        # Use picker's item quantity
        try:
            total_percentage = round((self.total_picker_system_quantity / self.expected_quantity) * Decimal("100"), 0)
        except decimal.DivisionByZero:
            total_percentage = 0

        overall_status = "danger"
        if total_percentage >= 100:
            overall_status = "success"
        elif total_percentage > 0:
            overall_status = "warning"

        quantity_status_dict = {
            "total_percentage": total_percentage,
            "overall_status": overall_status,
        }

        return quantity_status_dict

    @cached_property
    def get_position(self):
        """Return position of item in WarehouseReleaseOrder."""
        position = (
            list(
                self.__class__.objects.filter(release_order=self.release_order)
                .order_by("sort_order")
                .values_list("pk", flat=True)
            ).index(self.pk)
            + 1
        )

        return position

    @cached_property
    def running_number(self):
        """Return running number as in export txt."""
        return str(self.sort_order).zfill(5)

    # @cached_property
    # def racks(self):
    #     return Rack.objects.filter(
    #         warehouse__in=self.release_order.warehouses.all(),
    #         rackstorage__stock__item=self.item,
    #         rackstorage__stock__batch_no=self.batch_no,
    #         rackstorage__stock__expiry_date=self.expiry_date,
    #     )

    # @cached_property
    # def html_rack_display(self):
    #     """Return nice HTML display for all involved racks."""
    #     racks = self.racks

    #     li_html = "".join([f"<li class='showmore-item'>{rack.__str__().replace(' ', '&nbsp;')}</li>" for rack in racks])

    #     if li_html:
    #         return format_html(f"<ul class='list-unstyled mb-0 showmore-items'>{li_html}</li>")
    #     else:
    #         return DISPLAY_EMPTY_VALUE

    # @cached_property
    # def excel_rack_display(self):
    #     """Return Excel display for all involved racks.

    #     Requirement from Joann, only print max 5 in excel.
    #     """
    #     racks = self.racks

    #     return (
    #         format_html("\r\n".join([rack.__str__().replace(" ", "&nbsp;") for rack in racks[:5]]))
    #         or DISPLAY_EMPTY_VALUE
    #     )

    @cached_property
    def get_outbound_uom_display_conversion(self):
        """Return outbound uom display conversion in dict format.

        Assumption:
        - suppose to support up to multiple conversion dict in 1 big dict:

        Return:
            outbound_uom_display_conversion_dict = {
                # structure example:
                "uom_display_name": {
                    "converted_uom": converted_uom,
                    "converted_quantity": converted_quantity,
                    "base_uom": base_uom,
                    "base_quantity": base_quantity,
                },

                #value example (I.E: 1 carton = 10 PCE):
                "carton": {
                    "converted_uom": "carton",
                    "converted_quantity": 10,
                    "base_uom": "PCE",
                    "base_quantity": 100,
                },
                #value example (I.E: 1 pallet = 200 PCE):
                "pallet": {
                    "converted_uom": "pallet",
                    "converted_quantity": 10,
                    "base_uom": "PCE",
                    "base_quantity": 2000,
                },
            }
        """

        outbound_uom_display_conversion_dict = {}

        all_uom_display_qs = self.item.outbound_uom_display_conversions.all().order_by("base_value")

        for uom_display_obj in all_uom_display_qs:
            uom_display_quantity = self.quantity / uom_display_obj.base_value

            if uom_display_quantity.as_tuple().exponent < 0:
                uom_display_number, base_display_number = calculate_base_uom_to_expected_conversion_uom(
                    uom_display_quantity, uom_display_obj.base_value
                )
                outbound_uom_display_conversion_dict[uom_display_obj.uom_display_name] = {
                    "converted_uom": uom_display_obj.uom_display_name,
                    "converted_quantity": uom_display_number,
                    "base_uom": self.item.uom.symbol,
                    "base_quantity": base_display_number,
                }
            else:
                outbound_uom_display_conversion_dict[uom_display_obj.uom_display_name] = {
                    "converted_uom": uom_display_obj.uom_display_name,
                    "converted_quantity": uom_display_quantity,
                    "base_uom": self.item.uom.symbol,
                    "base_quantity": 0,
                }

        return outbound_uom_display_conversion_dict

    def get_stock(self, release_from=None):
        stock = None
        if release_from:
            try:
                stock = Stock.objects.get(
                    warehouse=release_from,
                    item=self.item,
                    batch_no=self.batch_no,
                    expiry_date=self.expiry_date,
                )
            except Stock.DoesNotExist:
                raise ValidationError(
                    _(
                        f"There is no Stock({self.item} :: {self.batch_no} :: {self.expiry_date}) exist "
                        f"in system yet based on this WRO Item in {self.release_order}."
                    )
                )
        return stock


    def validate_wro_item_rack_assigned(self):
        if self.is_rack_assigned is True and self.racktransaction_set.all().exists():
            return True
        else:
            return False

    def validate_available_rack_transaction(self, release_from=None):
        """
        Validate the param warehouse's all rack(s) whether got enough available RackTransaction
        to fulfil the auto-assign for the current WROItem or not.
        """
        from wms.apps.rackings.models import RackTransaction
        if release_from:
            try:
                stock = Stock.objects.get(
                    warehouse=release_from,
                    item=self.item,
                    batch_no=self.batch_no,
                    expiry_date=self.expiry_date,
                )
            except Stock.DoesNotExist:
                raise ValidationError(
                    _(
                        "Trying to assign a rack with non-exist stock to a WROItem is not possible, "
                        "please check if system consist of accurate "
                        f"stock({release_from} {self.item} :: {self.batch_no} :: {self.expiry_date})."
                    )
                )

            available_rack_transaction = RackTransaction.objects.balance_by_stock(
                stock=self.stock,
                balance_type="available",
            )

            if self.quantity < available_rack_transaction:
                return True
            else:
                return False
        else:
            raise ValidationError(_("Please specify the WROItem should release from which warehouse."))

    def assign_available_rack(self, stock=None, full_quantity_needed_to_assign=0):
        """
        To assign the full_quantity_needed_to_assign based on available rack(s)

        Scenarios to be considered:
        1) ONE rackstorage's available quantity able to fulfil the full_quantity_needed_to_assign
        2) needed Multiple rackstorage's available quantity to fulfil the full_quantity_needed_to_assign
        """
        from wms.apps.rackings.models import RackStorage, RackTransaction
        if stock and full_quantity_needed_to_assign > 0:
            rack_storage_qs = RackStorage.objects.filter(
                stock=stock,
            ).order_by("rack__full_name")

            for rack_storage in rack_storage_qs:
                available_quantity = rack_storage.get_current_available_rack_transaction_balance
                if available_quantity > 0:

                    # finds out how many available quantity this current rack_storage able
                    # to provide to fulfil the requested full_quantity_needed_to_assign
                    if available_quantity >= full_quantity_needed_to_assign:
                        # if 30 >= 20
                        delegate_quantity = full_quantity_needed_to_assign
                    elif available_quantity < full_quantity_needed_to_assign:
                        # if 30 < 100, (in this case, current rackstorage should only assign 30 reserved transaction,
                        # the remaining 70 should try to look for next available rackstorage)
                        full_quantity_needed_to_assign -= available_quantity
                        delegate_quantity = available_quantity

                    # create reserved RackTransaction
                    RackTransaction.objects.create(
                        type=RackTransaction.Type.WRO,
                        warehouse_release_order_item=self,
                        rackstorage=rack_storage,
                        quantity=-abs(delegate_quantity),
                        is_reserved=True,
                    )

                    self.is_rack_assigned = True
                    self.save(update_fields=["is_rack_assigned"])

    def unassign_rack(self, stock=None):
        """
        To unassign the rack(s) that have already assigned to this WROItem

        Scenarios to be considered:
        1) Really needed to Manual unassign the assigned rack
           (can be due to picker found out physical stock really empty in the pallet, admin need to reassign ASAP),
           so that admin/superadmin can select specific rack to re-assign for this WROItem.
        """
        from wms.apps.rackings.models import RackTransaction

        RackTransaction.objects.filter(
            type=RackTransaction.Type.WRO,
            rackstorage__stock=stock,
            is_reserved=True,
            warehouse_release_order_item=self.release_order_item,
        ).delete()

        self.is_rack_assigned = False
        self.save(update_fields=["is_rack_assigned"])

    # @cached_property
    # def reserved_rack_transactions(self):
    #     return RackTransaction.objects.select_related(
    #         "rackstorage",
    #         "rackstorage__rack",
    #         "rackstorage__stock",
    #         "warehouse_release_order",
    #         "warehouse_release_order_item",
    #     ).filter(
    #         type=RackTransaction.Type.RESERVED,
    #         warehouse_release_order=self.release_order,
    #         warehouse_release_order_item=self,
    #     )

    # @cached_property
    # def html_reserved_rack_display(self):
    #     formatted_html = ""

    #     reserved_racks = self.reserved_rack_transactions.values_list("rackstorage__rack__full_name", "quantity")

    #     li_html = "".join(
    #         [
    #             (
    #                 f"<li class='showmore-item'>{rack.replace(' ', '&nbsp;')}&nbsp;"
    #                 f"[{round(abs(quantity), self.item.uom.unit_precision)}]</li>"
    #             )
    #             for rack, quantity in reserved_racks
    #         ]
    #     )

    #     if li_html:
    #         formatted_html += f"<ul class='list-unstyled mb-0 showmore-items'>{li_html}</ul>"

    #     if self.reservation_status == self.ReservationStatus.RESERVED_INCOMPLETE:
    #         formatted_html += "<i class='fa fa-exclamation-circle fa-fw text-warning'></i>"

    #     return format_html(formatted_html) if formatted_html else DISPLAY_EMPTY_VALUE

    # @cached_property
    # def total_reserved_quantity(self) -> Decimal:
    #     return abs(self.reserved_rack_transactions.aggregate(sum=Coalesce(Sum("quantity"), Decimal("0"))).get("sum"))

    # def get_picked_reserved_quantity(self, racks: Rack | QuerySet[Rack]) -> Decimal:
    #     """
    #     Get quantity of reserved stock that has been picked from the specified rack/racks.
    #     """
    #     if isinstance(racks, Rack):
    #         racks = [racks]

    #     return (
    #         self.warehouse_release_order_item_pickers.filter(rack__in=racks)
    #         .aggregate(sum=Coalesce(Sum("quantity"), Decimal("0")))
    #         .get("sum")
    #     )

    # def reserve_stock(self) -> None:
    #     """
    #     Reserve stock for the current release order item, otherwise,
    #     confirm the reservation (stock out) if the item is approved.
    #     """
    #     from wms.apps.rackings.services import reserve_release_order_item

    #     if self.status == WarehouseReleaseOrderItem.Status.APPROVED:
    #         approved_datetime = localtime_now()
    #         rack_transaction_qs = RackTransaction.objects.filter(
    #             type=RackTransaction.Type.RESERVED,
    #             warehouse_release_order=self.release_order,
    #             warehouse_release_order_item=self,
    #         )

    #         for rack_transaction in rack_transaction_qs:
    #             rack_transaction.transaction_datetime = approved_datetime
    #             rack_transaction.type = RackTransaction.Type.STOCK_OUT
    #             rack_transaction.save()
    #     else:
    #         reserve_release_order_item(release_order_item=self)

    def clean(self):
        """Validation on status."""
        # self.is_cleaned = True
        if self.status == self.Status.NEW:
            return super().clean()

        if self.status == self.Status.APPROVED and self.expected_quantity != self.total_picker_system_quantity:
            raise ValidationError(
                _(
                    "Mismatch on picker quantity and expected quantity. "
                    f"{self.total_picker_system_quantity} / {self.expected_quantity}"
                )
            )
        elif Decimal(self.quantity) <= self.total_picker_system_quantity:
            self.picker_status = self.PickerStatus.PICKED_COMPLETE
        elif Decimal(self.quantity) >= self.total_picker_system_quantity:
            self.picker_status = self.PickerStatus.PICKED_INCOMPLETE

        super().clean()

    def save(self, skip_reserve=False, *args, **kwargs):
        need_regenerate_picking_list_item = False

        if self.pk:
            exiting_wro_item = WarehouseReleaseOrderItem.objects.get(pk=self.pk)

            if self.item != exiting_wro_item.item:
                self.warehouse_release_order_item_pickers.all().delete()
                self.status = WarehouseReleaseOrderItem.Status.NEW

            # Check if the quantity has changed, need to trigger picking list to regenerate_picking_list_item if any.
            if (
                exiting_wro_item.quantity != self.quantity
                or exiting_wro_item.uom != self.uom
            ):
                # print("Quantity has changed from", exiting_wro_item.quantity, "to", self.quantity)

                if self.release_order.picking_list:
                    need_regenerate_picking_list_item = True

        super().save(*args, **kwargs)

        if need_regenerate_picking_list_item:
            self.release_order.picking_list.regenerate_picking_list_item()

        if self.status == WarehouseReleaseOrderItem.Status.APPROVED:
            all_releaseorderitem = self.release_order.warehouse_release_order_items.all()
            all_new_processing_releaseorderitem = all_releaseorderitem.filter(
                Q(status=WarehouseReleaseOrderItem.Status.NEW) | Q(status=WarehouseReleaseOrderItem.Status.PROCESSING)
            )

            if all_new_processing_releaseorderitem.count() > 0:
                self.release_order.status = WarehouseReleaseOrder.Status.PROCESSING
            else:
                self.release_order.status = WarehouseReleaseOrder.Status.READY_TO_PRINT

            self.release_order.save(update_fields=["status"])

            # Additional logic when WRO is in "Ready To Print" status & user adds a new WROItem:
            # 1) If there's at least 1 WROItem being picked & has "Approved" status -> WRO reverts to Processing status.
            # 2) Otherwise (none of the WROItem is picked & "Approved") -> WRO reverts to New status.
        elif (
            self.status == WarehouseReleaseOrderItem.Status.NEW
            and self.release_order.status == WarehouseReleaseOrder.Status.READY_TO_PRINT
        ):
            all_releaseorderitem = self.release_order.warehouse_release_order_items.all()
            check_approved_releaseorderitem = all_releaseorderitem.filter(
                Q(status=WarehouseReleaseOrderItem.Status.APPROVED)
            )

            if check_approved_releaseorderitem.exists():
                self.release_order.status = WarehouseReleaseOrder.Status.PROCESSING
            else:
                self.release_order.status = WarehouseReleaseOrder.Status.NEW

            self.release_order.save(update_fields=["status"])

        # if not skip_reserve:
        #     self.reserve_stock()


class WarehouseReleaseOrderItemPicker(AbstractBaseModel):
    """WarehouseReleaseOrderItemPicker model for Warehouse Management System.

    Available fields:

    ~ created           (AbstractBaseModel => TimeStampedModel)
    ~ modified          (AbstractBaseModel => TimeStampedModel)
    ~ created_by        (AbstractBaseModel)
    ~ modified_by       (AbstractBaseModel)
    ~ release_order_item
    ~ quantity
    ~ uom
    ~ system_quantity
    ~ picked_datetime
    ~ picked_by
    ~ batch_no
    ~ expiry_date
    ~ warehouse
    ~ stock

    """

    release_order_item = models.ForeignKey(
        "releases.WarehouseReleaseOrderItem",
        related_name="warehouse_release_order_item_pickers",
        on_delete=models.CASCADE,
    )
    quantity = models.DecimalField(verbose_name=_("Quantity"), max_digits=19, decimal_places=6, default=Decimal("0"))
    uom = models.ForeignKey("settings.UnitOfMeasure", verbose_name=_("UOM"), on_delete=models.RESTRICT)
    system_quantity = models.DecimalField(
        verbose_name=_("System Quantity"), max_digits=19, decimal_places=6, default=0, editable=False
    )
    picked_datetime = models.DateTimeField(verbose_name=_("Picked Date Time"))
    picked_by = models.ForeignKey(settings.AUTH_USER_MODEL, related_name="+", on_delete=models.RESTRICT)
    batch_no = models.CharField(verbose_name=_("Batch No."), max_length=255, default="N/A")
    expiry_date = models.DateField(verbose_name=_("Expiry Date"), blank=True, null=True)
    warehouse = models.ForeignKey("settings.Warehouse", limit_choices_to={"is_storage": True}, on_delete=models.PROTECT)
    stock = models.ForeignKey("inventories.Stock", on_delete=models.CASCADE, null=True, blank=True)
    # rack = models.ForeignKey("rackings.Rack", on_delete=models.SET_NULL, null=True)

    class Meta(AbstractBaseModel.Meta):
        indexes = [
            # single index
            models.Index(fields=["release_order_item"]),
            models.Index(fields=["stock"]),
            # composite index
            models.Index(fields=["release_order_item", "stock"]),  # Composite Index
        ]

    def __str__(self):
        return f"{self.release_order_item} :: {self.quantity} picker log"

    @cached_property
    def system_quantity_uom_formatted(self):
        return round(self.system_quantity, self.stock.item.uom.unit_precision)

    def save(self, *args, **kwargs):
        """
        Logic to update release order item status
        """
        with transaction.atomic():
            self.system_quantity = uom_converter(
                origin_uom=self.uom,
                target_uom=self.release_order_item.uom,
                quantity=self.quantity,
                skip_unit_precision=True,
            )

            if self.batch_no and self.warehouse and self.release_order_item:
                self.stock = Stock.objects.get(
                    warehouse=self.warehouse,
                    item=self.release_order_item.item,
                    batch_no=self.batch_no,
                    expiry_date=self.expiry_date,
                    item__consignor=self.release_order_item.release_order.consignee.consignor,
                )

            super().save(*args, **kwargs)

            # perform reserve transaction
            # calculated_converted_picked_quantity = uom_converter(
            #     origin_uom=self.uom, target_uom=self.release_order_item.item.uom, quantity=self.quantity
            # )
            # reserved_stock, _ = ReservedStock.objects.get_or_create(stock=self.stock)
            # ReservedTransaction.objects.create(
            #     reserved_stock=self.stock,
            #     transaction_datetime=self.picked_datetime,
            #     quantity=-(calculated_converted_picked_quantity),
            #     uom=self.release_order_item.item.uom,
            #     system_number_ref=self.release_order_item.release_order.system_number,
            #     is_wro=True,
            #     customer_reference=self.release_order_item.release_order.customer_reference,
            #     created_by=self.picked_by,
            # )

            # to handle WarehouseReleaseOrderItem picker's status
            if self.release_order_item.total_picker_system_quantity >= self.release_order_item.quantity:
                self.release_order_item.picker_status = WarehouseReleaseOrderItem.PickerStatus.PICKED_COMPLETE
                # Remove item from the incomplete reservation queue (assuming a manual pick process occured)
                # self.release_order_item.reservation_status = WarehouseReleaseOrderItem.ReservationStatus.RESERVED_COMPLETE
            else:
                self.release_order_item.picker_status = WarehouseReleaseOrderItem.PickerStatus.PICKED_INCOMPLETE

            # to handle WarehouseReleaseOrderItem's status
            if self.release_order_item.status == WarehouseReleaseOrderItem.Status.NEW:
                self.release_order_item.status = WarehouseReleaseOrderItem.Status.PROCESSING

            self.release_order_item.save(update_fields=["picker_status", "status"])


class WarehouseReleaseOrderStockOut(AbstractBaseModel):
    """WarehouseReleaseOrderStockOut model for Warehouse Management System.

    Available fields:

    ~ created           (AbstractBaseModel => TimeStampedModel)
    ~ modified          (AbstractBaseModel => TimeStampedModel)
    ~ created_by        (AbstractBaseModel)
    ~ modified_by       (AbstractBaseModel)
    ~ release_order_item
    ~ released_by
    ~ released_quantity
    ~ stock_out_datetime
    ~ stock
    ~ uom
    ~ remark
    ~ transaction

    """

    release_order_item = models.ForeignKey(
        "releases.WarehouseReleaseOrderItem",
        related_name="warehouse_release_order_stockouts",
        on_delete=models.CASCADE,
        help_text=_("Each Warehouse Release Order Item indicate a relation to item's released quantity."),
    )
    released_by = models.ForeignKey(settings.AUTH_USER_MODEL, related_name="+", on_delete=models.RESTRICT)
    released_quantity = models.DecimalField(
        verbose_name=_("Released Quantity"), max_digits=19, decimal_places=6, default=Decimal("0")
    )
    stock_out_datetime = models.DateTimeField(verbose_name=_("Stock Out Date Time"))
    stock = models.ForeignKey("inventories.Stock", on_delete=models.CASCADE)
    uom = models.ForeignKey("settings.UnitOfMeasure", verbose_name=_("UOM"), on_delete=models.RESTRICT)
    remark = models.TextField(verbose_name=_("Remark"), max_length=512, blank=True)
    transaction = models.OneToOneField("inventories.Transaction", on_delete=models.SET_NULL, null=True, blank=True)

    class Meta(AbstractBaseModel.Meta):
        ordering = ["stock_out_datetime", "pk"]
        indexes = [
            # single index
            models.Index(fields=["release_order_item"]),
        ]

    def __str__(self):
        return f"{self.stock.item.name} :: {self.stock.warehouse.name} :: {self.released_quantity}"

    def save(self, *args, **kwargs):
        with transaction.atomic():
            if self.transaction is None and self.released_by:
                calculated_converted_release_quantity = uom_converter(
                    origin_uom=self.uom, target_uom=self.release_order_item.item.uom, quantity=self.released_quantity
                )

                self.transaction = Transaction.objects.create(
                    stock=self.stock,
                    transaction_datetime=self.stock_out_datetime,
                    quantity=-(calculated_converted_release_quantity),
                    uom=self.release_order_item.item.uom,
                    created_by=self.released_by,
                    customer_reference=self.release_order_item.release_order.customer_reference,
                )

                # Release RackTransaction if there is any reserved rack_transaction
                from wms.apps.rackings.models import RackTransaction
                RackTransaction.objects.filter(
                    type=RackTransaction.Type.WRO,
                    rackstorage__stock=self.stock,
                    is_reserved=True,
                    warehouse_release_order_item=self.release_order_item,
                ).update(is_reserved=False)

            super().save(*args, **kwargs)

            # update ReservedTransaction's status to RELEASED
            # reserved_transactions = ReservedTransaction.objects.filter(
            #     status=ReservedTransaction.Status.RESERVED,
            #     reserved_stock=self.stock,
            # ).update(status=ReservedTransaction.Status.RELEASED)

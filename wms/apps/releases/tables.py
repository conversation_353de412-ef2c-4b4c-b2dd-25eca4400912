# from django.urls import reverse
# from django.utils.translation import gettext_lazy as _

# import django_tables2 as tables
# from actstream.models import Action

# from wss.cores.models import DISPLAY_EMPTY_VALUE
# from wss.cores.tables import (
#     HTMX_LIST_ATTRS_CLASS,
#     HTMX_LIST_SM_ATTRS_CLASS,
#     PARTIAL_LIST_ATTRS_CLASS,
#     TABLE_ATTRS_CLASS,
#     AbstractHistoryDataTables,
# )
# from wss.cores.utils import convert_camel_case_to_space

# from .models import WarehouseReleaseOrder, WarehouseReleaseOrderItem


# class WarehouseReleaseOrderDataTables(tables.Table):
#     """Table used on warehouse_release_orders list page."""

#     numbering = tables.Column(verbose_name=_("Numbering"), linkify=True)
#     deliveryorder__numbering = tables.Column(verbose_name=_("DO"))
#     release_datetime = tables.DateTimeColumn(
#         verbose_name=_("Release Date"),
#         accessor="release_datetime",
#         format="Y-m-d",
#     )
#     created = tables.DateTimeColumn(
#         verbose_name=_("Created Date"),
#         accessor="created",
#         format="Y-m-d",
#     )
#     actions = tables.TemplateColumn(
#         verbose_name=_("Actions"),
#         orderable=False,
#         template_name="releases/warehouse_release_orders/partials/tables/_actions.html",
#         attrs={"th": {"class": "text-right"}, "td": {"class": "text-right"}},
#         exclude_from_export=True,
#     )

#     class Meta:
#         model = WarehouseReleaseOrder
#         order_by = "numbering"  # Must follow the first row display in the table
#         default = DISPLAY_EMPTY_VALUE
#         template_name = "partials/tables/_datatables_server_side.html"
#         attrs = {"class": TABLE_ATTRS_CLASS, "id": "warehouse_release_orders_datatables", "style": "display: none;"}
#         row_attrs = {"data-id": lambda record: record.pk}

#         fields = [
#             "numbering",
#             "deliveryorder__numbering",
#             "customer_reference",
#             "status",
#             "consignee",
#             "tag",
#             "consignor_picking_list_no",
#             "issued_by",
#             "release_datetime",
#             "created",
#         ]
#         sequence = [
#             "numbering",
#             "deliveryorder__numbering",
#             "customer_reference",
#             "status",
#             "consignee",
#             "tag",
#             "consignor_picking_list_no",
#             "issued_by",
#             "release_datetime",
#             "created",
#         ]

#     def render_status(self, value, record):
#         """Return nice html label display for status."""
#         return record.html_status_display

#     def value_status(self, value, record):
#         """
#         The django-tables2 is to render the status's cell value
#         into TableExport without html label display.
#         """
#         return value

#     def render_actions(self, column, record, table, value, bound_column, bound_row):
#         """Add permission checking into actions column."""
#         change_perms = self.request.user.has_perm("releases.change_warehouse_release_order")
#         delete_perms = self.request.user.has_perm("releases.delete_warehouse_release_order")
#         superadmin_group_perms = (
#             self.request.user.is_superuser or self.request.user.groups.filter(name="Superadmin").exists()
#         )
#         column.extra_context = {
#             "change_perms": change_perms,
#             "delete_perms": delete_perms,
#             "superadmin_group_perms": superadmin_group_perms,
#         }
#         return column.render(record, table, value, bound_column, **{"bound_row": bound_row})


# class WarehouseReleaseOrderDetailDataTables(tables.Table):
#     """Table used on warehouse_release_orders detail page."""

#     numbering = tables.Column(verbose_name=_("Numbering"), linkify=True)

#     class Meta:
#         model = WarehouseReleaseOrder
#         order_by = "-numbering"  # Must follow the first row display in the table
#         default = DISPLAY_EMPTY_VALUE
#         template_name = "partials/tables/_datatables_server_side.html"
#         attrs = {
#             "class": PARTIAL_LIST_ATTRS_CLASS,
#             "id": "warehouse_release_orders_datatables",
#             "style": "display: none;",
#         }
#         row_attrs = {"data-id": lambda record: record.pk}

#         fields = [
#             "pk",
#             "numbering",
#             "status",
#         ]
#         sequence = [
#             "pk",
#             "numbering",
#             "status",
#         ]

#     def render_status(self, value, record):
#         """Return nice html label display for status."""
#         return record.html_status_display

#     def render_pk(self, value, record):
#         return f"highlight_id_{value}"


# class WarehouseReleaseOrderRejectDataTables(tables.Table):
#     """Table used on warehouse_release_orders defect list page."""

#     stock_item_code = tables.Column(
#         verbose_name=_("Stock"),
#         accessor="stock",
#     )
#     stock_item_name = tables.Column(
#         verbose_name=_("NAME"),
#         accessor="stock",
#     )

#     class Meta:
#         model = WarehouseReleaseOrderItem
#         default = DISPLAY_EMPTY_VALUE
#         # Need to override table footer
#         template_name = "releases/warehouse_release_orders/partials/tables/_reject_compose_datatables.html"
#         attrs = {
#             "class": HTMX_LIST_ATTRS_CLASS,
#             "id": "warehouse_release_orders_reject_datatables",
#             "style": "display: none;",
#             "thead": {"class": "bg-danger"},
#         }
#         row_attrs = {"data-id": lambda record: record.pk}

#         fields = [
#             "quantity",
#             "remark",
#         ]
#         sequence = [
#             "stock_item_code",
#             "stock_item_name",
#             "quantity",
#             "remark",
#         ]

#     def render_stock_item_code(self, value, record):
#         return f"{value.item.code}"

#     def render_stock_item_name(self, value, record):
#         return f"{value.item.name}"

#     def render_quantity(self, value, record):
#         return f"{round(value, record.stock.item.uom.unit_precision)} {record.uom.symbol}"


# class WarehouseReleaseOrderHistoryDataTables(AbstractHistoryDataTables):
#     """Table used on WarehouseReleaseOrder's History tab."""

#     class Meta(AbstractHistoryDataTables.Meta):
#         attrs = {
#             "class": HTMX_LIST_SM_ATTRS_CLASS,
#             "id": "warehouse_release_order_history_datatables",
#             "style": "display: none;",
#             "thead": {"class": "bg-secondary"},
#         }

#     def render_verb(self, value: str, record: Action) -> str:
#         if value == "created WarehouseReleaseOrderItemPicker":
#             return "Picked Warehouse Release Order Item"

#         value_string = convert_camel_case_to_space(value)
#         value_string = value_string[0].upper() + value_string[1:]

#         if value.startswith("modified"):
#             link = reverse("releases:warehouse_release_orders:history-modified", kwargs={"pk": record.pk})
#             htmx_modal_attributes = (
#                 'href="#" style="text-decoration: underline dotted" '
#                 'data-toggle="modal" data-target="#modalXl" '
#                 'hx-target="#modalXlBody" hx-swap="innerHTML"'
#             )
#             return f'<a {htmx_modal_attributes} hx-get="{link}">{value_string}</a>'
#         else:
#             return value_string

import logging

from django.db.models.signals import post_save, post_delete, pre_save
from django.dispatch import receiver

from wms.cores.actstream import register_stream

from .models import (
    DeliveryOrder,
    WarehouseReleaseOrder,
    WarehouseReleaseOrderItem,
    WarehouseReleaseOrderItemPicker,
    WarehouseReleaseOrderStockOut,
)

logger = logging.getLogger(__name__)


#################
# FOR ACTSTREAM #
#################

register_stream(WarehouseReleaseOrder, logger=logger)
register_stream(WarehouseReleaseOrderItem, logger=logger, parent_field="release_order")
register_stream(WarehouseReleaseOrderItemPicker, logger=logger)
register_stream(WarehouseReleaseOrderStockOut, logger=logger)


###################
# FOR APP SIGNALS #
###################


@receiver(post_save, sender=DeliveryOrder)
@receiver(post_save, sender=WarehouseReleaseOrder)
def generate_system_number(sender, instance, created=False, **kwargs):
    """To generate system number on instance."""
    if created:
        instance.generate_system_number()
        logger.info(f"SIGNAL: {sender.__name__}: generated system number for {instance}")


@receiver(post_delete, sender=WarehouseReleaseOrder)
def auto_delete_imported_outbound_file_on_delete(sender, instance, **kwargs):
    """Deletes imported_outbound_file from filesystem when DeliveryOrder is deleted."""
    if instance.imported_outbound_file:
        instance.imported_outbound_file.delete(save=False)


@receiver(pre_save, sender=WarehouseReleaseOrder)
def auto_delete_imported_outbound_file_on_change(sender, instance, **kwargs):
    """
    Deletes old imported_outbound_file from filesystem when DeliveryOrder is updated with new imported_outbound_file.
    """
    if not instance.pk:
        return False

    try:
        old_file = sender.objects.get(pk=instance.pk).imported_outbound_file
    except sender.DoesNotExist:
        return False

    new_file = instance.imported_outbound_file
    if not old_file == new_file:
        try:
            old_file.delete(save=False)
        except Exception:
            return False


@receiver(post_delete, sender=WarehouseReleaseOrderItem)
def check_and_update_wro_status(sender, instance, **kwargs):
    """Check and Update its WRO's status."""

    all_releaseorderitem = instance.release_order.warehouse_release_order_items.all()
    all_approved_releaseorderitem = all_releaseorderitem.filter(status=WarehouseReleaseOrderItem.Status.APPROVED)

    if all_releaseorderitem.count() == all_approved_releaseorderitem.count():
        instance.release_order.status = WarehouseReleaseOrder.Status.READY_TO_PRINT
    elif all_approved_releaseorderitem.count() > 0:
        instance.release_order.status = WarehouseReleaseOrder.Status.PROCESSING
    else:
        instance.release_order.status = WarehouseReleaseOrder.Status.NEW

    instance.release_order.save(update_fields=["status"])

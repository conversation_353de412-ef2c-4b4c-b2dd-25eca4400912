# import datetime

# from django.db.models import Q
# from django.utils.translation import gettext_lazy as _

# import django_filters as filters

# from wss.cores.filters import Abstract<PERSON><PERSON><PERSON><PERSON>ilter, CoreBooleanWidget
# from wss.cores.utils import get_user_warehouse_choices

# from .models import WarehouseReleaseOrder, WarehouseReleaseOrderItem


# def get_status_choices():
#     status_choices = [(status, status) for status in WarehouseReleaseOrder.Status]
#     return status_choices


# def get_item_status_choices():
#     status_choices = [(status, status) for status in WarehouseReleaseOrderItem.Status]
#     return status_choices


# def get_item_picker_status_choices():
#     picker_status_choices = [(status, status) for status in WarehouseReleaseOrderItem.PickerStatus]
#     return picker_status_choices


# class WarehouseReleaseOrderFilter(filters.FilterSet):
#     """Filter class for WarehouseReleaseOrder DataTables."""

#     start_date = filters.DateFilter(
#         label=_("Release Start Date"),
#         field_name="release_datetime",
#         lookup_expr=("gte"),
#     )
#     end_date = filters.DateFilter(
#         label=_("Release End Date"),
#         field_name="release_datetime",
#         method="end_date_filter",
#     )
#     batch_keyword_search = filters.CharFilter(
#         label=_("Batch No."),
#         method="custom_batch_keyword_filter",
#     )
#     keyword_search = filters.CharFilter(
#         label=_("Keyword"),
#         method="custom_keyword_filter",
#     )
#     keyword = filters.CharFilter(
#         label=_("Keyword"),
#         method="custom_keyword_filter",
#     )  # Need to have keyword because still using search in DataTables
#     warehouses = filters.MultipleChoiceFilter(choices=[])
#     is_delivery_order = filters.BooleanFilter(widget=CoreBooleanWidget)
#     status = filters.MultipleChoiceFilter(method="status_filter", choices=get_status_choices())
#     item_status = filters.MultipleChoiceFilter(
#         label=_("Item Status"), method="item_status_filter", choices=get_item_status_choices()
#     )
#     item_picker_status = filters.MultipleChoiceFilter(
#         label=_("Picking Status"), method="item_picker_status_filter", choices=get_item_picker_status_choices()
#     )
#     tag = filters.CharFilter(
#         label=_("Tag"),
#         method="tag_filter",
#     )

#     class Meta:
#         model = WarehouseReleaseOrder
#         fields = [
#             "status",
#             "consignee__consignor",
#             "warehouses",
#         ]

#     def __init__(self, data=None, queryset=None, *, request=None, prefix=None):
#         super().__init__(data=data, queryset=queryset, request=request, prefix=prefix)

#         # Filter out the warehouses based on User Role.
#         self.filters["warehouses"].extra["choices"] = get_user_warehouse_choices(request.user)

#         self.filters["start_date"].field.widget.attrs.update({"class": "form-control"})
#         self.filters["end_date"].field.widget.attrs.update({"class": "form-control"})
#         self.filters["consignee__consignor"].field.label = _("Consignor")
#         self.filters["consignee__consignor"].field.widget.attrs.update({"class": "form-control core-select2"})
#         self.filters["is_delivery_order"].field.widget.attrs.update({"class": "form-control core-select2"})
#         self.filters["item_picker_status"].field.widget.attrs.update({"class": "form-control core-select2"})
#         self.filters["status"].field.widget.attrs.update({"class": "form-control core-select2"})
#         self.filters["warehouses"].field.widget.attrs.update({"class": "form-control core-select2"})
#         self.filters["keyword_search"].field.widget.attrs.update({"class": "form-control"})
#         self.filters["batch_keyword_search"].field.widget.attrs.update({"class": "form-control"})
#         self.filters["tag"].field.widget.attrs.update({"class": "form-control"})

#     def custom_keyword_filter(self, queryset, name, value):
#         qs = queryset

#         for keyword in value.split():
#             qs = qs.filter(
#                 Q(numbering__icontains=keyword)
#                 | Q(customer_reference__icontains=keyword)
#                 | Q(consignor_picking_list_no__icontains=keyword)
#                 | Q(consignor_picking_list_no__icontains=keyword)
#                 | Q(deliveryorder__numbering__icontains=keyword)
#                 | Q(consignee__company_name__icontains=keyword)
#                 | Q(consignee__display_name__icontains=keyword)
#                 | Q(consignee__code__icontains=keyword)
#                 | Q(consignee__consignor__company_name__icontains=keyword)
#                 | Q(consignee__consignor__display_name__icontains=keyword)
#                 | Q(consignee__consignor__code__icontains=keyword)
#                 | Q(status__icontains=keyword)
#                 | Q(issued_by__email__icontains=keyword)
#                 | Q(remark__icontains=keyword)
#             )

#         return qs.distinct()

#     def custom_batch_keyword_filter(self, queryset, name, value):
#         qs = queryset

#         for batch_keyword in value.split():
#             qs = qs.filter(warehouse_release_order_items__batch_no__icontains=batch_keyword)

#         return qs.distinct()

#     def end_date_filter(self, queryset, name, value):
#         qs = queryset

#         added_one_day_end_date = value + datetime.timedelta(days=1)
#         qs = qs.filter(release_datetime__lt=added_one_day_end_date)

#         return qs

#     def status_filter(self, queryset, name, value):
#         qs = queryset
#         qs = qs.filter(status__in=value)
#         return qs.distinct()

#     def item_status_filter(self, queryset, name, value):
#         qs = queryset
#         qs = qs.filter(warehouse_release_order_items__status__in=value)
#         return qs.distinct()

#     def item_picker_status_filter(self, queryset, name, value):
#         qs = queryset
#         qs = qs.filter(warehouse_release_order_items__picker_status__in=value)
#         return qs.distinct()

#     def tag_filter(self, queryset, name, value):
#         qs = queryset
#         qs = qs.filter(tag__exact=value)
#         return qs.distinct()


# class WarehouseReleaseOrderHistoryFilter(AbstractHistoryFilter):
#     """Filter class for WarehouseReleaseOrder's History DataTables."""

#     pass

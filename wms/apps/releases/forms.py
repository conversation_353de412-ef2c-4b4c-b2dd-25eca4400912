# import decimal
# import os
# from decimal import Decimal

# from django import forms
# from django.core.exceptions import ValidationError
# from django.db.models import BLANK_CHOICE_DASH, Sum
# from django.utils.translation import gettext_lazy as _

# from wss.cores.forms import CoreForm, CoreHtmxModelForm, CoreModelForm
# from wss.cores.forms.mixins import FormUOMSymbolMixin
# from wss.cores.utils import uom_choices_symbol
# from wss.cores.widgets import CoreGroupCheckboxSelectMultiple

# from wss.apps.consignors.models import Consignor
# from wss.apps.inventories.models import Item, Stock
# from wss.apps.rackings.models import Rack
# from wss.apps.releases.imports.bulk_edit_wro import ImportBulkEditWroExcel
# from wss.apps.releases.imports.outbound_mitsubishi import ImportOutboundMitsubishi
# from wss.apps.settings.models import UnitOfMeasure, Warehouse
# from wss.apps.settings.utils import uom_converter

# from .models import WarehouseReleaseOrder, WarehouseReleaseOrderItem, WarehouseReleaseOrderItemPicker


# class WarehouseReleaseOrderItemForm(CoreModelForm):
#     """WRO item form."""

#     item = forms.CharField(widget=forms.Select())
#     consignor_pk = forms.CharField(required=False, widget=forms.HiddenInput())

#     def __init__(self, *args, **kwargs):
#         super().__init__(*args, **kwargs)

#         try:
#             ea_uom = UnitOfMeasure.objects.get(symbol="EA")
#         except Exception:
#             pass
#         else:
#             self.fields["uom"].initial = ea_uom

#         self.fields["uom"].choices = uom_choices_symbol()

#         self.fields["item"].widget.attrs["class"] = "form-control core-select2 item-of-consignor"

#     def clean_item(self):
#         pk = self.cleaned_data["item"]
#         try:
#             data = Item.objects.get(pk=pk)
#         except Item.DoesNotExist:
#             raise ValidationError("Selected Item does not exist.")
#         return data

#     def clean(self):
#         """Check that if selected inline item is belongs to parent form's consignor."""
#         cleaned_data = super().clean()

#         consignor_pk = cleaned_data.get("consignor_pk")
#         item = cleaned_data.get("item")
#         batch_no = cleaned_data.get("batch_no")
#         consignor = Consignor.objects.get(pk=consignor_pk)

#         if item and str(item.consignor.pk) != consignor_pk:
#             msg = _(f"The selected item does not belongs to consignor {consignor}.")
#             self.add_error("item", msg)

#         if item:
#             stock_exists = Stock.objects.filter(item__code=item.code, batch_no=batch_no).exists()
#             if stock_exists is False:
#                 msg = _(f"Batch no {batch_no} does not exist for item {item.code}.")
#                 self.add_error("batch_no", msg)


# WarehouseReleaseOrderItemInlineFormSet = forms.inlineformset_factory(
#     WarehouseReleaseOrder,
#     WarehouseReleaseOrderItem,
#     form=WarehouseReleaseOrderItemForm,
#     fields=("item", "batch_no", "expiry_date", "quantity", "uom"),
#     extra=1,
# )


# class WarehouseReleaseOrderForm(CoreModelForm):
#     """Form to create WarehouseReleaseOrder."""

#     consignor = forms.ChoiceField(label=_("Consignor"))
#     warehouses = forms.ModelMultipleChoiceField(
#         queryset=Warehouse.objects.all(),
#         widget=CoreGroupCheckboxSelectMultiple(),
#     )

#     class Meta:
#         model = WarehouseReleaseOrder
#         fields = [
#             "status",
#             "issued_by",
#             "consignee",
#             "release_datetime",
#             "customer_document_no",
#             "customer_reference",
#             "tag",
#             "warehouses",
#             "is_delivery_order",
#             "remark",
#         ]
#         widgets = {
#             "status": forms.HiddenInput(),
#             "remark": forms.Textarea(attrs={"cols": 80, "rows": 5}),
#         }

#     def __init__(self, *args, **kwargs):
#         super().__init__(*args, **kwargs)

#         self.fields["status"].disabled = True
#         self.fields["issued_by"].disabled = True
#         self.fields["consignor"].widget.attrs["class"] = "form-control core-select2 update-items"

#         consignor_qs = Consignor.objects.all()
#         blank_choices = BLANK_CHOICE_DASH
#         self.fields["consignor"].choices = blank_choices + [(consignor.pk, consignor) for consignor in consignor_qs]

#         if self.request.user.is_superuser is True:
#             self.fields["warehouses"].queryset = Warehouse.objects.all()
#         else:
#             user_warehouse = list(self.request.user.warehouses.all().values_list("pk", flat=True))
#             self.fields["warehouses"].queryset = Warehouse.objects.filter(pk__in=user_warehouse)

#         instance = getattr(self, "instance", None)
#         if instance:
#             if instance.status != WarehouseReleaseOrder.Status.NEW:
#                 self.fields["consignee"].disabled = True
#                 self.fields["release_datetime"].disabled = True

#             if hasattr(instance, "consignee"):
#                 self.fields["consignor"].initial = instance.consignee.consignor.pk

#     def clean(self):
#         cleaned_data = super().clean()
#         customer_document_no = cleaned_data.get("customer_document_no")
#         customer_reference = cleaned_data.get("customer_reference")

#         if (
#             customer_document_no
#             and WarehouseReleaseOrder.objects.filter(customer_document_no=customer_document_no).exists()
#         ):
#             self.add_error("customer_document_no", ValidationError(_("Customer Document No. already exists.")))

#         if customer_reference and WarehouseReleaseOrder.objects.filter(customer_reference=customer_reference).exists():
#             self.add_error("customer_reference", ValidationError(_("Customer Reference already exists.")))

#         return cleaned_data


# class WarehouseReleaseOrderUpdateStatusMixin(CoreModelForm):
#     """Mixin class to perform update on WarehouseReleaseOrder status."""

#     class Meta:
#         model = WarehouseReleaseOrder
#         fields = [
#             "status",
#         ]

#     def __init__(self, *args, **kwargs):
#         super().__init__(*args, **kwargs)

#         self.fields["status"].disabled = True


# class WarehouseReleaseOrderFinalPrintForm(WarehouseReleaseOrderUpdateStatusMixin):
#     """Form to perform Final Print for WarehouseReleaseOrder."""

#     pass


# class WarehouseReleaseOrderFinalReleaseForm(WarehouseReleaseOrderUpdateStatusMixin):
#     """Form to perform Final Release for WarehouseReleaseOrder."""

#     pass


# class WarehouseReleaseOrderObsoleteForm(WarehouseReleaseOrderUpdateStatusMixin):
#     """Form to perform Obsolete for WarehouseReleaseOrder."""

#     pass


# ##############
# # FOR WIZARD #
# ##############


# class WarehouseReleaseOrderImportStep1Form(CoreForm):
#     """Form to import WarehouseReleaseOrder."""

#     imported_outbound_file = forms.FileField(label=_("Upload File"), required=True)

#     def __init__(self, *args, **kwargs):
#         """Populating the choices of  the favorite_choices field using the favorites_choices kwargs"""

#         super().__init__(*args, **kwargs)

#     def validate_file_extension(self, file):
#         ext = os.path.splitext(file.name)[1]  # [0] returns path+filename
#         valid_extensions = [".txt"]
#         if not ext.lower() in valid_extensions:
#             raise ValidationError("Unsupported file extension.")

#     def clean_imported_outbound_file(self):
#         data = self.cleaned_data["imported_outbound_file"]

#         self.validate_file_extension(data)

#         self.imported_object = ImportOutboundMitsubishi(data)

#         is_valid = self.imported_object.is_valid()
#         if is_valid is False:
#             raise ValidationError(self.imported_object.error_messages_list)
#         else:
#             # To build cleaned data in import class
#             self.imported_object.cleaned()

#         return data


# class WarehouseReleaseOrderImportStep2Form(CoreForm):
#     customer_document_no = forms.CharField(required=False)
#     customer_reference = forms.CharField(required=False)
#     release_datetime = forms.DateTimeField()
#     warehouses = forms.ModelMultipleChoiceField(queryset=Warehouse.objects.all())


# class WarehouseReleaseOrderItemNewImportForm(CoreForm):
#     """Use as formset in wizard."""

#     ACTION_CHOICES = [
#         ["new", _("Create as NEW")],
#         ["existing", _("Replace EXISTING")],
#         ["skip", _("SKIP")],
#     ]

#     running_number = forms.CharField(required=True)
#     item_code = forms.CharField(required=False, help_text=_("This will be the new item code."))
#     item_name = forms.CharField(required=False, help_text=_("This will be the new item name."))
#     item = forms.ModelChoiceField(
#         required=False, queryset=Item.objects.all(), blank=True, help_text=_("Please choose existing item.")
#     )
#     quantity = forms.DecimalField(required=True)
#     uom = forms.ModelChoiceField(label=_("UOM"), required=True, queryset=UnitOfMeasure.objects.all())
#     customer_document_no = forms.CharField(required=True, widget=forms.HiddenInput())
#     customer_reference = forms.CharField(required=True, widget=forms.HiddenInput())
#     actions = forms.ChoiceField(
#         label=_("Actions"),
#         required=True,
#         choices=ACTION_CHOICES,
#         help_text=_("Please choose an action for missing item."),
#     )

#     def __init__(self, item_queryset=None, *args, **kwargs):
#         super().__init__(*args, **kwargs)

#         self.fields["running_number"].widget.attrs["readonly"] = True
#         self.fields["customer_document_no"].widget.attrs["readonly"] = True
#         self.fields["customer_reference"].widget.attrs["readonly"] = True

#         if item_queryset:
#             self.fields["item"].queryset = item_queryset


# WarehouseReleaseOrderItemNewImportFormSet = forms.formset_factory(WarehouseReleaseOrderItemNewImportForm, extra=0)


# ############
# # FOR HTMX #
# ############


# class WarehouseReleaseOrderInfoUpdateForm(CoreHtmxModelForm):
#     """Form to update WarehouseReleaseOrder's info."""

#     class Meta:
#         model = WarehouseReleaseOrder
#         fields = [
#             "status",
#             "issued_by",
#             "consignee",
#             "release_datetime",
#             "customer_document_no",
#             "customer_reference",
#             "tag",
#             "is_delivery_order",
#             "remark",
#         ]
#         widgets = {
#             "status": forms.HiddenInput(),
#             "remark": forms.Textarea(attrs={"cols": 80, "rows": 5}),
#         }

#     def __init__(self, *args, **kwargs):
#         super().__init__(*args, **kwargs)
#         instance = getattr(self, "instance", None)

#         self.fields["status"].disabled = True
#         self.fields["issued_by"].disabled = True
#         self.fields["consignee"].disabled = True

#         if instance and instance.status != WarehouseReleaseOrder.Status.NEW:
#             self.fields["consignee"].disabled = True
#             self.fields["release_datetime"].disabled = True

#     def clean(self):
#         cleaned_data = super().clean()
#         customer_document_no = cleaned_data.get("customer_document_no")
#         customer_reference = cleaned_data.get("customer_reference")

#         if (
#             customer_document_no
#             and WarehouseReleaseOrder.objects.filter(customer_document_no=customer_document_no)
#             .exclude(id=self.instance.id)
#             .exists()
#         ):
#             self.add_error("customer_document_no", ValidationError(_("Customer Document No. already exists.")))

#         if (
#             customer_reference
#             and WarehouseReleaseOrder.objects.filter(customer_reference=customer_reference)
#             .exclude(id=self.instance.id)
#             .exists()
#         ):
#             self.add_error("customer_reference", ValidationError(_("Customer Reference already exists.")))


# class WarehouseReleaseOrderPDFInfoUpdateForm(CoreHtmxModelForm):
#     """Form to update WarehouseReleaseOrder's pdf info."""

#     class Meta:
#         model = WarehouseReleaseOrder
#         fields = [
#             "show_shipper_info",
#             "shipper_address",
#             "shipper_phone",
#             "shipper_mobile",
#             "shipper_attn",
#             "shipping_address",
#             "shipping_phone",
#             "shipping_mobile",
#             "shipping_attn",
#             "total_cartons",
#             "total_weight",
#         ]
#         widgets = {
#             "shipper_address": forms.Textarea(attrs={"cols": 80, "rows": 5}),
#             "shipping_address": forms.Textarea(attrs={"cols": 80, "rows": 5}),
#         }


# class WarehouseReleaseOrderApproveAllForm(CoreForm):
#     """
#     Form to approve all fully picked WarehouseReleaseOrderItems.
#     """

#     remark = forms.CharField(widget=forms.Textarea(attrs={"cols": 80, "rows": 5}), required=False)

#     def save(self, *args, **kwargs):
#         instance = kwargs.pop("instance")
#         remark = self.cleaned_data.get("remark")

#         items = instance.warehouse_release_order_items.filter(
#             picker_status=WarehouseReleaseOrderItem.PickerStatus.PICKED_COMPLETE,
#             status=WarehouseReleaseOrderItem.Status.PROCESSING,
#         )
#         # Update items with save() method to trigger signals
#         for item in items:
#             item.status = WarehouseReleaseOrderItem.Status.APPROVED
#             item.remark = remark
#             item.approved_by = self.request.user
#             item.modified_by = self.request.user
#             item.save()

#         return instance


# class WarehouseReleaseOrderItemApproveForm(CoreHtmxModelForm):
#     """Form to approve a WarehouseReleaseOrderItem."""

#     class Meta:
#         model = WarehouseReleaseOrderItem
#         fields = ["status", "approved_by", "remark", "modified_by"]
#         widgets = {
#             "remark": forms.Textarea(attrs={"cols": 80, "rows": 9}),
#             "modified_by": forms.HiddenInput(),
#         }

#     def __init__(self, *args, **kwargs):
#         """Override fields configuration."""
#         super().__init__(*args, **kwargs)

#         self.fields["status"].disabled = True
#         self.fields["approved_by"].disabled = True


# class WarehouseReleaseOrderItemPickerForm(CoreHtmxModelForm, FormUOMSymbolMixin):
#     """Form to pick WarehouseReleaseOrderItemPicker."""

#     item_code = forms.CharField(label=_("Item Code"))
#     uom = forms.ChoiceField(label=_("UOM"), choices=[])
#     rack = forms.ModelChoiceField(
#         label=_("Rack"), queryset=Rack.objects.all(), required=False, widget=forms.HiddenInput()
#     )

#     class Meta:
#         model = WarehouseReleaseOrderItemPicker
#         fields = [
#             "item_code",
#             "batch_no",
#             "expiry_date",
#             "warehouse",
#             "quantity",
#             "uom",
#             "release_order_item",
#             "picked_datetime",
#             "picked_by",
#             "rack",
#         ]
#         widgets = {
#             "release_order_item": forms.HiddenInput(),
#             "picked_datetime": forms.HiddenInput(),
#             "picked_by": forms.HiddenInput(),
#         }

#     def __init__(self, *args, **kwargs):
#         """Override fields configuration."""
#         warehouses = kwargs.pop("warehouses")
#         self.reserved_rack_transactions = kwargs.pop("reserved_rack_transactions", None)

#         super().__init__(*args, **kwargs)

#         self.fields["warehouse"].queryset = warehouses
#         self.fields["uom"].choices = uom_choices_symbol()

#         # Add onclick select text
#         self.fields["batch_no"].widget.attrs["onClick"] = "this.select();"

#         wro_item = self.initial.get("release_order_item", None)
#         item = wro_item.item if wro_item is not None else None
#         uom = self.initial.get("uom", None)

#         # Override UOM choices
#         self.fields["uom"].choices = uom_choices_symbol(item=item, pre_selected_uom=uom)

#     def clean(self):
#         cleaned_data = super().clean()

#         rack = cleaned_data.get("rack", None)

#         if rack:
#             rack_transaction = self.reserved_rack_transactions.filter(rackstorage__rack=rack).first()
#             picked_quantity = rack_transaction.warehouse_release_order_item.get_picked_reserved_quantity(racks=rack)
#             remaining_quantity = rack_transaction.reserved_quantity - picked_quantity

#             uom = rack_transaction.rackstorage.stock.item.uom
#             item_code = rack_transaction.rackstorage.stock.item.code
#             batch_no = rack_transaction.rackstorage.stock.batch_no
#             expiry_date = rack_transaction.rackstorage.stock.expiry_date
#             warehouse = rack_transaction.warehouse_release_order.warehouses.first()
#             quantity = remaining_quantity
#             release_order_item = rack_transaction.warehouse_release_order_item
#         else:
#             item_code = cleaned_data["item_code"]
#             release_order_item = cleaned_data["release_order_item"]
#             warehouse = cleaned_data["warehouse"]
#             batch_no = cleaned_data["batch_no"]
#             expiry_date = cleaned_data["expiry_date"]
#             quantity = cleaned_data["quantity"]
#             uom = cleaned_data["uom"]

#             # Check if the item has reserved stock that has not been picked
#             reserved_racks = self.reserved_rack_transactions.values("rackstorage__rack")
#             total_picked_reserved_quantity = release_order_item.get_picked_reserved_quantity(racks=reserved_racks)
#             if total_picked_reserved_quantity < release_order_item.total_reserved_quantity:
#                 self.add_error(field=None, error="Please pick from the reserved stock first.")

#         if item_code != release_order_item.item.code:
#             msg = _("Item Code does not Match.")
#             self.add_error("item_code", msg)

#         stock = None

#         try:
#             stock = Stock.objects.get(
#                 warehouse=warehouse,
#                 item__code=item_code,
#                 batch_no=batch_no,
#                 expiry_date=expiry_date,
#                 item__consignor=release_order_item.release_order.consignee.consignor,
#             )
#         except Stock.DoesNotExist:
#             msg = _(
#                 f"Please double check Batch no {batch_no} with Expiry Date {expiry_date} exist in item {item_code} "
#                 f"and selected warehouse {warehouse}."
#             )
#             self.add_error("batch_no", msg)
#             self.add_error("expiry_date", msg)

#         if stock is not None and stock.balance < quantity:
#             msg = _("Stock balance is not enough.")
#             self.add_error("quantity", msg)
#         # else:
#         #     msg = _(f"Stock with Batch no {batch_no} does not exist in selected warehouse {warehouse}.")
#         #     self.add_error("warehouse", msg)

#         all_existing_pickers = release_order_item.warehouse_release_order_item_pickers.all()
#         total_picker_system_quantity = all_existing_pickers.aggregate(Sum("system_quantity")).get(
#             "system_quantity__sum", Decimal("0")
#         ) or Decimal("0")
#         system_quantity = uom_converter(
#             origin_uom=uom,
#             target_uom=release_order_item.uom,
#             quantity=quantity,
#             skip_unit_precision=True,
#         )
#         total_picker_system_quantity += system_quantity

#         try:
#             new_percentage = round((total_picker_system_quantity / release_order_item.quantity) * Decimal("100"), 0)
#         except decimal.DivisionByZero:
#             new_percentage = 0

#         # new_percentage here takes into account of overall combined incoming quantity and the existing ones
#         if new_percentage <= Decimal("0") or new_percentage > Decimal("100"):
#             self.add_error("quantity", f"Picking progress/percentage will be invalid:  {new_percentage}%")


# class WarehouseReleaseOrderItemRejectForm(CoreHtmxModelForm):
#     """Form to reject a WarehouseReleaseOrderItem."""

#     class Meta:
#         model = WarehouseReleaseOrderItem
#         fields = ["status", "remark", "modified_by"]
#         widgets = {
#             "remark": forms.Textarea(attrs={"cols": 80, "rows": 9}),
#             "modified_by": forms.HiddenInput(),
#         }

#     def __init__(self, *args, **kwargs):
#         """Disable status field"""
#         super().__init__(*args, **kwargs)
#         self.fields["status"].disabled = True


# class WarehouseReleaseOrderItemQuantityForm(CoreHtmxModelForm):
#     """Form to update expected quantity for WarehouseReleaseOrderItem."""

#     quantity = forms.CharField(
#         label=_("Expected Quantity"), widget=forms.TextInput(attrs={"onClick": "this.select();"})
#     )

#     class Meta:
#         model = WarehouseReleaseOrderItem
#         fields = [
#             "quantity",
#             "uom",
#         ]

#     def __init__(self, *args, **kwargs):
#         """Disable uom field"""
#         super().__init__(*args, **kwargs)
#         self.fields["uom"].disabled = True


# class WarehouseReleaseOrderItemCreateForm(CoreHtmxModelForm, FormUOMSymbolMixin):
#     """Form to create WarehouseReleaseOrderItem."""

#     class Meta:
#         model = WarehouseReleaseOrderItem
#         fields = [
#             "release_order",
#             "item",
#             "batch_no",
#             "expiry_date",
#             "uom",
#             "quantity",
#         ]
#         widgets = {
#             "release_order": forms.HiddenInput(),
#         }

#     def __init__(self, *args, **kwargs) -> None:
#         super().__init__(*args, **kwargs)

#         try:
#             ea_uom = UnitOfMeasure.objects.get(symbol="EA")
#         except Exception:
#             pass
#         else:
#             self.fields["uom"].initial = ea_uom

#         self.fields["uom"].choices = uom_choices_symbol()
#         self.fields["release_order"].initial = self.initial.get("warehouse_release_order")
#         self.fields["item"].widget.attrs["class"] = "form-control core-select2 item-of-consignor"

#     def clean(self) -> None:
#         """Check that if selected item belongs to parent form's consignor."""
#         cleaned_data = super().clean()
#         item = cleaned_data.get("item")
#         consignor = cleaned_data.get("release_order").consignee.consignor

#         if item and item.consignor.pk != consignor.pk:
#             msg = _(f"The selected item does not belongs to consignor {item.consignor}.")
#             self.add_error("item", msg)


# class WroImportBulkEditExcelForm(CoreForm):
#     """Form to import bulk update WRO(s) via Excel."""

#     upload_excel_file = forms.FileField(label=_("Upload Excel"), required=True)

#     def clean_upload_excel_file(self):
#         data = self.cleaned_data["upload_excel_file"]

#         self.imported_object = ImportBulkEditWroExcel(data=data)

#         if self.imported_object.is_valid():
#             # To build cleaned data in import class
#             self.imported_object.cleaned()
#             self.imported_object.process_bulk_edit_wro()

#         return data

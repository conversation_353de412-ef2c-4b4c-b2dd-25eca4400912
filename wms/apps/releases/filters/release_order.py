from django.db.models import Q
from django.utils.translation import gettext_lazy as _
from django_filters import filters, FilterSet

from wms.apps.releases.models import WarehouseReleaseOrder
from wms.cores.forms.widget import(
    CoreSelectWidget,
    CoreSelectMultipleWidget,
    CoreDateRangeWidget,
    CoreBooleanWidget,
    CoreTextWidget
)
from wms.cores.utils import get_user_warehouse_choices


class ReleaseOrderFilter(FilterSet):
    """
    Filter class for the Release Order list view.
    Provides filtering capabilities for Release Order attributes.
    """
    date_range = filters.DateFromToRangeFilter(
        label=_("Release Date Range"),
        field_name="release_datetime",
        widget=CoreDateRangeWidget()
    )

    status = filters.MultipleChoiceFilter(
        choices=WarehouseReleaseOrder.Status.choices,
        label=_("Status"),
        widget=CoreSelectMultipleWidget()
    )

    consignee__consignor = filters.ModelChoiceFilter(
        queryset=None,  # Will be set in __init__
        label=_("Consignor"),
        widget=CoreSelectWidget()
    )

    is_delivery_order = filters.BooleanFilter(
        label=_("Is Delivery Order"),
        widget=CoreSelectWidget(
            attrs={
                'id': 'id_is_delivery_order',
                'data-auto-submit': 'true'
            },
            choices=[
                ('', _('All')),
                ('true', _('Yes')),
                ('false', _('No')),
            ]
        )
    )

    warehouses = filters.MultipleChoiceFilter(
        choices=[],  # Will be set in __init__
        label=_("Warehouses"),
        widget=CoreSelectMultipleWidget()
    )

    picker_status = filters.MultipleChoiceFilter(
        choices=[],  # Will be set in __init__
        label=_("Picking Status"),
        widget=CoreSelectMultipleWidget()
    )

    batch_keyword_search = filters.CharFilter(
        label=_("Batch No."),
        method="custom_batch_keyword_filter",
        widget=CoreTextWidget()
    )
    tag = filters.CharFilter(
        label=_("Tag"),
        method="tag_filter",
        widget=CoreTextWidget()
    )

    class Meta:
        model = WarehouseReleaseOrder
        fields = ['date_range', 'status', 'warehouses', 'consignee__consignor', 'is_delivery_order', 'picker_status']

    def __init__(self, data=None, queryset=None, *, request=None, prefix=None):
        super().__init__(data=data, queryset=queryset, request=request, prefix=prefix)

        # Set up dynamic filter choices
        from wms.apps.releases.models import WarehouseReleaseOrderItem
        from wms.apps.consignors.models import Consignor

        # Filter out the warehouses based on User Role
        self.filters['warehouses'].extra['choices'] = get_user_warehouse_choices(request.user)

        # Set up consignor queryset
        self.filters['consignee__consignor'].queryset = Consignor.objects.all()

        # Set up picker status choices
        picker_status_choices = [(status, status) for status in WarehouseReleaseOrderItem.PickerStatus]
        self.filters['picker_status'].extra['choices'] = picker_status_choices

    def filter_queryset(self, queryset):
        """
        Apply custom filtering logic beyond the standard field filtering.
        """
        queryset = super().filter_queryset(queryset)

        # Apply picker status filter if provided
        picker_status = self.form.cleaned_data.get('picker_status')
        if picker_status:
            queryset = queryset.filter(
                warehouse_release_order_items__picker_status=picker_status
            ).distinct()

        return queryset

    def custom_batch_keyword_filter(self, queryset, name, value):
        qs = queryset

        for batch_keyword in value.split():
            qs = qs.filter(warehouse_release_order_items__batch_no__icontains=batch_keyword)

        return qs.distinct()

    def tag_filter(self, queryset, name, value):
        qs = queryset
        qs = qs.filter(tag__exact=value)
        return qs.distinct()

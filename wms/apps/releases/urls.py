from django.urls import include, path

from wms.apps.releases.views import (
    release_order_list_view,
    release_order_create_view,
    release_order_update_view,
    wro_import_bulk_update_excel_view,
    wro_import_bulk_update_summary_view,
)
from wms.apps.releases.views.release_order import (
    ReleaseOrderDetailHomeView, ReleaseOrderDataTableDetailView,
    ReleaseOrderDetailView, ReleaseOrderItemListView,
    release_order_obsolete_form, release_order_obsolete,
    release_order_delete_form, release_order_delete,
    release_order_proceed_print, release_order_proceed_release
)
from wms.apps.releases.views.release_order_item import (
    ReleaseOrderItemPickFormView,
    ReleaseOrderItemAddView,
    release_order_item_delete_form,
    release_order_item_delete,
    release_order_item_approve_form,
    release_order_item_approve,
    release_order_item_expected_quantity_form,
    release_order_item_expected_quantity_update
)

app_name = "releases"

release_orders_urlpatterns = [
    path("", view=release_order_list_view, name="list"),
    path("create/", view=release_order_create_view, name="create"),
    path("<int:pk>/update/", view=release_order_update_view, name="update"),
    path("<int:pk>/", view=ReleaseOrderDataTableDetailView.as_view(), name="panel"),
    path("<int:pk>/detail/", view=ReleaseOrderDetailView.as_view(), name="detail"),
    path("<int:pk>/detail-home/", view=ReleaseOrderDetailHomeView.as_view(), name="detail_home"),
    path("<int:pk>/item/list/", view=ReleaseOrderItemListView.as_view(), name="item_list"),
    # Import Excel Bulk Edit
    path("import-bulk-update/", view=wro_import_bulk_update_excel_view, name="import_bulk_update"),
    path("import-bulk-summary/", view=wro_import_bulk_update_summary_view, name="import_bulk_summary"),
    # Item actions
    path("item/<int:pk>/pick-form/", view=ReleaseOrderItemPickFormView.as_view(), name="item_pick_form"),
    path("item/<int:pk>/delete-form/", view=release_order_item_delete_form, name="item_delete_form"),
    path("item/<int:pk>/delete/", view=release_order_item_delete, name="item_delete"),
    path("item/<int:pk>/approve-form/", view=release_order_item_approve_form, name="item_approve_form"),
    path("item/<int:pk>/approve/", view=release_order_item_approve, name="item_approve"),
    path("item/<int:pk>/expected-quantity-form/", view=release_order_item_expected_quantity_form, name="item_expected_quantity_form"),
    path("item/<int:pk>/expected-quantity-update/", view=release_order_item_expected_quantity_update, name="item_expected_quantity_update"),
    path("<int:pk>/item-add-form/", view=ReleaseOrderItemAddView.as_view(), name="item_add_form"),
    path("<int:pk>/item-add/", view=ReleaseOrderItemAddView.as_view(), name="item_add"),
    # Release Order actions
    path("<int:pk>/obsolete-form/", view=release_order_obsolete_form, name="obsolete_form"),
    path("<int:pk>/obsolete/", view=release_order_obsolete, name="obsolete"),
    path("<int:pk>/delete-form/", view=release_order_delete_form, name="delete_form"),
    path("<int:pk>/delete/", view=release_order_delete, name="delete"),
    path("<int:pk>/proceed-print/", view=release_order_proceed_print, name="proceed_print"),
    path("<int:pk>/proceed-release/", view=release_order_proceed_release, name="proceed_release"),
]

# warehouse_release_orders_urlpatterns = [
#     path("", view=warehouse_release_order_list_view, name="list"),
    # Uncomment and implement these as needed
    # path("create/", view=warehouse_release_order_create_view, name="create"),
    # path("import/", view=warehouse_release_order_import_wizard_view, name="import"),
    # path("import-bulk-update/", view=wro_import_bulk_update_excel_view, name="import_bulk_update"),
    # path("import-bulk-summary/", view=wro_import_bulk_update_summary_view, name="import_bulk_summary"),
    # path("export-nem/<int:pk>/", view=warehouse_release_order_export_nem, name="export_nem"),
    # path("delete/<int:pk>/", view=warehouse_release_order_delete_view, name="delete"),
    # path("detail/<int:pk>/", view=warehouse_release_order_detail_view, name="detail"),
    # path("detail/<int:pk>/update/", view=warehouse_release_order_update_view, name="update"),
    # path("detail/<int:pk>/print-all/", view=warehouse_release_order_final_print_view, name="print_all"),
    # path("detail/<int:pk>/release-all/", view=warehouse_release_order_final_release_view, name="release_all"),
    # path("detail/<int:pk>/info/", view=warehouse_release_order_info_detail_view, name="info"),
    # path("detail/<int:pk>/info/update/", view=warehouse_release_order_info_update_view, name="info_update"),
#     path("detail/<int:pk>/info-pdf/", view=warehouse_release_order_info_pdf_detail_view, name="info_pdf"),
#     path("detail/<int:pk>/info-pdf/update/", view=warehouse_release_order_info_pdf_update_view, name="info_pdf_update"),
#     path("detail/<int:pk>/items/", view=warehouse_release_order_items_list_view, name="items"),
#     path("detail/<int:pk>/qrcode/", view=warehouse_release_order_print_qr_view, name="qrcode"),
#     path("detail/<int:pk>/print-info/", view=warehouse_release_order_print_info_view, name="print_info"),
#     path("detail/<int:pk>/rejects/", view=warehouse_release_order_rejects_list_view, name="rejects"),
#     path("datatables/", view=warehouse_release_order_datatables_view, name="datatables"),
#     path("datatables-detail/", view=warehouse_release_order_detail_datatables_view, name="datatables-detail"),
#     path("detail/<int:pk>/history/", view=warehouse_release_order_history_list_view, name="history"),
#     path(
#         "detail/<int:pk>/history/popup-modified",
#         view=warehouse_release_order_history_modified_view,
#         name="history-modified",
#     ),
#     path(
#         "detail/<int:pk>/datatables-history/",
#         view=warehouse_release_order_history_list_datatables_view,
#         name="datatables-history",
#     ),
#     path("print-button/", view=warehouse_release_order_print_button_view, name="print_button"),
#     path("release-button/", view=warehouse_release_order_release_button_view, name="release_button"),
#     path("detail/<int:pk>/obsolete/", view=warehouse_release_order_obsolete_view, name="obsolete"),
#     path("export-all/", view=export_all_warehouse_release_orders_to_xlsx_view, name="export_all"),
#     path("detail/<int:pk>/approve-all/", view=warehouse_release_order_approve_all_view, name="approve_all"),
# ]

# warehouse_release_orders_item_urlpatterns = [
#     path("<int:wro_pk>/create/", view=warehouse_release_order_item_create_view, name="create"),
#     path("delete/<int:pk>/", view=warehouse_release_order_item_delete_view, name="delete"),
#     path("detail/<int:pk>/picker/list/", view=warehouse_release_order_item_picker_list_view, name="picker_list"),
#     path("detail/<int:pk>/approve/", view=warehouse_release_order_item_approve_view, name="approve"),
#     path("detail/<int:pk>/pick/", view=warehouse_release_order_item_picker_view, name="pick"),
#     path("detail/<int:pk>/reject/", view=warehouse_release_order_item_reject_view, name="reject"),
#     path("detail/<int:pk>/tr/", view=warehouse_release_order_item_tr_view, name="tr"),
#     path("detail/<int:pk>/quantity/update/", view=warehouse_release_order_item_quantity_update_view, name="quantity"),
# ]

urlpatterns = [
    path("orders/",
        include(
            (release_orders_urlpatterns, "releases.release_orders"), namespace="orders"),
    ),
    # path(
    #     "warehouse-release-orders-item/",
    #     include(
    #         (warehouse_release_orders_item_urlpatterns, "releases.warehouse_release_orders_item"),
    #         namespace="warehouse_release_orders_item",
    #     ),
    # ),
]

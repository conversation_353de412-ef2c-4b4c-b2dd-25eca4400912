# Generated by Django 5.1 on 2025-03-15 13:12

import django.contrib.postgres.indexes
import django.core.validators
import django.db.models.deletion
import django.utils.timezone
import model_utils.fields
import wms.cores.models
import wms.cores.utils
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('cores', '0001_create_extensions'),
        ('consignees', '0001_initial'),
        ('inventories', '0001_initial'),
        ('pickings', '0001_initial'),
        ('settings', '0002_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='WarehouseReleaseOrder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('system_number', models.CharField(blank=True, max_length=32, verbose_name='System Number')),
                ('release_datetime', models.DateTimeField(default=wms.cores.utils.localtime_now, verbose_name='Release Date Time')),
                ('status', models.CharField(choices=[('New', 'New'), ('Processing', 'Processing'), ('Ready To Print', 'Ready To Print'), ('Ready To Release', 'Ready To Release'), ('Obsolete', 'Obsolete'), ('Completed', 'Completed'), ('Completed with reject', 'Completed with reject')], default='New', max_length=32, verbose_name='Status')),
                ('customer_document_no', models.CharField(blank=True, max_length=32, verbose_name='Customer Document No')),
                ('customer_reference', models.CharField(blank=True, max_length=64, verbose_name='Customer Reference')),
                ('tag', models.CharField(blank=True, max_length=256, verbose_name='Tag')),
                ('show_shipper_info', models.BooleanField(default=True, verbose_name='Show Shipper Info?')),
                ('total_cartons', models.CharField(blank=True, max_length=128, verbose_name='Total Cartons')),
                ('total_weight', models.CharField(blank=True, max_length=128, verbose_name='Total Weight')),
                ('shipper_address', models.TextField(blank=True, max_length=512, verbose_name='Shipper Address')),
                ('shipper_phone', models.CharField(blank=True, max_length=128, verbose_name='Shipper Phone')),
                ('shipper_mobile', models.CharField(blank=True, max_length=128, verbose_name='Shipper Mobile')),
                ('shipper_attn', models.CharField(blank=True, max_length=256, verbose_name='Shipper Attn')),
                ('shipping_address', models.TextField(blank=True, max_length=512, verbose_name='Shipping Address')),
                ('shipping_phone', models.CharField(blank=True, max_length=128, verbose_name='Shipping Phone')),
                ('shipping_mobile', models.CharField(blank=True, max_length=128, verbose_name='Shipping Mobile')),
                ('shipping_attn', models.CharField(blank=True, max_length=256, verbose_name='Shipping Attn')),
                ('imported_outbound_file', models.FileField(blank=True, null=True, upload_to='warehouse_release_orders', validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['txt', 'xml'])], verbose_name='Imported Outbound File')),
                ('is_delivery_order', models.BooleanField(default=False, verbose_name='Is Delivery Order?')),
                ('is_edi_confirmation_sent', models.BooleanField(default=False, help_text='To indicate EDI confirmation sent or not', verbose_name='Is EDI Confirmation Sent?')),
                ('is_from_edi', models.BooleanField(default=False, help_text='To differentiate WRO is manually created or from EDI', verbose_name='Is From EDI?')),
                ('is_reverse_pgi', models.BooleanField(default=False, help_text='To indicate whether the WRO have been reverse transaction through Adjustment', verbose_name='Is Reverse PGI?')),
                ('is_return', models.BooleanField(default=False, help_text='To indicate whether the WRO have been returned transaction through GRN', verbose_name='Is Return?')),
                ('consignor_outbound_delivery_no', models.CharField(blank=True, max_length=256, verbose_name='Outbound Delivery No')),
                ('consignor_picking_list_no', models.CharField(blank=True, max_length=256, verbose_name='Picking List No')),
                ('consignor_sales_order_no', models.CharField(blank=True, max_length=256, verbose_name='Sales Order No')),
                ('consignor_customer_requisition_no', models.CharField(blank=True, max_length=256, verbose_name='Customer Requisition No')),
                ('consignor_ppl_date', models.DateField(blank=True, null=True, verbose_name='PPL Posted date')),
                ('consignor_system_delivery_date', models.DateField(blank=True, null=True, verbose_name='System Delivery Date')),
                ('remark', models.TextField(blank=True, max_length=512, verbose_name='Remark')),
                ('consignee', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='consignees.consignee')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('issued_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Modified by')),
                ('picking_list', models.ForeignKey(blank=True, help_text='To indicate if this WRO is being release through which picking list', null=True, on_delete=django.db.models.deletion.SET_NULL, to='pickings.pickinglist')),
                ('warehouses', models.ManyToManyField(blank=True, related_name='wro_warehouses', to='settings.warehouse', verbose_name='Warehouses')),
            ],
            options={
                'ordering': ['-system_number'],
                'get_latest_by': 'created',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='DeliveryOrder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('system_number', models.CharField(blank=True, max_length=32, verbose_name='System Number')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('modified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Modified by')),
                ('warehouse_release_order', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='releases.warehousereleaseorder', verbose_name='Warehouse Release Order')),
            ],
            options={
                'ordering': ['-system_number'],
                'get_latest_by': 'created',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='WarehouseReleaseOrderItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('sort_order', models.PositiveIntegerField(default=0, verbose_name='Ordering')),
                ('status', models.CharField(choices=[('New', 'New'), ('Processing', 'Processing'), ('Approved', 'Approved'), ('Rejected', 'Rejected')], default='New', max_length=32, verbose_name='Status')),
                ('picker_status', models.CharField(choices=[('Draft', 'Draft'), ('Picked (Complete)', 'Picked (Complete)'), ('Picked (Incomplete)', 'Picked (Incomplete)')], default='Draft', max_length=32, verbose_name='Picker Status')),
                ('batch_no', models.CharField(default='N/A', max_length=255, verbose_name='Batch No.')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='Expiry Date')),
                ('is_serial_no', models.BooleanField(default=False, verbose_name='Is Serial Number?')),
                ('quantity', models.DecimalField(decimal_places=6, default=Decimal('0'), max_digits=19, validators=[wms.cores.models.greater_than_zero], verbose_name='Quantity')),
                ('remark', models.TextField(blank=True, max_length=512, verbose_name='Remark')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventories.item')),
                ('modified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Modified by')),
                ('release_order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='warehouse_release_order_items', to='releases.warehousereleaseorder')),
                ('uom', models.ForeignKey(help_text='The item will be measured in terms of this unit (e.g.: kg, pcs, box).', on_delete=django.db.models.deletion.RESTRICT, to='settings.unitofmeasure', verbose_name='UOM')),
            ],
            options={
                'ordering': ['sort_order'],
                'get_latest_by': 'created',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='WarehouseReleaseOrderItemPicker',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('quantity', models.DecimalField(decimal_places=6, default=Decimal('0'), max_digits=19, verbose_name='Quantity')),
                ('system_quantity', models.DecimalField(decimal_places=6, default=0, editable=False, max_digits=19, verbose_name='System Quantity')),
                ('picked_datetime', models.DateTimeField(verbose_name='Picked Date Time')),
                ('batch_no', models.CharField(default='N/A', max_length=255, verbose_name='Batch No.')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='Expiry Date')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('modified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Modified by')),
                ('picked_by', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('release_order_item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='warehouse_release_order_item_pickers', to='releases.warehousereleaseorderitem')),
                ('stock', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='inventories.stock')),
                ('uom', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='settings.unitofmeasure', verbose_name='UOM')),
                ('warehouse', models.ForeignKey(limit_choices_to={'is_storage': True}, on_delete=django.db.models.deletion.PROTECT, to='settings.warehouse')),
            ],
            options={
                'ordering': ['-created'],
                'get_latest_by': 'created',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='WarehouseReleaseOrderStockOut',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('released_quantity', models.DecimalField(decimal_places=6, default=Decimal('0'), max_digits=19, verbose_name='Released Quantity')),
                ('stock_out_datetime', models.DateTimeField(verbose_name='Stock Out Date Time')),
                ('remark', models.TextField(blank=True, max_length=512, verbose_name='Remark')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('modified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Modified by')),
                ('release_order_item', models.ForeignKey(help_text="Each Warehouse Release Order Item indicate a relation to item's released quantity.", on_delete=django.db.models.deletion.CASCADE, related_name='warehouse_release_order_stockouts', to='releases.warehousereleaseorderitem')),
                ('released_by', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('stock', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventories.stock')),
                ('transaction', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='inventories.transaction')),
                ('uom', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='settings.unitofmeasure', verbose_name='UOM')),
            ],
            options={
                'ordering': ['stock_out_datetime', 'pk'],
                'get_latest_by': 'created',
                'abstract': False,
            },
        ),
        migrations.AddIndex(
            model_name='warehousereleaseorder',
            index=django.contrib.postgres.indexes.GinIndex(fields=['customer_reference'], name='wro_customer_reference_gin', opclasses=['gin_trgm_ops']),
        ),
        migrations.AddIndex(
            model_name='warehousereleaseorder',
            index=django.contrib.postgres.indexes.GinIndex(fields=['remark'], name='wro_remark_gin', opclasses=['gin_trgm_ops']),
        ),
        migrations.AddIndex(
            model_name='warehousereleaseorder',
            index=models.Index(fields=['status'], name='releases_wa_status_f5a3b3_idx'),
        ),
        migrations.AddIndex(
            model_name='warehousereleaseorder',
            index=models.Index(fields=['consignee'], name='releases_wa_consign_a2ce0c_idx'),
        ),
        migrations.AddIndex(
            model_name='warehousereleaseorder',
            index=models.Index(fields=['created'], name='releases_wa_created_b592ec_idx'),
        ),
        migrations.AddIndex(
            model_name='warehousereleaseorder',
            index=models.Index(fields=['is_from_edi'], name='releases_wa_is_from_b1c60d_idx'),
        ),
        migrations.AddIndex(
            model_name='warehousereleaseorder',
            index=models.Index(fields=['consignor_outbound_delivery_no', 'is_from_edi', 'consignee'], name='releases_wa_consign_5176dd_idx'),
        ),
        migrations.AddIndex(
            model_name='warehousereleaseorder',
            index=models.Index(fields=['consignee', 'consignor_outbound_delivery_no'], name='releases_wa_consign_731eed_idx'),
        ),
        migrations.AddIndex(
            model_name='warehousereleaseorderitem',
            index=models.Index(fields=['release_order'], name='releases_wa_release_cb35dd_idx'),
        ),
        migrations.AddIndex(
            model_name='warehousereleaseorderitem',
            index=models.Index(fields=['picker_status'], name='releases_wa_picker__9658ac_idx'),
        ),
        migrations.AddIndex(
            model_name='warehousereleaseorderitem',
            index=models.Index(fields=['release_order', 'picker_status', 'status'], name='releases_wa_release_97025e_idx'),
        ),
        migrations.AddIndex(
            model_name='warehousereleaseorderitem',
            index=models.Index(fields=['release_order', 'status', 'picker_status'], name='releases_wa_release_d7c1a7_idx'),
        ),
        migrations.AddIndex(
            model_name='warehousereleaseorderitem',
            index=models.Index(fields=['status', 'picker_status'], name='releases_wa_status_3e8738_idx'),
        ),
        migrations.AddIndex(
            model_name='warehousereleaseorderitem',
            index=models.Index(fields=['picker_status', 'status'], name='releases_wa_picker__c695df_idx'),
        ),
        migrations.AddIndex(
            model_name='warehousereleaseorderitempicker',
            index=models.Index(fields=['release_order_item'], name='releases_wa_release_93dd5a_idx'),
        ),
        migrations.AddIndex(
            model_name='warehousereleaseorderitempicker',
            index=models.Index(fields=['stock'], name='releases_wa_stock_i_2a0416_idx'),
        ),
        migrations.AddIndex(
            model_name='warehousereleaseorderitempicker',
            index=models.Index(fields=['release_order_item', 'stock'], name='releases_wa_release_462f32_idx'),
        ),
        migrations.AddIndex(
            model_name='warehousereleaseorderstockout',
            index=models.Index(fields=['release_order_item'], name='releases_wa_release_91cba8_idx'),
        ),
    ]

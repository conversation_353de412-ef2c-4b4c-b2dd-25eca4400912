from decimal import Decimal

from django import forms
from django.utils.translation import gettext_lazy as _

from wms.cores.forms.fields import CoreModelForm
from wms.apps.releases.models import WarehouseReleaseOrderItem


class ReleaseOrderItemExpectedQuantityForm(CoreModelForm):
    """Form to update expected quantity for WarehouseReleaseOrderItem."""

    expected_quantity = forms.DecimalField(
        label=_("Expected Quantity"),
        min_value=0,
        required=True,
        widget=forms.NumberInput(attrs={"onClick": "this.select();"}),
    )

    class Meta:
        model = WarehouseReleaseOrderItem
        fields = []  # We'll handle the quantity field manually

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Initialize the expected_quantity field with the current quantity value
        if self.instance and self.instance.pk:
            self.fields['expected_quantity'].initial = self.instance.quantity

    def clean_expected_quantity(self):
        """Validate the expected quantity."""
        expected_quantity = self.cleaned_data.get("expected_quantity")

        if expected_quantity is None:
            raise forms.ValidationError(_("Expected quantity is required."))

        if expected_quantity <= 0:
            raise forms.ValidationError(_("Expected quantity must be greater than 0."))

        return expected_quantity

    def save(self, commit=True):
        """Save the form data to the model."""
        instance = self.instance
        expected_quantity = self.cleaned_data.get("expected_quantity")

        # Update the quantity field
        instance.quantity = Decimal(str(expected_quantity))

        if commit:
            instance.save()

        return instance

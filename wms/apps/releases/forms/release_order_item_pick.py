from django import forms
from django.db.models import BLANK_CHOICE_DASH
from django.utils.translation import gettext_lazy as _

from wms.cores.forms.fields import CoreModelForm, CoreModelChoiceField, FormFieldSize
from wms.cores.forms.widget import CoreNumberWidget

from wms.apps.releases.models import WarehouseReleaseOrderItem, WarehouseReleaseOrderItemPicker
from wms.apps.settings.models import Warehouse
from decimal import Decimal
from wms.cores.utils import format_decimal_values


class ReleaseOrderItemPickForm(CoreModelForm):
    """
    Form for picking a release order item.
    """
    warehouse = CoreModelChoiceField(
        queryset=Warehouse.objects.none(),
        label=_("Warehouse"),
        required=True,
        size=FormFieldSize.FULL
    )

    pick_quantity = forms.IntegerField(
        label=_("Pick Quantity"),
        required=True,
        widget=CoreNumberWidget(
            attrs={
                'class': 'w-full',
                'id': 'id_pick_quantity'
            }
        )
    )

    class Meta:
        model = WarehouseReleaseOrderItemPicker
        fields = ['warehouse', 'pick_quantity']

    def __init__(self, release_order_item=None, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Store the release_order_item for use in clean method
        self.release_order_item = release_order_item

        if release_order_item:
            # Set the queryset to only include storage warehouses from the release order
            self.fields['warehouse'].queryset = release_order_item.release_order.warehouses.filter(is_storage=True)

            # Calculate remaining quantity
            existing_picks = WarehouseReleaseOrderItemPicker.objects.filter(
                release_order_item=release_order_item
            ).exclude(pk=self.instance.pk if self.instance else None)

            total_picked = sum(pick.quantity for pick in existing_picks)
            expected_qty = Decimal(str(release_order_item.quantity))
            remaining_qty = expected_qty - Decimal(str(total_picked))
            remaining_qty = max(remaining_qty, Decimal('0'))

            # Format the remaining quantity to remove trailing zeros
            formatted_remaining_qty = format_decimal_values(remaining_qty)

            # Set the remaining quantity as the default value
            self.fields['pick_quantity'].widget.attrs['value'] = formatted_remaining_qty
            self.fields['pick_quantity'].initial = formatted_remaining_qty

    def clean(self):
        cleaned_data = super().clean()
        pick_quantity = cleaned_data.get('pick_quantity')
        warehouse = cleaned_data.get('warehouse')

        # If either field has an error, skip this validation
        if not pick_quantity or not warehouse or 'pick_quantity' in self._errors or 'warehouse' in self._errors:
            return cleaned_data

        # Get the release order item from the form instance
        release_order_item = getattr(self, 'release_order_item', None)
        if not release_order_item:
            return cleaned_data

        # Check if the total picked quantity would go negative
        existing_picks = WarehouseReleaseOrderItemPicker.objects.filter(
            release_order_item=release_order_item,
            warehouse=warehouse,
            batch_no=release_order_item.batch_no,
            expiry_date=release_order_item.expiry_date
        ).exclude(pk=self.instance.pk if self.instance else None)

        total_picked = sum(pick.quantity for pick in existing_picks)
        if (total_picked + pick_quantity) < 0:
            self.add_error('pick_quantity', _("Total picked quantity cannot go below zero. Current picked: {0}").format(total_picked))
            return cleaned_data

        if (total_picked + pick_quantity) > release_order_item.expected_quantity:
            self.add_error('pick_quantity', _("Pick quantity cannot be greater than expected quantity."))
            return cleaned_data

        # Check if there's enough stock with the exact matching criteria
        from wms.apps.inventories.models import Stock, Transaction

        try:
            # Try to find the exact stock matching all criteria
            stock_filter = {
                'warehouse': warehouse,
                'item': release_order_item.item,
            }

            # Add batch_no if specified
            if release_order_item.batch_no:
                stock_filter['batch_no'] = release_order_item.batch_no

            # Add expiry_date if specified
            if release_order_item.expiry_date:
                stock_filter['expiry_date'] = release_order_item.expiry_date

            # Add consignor check if available
            if hasattr(release_order_item.release_order, 'consignee') and \
               release_order_item.release_order.consignee and \
               hasattr(release_order_item.release_order.consignee, 'consignor') and \
               release_order_item.release_order.consignee.consignor:
                stock_filter['item__consignor'] = release_order_item.release_order.consignee.consignor

            # Get matching stocks
            matching_stocks = Stock.objects.filter(**stock_filter)
            if not matching_stocks.exists():
                self.add_error('warehouse', _("No matching stock found in this warehouse with the specified batch, expiry date, and consignor."))
                return cleaned_data

            # Check if there's enough stock
            total_available = 0
            for stock in matching_stocks:
                # Get the available balance (considering reserved quantities)
                stock_balance = Transaction.objects.balance_by_stock(stock)
                if hasattr(stock, 'available_balance'):
                    stock_balance = stock.available_balance
                total_available += stock_balance

            if total_available < pick_quantity:
                self.add_error('pick_quantity', _("Not enough stock available. Available: {0} {1}").format(
                    total_available, release_order_item.uom.symbol))

        except Exception as e:
            self.add_error('warehouse', _("Error checking stock: {0}").format(str(e)))

        return cleaned_data

    def clean_pick_quantity(self):
        pick_quantity = self.cleaned_data.get('pick_quantity')

        if not pick_quantity:
            raise forms.ValidationError(_("Pick quantity is required."))

        return pick_quantity

from django import forms
from django.core.exceptions import ValidationError
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from wms.apps.consignees.models import Consignee
from wms.apps.consignors.models import Consignor
from wms.apps.settings.models import UnitOfMeasure, Warehouse
from wms.apps.releases.models import WarehouseReleaseOrder, WarehouseReleaseOrderItem
from wms.apps.releases.imports.bulk_edit_wro import ImportBulkEditWroExcel
from wms.cores.forms.fields import CoreModelForm, CoreCharField, CoreForm
from wms.apps.inventories.models import Stock, Item
from wms.cores.forms.widget import CoreSelectWidget, CoreNumberWidget, CoreTextWidget, CoreCheckboxSelectMultipleWidget, \
    CoreCheckboxWidget


class NoValidationChoiceField(forms.ChoiceField):
    """
    A ChoiceField that doesn't validate its value.
    Used for dynamic fields that are populated via JavaScript.
    """

    def validate(self, value):
        pass  # Skip validation


class ReleaseOrderForm(CoreModelForm):
    """
    Form for creating and updating Release Orders.
    """
    # Hidden fields to store IDs
    consignor_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)
    consignee_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)

    consignor = forms.CharField(
        required=True,
        widget=CoreSelectWidget(
            attrs={
                'class': 'release-order-consignor',
                'data-api-url': '/api/consignors/',
            }
        ),
        help_text="Select a consignor first to enable consignee selection."
    )

    consignee = forms.CharField(
        required=True,
        disabled=True,
        widget=CoreSelectWidget(
            attrs={
                'class': 'release-order-consignee',
                'data-api-url': '/api/consignees/by-consignor/{consignor_id}/',
            }
        ),
    )

    warehouses = forms.ModelMultipleChoiceField(
        queryset=Warehouse.objects.all(),
        widget=CoreCheckboxSelectMultipleWidget(),
        help_text="Select one or more warehouses for this release order."
    )

    customer_document_no = CoreCharField(
        label="Document No",
        required=False,
        widget=CoreTextWidget(
            attrs={
                'class': 'w-xs',
            }
        )
    )

    customer_reference = CoreCharField(
        label="Reference",
        required=False,
        widget=CoreTextWidget(
            attrs={
                'class': 'w-xs',
            }
        )
    )

    tag = CoreCharField(
        label="Tag",
        required=False,
        widget=CoreTextWidget(
            attrs={
                'class': 'w-xs',
            }
        )
    )

    is_delivery_order = forms.BooleanField(
        required=False,
        widget=CoreCheckboxWidget()
    )

    class Meta:
        model = WarehouseReleaseOrder
        fields = [
            "status",
            "issued_by",
            "release_datetime",
            "consignor",
            "consignee",
            "customer_document_no",
            "customer_reference",
            "tag",
            "is_delivery_order",
            "warehouses",
            "remark",
        ]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.fields["status"].disabled = True
        self.fields["status"].widget = forms.HiddenInput()
        self.fields["issued_by"].disabled = True
        self.fields["release_datetime"].label = 'Release Time'
        self.fields["remark"].widget.attrs.update({"cols": 70, "rows": 4})

        # If we have an instance with a consignee or data with a consignee, we need to enable the consignee field
        if (self.instance and self.instance.pk and self.instance.consignee) or (
            self.data and 'consignee' in self.data and self.data['consignee']):
            self.fields["consignee"].disabled = False

            # Set the consignor field value to match the consignee's consignor
            if self.instance and self.instance.pk and self.instance.consignee:
                self.initial['consignor'] = self.instance.consignee.consignor.display_name
                self.initial['consignor_id'] = self.instance.consignee.consignor.pk
                self.initial['consignee_id'] = self.instance.consignee.pk

                # Update the consignee widget to show the current value
                self.fields["consignee"].widget.choices = [
                    (self.instance.consignee.pk, self.instance.consignee.display_name)
                ]

            # If we have data (form submission), ensure consignee is enabled
            if self.data and 'consignee' in self.data and self.data['consignee'] and 'consignor' in self.data and \
                self.data['consignor']:
                from wms.apps.consignees.models import Consignee
                try:
                    consignee_id = self.data.get('consignee_id') or self.data.get('consignee')
                    consignee = Consignee.objects.get(pk=consignee_id)
                    self.fields["consignee"].widget.choices = [
                        (consignee.pk, consignee.display_name)
                    ]
                except (Consignee.DoesNotExist, ValueError):
                    pass


    def clean(self):
        cleaned_data = super().clean()
        consignor_id = cleaned_data.get('consignor_id')
        consignee_id = cleaned_data.get('consignee_id')
        print(self.data)

        # If we don't have IDs but have names, try to get the IDs
        if not consignor_id and 'consignor' in cleaned_data:
            try:
                # Try to get consignor by name or ID
                consignor_name = cleaned_data.get('consignor')
                if consignor_name.isdigit():
                    consignor = Consignor.objects.filter(pk=consignor_name).first()
                else:
                    consignor = Consignor.objects.filter(display_name__iexact=consignor_name).first()
                    if not consignor:
                        # Try by code as fallback
                        consignor = Consignor.objects.filter(code__iexact=consignor_name).first()
                if consignor:
                    consignor_id = consignor.pk
                    cleaned_data['consignor_id'] = consignor_id
            except (Consignor.DoesNotExist, ValueError, AttributeError):
                pass

        if not consignee_id and 'consignee' in cleaned_data:
            try:
                # Try to get consignee by name or ID
                consignee_name = cleaned_data.get('consignee')
                if consignee_name.isdigit():
                    consignee = Consignee.objects.filter(pk=consignee_name).first()
                else:
                    consignee = Consignee.objects.filter(display_name__iexact=consignee_name).first()
                    if not consignee:
                        # Try by code as fallback
                        consignee = Consignee.objects.filter(code__iexact=consignee_name).first()
                if consignee:
                    consignee_id = consignee.pk
                    cleaned_data['consignee_id'] = consignee_id
            except (Consignee.DoesNotExist, ValueError, AttributeError):
                pass

        # Get the actual model instances
        consignor = Consignor.objects.filter(pk=consignor_id).first() if consignor_id else None
        consignee = Consignee.objects.filter(pk=consignee_id).first() if consignee_id else None

        if consignor and consignee and consignee.consignor != consignor:
            self.add_error('consignee', 'Consignee must belong to the selected consignor.')

        # Store the actual model instances in cleaned_data
        cleaned_data['consignor'] = consignor
        cleaned_data['consignee'] = consignee

        return cleaned_data


class ReleaseOrderItemForm(CoreModelForm):
    """
    Form for Release Order Items.
    """
    item = NoValidationChoiceField(
        disabled=True,
        required=True,
        widget=CoreSelectWidget(
            attrs={
                'class': 'release-order-item w-xl',
                'data-api-url': '/api/consignors/{consignor_id}/items/'
            }
        )
    )

    batch_no = NoValidationChoiceField(
        choices=[],
        required=False,
        widget=CoreSelectWidget(
            attrs={
                'class': 'release-order-batch w-40',
                'disabled': 'disabled',
            }
        )
    )

    expiry_date = NoValidationChoiceField(
        choices=[],
        required=False,
        widget=CoreSelectWidget(
            attrs={
                'class': 'transfer-item-expiry w-40',
                'disabled': 'disabled',
            }
        )
    )

    quantity = forms.DecimalField(
        required=True,
        widget=CoreNumberWidget(
            attrs={
                'class': 'release-order-quantity w-25',
            },
        )
    )

    uom = forms.CharField(
        required=False,
        widget=CoreTextWidget(
            attrs={
                'class': 'transfer-item-uom w-25',
                'disabled': 'disabled',
                'readonly': 'readonly'
            }
        )
    )

    item_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)
    uom_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)

    # Hidden fields to store batch_no and expiry_date values
    batch_no_hidden = forms.CharField(widget=forms.HiddenInput(), required=False)
    expiry_date_hidden = forms.CharField(widget=forms.HiddenInput(), required=False)

    class Meta:
        model = WarehouseReleaseOrderItem
        fields = [
            "item",
            "batch_no",
            "expiry_date",
            "quantity",
            "uom",
            "item_id",
            "uom_id",
            "batch_no_hidden",
            "expiry_date_hidden",
        ]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # If we have an instance with data, we need to populate the fields
        if self.instance and self.instance.pk:
            # If editing an existing release order item, we need to set the initial values
            if self.instance.item:
                # Set initial values for hidden fields
                self.initial['item_id'] = self.instance.item.id

                # Enable the item field and set its initial value
                self.fields['item'].disabled = False
                self.fields['item'].initial = self.instance.item.name

                # Set batch_no and expiry_date if available
                if self.instance.batch_no:
                    self.fields['batch_no'].initial = self.instance.batch_no
                    self.initial['batch_no_hidden'] = self.instance.batch_no

                if self.instance.expiry_date:
                    self.fields['expiry_date'].initial = self.instance.expiry_date
                    self.initial['expiry_date_hidden'] = self.instance.expiry_date.strftime(
                        '%Y-%m-%d') if self.instance.expiry_date else ''

    def clean(self):
        """Main form cleaning method"""
        cleaned_data = super().clean()
        item_id = cleaned_data.get('item_id')
        uom_id = cleaned_data.get('uom_id')
        batch_no = cleaned_data.get('batch_no')
        expiry_date = cleaned_data.get('expiry_date')

        def validate_item():
            """Validate item selection"""
            if not item_id:
                raise ValidationError("Item selection is required")

            try:
                return Item.objects.get(id=item_id)
            except Item.DoesNotExist:
                raise ValidationError("Invalid item selection")

        def validate_uom():
            """Validate UOM selection"""
            if not uom_id:
                raise ValidationError("UOM is required when item is selected")

            try:
                return UnitOfMeasure.objects.get(id=uom_id)
            except UnitOfMeasure.DoesNotExist:
                raise ValidationError("Invalid UOM selection")

        if expiry_date == 'N/A' or expiry_date == '':
            cleaned_data['expiry_date'] = None
            expiry_date = None

        # Validate UOM
        try:
            uom = validate_uom()
            cleaned_data['uom'] = uom
        except ValidationError as e:
            self.add_error('uom', str(e))
            raise

        # Validate item
        try:
            item = validate_item()
            cleaned_data['item'] = item
        except ValidationError as e:
            self.add_error('item', str(e))
            raise

        # Validate that UOM belongs to the selected item
        if item and uom and item.uom.id != uom.id:
            self.add_error('uom', f"The selected UOM ({uom}) does not match the item's UOM ({item.uom})")

        # Validate batch_no and expiry_date if provided
        if item:
            # Check if the batch_no exists for this item
            stock_exists = Stock.objects.filter(
                item=item,
                batch_no=batch_no
            ).exists()

            if not stock_exists:
                self.add_error('batch_no', f"Batch number does not exist or there is no stock record")

            # Convert string date to date object if needed
            if isinstance(expiry_date, str) and expiry_date:
                try:
                    from datetime import datetime
                    expiry_date = datetime.strptime(expiry_date, '%Y-%m-%d').date()
                except ValueError:
                    self.add_error('expiry_date', f"Invalid date format: {expiry_date}")
                    return cleaned_data

            # Check if the batch_no and expiry_date combination exists
            # Handle NULL expiry_date differently
            if stock_exists:
                if expiry_date is None:
                    stock_with_expiry_exists = Stock.objects.filter(
                        item=item,
                        batch_no=batch_no,
                        expiry_date__isnull=True
                    ).exists()
                else:
                    stock_with_expiry_exists = Stock.objects.filter(
                        item=item,
                        batch_no=batch_no,
                        expiry_date=expiry_date
                    ).exists()

                if not stock_with_expiry_exists:
                    self.add_error('expiry_date',
                                   f"Expiry date does not exist for the selected batch")

        return cleaned_data


class WroImportBulkEditExcelForm(CoreForm):
    """Form to import bulk update WRO(s) via Excel."""

    upload_excel_file = forms.FileField(label=_("Upload Excel"), required=True)

    def clean_upload_excel_file(self):
        data = self.cleaned_data["upload_excel_file"]

        self.imported_object = ImportBulkEditWroExcel(data=data)

        if self.imported_object.is_valid():
            # To build cleaned data in import class
            self.imported_object.cleaned()
            self.imported_object.process_bulk_edit_wro()

        return data

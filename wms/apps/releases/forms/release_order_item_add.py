from django import forms
from django.forms import ChoiceField
from django.utils.translation import gettext_lazy as _

from wms.cores.forms.fields import CoreModelForm
from wms.apps.inventories.models import Item
from wms.apps.settings.models import UnitOfMeasure
from wms.cores.forms.widget import CoreSelectWidget, CoreNumberWidget, CoreTextWidget
from wms.apps.releases.models import WarehouseReleaseOrderItem


class NoValidationChoiceField(ChoiceField):
    def validate(self, value):
        pass  # Skip validation


class ReleaseOrderItemAddForm(CoreModelForm):
    """
    Form for adding a single Release Order Item via modal.
    """
    item = forms.CharField(
        required=True,
        widget=CoreSelectWidget(
            attrs={
                'class': 'release-order-item w-full',
                'data-api-url': '/api/consignors/{consignor_id}/items/',
                'required': 'required'
            }
        )
    )

    batch_no = NoValidationChoiceField(
        choices=[],
        required=False,
        widget=CoreSelectWidget(
            attrs={
                'class': 'release-order-batch w-full',
                'disabled': 'disabled',
            }
        )
    )

    expiry_date = NoValidationChoiceField(
        choices=[],
        required=False,
        widget=CoreSelectWidget(
            attrs={
                'class': 'release-order-expiry w-full',
                'disabled': 'disabled',
            }
        )
    )

    quantity = forms.DecimalField(
        required=True,
        widget=CoreNumberWidget(
            attrs={
                'class': 'release-order-quantity w-full',
            },
        )
    )

    uom = forms.CharField(
        required=False,
        widget=CoreTextWidget(
            attrs={
                'class': 'release-order-uom w-full',
                'disabled': 'disabled',
                'readonly': 'readonly'
            }
        )
    )

    item_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)
    uom_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)

    # Hidden fields to store batch_no and expiry_date values
    batch_no_hidden = forms.CharField(widget=forms.HiddenInput(), required=False)
    expiry_date_hidden = forms.CharField(widget=forms.HiddenInput(), required=False)

    class Meta:
        model = WarehouseReleaseOrderItem
        fields = [
            "item",
            "batch_no",
            "expiry_date",
            "quantity",
            "uom",
            "item_id",
            "uom_id",
            "batch_no_hidden",
            "expiry_date_hidden",
        ]

    def __init__(self, release_order=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.release_order = release_order

        # Set the consignor_id data attribute for the item select if available
        if release_order and release_order.consignee and release_order.consignee.consignor:
            consignor_id = release_order.consignee.consignor.id
            # Replace the placeholder in the data-api-url attribute
            self.fields['item'].widget.attrs['data-api-url'] = self.fields['item'].widget.attrs['data-api-url'].replace(
                '{consignor_id}', str(consignor_id))
            # Add a data-consignor-id attribute for direct access in JavaScript
            self.fields['item'].widget.attrs['data-consignor-id'] = str(consignor_id)

    def clean(self):
        cleaned_data = super().clean()

        # Validate item selection
        item_id = cleaned_data.get('item_id')
        if not item_id:
            self.add_error('item', _('Please select a valid item.'))
            return cleaned_data

        # Validate UOM
        uom_id = cleaned_data.get('uom_id')
        if not uom_id:
            self.add_error('uom', _('UOM is required.'))

            # Get the actual model instances
        try:
            item = Item.objects.get(id=item_id)
            cleaned_data['item'] = item
        except Item.DoesNotExist:
            self.add_error('item', _('Invalid item selection.'))

        try:
            uom = UnitOfMeasure.objects.get(id=uom_id)
            cleaned_data['uom'] = uom
        except UnitOfMeasure.DoesNotExist:
            self.add_error('uom', _('Invalid UOM selection.'))

            # Validate that UOM belongs to the selected item
        if item and uom and item.uom.id != uom.id:
            self.add_error('uom', _(f'The selected UOM ({uom}) does not match the item\'s UOM ({item.uom})'))

            # Validate batch_no and expiry_date if provided
        batch_no = cleaned_data.get('batch_no_hidden')
        expiry_date = cleaned_data.get('expiry_date_hidden')

        if expiry_date == 'N/A' or expiry_date == '':
            cleaned_data['expiry_date'] = None
            expiry_date = None

        if item:
            # Check if the batch_no exists for this item
            from wms.apps.inventories.models import Stock

            stock_filter = {
                'item': item,
            }

            # Add batch_no if specified
            if batch_no:
                stock_filter['batch_no'] = batch_no

            # Add consignor check if available
            if self.release_order and self.release_order.consignee and self.release_order.consignee.consignor:
                stock_filter['item__consignor'] = self.release_order.consignee.consignor

            stock_exists = Stock.objects.filter(**stock_filter).exists()

            if not stock_exists:
                self.add_error('batch_no',
                               _('Batch number does not exist or there is no stock record for the selected item.'))

                # Convert string date to date object if needed
            if isinstance(expiry_date, str) and expiry_date:
                try:
                    from datetime import datetime
                    expiry_date = datetime.strptime(expiry_date, '%Y-%m-%d').date()
                except ValueError:
                    self.add_error('expiry_date', _(f'Invalid date format: {expiry_date}'))

                    # Check if the batch_no and expiry_date combination exists
            # Handle NULL expiry_date differently
            if stock_exists:
                if expiry_date is None:
                    stock_with_expiry_exists = Stock.objects.filter(
                        item=item,
                        batch_no=batch_no,
                        expiry_date__isnull=True
                    ).exists()
                else:
                    stock_with_expiry_exists = Stock.objects.filter(
                        item=item,
                        batch_no=batch_no,
                        expiry_date=expiry_date
                    ).exists()

                if not stock_with_expiry_exists:
                    self.add_error('expiry_date', _('Expiry date does not exist for the selected batch.'))

        # Validate quantity is greater than zero
        quantity = cleaned_data.get('quantity')
        if quantity is not None and quantity <= 0:
            self.add_error('quantity', _("Quantity must be greater than zero."))

        return cleaned_data

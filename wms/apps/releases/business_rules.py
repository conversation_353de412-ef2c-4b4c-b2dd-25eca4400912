from .models import WarehouseReleaseOrder


def check_current_state_status_before_commit_save(form_submitted_state_wro: WarehouseReleaseOrder) -> tuple[bool, str]:
    """
    Double check the current state of WarehouseReleaseOrder object's status is valid
    Before really committing a save() request.

    Scenario:
    - two browser tabs are opening the same WRO detail view
    - first tab clicked "Ready To Print" button,
      and transaction happened, Status of this WRO updated to READY TO RELEASE
    - second tab is still able to see the "Ready To Print" button,
      at this point of time, if User try to click "Ready To Print",
      this Latest WRO shouldn't be able to update
      from "Ready To Print" -> "Ready To RELEASE" anymore.
      Because the current state is already at "Ready To RELEASE".

    Return: Boolean, str
        - Boolean:
            True: Able to update the WRO
            False: Unable to update the WRO
        - str:
            either a error message or a success message
    """
    if isinstance(form_submitted_state_wro, WarehouseReleaseOrder):
        # to make sure it's an update.
        if form_submitted_state_wro.pk:
            latest_db_state_wro = WarehouseReleaseOrder.objects.get(pk=form_submitted_state_wro.pk)

            before_transaction_statuses = [
                WarehouseReleaseOrder.Status.NEW,
                WarehouseReleaseOrder.Status.PROCESSING,
                WarehouseReleaseOrder.Status.READY_TO_PRINT
            ]
            after_transaction_statuses = [
                WarehouseReleaseOrder.Status.READY_TO_RELEASE,
                WarehouseReleaseOrder.Status.OBSOLETE,
                WarehouseReleaseOrder.Status.COMPLETED,
                WarehouseReleaseOrder.Status.COMPLETED_WITH_REJECT
            ]
            alterable_transaction_status = [
                WarehouseReleaseOrder.Status.NEW,
                WarehouseReleaseOrder.Status.PROCESSING,
                WarehouseReleaseOrder.Status.READY_TO_PRINT,
                WarehouseReleaseOrder.Status.READY_TO_RELEASE
            ]
            unalterable_transaction_status = [
                WarehouseReleaseOrder.Status.OBSOLETE,
                WarehouseReleaseOrder.Status.COMPLETED,
                WarehouseReleaseOrder.Status.COMPLETED_WITH_REJECT
            ]

            # Scenario needs to covered(on this whole if else logic):
            # 1) if current obj already in unalterable_transaction_status,
            #    and new obj's status are from alterable_transaction_status, to request to udpate anything
            #    this new obj's action should be blocked/stop/reject due to it's not normal.
            #    - Simple Example: my WRO's db state is already COMPLETED,
            #                      Why is my new form submitted WRO's state is still NEW/PROCESSING? doesn't make sense.
            # 2) if current obj already in unalterable_transaction_status,
            #    I should still be able to update some general info such as shipping details and etc.
            if (
                latest_db_state_wro.status in unalterable_transaction_status
                and form_submitted_state_wro.status in alterable_transaction_status
            ):
                return False, (
                    f"WRO currently in state of {latest_db_state_wro.status}, unable to update it to "
                    f"{form_submitted_state_wro.status} Status."
                )
            # this elif have a special case to handle on the state of READY_TO_RELEASE,
            # so it's a little different from above mentioned Scenario 1)
            elif (
                latest_db_state_wro.status in after_transaction_statuses
                and form_submitted_state_wro.status in before_transaction_statuses
            ):
                return False, (
                    f"WRO currently in state of {latest_db_state_wro.status}, unable to update it from "
                    f"{form_submitted_state_wro.status} Status."
                )
            else:
                return True, "Success"

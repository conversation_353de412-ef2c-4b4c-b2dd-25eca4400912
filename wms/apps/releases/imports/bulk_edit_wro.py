import logging
import os
from datetime import datetime, time

from django.core.exceptions import MultipleObjectsReturned, ValidationError

import openpyxl
import pytz

from wms.apps.releases.models import WarehouseReleaseOrder

logger = logging.getLogger(__name__)


class ImportBulkEditWroExcel:
    """
    Base class for handling imported Excel sheet for bulk edit WRO data.
    """

    def __init__(self, data=None, user=None) -> None:
        self.user = user
        self.data = data
        self.delivery_number_cells = ()
        self._valid = False
        self.import_line_list = []
        self.request_delivery_datetime = None
        self.summary = {
            "delivery_number_count": 0,
            # Those keys below that ends with "*_info" stores list of line numbers from Excel
            # for tracking purposes in Summary page later.
            "skip_rows_with_empty_delivery_number": 0,
            "skip_rows_with_empty_delivery_number_info": [],
            "skip_rows_with_invalid_delivery_number": 0,
            "skip_rows_with_invalid_delivery_number_info": [],
            "skip_delivery_number_duplicated": 0,
            "skip_delivery_number_duplicated_info": [],
            # "skip_delivery_number_invalid_delivery_number_status": 0,
            # "skip_delivery_number_invalid_delivery_number_status_info": [],
            "skip_delivery_number_invalid_request_delivery_date": 0,
            "skip_delivery_number_invalid_request_delivery_date_info": [],
        }

    def _check_file_extension(self):
        ext = os.path.splitext(self.data.name)[1]  # [0] returns path+filename
        valid_extensions = [".xlsx"]
        if not ext.lower() in valid_extensions:
            raise ValidationError(f"Unsupported file extension for '{ext}'.")

    def _check_valid_request_delivery_date(self, date_obj, date_format="%Y/%m/%d"):
        try:
            excel_empty_value_list = ["", "None", None]
            if date_obj in excel_empty_value_list:
                return False

            # Try to parse the string into a date
            date_obj.strftime(date_format)

            # Malaysia timezone
            malaysia_timezone = pytz.timezone("Asia/Kuala_Lumpur")

            # Combine date with time, then add timezone
            datetime_obj = malaysia_timezone.localize(datetime.combine(date_obj, time(0, 0)))

            self.request_delivery_datetime = datetime_obj

            return True
        except (Exception, ValueError):
            # If parsing fails, it's not in the correct format
            return False

    def _check_header_row(self, header_cells: tuple) -> None:
        """
        Gracefully returns error messages if the imported header row does not fulfill
        the minimum criteria of labelling from Floor to Pallet at header columns.
        """
        extracted_header_cells = header_cells[0]
        check_header_cells = {
            0: "Delivery Number",
            1: "Tag",
            2: "Requested Delivery Date",
        }

        for idx in list(check_header_cells.keys()):
            if (
                extracted_header_cells[idx].value is not None
                and isinstance(extracted_header_cells[idx].value, str)
                and extracted_header_cells[idx].value.upper().strip() != check_header_cells[idx].upper()
            ):
                raise ValidationError(
                    f"Invalid data format at Line 2's Header: Expected {check_header_cells[idx]} "
                    + f"but instead gotten {extracted_header_cells[idx].value}"
                )

    def cleaned(self):
        """
        Function to build cleaned data from uploaded file and track unsual values based on expected format.

        Make this function public, so that it can also be reused and called by external places whenever
        we instantiate this class.
        """

        if self._valid is True:
            # Call private method
            self._check_delivery_number_cells()

    def _check_delivery_number_values(self, value) -> str:
        """
        Check the incoming value for all the formats, for column A (WRO column),
        """
        output = ""

        excel_empty_value_list = ["", "None", None]

        if value not in excel_empty_value_list:
            if isinstance(value, str):
                formatted_valid_delivery_number_str = value.strip()
            elif isinstance(value, int):
                formatted_valid_delivery_number_str = str(value).strip()
            else:
                formatted_valid_delivery_number_str = str(int(value)).strip()
            output = formatted_valid_delivery_number_str
        return output

    def _check_delivery_number_cells(self):
        """
        * Update new row/entries within self.import_line_list to be further processed later.
        """
        prepared_list = []

        for delivery_number_cell, tag_cell, rdd_cell in self.delivery_number_cells:

            excel_empty_value_list = ["", "None", None]
            if delivery_number_cell.value in excel_empty_value_list and tag_cell.value in excel_empty_value_list:
                line_number = "Empty row on this line."
            elif delivery_number_cell.value in excel_empty_value_list:
                line_number = tag_cell.row
            else:
                line_number = delivery_number_cell.row
            delivery_number = self._check_delivery_number_values(delivery_number_cell.value)
            tag = str(tag_cell.value)
            request_delivery_date = rdd_cell.value

            # Skips current row if there's empty row in all of the rack naming (column B to column G)
            if delivery_number == "":
                self.summary["skip_rows_with_empty_delivery_number"] += 1
                self.summary["skip_rows_with_empty_delivery_number_info"].append({f"Line {line_number}": ""})
                continue

            prepared_list.append([delivery_number, tag, request_delivery_date, line_number])

        # check if delivery number cell has duplicates
        # Initialize an empty set to track first elements
        delivery_numbers_set = set()
        delivery_number_duplicates = []
        line_lists = []

        # Check for duplicates in the first element(delivery_number) and second element(DN) of each sublist
        for each_list in prepared_list:
            delivery_number = each_list[0]

            if delivery_number in delivery_numbers_set:
                delivery_number_duplicates.append(delivery_number)
                continue
            else:
                delivery_numbers_set.add(delivery_number)

        unique_delivery_number_duplicates = list(set(delivery_number_duplicates))

        for line_list in prepared_list:
            if line_list[0] not in unique_delivery_number_duplicates:
                line_lists.append(line_list)
            elif line_list[0] in unique_delivery_number_duplicates:
                error_msg = (
                    "Error when reading Delivery Number field at column A, "
                    + f"It is duplicated {line_list[0]}. "
                    + "Skipped updating WRO for current line."
                )
                logger.error(error_msg)
                self.summary["skip_delivery_number_duplicated"] += 1
                self.summary["skip_delivery_number_duplicated_info"].append(
                    {f"Line {line_list[3]}, Column A": line_list[0]}
                )

        self.import_line_list = line_lists

    def process_bulk_edit_wro(self) -> None:
        """
        To bulk update WRO based on validated self.import_line_list.

        Example of self.import_line_list:

        self.import_line_list = [
            ["350000001", "ABC", datetime.datetime(2026, 05, 31, 0, 0), 1]
            ...
        ]
        """
        for (delivery_number, tag, request_delivery_date, line_number) in self.import_line_list:

            self.request_delivery_datetime = None

            if self._check_valid_request_delivery_date(request_delivery_date) is False:
                error_msg = (
                    "Error when reading Request Delivery Date field, expected yyyy/MM/dd "
                    + f"at column D but instead got {request_delivery_date}. "
                    + "Skipped updating WRO for current line."
                )
                logger.error(error_msg)
                self.summary["skip_delivery_number_invalid_request_delivery_date"] += 1
                self.summary["skip_delivery_number_invalid_request_delivery_date_info"].append(
                    {f"Line {line_number}, Column C": request_delivery_date}
                )
                continue

            delivery_number_obj = None
            try:
                delivery_number_obj = WarehouseReleaseOrder.objects.get(
                    consignee__consignor__code="FMCSB", consignor_outbound_delivery_no=delivery_number
                )

                if delivery_number_obj is not None and self.request_delivery_datetime is not None:
                    delivery_number_obj.tag = tag
                    delivery_number_obj.release_datetime = self.request_delivery_datetime
                    delivery_number_obj.modified_by = self.user
                    delivery_number_obj.save(update_fields=["tag", "release_datetime"])
                    self.summary["delivery_number_count"] += 1

            except WarehouseReleaseOrder.DoesNotExist:
                error_msg = (
                    f"Error when reading Delivery Number field, {delivery_number} is invalid "
                    + "Skipped updating WRO for current line."
                )
                logger.error(error_msg)
                self.summary["skip_rows_with_invalid_delivery_number"] += 1
                self.summary["skip_rows_with_invalid_delivery_number_info"].append(
                    {f"Line {line_number}, Column A": delivery_number}
                )
                continue
            except MultipleObjectsReturned:
                delivery_number_qs = WarehouseReleaseOrder.objects.filter(
                    consignee__consignor__code="FMCSB", consignor_outbound_delivery_no=delivery_number
                )

                if delivery_number_qs and self.request_delivery_datetime is not None:
                    for delivery_number_obj in delivery_number_qs:
                        delivery_number_obj.tag = tag
                        delivery_number_obj.release_datetime = self.request_delivery_datetime
                        delivery_number_obj.modified_by = self.user
                        delivery_number_obj.save(update_fields=["tag", "release_datetime"])
                        self.summary["delivery_number_count"] += 1
                continue

        return None

    def is_valid(self):
        """Function to check if the uploaded file is valid."""

        valid = True

        self._check_file_extension()

        # read_only argument uses much less memory & faster but not all
        # features are available (charts, images, etc.)
        # data_only argument ensures that we only retrieve values, not formulae for Qty column
        book = openpyxl.load_workbook(self.data, read_only=True, data_only=True)
        sheet = book.active

        try:
            # To determine the starting row
            starting_row = 2

            # To determine the final row
            final_row = len([i for i in sheet.rows])

            header_cells = sheet[f"A{starting_row-1}":f"C{starting_row-1}"]
            self._check_header_row(header_cells)

            delivery_number_cells = sheet[f"A{starting_row}":f"C{final_row}"]
            self.delivery_number_cells = delivery_number_cells
        except (ValueError, TypeError) as ex:
            logger.error(ex)
            valid = False
            raise ValidationError(ex)

        self._valid = valid

        return self._valid

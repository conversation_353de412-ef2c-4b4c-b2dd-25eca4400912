from django.contrib import admin
from django.utils.translation import gettext_lazy as _

from wms.cores.admin import BaseModelAdmin, BaseTabularInline

from ..models import (
    WarehouseReleaseOrder,
    WarehouseReleaseOrderItem,
    WarehouseReleaseOrderItemPicker,
    WarehouseReleaseOrderStockOut,
)


class WarehouseReleaseOrderItemPickerInline(BaseTabularInline):
    model = WarehouseReleaseOrderItemPicker
    extra = 1


class WarehouseReleaseOrderItemInline(BaseTabularInline):
    model = WarehouseReleaseOrderItem
    extra = 1


@admin.register(WarehouseReleaseOrder)
class WarehouseReleaseOrderAdmin(BaseModelAdmin):
    """Django admin for WarehouseReleaseOrder."""

    list_display = [
        "system_number",
        "issued_by",
        "consignee",
        "release_datetime",
        "status",
        "is_edi_confirmation_sent",
        "picking_list",
        "tag",
        "remark",
    ]
    fieldsets = (
        (
            None,
            {
                "fields": (
                    "issued_by",
                    "consignee",
                    "release_datetime",
                    "status",
                    "customer_document_no",
                    "customer_reference",
                    "total_cartons",
                    "total_weight",
                    "imported_outbound_file",
                    "warehouses",
                    "is_delivery_order",
                    "is_edi_confirmation_sent",
                    "is_from_edi",
                    "show_shipper_info",
                    "picking_list",
                    "tag",
                    "remark",
                )
            },
        ),
        (
            _("Consignor's side info"),
            {
                "fields": (
                    "consignor_picking_list_no",
                    "consignor_sales_order_no",
                    "consignor_customer_requisition_no",
                    "consignor_ppl_date",
                    "consignor_system_delivery_date",
                )
            },
        ),
        (
            _("Shipper info"),
            {
                "fields": (
                    "shipper_address",
                    "shipper_phone",
                    "shipper_mobile",
                    "shipper_attn",
                )
            },
        ),
        (
            _("Shipping info"),
            {
                "fields": (
                    "shipping_address",
                    "shipping_phone",
                    "shipping_mobile",
                    "shipping_attn",
                )
            },
        ),
    )
    inlines = [WarehouseReleaseOrderItemInline]
    search_fields = ["system_number", "consignee__display_name"]
    list_filter = ["status", "consignee__consignor"]


@admin.register(WarehouseReleaseOrderItem)
class WarehouseReleaseOrderItemAdmin(BaseModelAdmin):
    """Django admin for WarehouseReleaseOrderItem."""

    list_display = [
        "release_order",
        "item",
        "uom",
        "quantity",
        "batch_no",
        "remark",
        "status",
        "picker_status",
        "is_serial_no",
        "is_rack_assigned",
    ]
    inlines = [WarehouseReleaseOrderItemPickerInline]
    search_fields = ["item__name", "release_order__system_number"]
    list_filter = ["status", "picker_status", "release_order__consignee__consignor"]


@admin.register(WarehouseReleaseOrderItemPicker)
class WarehouseReleaseOrderItemPickerAdmin(BaseModelAdmin):
    """Django admin for WarehouseReleaseOrderItemPicker."""

    list_display = [
        "release_order_item",
        "stock",
        "quantity",
        "uom",
        "system_quantity",
    ]
    search_fields = [
        "release_order_item__release_order__system_number",
        "stock__item__name",
        "stock__warehouse__name",
        "stock__batch_no",
    ]


@admin.register(WarehouseReleaseOrderStockOut)
class WarehouseReleaseOrderStockOutAdmin(BaseModelAdmin):
    list_display = [
        "release_order_item",
        "released_by",
        "released_quantity",
        "stock_out_datetime",
        "stock",
        "uom",
        "remark",
        "transaction",
    ]
    raw_id_fields = (
        "transaction",
        "release_order_item",
        "stock",
    )
    search_fields = (
        "release_order_item__release_order__system_number",
        "stock__item__name",
        "stock__warehouse__name",
    )
    list_filter = [
        "stock__warehouse",
        "stock__item",
        "stock__batch_no",
        "release_order_item__release_order__consignee__consignor",
    ]
    date_hierarchy = "stock_out_datetime"

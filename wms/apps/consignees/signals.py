import logging

from django.db.models.signals import post_save
from django.dispatch import receiver

from wms.cores.actstream import register_stream

from .models import <PERSON><PERSON><PERSON><PERSON><PERSON>, Consignee, Consignee<PERSON>ont<PERSON>, ShippingAddress

logger = logging.getLogger(__name__)

#################
# FOR ACTSTREAM #
#################


register_stream(Consignee, logger=logger)
register_stream(ConsigneeContact, logger=logger, parent_field="consignee")
register_stream(ShippingAddress, logger=logger, parent_field="consignee")
register_stream(Billing<PERSON>dd<PERSON>, logger=logger, parent_field="consignee")


###################
# FOR APP SIGNALS #
###################


@receiver(post_save, sender=Consignee)
def generate_system_number(sender, instance, created=False, **kwargs):
    """To generate numbering on instance."""
    if created:
        instance.generate_system_number()
        logger.info(f"SIGNAL: {sender.__name__}: generated numbering for {instance}")

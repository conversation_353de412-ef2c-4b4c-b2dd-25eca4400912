from django import forms
from django.utils.translation import gettext_lazy as _

from phonenumber_field.formfields import PhoneNumber<PERSON>ield

# from wss.cores.forms import CoreHtmxModelForm, CoreModelForm
from wms.cores.forms.fields import CoreModelForm, CoreCharField, SizedFormField, FormFieldSize, CoreChoiceField

from .models import BillingAddress, Consignee, ConsigneeContact, ShippingAddress


class ConsigneeForm(CoreModelForm):

    is_billing_same_as_shipping = forms.BooleanField(label=_("Same as billing address"), required=False)
    salutation = CoreChoiceField(label=_("Salutation"), required=False, choices=ConsigneeContact.Salutation.choices)
    first_name = <PERSON>CharField(label=_("First Name"), required=False)
    last_name = CoreCharField(label=_("Last Name"), required=False)
    email = forms.EmailField(label=_("Email"), required=False)
    phone = PhoneNumberField(
        label=_("Phone"),
        required=False,
        error_messages={"invalid": _("Please enter a valid phone number (e.g. +60128585299).")},
        help_text=_("Phone number (e.g. +60128585299)."),
    )
    designation = CoreCharField(label=_("Designation"), required=False)
    department = CoreCharField(label=_("Department"), required=False)
    # Billing Address Form fields
    billing_address_attention = CoreCharField(label=_("Attention"), required=False)
    billing_address_street_1 = CoreCharField(label=_("Street 1"), required=False)
    billing_address_street_2 = CoreCharField(label=_("Street 2"), required=False)
    billing_address_postal_code = CoreCharField(label=_("Postal Code"), required=False)
    billing_address_district = CoreCharField(label=_("District"), required=False)
    billing_address_city = CoreCharField(label=_("City"), required=False)
    billing_address_state = CoreCharField(label=_("State"), required=False)
    billing_address_country = CoreCharField(label=_("Country"), required=False)
    billing_address_phone = CoreCharField(label=_("Phone"), required=False)
    # Shipping Address Form fields
    shipping_address_attention = CoreCharField(label=_("Attention"), required=False)
    shipping_address_street_1 = CoreCharField(label=_("Street 1"), required=False)
    shipping_address_street_2 = CoreCharField(label=_("Street 2"), required=False)
    shipping_address_postal_code = CoreCharField(label=_("Postal Code"), required=False)
    shipping_address_district = CoreCharField(label=_("District"), required=False)
    shipping_address_city = CoreCharField(label=_("City"), required=False)
    shipping_address_state = CoreCharField(label=_("State"), required=False)
    shipping_address_country = CoreCharField(label=_("Country"), required=False)
    shipping_address_phone = CoreCharField(label=_("Phone"), required=False)

    class Meta:
        model = Consignee
        fields = [
            "company_name",
            "display_name",
            "slug",
            "code",
            "website",
            "consignor",
            "remark",
        ]
        widgets = {
            "remark": forms.Textarea(attrs={"cols": 80, "rows": 9}),
        }

    def clean(self):
        cleaned_data = super().clean()

        salutation = cleaned_data.get("salutation")
        first_name = cleaned_data.get("first_name")
        last_name = cleaned_data.get("last_name")
        email = cleaned_data.get("email")
        phone = cleaned_data.get("phone")
        designation = cleaned_data.get("designation")
        department = cleaned_data.get("department")

        if (salutation or last_name or email or phone or designation or department) and not first_name:
            msg = _("Please enter first name.")
            self.add_error("first_name", msg)


class ConsigneeUpdateForm(CoreModelForm):
    is_billing_same_as_shipping = forms.BooleanField(label=_("Same as billing address"), required=False)
    salutation = CoreChoiceField(label=_("Salutation"), required=False, choices=ConsigneeContact.Salutation.choices)
    first_name = CoreCharField(label=_("First Name"), required=False)
    last_name = CoreCharField(label=_("Last Name"), required=False)
    email = forms.EmailField(label=_("Email"), required=False)
    phone = PhoneNumberField(
        label=_("Phone"),
        required=False,
        error_messages={"invalid": _("Please enter a valid phone number (e.g. +60128585299).")},
        help_text=_("Phone number (e.g. +60128585299)."),
    )
    designation = CoreCharField(label=_("Designation"), required=False)
    department = CoreCharField(label=_("Department"), required=False)
    # Billing Address Form fields
    billing_address_attention = CoreCharField(label=_("Attention"), required=False)
    billing_address_street_1 = CoreCharField(label=_("Street 1"), required=False)
    billing_address_street_2 = CoreCharField(label=_("Street 2"), required=False)
    billing_address_postal_code = CoreCharField(label=_("Postal Code"), required=False)
    billing_address_district = CoreCharField(label=_("District"), required=False)
    billing_address_city = CoreCharField(label=_("City"), required=False)
    billing_address_state = CoreCharField(label=_("State"), required=False)
    billing_address_country = CoreCharField(label=_("Country"), required=False)
    billing_address_phone = CoreCharField(label=_("Phone"), required=False)
    # Shipping Address Form fields
    shipping_address_attention = CoreCharField(label=_("Attention"), required=False)
    shipping_address_street_1 = CoreCharField(label=_("Street 1"), required=False)
    shipping_address_street_2 = CoreCharField(label=_("Street 2"), required=False)
    shipping_address_postal_code = CoreCharField(label=_("Postal Code"), required=False)
    shipping_address_district = CoreCharField(label=_("District"), required=False)
    shipping_address_city = CoreCharField(label=_("City"), required=False)
    shipping_address_state = CoreCharField(label=_("State"), required=False)
    shipping_address_country = CoreCharField(label=_("Country"), required=False)
    shipping_address_phone = CoreCharField(label=_("Phone"), required=False)

    class Meta:
        model = Consignee
        fields = [
            "company_name",
            "display_name",
            "slug",
            "code",
            "website",
            "consignor",
            "remark",
        ]
        widgets = {
            "remark": forms.Textarea(attrs={"cols": 80, "rows": 9}),
        }

    def clean(self):
        cleaned_data = super().clean()

        salutation = cleaned_data.get("salutation")
        first_name = cleaned_data.get("first_name")
        last_name = cleaned_data.get("last_name")
        email = cleaned_data.get("email")
        phone = cleaned_data.get("phone")
        designation = cleaned_data.get("designation")
        department = cleaned_data.get("department")

        if (salutation or last_name or email or phone or designation or department) and not first_name:
            msg = _("Please enter first name.")
            self.add_error("first_name", msg)


# class ConsigneeForm(CoreModelForm):
#     """Form for consignee."""

#     is_billing_same_as_shipping = forms.BooleanField(label=_("Same as billing address"), required=False)

#     salutation = forms.ChoiceField(label=_("Salutation"), required=False, choices=ConsigneeContact.Salutation.choices)
#     first_name = forms.CharField(label=_("First Name"), required=False)
#     last_name = forms.CharField(label=_("Last Name"), required=False)
#     email = forms.EmailField(label=_("Email"), required=False)
#     phone = PhoneNumberField(
#         label=_("Phone"),
#         required=False,
#         error_messages={"invalid": _("Please enter a valid phone number (e.g. +60128585299).")},
#         help_text=_("Phone number (e.g. +60128585299)."),
#     )
#     designation = forms.CharField(label=_("Designation"), required=False)
#     department = forms.CharField(label=_("Department"), required=False)
#     # Billing Address Form fields
#     billing_address_attention = forms.CharField(label=_("Attention"), required=False)
#     billing_address_street_1 = forms.CharField(label=_("Street 1"), required=False)
#     billing_address_street_2 = forms.CharField(label=_("Street 2"), required=False)
#     billing_address_postal_code = forms.CharField(label=_("Postal Code"), required=False)
#     billing_address_district = forms.CharField(label=_("District"), required=False)
#     billing_address_city = forms.CharField(label=_("City"), required=False)
#     billing_address_state = forms.CharField(label=_("State"), required=False)
#     billing_address_country = forms.CharField(label=_("Country"), required=False)
#     billing_address_phone = forms.CharField(label=_("Phone"), required=False)
#     # Shipping Address Form fields
#     shipping_address_attention = forms.CharField(label=_("Attention"), required=False)
#     shipping_address_street_1 = forms.CharField(label=_("Street 1"), required=False)
#     shipping_address_street_2 = forms.CharField(label=_("Street 2"), required=False)
#     shipping_address_postal_code = forms.CharField(label=_("Postal Code"), required=False)
#     shipping_address_district = forms.CharField(label=_("District"), required=False)
#     shipping_address_city = forms.CharField(label=_("City"), required=False)
#     shipping_address_state = forms.CharField(label=_("State"), required=False)
#     shipping_address_country = forms.CharField(label=_("Country"), required=False)
#     shipping_address_phone = forms.CharField(label=_("Phone"), required=False)

#     class Meta:
#         model = Consignee
#         fields = [
#             "company_name",
#             "display_name",
#             "consignor",
#             "code",
#             "slug",
#             "website",
#             "payment_terms",
#             "remark",
#         ]
#         widgets = {
#             "payment_terms": forms.Textarea(attrs={"cols": 80, "rows": 3}),
#             "remark": forms.Textarea(attrs={"cols": 80, "rows": 5}),
#         }

#     def clean(self):
#         cleaned_data = super().clean()

#         salutation = cleaned_data.get("salutation")
#         first_name = cleaned_data.get("first_name")
#         last_name = cleaned_data.get("last_name")
#         email = cleaned_data.get("email")
#         phone = cleaned_data.get("phone")
#         designation = cleaned_data.get("designation")
#         department = cleaned_data.get("department")

#         if (salutation or last_name or email or phone or designation or department) and not first_name:
#             msg = _("Please enter first name.")
#             self.add_error("first_name", msg)


# ############
# # FOR HTMX #
# ############


# class ConsigneeInfoUpdateForm(CoreHtmxModelForm):
#     class Meta:
#         model = Consignee
#         fields = [
#             "company_name",
#             "display_name",
#             "consignor",
#             "code",
#             "website",
#             "payment_terms",
#             "remark",
#         ]
#         widgets = {
#             "payment_terms": forms.Textarea(attrs={"cols": 80, "rows": 3}),
#             "remark": forms.Textarea(attrs={"cols": 80, "rows": 3}),
#         }


# class ConsigneeContactUpdateForm(CoreHtmxModelForm):
#     class Meta:
#         model = ConsigneeContact
#         fields = [
#             "salutation",
#             "first_name",
#             "last_name",
#             "email",
#             "phone",
#             "mobile",
#             "designation",
#             "department",
#         ]

#     def __init__(self, *args, **kwargs):
#         super().__init__(*args, **kwargs)

#         # Override label
#         self.fields["email"].label = _("Email")
#         self.fields["phone"].label = _("Phone")
#         self.fields["mobile"].label = _("Mobile")


# class ConsigneeBillingAddressUpdateForm(CoreHtmxModelForm):
#     class Meta:
#         model = BillingAddress
#         fields = [
#             "address_attention",
#             "address_street_1",
#             "address_street_2",
#             "address_postal_code",
#             "address_district",
#             "address_city",
#             "address_state",
#             "address_country",
#             "address_phone",
#         ]


# class ConsigneeShippingAddressUpdateForm(CoreHtmxModelForm):
#     class Meta:
#         model = ShippingAddress
#         fields = [
#             "address_attention",
#             "address_street_1",
#             "address_street_2",
#             "address_postal_code",
#             "address_district",
#             "address_city",
#             "address_state",
#             "address_country",
#             "address_phone",
#         ]

from django.db.models import Q
from django.utils.translation import gettext_lazy as _

import django_filters as filters

# from wms.bases.filters import AbstractHistoryFilter

from wms.cores.forms.widget import CoreSelectWidget, CoreTextWidget

from .models import Consignee


class ConsigneeFilter(filters.FilterSet):
    # keyword = filters.CharFilter(
    #     label=_("Numbering or Status contains"),
    #     method="custom_keyword_filter",
    # )

    class Meta:
        model = Consignee
        fields = [
            "consignor",
        ]

    def __init__(self, data=None, queryset=None, *, request=None, prefix=None):
        super().__init__(data=data, queryset=queryset, request=request, prefix=prefix)

        # self.filters["keyword"].field.widget = CoreTextWidget()
        self.filters["consignor"].field.widget = CoreSelectWidget()

    # def custom_keyword_filter(self, queryset, name, value):
    #     qs = queryset

    #     for keyword in value.split():
    #         qs = qs.filter(
    #             Q(numbering__icontains=keyword)
    #             | Q(display_name__icontains=keyword)
    #             | Q(website__icontains=keyword)
    #             | Q(consignor__company_name__icontains=keyword)
    #             | Q(consignor__display_name__icontains=keyword)
    #             | Q(consignor__code__icontains=keyword)
    #             | Q(consigneecontact__first_name__icontains=keyword)
    #             | Q(consigneecontact__last_name__icontains=keyword)
    #             | Q(consigneecontact__phone__icontains=keyword)
    #             | Q(consigneecontact__mobile__icontains=keyword)
    #             | Q(consigneecontact__email__icontains=keyword)
    #         )

    #     return qs.distinct()


# class ConsigneeHistoryFilter(AbstractHistoryFilter):
#     """Filter class for Consignee's History DataTables."""

#     pass

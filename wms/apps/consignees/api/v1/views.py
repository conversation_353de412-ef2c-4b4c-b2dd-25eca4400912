from django.db.models import Q
from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.response import Response
from django.shortcuts import get_object_or_404

from wms.apps.consignees.models import Consignee
from wms.apps.consignors.models import Consignor
from wms.cores.views import Select2Pagination
from .serializers import ConsigneeSerializer


class ConsigneeViewSet(viewsets.ViewSet):
    """
    ViewSet for consignee operations.
    """
    pagination_class = Select2Pagination

    def retrieve(self, request, pk=None):
        """
        Get a single consignee by ID.

        Parameters:
        - pk: Consignee ID
        """
        # Get the consignee by ID
        consignee = get_object_or_404(Consignee, pk=pk)

        # Serialize and return the consignee
        serializer = ConsigneeSerializer(consignee)
        return Response(serializer.data)

    @action(detail=False, methods=['get'], url_path='by-consignor/(?P<consignor_id>[^/.]+)')
    def by_consignor(self, request, consignor_id=None):
        """
        Get consignees for a specific consignor.

        Parameters:
        - consignor_id: Consignor ID

        Query parameters:
        - search: Search term for filtering consignees
        - page: Page number for pagination
        - page_size: Number of items per page
        """
        # Verify consignor exists
        consignor = get_object_or_404(Consignor, pk=consignor_id)

        # Get search term from query params
        search_term = request.query_params.get('search', '')

        # Filter consignees
        queryset = Consignee.objects.filter(consignor=consignor)

        # Apply search if provided
        if search_term:
            queryset = queryset.filter(
                Q(display_name__icontains=search_term) |
                Q(code__icontains=search_term) |
                Q(company_name__icontains=search_term)
            )

        # Apply pagination
        paginator = self.pagination_class()
        paginated_queryset = paginator.paginate_queryset(queryset, request)

        serializer = ConsigneeSerializer(paginated_queryset, many=True)
        return paginator.get_paginated_response(serializer.data)

from django.apps import AppConfig
from django.utils.translation import gettext_lazy as _


class ConsigneesConfig(AppConfig):
    name = "wms.apps.consignees"
    verbose_name = _("Consignees")

    def ready(self):
        """Enabled signals and actstream."""

        from actstream import registry

        import wms.apps.consignees.signals  # noqa F401
        from wms.apps.consignees.models import <PERSON><PERSON><PERSON><PERSON><PERSON>, Consignee, ConsigneeContact, ShippingAddress

        registry.register(Consignee, ConsigneeContact, ShippingAddress, BillingAddress)

        # To prevent related activity stream being removed when object is deleted.
        def not_target_actions(field):
            return field.name not in ["target_actions", "action_object_actions"]

        # Consignees apps
        Consignee._meta.private_fields = list(filter(not_target_actions, Consignee._meta.private_fields))
        ConsigneeContact._meta.private_fields = list(filter(not_target_actions, ConsigneeContact._meta.private_fields))
        ShippingAddress._meta.private_fields = list(filter(not_target_actions, ShippingAddress._meta.private_fields))
        BillingAddress._meta.private_fields = list(filter(not_target_actions, BillingAddress._meta.private_fields))

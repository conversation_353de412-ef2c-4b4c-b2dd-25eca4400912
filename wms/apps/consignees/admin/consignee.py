from django.contrib import admin

from import_export import resources
from import_export.admin import ImportExportModelAdmin

from wms.cores.admin import BaseModelAdmin, BaseStackedInline

from ..models import BillingAddress, Consignee, ConsigneeContact, ShippingAddress


class ConsigneeResource(resources.ModelResource):
    class Meta:
        model = Consignee
        skip_unchanged = True
        exclude = (
            "created_by",
            "created",
            "modified_by",
            "modified",
        )


class ConsigneeContactResource(resources.ModelResource):
    class Meta:
        model = ConsigneeContact
        skip_unchanged = True
        exclude = (
            "created_by",
            "created",
            "modified_by",
            "modified",
        )


class ConsigneeContactInline(BaseStackedInline):
    model = ConsigneeContact
    extra = 1


class BillingAddressInline(BaseStackedInline):
    model = BillingAddress
    extra = 1


class ShippingAddressInline(BaseStackedInline):
    model = ShippingAddress
    extra = 1


@admin.register(Consignee)
class ConsigneeAdmin(BaseModelAdmin, ImportExportModelAdmin):
    """Django admin for Consignee."""

    resource_class = ConsigneeResource

    list_display = [
        "pk",
        "code",
        "display_name",
        "system_number",
        "slug",
        "company_name",
        "get_primary_contact_formal_full_name",
        "website",
        "payment_terms",
        "remark",
    ]
    inlines = [ConsigneeContactInline, BillingAddressInline, ShippingAddressInline]
    search_fields = [
        "display_name",
        "company_name",
        "code",
        "remark",
    ]
    fieldsets = (
        (
            None,
            {
                "fields": (
                    "code",
                    "company_name",
                    "display_name",
                    "slug",
                    "consignor",
                    "system_number",
                    "website",
                    "payment_terms",
                    "remark",
                )
            },
        ),
    )
    list_filter = ["consignor", "created_by"]


@admin.register(ConsigneeContact)
class ConsigneeContactAdmin(BaseModelAdmin, ImportExportModelAdmin):
    """Django admin for ConsigneeContact."""

    resource_class = ConsigneeContactResource

    list_display = [
        "pk",
        "consignee",
        "is_primary",
        "salutation",
        "first_name",
        "last_name",
        "email",
        "phone",
        "mobile",
        "designation",
        "department",
    ]
    search_fields = [
        "first_name",
        "last_name",
        "email",
    ]

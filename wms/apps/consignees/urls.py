from django.urls import path

# from wss.apps.consignees.views import (  # htmx
#     consignee_billing_address_detail_view,
#     consignee_billing_address_update_view,
#     consignee_create_view,
#     consignee_datatables_view,
#     consignee_delete_view,
#     consignee_detail_datatables_view,
#     consignee_detail_view,
#     consignee_history_datatables_view,
#     consignee_history_list_view,
#     consignee_history_modified_view,
#     consignee_info_detail_view,
#     consignee_info_update_view,
#     consignee_list_view,
#     consignee_primary_contact_detail_view,
#     consignee_primary_contact_update_view,
#     consignee_release_orders_list_view,
#     consignee_shipping_address_detail_view,
#     consignee_shipping_address_update_view,
#     consignee_update_view,
# )
from wms.apps.consignees.views import (
    ConsigneeListView,
    ConsigneeCreateView,
    ConsigneeUpdateView,
    ConsigneeDetailView,
    ConsigneeDetailHomeView,
    ConsigneeEventView,
    ConsigneeDataTableDetailView,
)


app_name = "consignees"


urlpatterns = [
    path("", view=ConsigneeListView.as_view(), name="list"),
    path("create/", ConsigneeCreateView.as_view(), name="create"),
    path("<int:pk>/update/", ConsigneeUpdateView.as_view(), name="update"),
    path("<int:pk>/", ConsigneeDataTableDetailView.as_view(), name="panel"),
    path("<int:pk>/home/", ConsigneeDetailHomeView.as_view(), name="detail_home"),
    path("<int:pk>/detail/", ConsigneeDetailView.as_view(), name="detail"),
    path("<int:pk>/event/", ConsigneeEventView.as_view(), name="event"),
#     path("", view=consignee_list_view, name="list"),
#     path("create/", view=consignee_create_view, name="create"),
#     path("update/<str:slug>/", view=consignee_update_view, name="update"),
#     path("delete/<str:slug>/", view=consignee_delete_view, name="delete"),
#     path("detail/<str:slug>/", view=consignee_detail_view, name="detail"),
#     path("detail/<str:slug>/history/", view=consignee_history_list_view, name="history"),
#     path("detail/<int:pk>/history/popup-modified", view=consignee_history_modified_view, name="history-modified"),
#     path("detail/<str:slug>/datatables-history/", view=consignee_history_datatables_view, name="datatables-history"),
#     path("detail/<str:slug>/info/", view=consignee_info_detail_view, name="info"),
#     path("detail/<str:slug>/info/update/", view=consignee_info_update_view, name="info_update"),
#     path("detail/<str:slug>/release-orders/", view=consignee_release_orders_list_view, name="release-orders"),
#     path("detail/<str:slug>/primary-contact/", view=consignee_primary_contact_detail_view, name="primary_contact"),
#     path(
#         "detail/<str:slug>/primary-contact/update/",
#         view=consignee_primary_contact_update_view,
#         name="primary_contact_update",
#     ),
#     path("detail/<str:slug>/billing-address/", view=consignee_billing_address_detail_view, name="billing_address"),
#     path(
#         "detail/<str:slug>/billing-address/update/",
#         view=consignee_billing_address_update_view,
#         name="billing_address_update",
#     ),
#     path("detail/<str:slug>/shipping-address/", view=consignee_shipping_address_detail_view, name="shipping_address"),
#     path(
#         "detail/<str:slug>/shipping-address/update/",
#         view=consignee_shipping_address_update_view,
#         name="shipping_address_update",
#     ),
#     path("datatables/", view=consignee_datatables_view, name="datatables"),
#     path("datatables-detail/", view=consignee_detail_datatables_view, name="datatables-detail"),
]

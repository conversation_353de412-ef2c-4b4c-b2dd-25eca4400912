# from django.urls import reverse
# from django.utils.translation import gettext_lazy as _

# import django_tables2 as tables
# from actstream.models import Action

# from wss.cores.models import DISPLAY_EMPTY_VALUE
# from wss.cores.tables import (
#     HTMX_LIST_ATTRS_CLASS,
#     HTMX_LIST_SM_ATTRS_CLASS,
#     PARTIAL_LIST_ATTRS_CLASS,
#     TABLE_ATTRS_CLASS,
#     AbstractHistoryDataTables,
# )
# from wss.cores.utils import convert_camel_case_to_space

# from wss.apps.releases.models import WarehouseReleaseOrder

import django_tables2 as tables
from django.urls import reverse, NoReverseMatch

from wms.cores.columns import HTMXColumn

from .models import Consignee



class ConsigneeDetailTable(tables.Table):
    code = HTMXColumn(
        url_name="consignees:detail_home",
        target_id="detail-panel",
        verbose_name="Code",
        push_url=True,
        push_url_name="consignees:panel"
    )

    class Meta:
        model = Consignee
        fields = ("display_name",)
        order_by = 'display_name'
        template_name = "tables/table_htmx.html"

    def __init__(self, *args, object_pk=None, **kwargs):
        super().__init__(*args, **kwargs)
        self.object_pk = object_pk


class ConsigneeTable(tables.Table):
    section_title = "Consignee Lists"
    section_name = "Consignee"
    selectable = True
    display_name = tables.LinkColumn("consignees:panel", args=[tables.utils.A("pk")])
    # actions = tables.TemplateColumn(
    #     verbose_name="Actions",
    #     template_name="tables/table_actions_column.html", orderable=False,
    #     extra_context={
    #         "edit_url": "inventories:items:update",
    #         "delete_url": "inventories:items:delete"
    #     }
    # )

    @property
    def create_url(self):
        try:
            return reverse('consignees:create')
        except NoReverseMatch:
            return None

    class Meta:
        model = Consignee
        order_by = 'system_number'
        template_name = "tables/table_htmx.html"
        fields = (
            "system_number",
            "display_name",
            "company_name",
            "code",
            "consignor",
            # "actions"
        )


# class ConsigneeDataTables(tables.Table):
#     """Table used on consignee list page."""

#     display_name = tables.Column(verbose_name=_("Display Name"), linkify=True)
#     primary_contact_formal_full_name = tables.Column(
#         verbose_name=_("Primary Contact"), accessor="get_primary_contact_formal_full_name"
#     )
#     primary_contact_phone = tables.Column(verbose_name=_("Phone"), accessor="get_primary_contact_phone")
#     primary_contact_mobile = tables.Column(verbose_name=_("Mobile"), accessor="get_primary_contact_mobile")
#     primary_contact_email = tables.TemplateColumn(
#         verbose_name=_("Email"),
#         accessor="get_primary_contact_email",
#         template_name="consignees/partials/tables/server_side/_primary_contact_email.html",
#     )
#     actions = tables.TemplateColumn(
#         verbose_name=_("Actions"),
#         orderable=False,
#         template_name="consignees/partials/tables/server_side/_actions.html",
#         attrs={"th": {"class": "text-right"}, "td": {"class": "text-right"}},
#         exclude_from_export=True,
#     )

#     class Meta:
#         model = Consignee
#         order_by = "numbering"  # Must follow the first row display in the table
#         default = DISPLAY_EMPTY_VALUE
#         template_name = "partials/tables/_datatables_server_side.html"
#         attrs = {"class": TABLE_ATTRS_CLASS, "id": "consignee_datatables", "style": "display: none;"}
#         row_attrs = {"data-id": lambda record: record.pk}

#         fields = [
#             "numbering",
#             "display_name",
#             "consignor",
#             "website",
#         ]
#         sequence = [
#             "numbering",
#             "display_name",
#             "consignor",
#             "primary_contact_formal_full_name",
#             "primary_contact_phone",
#             "primary_contact_mobile",
#             "primary_contact_email",
#             "website",
#             "actions",
#         ]

#     def render_actions(self, column, record, table, value, bound_column, bound_row):
#         """Add permission checking into actions column."""
#         change_perms = self.request.user.has_perm("consignees.change_consignee")
#         delete_perms = self.request.user.has_perm("consignees.delete_consignee")
#         column.extra_context = {
#             "change_perms": change_perms,
#             "delete_perms": delete_perms,
#         }
#         return column.render(record, table, value, bound_column, **{"bound_row": bound_row})


# class ConsigneeDetailDataTables(tables.Table):
#     """Table used on consignee detail page."""

#     display_name = tables.Column(verbose_name=_("Display Name"), linkify=True)

#     class Meta:
#         model = Consignee
#         order_by = "-numbering"  # Must follow the first row display in the table
#         default = DISPLAY_EMPTY_VALUE
#         template_name = "partials/tables/_datatables_server_side.html"
#         attrs = {
#             "class": PARTIAL_LIST_ATTRS_CLASS + " table-wrap",
#             "id": "consignee_datatables",
#             "style": "display: none;",
#         }
#         row_attrs = {"data-id": lambda record: record.pk}

#         fields = [
#             "pk",
#             "numbering",
#             "display_name",
#         ]
#         sequence = [
#             "pk",
#             "numbering",
#             "display_name",
#         ]

#     def render_pk(self, value, record):
#         return f"highlight_id_{value}"


# class ConsigneeHistoryDataTables(AbstractHistoryDataTables):
#     """Table used on Consignee's history tab."""

#     class Meta(AbstractHistoryDataTables.Meta):
#         attrs = {
#             "class": HTMX_LIST_SM_ATTRS_CLASS,
#             "id": "consignee_history_datatables",
#             "style": "display: none;",
#             "thead": {"class": "bg-secondary"},
#         }

#     def render_verb(self, value: str, record: Action) -> str:
#         if value == "created BillingAddress":
#             return "Created Billing Address"
#         elif value == "created ShippingAddress":
#             return "Created Shipping Address"
#         elif value == "created ConsigneeContact":
#             return "Created Consignor Contact"

#         value_string = convert_camel_case_to_space(value)
#         value_string = value_string[0].upper() + value_string[1:]

#         if value.startswith("modified"):
#             link = reverse("consignees:history-modified", kwargs={"pk": record.pk})
#             htmx_modal_attributes = (
#                 'href="#" style="text-decoration: underline dotted" '
#                 'data-toggle="modal" data-target="#modalXl" '
#                 'hx-target="#modalXlBody" hx-swap="innerHTML"'
#             )
#             return f'<a {htmx_modal_attributes} hx-get="{link}">{value_string}</a>'
#         else:
#             return value_string


# class ConsigneeReleaseOrdersDataTables(tables.Table):
#     """Table used on consignee's warehouse_release_orders list page."""

#     numbering = tables.Column(verbose_name=_("Numbering"), linkify=True)
#     deliveryorder__numbering = tables.Column(verbose_name=_("DO"))
#     release_datetime = tables.DateTimeColumn(
#         verbose_name=_("Release Date"),
#         accessor="release_datetime",
#         format="Y-m-d",
#     )
#     created = tables.DateTimeColumn(
#         verbose_name=_("Created Date"),
#         accessor="created",
#         format="Y-m-d",
#     )
#     actions = tables.TemplateColumn(
#         verbose_name=_("Actions"),
#         orderable=False,
#         template_name="consignees/partials/tables/consigneereleaseordersdatatables/_actions.html",
#         attrs={"th": {"class": "text-right"}, "td": {"class": "text-right"}},
#         exclude_from_export=True,
#     )

#     class Meta:
#         model = WarehouseReleaseOrder
#         order_by = "numbering"  # Must follow the first row display in the table
#         default = DISPLAY_EMPTY_VALUE
#         template_name = "partials/tables/_datatables.html"
#         attrs = {
#             "class": HTMX_LIST_ATTRS_CLASS,
#             "id": "consignee_release_orders_datatables",
#             "style": "display: none;",
#             "thead": {"class": "bg-info"},
#         }
#         row_attrs = {"data-id": lambda record: record.pk}

#         fields = [
#             "numbering",
#             "deliveryorder__numbering",
#             "customer_reference",
#             "status",
#             "consignor_picking_list_no",
#             "issued_by",
#             "release_datetime",
#             "created",
#         ]
#         sequence = [
#             "numbering",
#             "deliveryorder__numbering",
#             "customer_reference",
#             "status",
#             "consignor_picking_list_no",
#             "issued_by",
#             "release_datetime",
#             "created",
#         ]

#     def render_status(self, value, record: WarehouseReleaseOrder):
#         """Return nice html label display for status."""
#         return record.html_status_display

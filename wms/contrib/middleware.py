import re
from urllib.parse import urlparse
from django.conf import settings
from django.contrib.auth import REDIRECT_FIELD_NAME
from django.contrib.auth.views import redirect_to_login
from django.core.exceptions import ImproperlyConfigured
from django.shortcuts import resolve_url
from django.utils.deprecation import MiddlewareMixin

class CustomLoginRequiredMiddleware(MiddlewareMixin):
    async_mode = False

    redirect_field_name = REDIRECT_FIELD_NAME

    def __init__(self, get_response=None):
        self.get_response = get_response
        self.exempt_urls = [re.compile(url) for url in getattr(settings, "LOGIN_EXEMPT_URLS", [])]

    def process_view(self, request, view_func, view_args, view_kwargs):
        if request.user.is_authenticated:
            return None

        path = request.path_info.lstrip("/")

        if any(url.match(path) for url in self.exempt_urls):
            return None

        if not getattr(view_func, "login_required", True):
            return None

        return self.handle_no_permission(request, view_func)

    def get_login_url(self, view_func):
        login_url = getattr(view_func, "login_url", None) or settings.LOGIN_URL
        if not login_url:
            raise ImproperlyConfigured(
                "No login URL to redirect to. Define settings.LOGIN_URL or "
                "provide a login_url via the 'django.contrib.auth.decorators."
                "login_required' decorator."
            )
        return str(login_url)

    def get_redirect_field_name(self, view_func):
        return getattr(view_func, "redirect_field_name", self.redirect_field_name)

    def handle_no_permission(self, request, view_func):
        path = request.build_absolute_uri()
        resolved_login_url = resolve_url(self.get_login_url(view_func))
        login_scheme, login_netloc = urlparse(resolved_login_url)[:2]
        current_scheme, current_netloc = urlparse(path)[:2]
        if (not login_scheme or login_scheme == current_scheme) and (
            not login_netloc or login_netloc == current_netloc
        ):
            path = request.get_full_path()

        return redirect_to_login(
            path,
            resolved_login_url,
            self.get_redirect_field_name(view_func),
        )

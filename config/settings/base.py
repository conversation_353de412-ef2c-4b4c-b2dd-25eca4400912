# ruff: noqa: ERA001, E501
"""Base settings to build other settings files upon."""
import os
import ssl
from pathlib import Path

import environ

BASE_DIR = Path(__file__).resolve(strict=True).parent.parent.parent
# wms/
APPS_DIR = BASE_DIR / "wms"
env = environ.Env()

READ_DOT_ENV_FILE = env.bool("DJANGO_READ_DOT_ENV_FILE", default=False)
if READ_DOT_ENV_FILE:
    # OS environment variables take precedence over variables from .env
    env.read_env(str(BASE_DIR / ".env"))

# GENERAL
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#debug
DEBUG = env.bool("DJAN<PERSON>O_DEBUG", False)
# Local time zone. Choices are
# http://en.wikipedia.org/wiki/List_of_tz_zones_by_name
# though not all of them may be available with every OS.
# In Windows, this must be set to your system time zone.
TIME_ZONE = "Asia/Kuala_Lumpur"
# https://docs.djangoproject.com/en/dev/ref/settings/#language-code
LANGUAGE_CODE = "en-us"
# https://docs.djangoproject.com/en/dev/ref/settings/#languages
# from django.utils.translation import gettext_lazy as _
# LANGUAGES = [
#     ('en', _('English')),
#     ('fr-fr', _('French')),
#     ('pt-br', _('Portuguese')),
# ]
# https://docs.djangoproject.com/en/dev/ref/settings/#site-id
SITE_ID = 1
# https://docs.djangoproject.com/en/dev/ref/settings/#use-i18n
USE_I18N = True
# https://docs.djangoproject.com/en/dev/ref/settings/#use-tz
USE_TZ = True
# https://docs.djangoproject.com/en/dev/ref/settings/#locale-paths
LOCALE_PATHS = [str(BASE_DIR / "locale")]

# Date and Time formatting settings
DATE_FORMAT = 'Y-m-d'  # Will display as: 2024-12-03
DATETIME_FORMAT = 'Y-m-d h:i A'  # Will display as: 2024-12-03 13:30

# List of accepted input formats for parsing dates
DATE_INPUT_FORMATS = [
    '%Y-%m-%d',  # 2024-12-03
    '%m/%d/%Y',  # 12/03/2024
    '%d/%m/%Y',  # 03/12/2024
]

# List of accepted input formats for parsing datetimes
DATETIME_INPUT_FORMATS = [
    # 12-hour formats with AM/PM
    '%Y-%m-%d %I:%M %p',     # '2025-05-05 10:05 AM' (without seconds)
    '%Y-%m-%d %I:%M:%S %p',  # '2025-05-05 10:05:00 AM' (with seconds)

    # 24-hour formats
    '%Y-%m-%d %H:%M',        # '2025-05-05 10:05' (without seconds)
    '%Y-%m-%d %H:%M:%S',     # '2025-05-05 10:05:00' (with seconds)
    '%Y-%m-%d %H:%M:%S.%f',  # 2024-12-03 14:30:45.000000

    '%m/%d/%Y %H:%M:%S',     # 12/03/2024 14:30:45
    '%d/%m/%Y %H:%M:%S',     # 03/12/2024 14:30:45
]

# DATABASES
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#databases

DATABASES = {
    "default": env.db(
        "DATABASE_URL",
        default=("postgres://superpsql:password@127.0.0.1:5432/wms_db"),
    )
}
DATABASES["default"]["ATOMIC_REQUESTS"] = True

# https://docs.djangoproject.com/en/stable/ref/settings/#std:setting-DEFAULT_AUTO_FIELD
DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# URLS
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#root-urlconf
ROOT_URLCONF = "config.urls"
# https://docs.djangoproject.com/en/dev/ref/settings/#wsgi-application
WSGI_APPLICATION = "config.wsgi.application"

# APPS
# ------------------------------------------------------------------------------
DJANGO_APPS = [
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.sites",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "django.contrib.humanize",
    "django.contrib.admin",
    "django.forms",
]
THIRD_PARTY_APPS = [
    "actstream",
    "crispy_forms",
    "crispy_tailwind",
    "allauth",
    "allauth.account",
    "allauth.mfa",
    "allauth.socialaccount",
    "rest_framework",
    "rest_framework.authtoken",
    "corsheaders",
    "drf_spectacular",
    "phonenumber_field",
    "sorl.thumbnail",
    "treebeard",
    "post_office",
    "pyas2",
    "extra_settings",
    "django_tables2",
    "django_filters",
    "django_htmx",
    "django_json_widget",
    "import_export",
    "django_admin_listfilter_dropdown",
    "admin_auto_filters",
    "django_extensions",
]

LOCAL_APPS = [
    'wms.cores',
    "wms.apps.dashboards",
    "wms.apps.users",
    "wms.apps.consignors",
    "wms.apps.consignees",
    "wms.apps.settings",
    "wms.apps.inventories",
    "wms.apps.receives",
    "wms.apps.releases",
    "wms.apps.adjustments",
    "wms.apps.transfers",
    # "wms.apps.portals",
    "wms.apps.rackings",
    "wms.apps.pickings",
    "wms.apps.count_sheets",
    # Your stuff: custom apps go here
]

# https://docs.djangoproject.com/en/dev/ref/settings/#installed-apps
INSTALLED_APPS = DJANGO_APPS + THIRD_PARTY_APPS + LOCAL_APPS

# MIGRATIONS
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#migration-modules
MIGRATION_MODULES = {
    "sites": "wms.contrib.sites.migrations",
    # "post_office": "wms.contrib.post_office.migrations",
    # "pyas2": "wms.contrib.pyas2.migrations",
}

# AUTHENTICATION
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#authentication-backends
AUTHENTICATION_BACKENDS = [
    "django.contrib.auth.backends.ModelBackend",
    "allauth.account.auth_backends.AuthenticationBackend",
]
# https://docs.djangoproject.com/en/dev/ref/settings/#auth-user-model
AUTH_USER_MODEL = "users.User"
# https://docs.djangoproject.com/en/dev/ref/settings/#login-redirect-url
LOGIN_REDIRECT_URL = "users:redirect"
# https://docs.djangoproject.com/en/dev/ref/settings/#login-url
LOGIN_URL = "account_login"
LOGIN_EXEMPT_URLS = [
    r'^pyas2/',   # allow everything under /pyas2/
]

# PASSWORDS
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#password-hashers
PASSWORD_HASHERS = [
    # https://docs.djangoproject.com/en/dev/topics/auth/passwords/#using-argon2-with-django
    "django.contrib.auth.hashers.Argon2PasswordHasher",
    "django.contrib.auth.hashers.PBKDF2PasswordHasher",
    "django.contrib.auth.hashers.PBKDF2SHA1PasswordHasher",
    "django.contrib.auth.hashers.BCryptSHA256PasswordHasher",
]
# https://docs.djangoproject.com/en/dev/ref/settings/#auth-password-validators
AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {"NAME": "django.contrib.auth.password_validation.MinimumLengthValidator"},
    {"NAME": "django.contrib.auth.password_validation.CommonPasswordValidator"},
    {"NAME": "django.contrib.auth.password_validation.NumericPasswordValidator"},
]

# MIDDLEWARE
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#middleware
MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.locale.LocaleMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    # 'django.contrib.auth.middleware.LoginRequiredMiddleware',
    'wms.contrib.middleware.CustomLoginRequiredMiddleware',
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "allauth.account.middleware.AccountMiddleware",
    "django_htmx.middleware.HtmxMiddleware",
]

# STATIC
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#static-root
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
# https://docs.djangoproject.com/en/dev/ref/settings/#static-url
STATIC_URL = "/static/"
# https://docs.djangoproject.com/en/dev/ref/contrib/staticfiles/#std:setting-STATICFILES_DIRS
STATICFILES_DIRS = [
    os.path.join(APPS_DIR, 'static'),
]
# https://docs.djangoproject.com/en/dev/ref/contrib/staticfiles/#staticfiles-finders
STATICFILES_FINDERS = [
    "django.contrib.staticfiles.finders.FileSystemFinder",
    "django.contrib.staticfiles.finders.AppDirectoriesFinder",
]

# MEDIA
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#media-root
MEDIA_ROOT = str(BASE_DIR / "public/media")
# https://docs.djangoproject.com/en/dev/ref/settings/#media-url
MEDIA_URL = "/media/"

# http://django-crispy-forms.readthedocs.io/en/latest/install.html#template-packs
CRISPY_ALLOWED_TEMPLATE_PACKS = "tailwind"
CRISPY_TEMPLATE_PACK = "tailwind"

# TEMPLATES
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#templates
TEMPLATES = [
    {
        # https://docs.djangoproject.com/en/dev/ref/settings/#std:setting-TEMPLATES-BACKEND
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        # https://docs.djangoproject.com/en/dev/ref/settings/#dirs
        "DIRS": [str(APPS_DIR / "templates")],
        # https://docs.djangoproject.com/en/dev/ref/settings/#app-dirs
        "APP_DIRS": True,
        "OPTIONS": {
            # https://docs.djangoproject.com/en/dev/ref/settings/#template-context-processors
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.template.context_processors.i18n",
                "django.template.context_processors.media",
                "django.template.context_processors.static",
                "django.template.context_processors.tz",
                "django.contrib.messages.context_processors.messages",
                "wms.apps.users.context_processors.allauth_settings",
                "wms.cores.context_processors.settings_context",
            ],
        },
    },
]

# https://docs.djangoproject.com/en/dev/ref/settings/#form-renderer
FORM_RENDERER = "django.forms.renderers.TemplatesSetting"


# FIXTURES
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#fixture-dirs
FIXTURE_DIRS = (str(APPS_DIR / "fixtures"),)

# SECURITY
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#session-cookie-httponly
SESSION_COOKIE_HTTPONLY = True
# https://docs.djangoproject.com/en/dev/ref/settings/#csrf-cookie-httponly
CSRF_COOKIE_HTTPONLY = True
# https://docs.djangoproject.com/en/dev/ref/settings/#x-frame-options
X_FRAME_OPTIONS = "DENY"

# EMAIL
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#email-backend
EMAIL_BACKEND = env(
    "DJANGO_EMAIL_BACKEND",
    # default="django.core.mail.backends.smtp.EmailBackend",
    default="post_office.EmailBackend",
)
# https://docs.djangoproject.com/en/dev/ref/settings/#email-timeout
EMAIL_TIMEOUT = 5

# ADMIN
# ------------------------------------------------------------------------------
# Django Admin URL.
ADMIN_URL = "admin/"
# https://docs.djangoproject.com/en/dev/ref/settings/#admins
ADMINS = [
    ("Admin", "<EMAIL>"),
]
# https://docs.djangoproject.com/en/dev/ref/settings/#managers
MANAGERS = ADMINS
# https://cookiecutter-django.readthedocs.io/en/latest/settings.html#other-environment-settings
# Force the `admin` sign in process to go through the `django-allauth` workflow
DJANGO_ADMIN_FORCE_ALLAUTH = env.bool("DJANGO_ADMIN_FORCE_ALLAUTH", default=False)

# LOGGING
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#logging
# See https://docs.djangoproject.com/en/dev/topics/logging for
# more details on how to customize your logging configuration.
LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "verbose": {
            "format": "%(levelname)s %(asctime)s %(module)s %(process)d %(thread)d %(message)s",
        },
    },
    "handlers": {
        "console": {
            "level": "DEBUG",
            "class": "logging.StreamHandler",
            "formatter": "verbose",
        },
    },
    "root": {"level": "INFO", "handlers": ["console"]},
}

REDIS_URL = env("REDIS_URL", default="redis://redis:6379/0")
REDIS_SSL = REDIS_URL.startswith("rediss://")

# django-allauth
# ------------------------------------------------------------------------------
ACCOUNT_LOGOUT_ON_PASSWORD_CHANGE = True
ACCOUNT_LOGIN_ON_PASSWORD_RESET = True
ACCOUNT_SESSION_REMEMBER = True
ACCOUNT_ALLOW_REGISTRATION = env.bool("DJANGO_ACCOUNT_ALLOW_REGISTRATION", True)
# https://docs.allauth.org/en/latest/account/configuration.html
# https://docs.allauth.org/en/latest/account/configuration.html
ACCOUNT_EMAIL_REQUIRED = True
# https://docs.allauth.org/en/latest/account/configuration.html
ACCOUNT_USERNAME_REQUIRED = True
# https://docs.allauth.org/en/latest/account/configuration.html
ACCOUNT_USER_MODEL_USERNAME_FIELD = "username"
# https://docs.allauth.org/en/latest/account/configuration.html
ACCOUNT_EMAIL_VERIFICATION = "mandatory"
ACCOUNT_PREVENT_ENUMERATION = True
# https://docs.allauth.org/en/latest/account/configuration.html
ACCOUNT_LOGIN_METHODS = {'username', 'email'}
ACCOUNT_ADAPTER = "wms.apps.users.adapters.AccountAdapter"
# https://docs.allauth.org/en/latest/account/forms.html
ACCOUNT_FORMS = {'add_email': 'wms.apps.users.forms.MyCustomAddEmailForm'}
# https://docs.allauth.org/en/latest/socialaccount/configuration.html
SOCIALACCOUNT_ADAPTER = "wms.apps.users.adapters.SocialAccountAdapter"
# https://docs.allauth.org/en/latest/socialaccount/configuration.html
SOCIALACCOUNT_FORMS = {"signup": "wms.apps.users.forms.UserSocialSignupForm"}

# django-rest-framework
# -------------------------------------------------------------------------------
# django-rest-framework - https://www.django-rest-framework.org/api-guide/settings/
REST_FRAMEWORK = {
    "DEFAULT_AUTHENTICATION_CLASSES": (
        "rest_framework.authentication.SessionAuthentication",
        "rest_framework.authentication.TokenAuthentication",
    ),
    "DEFAULT_PERMISSION_CLASSES": ("rest_framework.permissions.IsAuthenticated",),
    "DEFAULT_SCHEMA_CLASS": "drf_spectacular.openapi.AutoSchema",
}

# django-cors-headers - https://github.com/adamchainz/django-cors-headers#setup
CORS_URLS_REGEX = r"^/api/.*$"

# By Default swagger ui is available only to admin user(s). You can change permission classes to change that
# See more configuration options at https://drf-spectacular.readthedocs.io/en/latest/settings.html#settings
SPECTACULAR_SETTINGS = {
    "TITLE": "wms API",
    "DESCRIPTION": "Documentation of API endpoints of wms",
    "VERSION": "1.0.0",
    "SERVE_PERMISSIONS": ["rest_framework.permissions.IsAdminUser"],
    "SCHEMA_PATH_PREFIX": "/api/",
}

# django-activity-stream
# -------------------------------------------------------------------------------
# https://django-activity-stream.readthedocs.io/en/latest/configuration.html
ACTSTREAM_SETTINGS = {
    "USE_JSONFIELD": True,
}

# Your stuff...
# ------------------------------------------------------------------------------
ACTSTREAM_ENABLED = False
ACTSTREAM_EXCLUDE_KEYS = {
    "users.User": ["_state", "_password", "backend", "last_login"],
    # "inventories.Item": ["total_balance"],
    # "inventories.Stock": ["balance", "total_in", "total_out"],
    # "inventories.Transaction": ["balance"],
}

# MITSUBISHI IMPORT SETTINGS
# ------------------------------------------------------------------------------
IMPORT_MITSUBISHI_UPDATE_EXISTING = False
# WRO SETTING
MITSUBISHI_DEFAULT_DO_DUE_DATETIME = 7
MITSUBISHI_DEFAULT_WRO_DUE_DATETIME = 2
MITSUBISHI_DEFAULT_WRO_WAREHOUSES = ["Warehouse KK01"]
MITSUBISHI_FTP_MMM_PICK_LIST_PATH = "/var/www/ftp/mitsubishi/Sino/MMM_PICK_LIST"
MITSUBISHI_FTP_MMM_PICK_LIST_ERROR_PATH = "/var/www/ftp/mitsubishi/Sino/MMM_PICK_LIST_ERROR"
MITSUBISHI_FTP_MMM_PICK_LIST_HISTORY_PATH = "/var/www/ftp/mitsubishi/Sino/MMM_PICK_LIST_HISTORY"
MITSUBISHI_FTP_SINO_PACKINGSLIP_PATH = "/var/www/ftp/mitsubishi/Sino/Process/SINO_PACKINGSLIP"
MITSUBISHI_FTP_MMM_BACKUP_PATH = "/var/www/ftp-backup"
# GRN SETTING
MITSUBISHI_DEFAULT_GRN_DUE_DATETIME = 4
MITSUBISHI_DEFAULT_GRN_WAREHOUSES = ["Warehouse KK01"]
MITSUBISHI_FTP_MMM_SINO_STRF_PATH = "/var/www/ftp/mitsubishi/Sino/MMM_SINO_STRF"
MITSUBISHI_FTP_MMM_SINO_STRF_ERROR_PATH = "/var/www/ftp/mitsubishi/Sino/MMM_SINO_STRF_ERROR"
MITSUBISHI_FTP_MMM_SINO_STRF_HISTORY_PATH = "/var/www/ftp/mitsubishi/Sino/MMM_SINO_STRF_HISTORY"
MITSUBISHI_FTP_SINO_STRF_RESULT_PATH = "/var/www/ftp/mitsubishi/Sino/SINO_STRF_RESULT"


# FMC IMPORT SETTINGS
# ------------------------------------------------------------------------------
# WRO SETTING
FMC_DEFAULT_DO_DUE_DATETIME = 2
FMC_DEFAULT_WRO_DUE_DATETIME = 2
FMC_DEFAULT_WRO_WAREHOUSES = {
    "MY04": ["Warehouse KK01"],
    "MY05": ["Warehouse KCH01"],
    "MY0A": ["Warehouse SA02"],
    "MY03": ["Warehouse SA02"],
}
# GRN SETTING
FMC_DEFAULT_GRN_DUE_DATETIME = 10
FMC_DEFAULT_GRN_WAREHOUSES = {
    "MY04": ["Warehouse KK01"],
    "MY05": ["Warehouse KCH01"],
    "MY0A": ["Warehouse SA02"],
    "MY03": ["Warehouse SA02"],
}
# AS2 SETTING
FMC_AS2_IDENTIFIER = "test-local-as2"
# FMC_AS2_IDENTIFIER = "FreseniusAS2QAS"
# FMC_AS2_IDENTIFIER = "FreseniusAS2PRD"

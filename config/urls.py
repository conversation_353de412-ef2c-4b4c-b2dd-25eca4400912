# ruff: noqa
from django.conf import settings
from django.conf.urls.static import static
from django.contrib import admin
from django.contrib.auth.views import LogoutView
from django.urls import include
from django.urls import path
from django.views import defaults as default_views
from django.views.generic import TemplateView
from drf_spectacular.views import SpectacularAPIView
from drf_spectacular.views import SpectacularSwaggerView
from rest_framework.authtoken.views import obtain_auth_token

from wms.apps.users.views import SecurePasswordResetView

from config.api_router import router as api_v1_router

urlpatterns = [
    path("", TemplateView.as_view(template_name="pages/home.html"), name="home"),
    path(
        "about/",
        TemplateView.as_view(template_name="pages/about.html"),
        name="about",
    ),
    # Django Admin, use {% url 'admin:index' %}
    path(settings.ADMIN_URL, admin.site.urls),
    # User management
    path("users/", include("wms.apps.users.urls", namespace="users")),
    path("dashboards/", include("wms.apps.dashboards.urls", namespace="dashboards")),
    path("accounts/password/reset/", SecurePasswordResetView.as_view(), name="account_reset_password"),
    path("accounts/", include("allauth.urls")),
    path('accounts/logout/', LogoutView.as_view(next_page='/'), name='account_logout'),
    # Your stuff: custom urls includes go here
    # ...
    # Apps

    path("inventories/", include("wms.apps.inventories.urls", namespace="inventories")),
    path("settings/", include("wms.apps.settings.urls", namespace="settings")),
    path("consignees/", include("wms.apps.consignees.urls", namespace="consignees")),
    path("consignors/", include("wms.apps.consignors.urls", namespace="consignors")),
    # # Transaction Channels
    path("adjustments/", include("wms.apps.adjustments.urls", namespace="adjustments")),
    path("transfers/", include("wms.apps.transfers.urls", namespace="transfers")),
    path("receives/", include("wms.apps.receives.urls", namespace="receives")),
    path("releases/", include("wms.apps.releases.urls", namespace="releases")),
    # # Reports
    path("reports/", include("wms.apps.reports.urls", namespace="reports")),
    # # PDF
    path("pdfs/", include("wms.apps.pdfs.urls", namespace="pdfs")),
    # # StockTake process
    # path("count-sheets/", include("wms.apps.count_sheets.urls", namespace="count_sheets")),
    # outbound picking process
    path("pickings/", include("wms.apps.pickings.urls", namespace="pickings")),
    # # Rackings
    path("rackings/", include("wms.apps.rackings.urls", namespace="rackings")),

    # django-activity-stream
    path("activities/", include("actstream.urls")),
    # PYAS2 for EDI
    path("pyas2/", include("pyas2.urls")),
    # Media files
    *static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT),
]

# API URLS
urlpatterns += [
    # API base url
    path("api/", include("config.api_router")),
    # DRF auth token
    path("api/auth-token/", obtain_auth_token),
    path("api/schema/", SpectacularAPIView.as_view(), name="api-schema"),
    path(
        "api/docs/",
        SpectacularSwaggerView.as_view(url_name="api-schema"),
        name="api-docs",
    ),
]

if settings.DEBUG:
    # This allows the error pages to be debugged during development, just visit
    # these url in browser to see how these error pages look like.
    urlpatterns += [
        path(
            "400/",
            default_views.bad_request,
            kwargs={"exception": Exception("Bad Request!")},
        ),
        path(
            "403/",
            default_views.permission_denied,
            kwargs={"exception": Exception("Permission Denied")},
        ),
        path(
            "404/",
            default_views.page_not_found,
            kwargs={"exception": Exception("Page not Found")},
        ),
        path("500/", default_views.server_error),
    ]
    if "debug_toolbar" in settings.INSTALLED_APPS:
        import debug_toolbar

        urlpatterns = [path("__debug__/", include(debug_toolbar.urls))] + urlpatterns

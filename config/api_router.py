from django.conf import settings
from rest_framework.routers import <PERSON>fault<PERSON>out<PERSON>
from rest_framework.routers import SimpleRouter

from wms.apps.inventories.api.v1.views import ItemViewSet
from wms.apps.settings.api.v1.views import WarehouseViewSet
from wms.apps.users.api.views import UserViewSet
from wms.apps.consignees.api.v1.views import ConsigneeViewSet
from wms.apps.consignors.api.v1.views import ConsignorViewSet
from wms.apps.rackings.api.v1.views import RackStorageViewSet

router = DefaultRouter() if settings.DEBUG else SimpleRouter()

router.register("users", UserViewSet)
router.register("items", ItemViewSet, basename="item")
router.register("warehouses", WarehouseViewSet, basename="warehouse")
router.register("consignees", ConsigneeViewSet, basename="consignee")
router.register("consignors", ConsignorViewSet, basename="consignor")
router.register("rackstorages", RackStorageViewSet, basename="rackstorage")

app_name = "api"
urlpatterns = router.urls

volumes:
  production_postgres_data: { }
  production_postgres_data_backups: { }
  production_traefik: { }

  production_redis_data: { }

services:
  django: &django
    build:
      context: .
      dockerfile: ./compose/production/django/Dockerfile

    image: wms_production_django
    depends_on:
      - postgres
      - redis
    env_file:
      - ./.envs/.production/.django
      - ./.envs/.production/.postgres
    command: /start

  postgres:
    build:
      context: .
      dockerfile: ./compose/production/postgres/Dockerfile
    image: wms_production_postgres
    volumes:
      - production_postgres_data:/var/lib/postgresql/data
      - production_postgres_data_backups:/backups
      - ./compose/production/postgres/init-extensions.sql:/docker-entrypoint-initdb.d/init-extensions.sql
    env_file:
      - ./.envs/.production/.postgres

  traefik:
    build:
      context: .
      dockerfile: ./compose/production/traefik/Dockerfile
    image: wms_production_traefik
    depends_on:
      - django
    volumes:
      - production_traefik:/etc/traefik/acme
    ports:
      - '0.0.0.0:8080:80'
      - '0.0.0.0:443:443'
      - '0.0.0.0:5555:5555'

  redis:
    image: docker.io/redis:7

    volumes:
      - production_redis_data:/data

# Translations for the wms project
# Copyright (C) 2025 rust.yin
# <AUTHOR> <EMAIL>, 2025.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: 0.1.0\n"
"Language: pt-BR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"
#: wms/templates/account/account_inactive.html:5
#: wms/templates/account/account_inactive.html:8
msgid "Account Inactive"
msgstr "Conta Inativa"

#: wms/templates/account/account_inactive.html:10
msgid "This account is inactive."
msgstr "Esta conta está inativa."

#: wms/templates/account/email.html:7
msgid "Account"
msgstr "Conta"

#: wms/templates/account/email.html:10
msgid "E-mail Addresses"
msgstr "Endereços de E-mail"

#: wms/templates/account/email.html:13
msgid "The following e-mail addresses are associated with your account:"
msgstr "Os seguintes endereços de e-mail estão associados à sua conta:"

#: wms/templates/account/email.html:27
msgid "Verified"
msgstr "Verificado"

#: wms/templates/account/email.html:29
msgid "Unverified"
msgstr "Não verificado"

#: wms/templates/account/email.html:31
msgid "Primary"
msgstr "Primário"

#: wms/templates/account/email.html:37
msgid "Make Primary"
msgstr "Tornar Primário"

#: wms/templates/account/email.html:38
msgid "Re-send Verification"
msgstr "Reenviar verificação"

#: wms/templates/account/email.html:39
msgid "Remove"
msgstr "Remover"

#: wms/templates/account/email.html:46
msgid "Warning:"
msgstr "Aviso:"

#: wms/templates/account/email.html:46
msgid ""
"You currently do not have any e-mail address set up. You should really add "
"an e-mail address so you can receive notifications, reset your password, etc."
msgstr ""
"No momento, você não tem nenhum endereço de e-mail configurado. Você "
"realmente deve adicionar um endereço de e-mail para receber notificações, "
"redefinir sua senha etc."

#: wms/templates/account/email.html:51
msgid "Add E-mail Address"
msgstr "Adicionar Endereço de E-mail"

#: wms/templates/account/email.html:56
msgid "Add E-mail"
msgstr "Adicionar E-mail"

#: wms/templates/account/email.html:66
msgid "Do you really want to remove the selected e-mail address?"
msgstr "Você realmente deseja remover o endereço de e-mail selecionado?"

#: wms/templates/account/email_confirm.html:6
#: wms/templates/account/email_confirm.html:10
msgid "Confirm E-mail Address"
msgstr "Confirme o endereço de e-mail"

#: wms/templates/account/email_confirm.html:16
#, python-format
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an e-mail "
"address for user %(user_display)s."
msgstr ""
"Confirme se <a href=\"mailto:%(email)s\">%(email)s</a> é um endereço de "
"e-mail do usuário %(user_display)s."

#: wms/templates/account/email_confirm.html:20
msgid "Confirm"
msgstr "Confirmar"

#: wms/templates/account/email_confirm.html:27
#, python-format
msgid ""
"This e-mail confirmation link expired or is invalid. Please <a href="
"\"%(email_url)s\">issue a new e-mail confirmation request</a>."
msgstr "Este link de confirmação de e-mail expirou ou é inválido. "
"Por favor, <a href=\"%(email_url)s\">emita um novo pedido de confirmação por e-mail</a>."

#: wms/templates/account/login.html:7
#: wms/templates/account/login.html:11
#: wms/templates/account/login.html:56
#: wms/templates/base.html:72
msgid "Sign In"
msgstr "Entrar"

#: wms/templates/account/login.html:17
msgid "Please sign in with one of your existing third party accounts:"
msgstr "Faça login com uma de suas contas de terceiros existentes:"

#: wms/templates/account/login.html:19
#, python-format
msgid ""
"Or, <a href=\"%(signup_url)s\">sign up</a> for a %(site_name)s account and "
"sign in below:"
msgstr "Ou, <a href=\"%(signup_url)s\">cadastre-se</a> para uma conta em %(site_name)s e entre abaixo:"

#: wms/templates/account/login.html:32
msgid "or"
msgstr "ou"

#: wms/templates/account/login.html:41
#, python-format
msgid ""
"If you have not created an account yet, then please <a href=\"%(signup_url)s"
"\">sign up</a> first."
msgstr "Se você ainda não criou uma conta, <a href=\"%(signup_url)s"
"\">registre-se primeiro</a>."

#: wms/templates/account/login.html:55
msgid "Forgot Password?"
msgstr "Esqueceu sua senha?"

#: wms/templates/account/logout.html:5
#: wms/templates/account/logout.html:8
#: wms/templates/account/logout.html:17
#: wms/templates/base.html:61
msgid "Sign Out"
msgstr "Sair"

#: wms/templates/account/logout.html:10
msgid "Are you sure you want to sign out?"
msgstr "Você tem certeza que deseja sair?"

#: wms/templates/account/password_change.html:6
#: wms/templates/account/password_change.html:9
#: wms/templates/account/password_change.html:14
#: wms/templates/account/password_reset_from_key.html:5
#: wms/templates/account/password_reset_from_key.html:8
#: wms/templates/account/password_reset_from_key_done.html:4
#: wms/templates/account/password_reset_from_key_done.html:7
msgid "Change Password"
msgstr "Alterar Senha"

#: wms/templates/account/password_reset.html:7
#: wms/templates/account/password_reset.html:11
#: wms/templates/account/password_reset_done.html:6
#: wms/templates/account/password_reset_done.html:9
msgid "Password Reset"
msgstr "Redefinição de senha"

#: wms/templates/account/password_reset.html:16
msgid ""
"Forgotten your password? Enter your e-mail address below, and we'll send you "
"an e-mail allowing you to reset it."
msgstr "Esqueceu sua senha? Digite seu endereço de e-mail abaixo e enviaremos um e-mail permitindo que você o redefina."

#: wms/templates/account/password_reset.html:21
msgid "Reset My Password"
msgstr "Redefinir minha senha"

#: wms/templates/account/password_reset.html:24
msgid "Please contact us if you have any trouble resetting your password."
msgstr "Entre em contato conosco se tiver algum problema para redefinir sua senha."

#: wms/templates/account/password_reset_done.html:15
msgid ""
"We have sent you an e-mail. Please contact us if you do not receive it "
"within a few minutes."
msgstr "Enviamos um e-mail para você. Entre em contato conosco se você não recebê-lo dentro de alguns minutos."

#: wms/templates/account/password_reset_from_key.html:8
msgid "Bad Token"
msgstr "Token Inválido"

#: wms/templates/account/password_reset_from_key.html:12
#, python-format
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr "O link de redefinição de senha era inválido, possivelmente porque já foi usado. "
"<a href=\"%(passwd_reset_url)s\">Solicite uma nova redefinição de senha</a>."

#: wms/templates/account/password_reset_from_key.html:18
msgid "change password"
msgstr "alterar senha"

#: wms/templates/account/password_reset_from_key.html:21
#: wms/templates/account/password_reset_from_key_done.html:8
msgid "Your password is now changed."
msgstr "Sua senha agora foi alterada."

#: wms/templates/account/password_set.html:6
#: wms/templates/account/password_set.html:9
#: wms/templates/account/password_set.html:14
msgid "Set Password"
msgstr "Definir Senha"

#: wms/templates/account/signup.html:6
msgid "Signup"
msgstr "Cadastro"

#: wms/templates/account/signup.html:9
#: wms/templates/account/signup.html:19
#: wms/templates/base.html:67
msgid "Sign Up"
msgstr "Cadastro"

#: wms/templates/account/signup.html:11
#, python-format
msgid ""
"Already have an account? Then please <a href=\"%(login_url)s\">sign in</a>."
msgstr "já tem uma conta? Então, por favor, faça <a href=\"%(login_url)s\">login</a>."

#: wms/templates/account/signup_closed.html:5
#: wms/templates/account/signup_closed.html:8
msgid "Sign Up Closed"
msgstr "Inscrições encerradas"

#: wms/templates/account/signup_closed.html:10
msgid "We are sorry, but the sign up is currently closed."
msgstr "Lamentamos, mas as inscrições estão encerradas no momento."

#: wms/templates/account/verification_sent.html:5
#: wms/templates/account/verification_sent.html:8
#: wms/templates/account/verified_email_required.html:5
#: wms/templates/account/verified_email_required.html:8
msgid "Verify Your E-mail Address"
msgstr "Verifique seu endereço de e-mail"

#: wms/templates/account/verification_sent.html:10
msgid ""
"We have sent an e-mail to you for verification. Follow the link provided to "
"finalize the signup process. Please contact us if you do not receive it "
"within a few minutes."
msgstr "Enviamos um e-mail para você para verificação. Siga o link fornecido para finalizar o processo de inscrição. Entre em contato conosco se você não recebê-lo dentro de alguns minutos."

#: wms/templates/account/verified_email_required.html:12
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your e-mail address. "
msgstr "Esta parte do site exige que verifiquemos se você é quem afirma ser.\n"
"Para esse fim, exigimos que você verifique a propriedade\n"
"do seu endereço de e-mail."

#: wms/templates/account/verified_email_required.html:16
msgid ""
"We have sent an e-mail to you for\n"
"verification. Please click on the link inside this e-mail. Please\n"
"contact us if you do not receive it within a few minutes."
msgstr "Enviamos um e-mail para você para verificação.\n"
"Por favor, clique no link dentro deste e-mail.\n"
"Entre em contato conosco se você não recebê-lo dentro de alguns minutos."

#: wms/templates/account/verified_email_required.html:20
#, python-format
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your e-"
"mail address</a>."
msgstr "<strong>Nota</strong>: você ainda pode <a href=\"%(email_url)s\">alterar seu endereço de e-mail</a>."

#: wms/templates/base.html:57
msgid "My Profile"
msgstr "Meu perfil"

#: wms/users/admin.py:17
msgid "Personal info"
msgstr "Informação pessoal"

#: wms/users/admin.py:19
msgid "Permissions"
msgstr "Permissões"

#: wms/users/admin.py:30
msgid "Important dates"
msgstr "Datas importantes"

#: wms/users/apps.py:7
msgid "Users"
msgstr "Usuários"

#: wms/users/forms.py:24
#: wms/users/tests/test_forms.py:36
msgid "This username has already been taken."
msgstr "Este nome de usuário já foi usado."

#: wms/users/models.py:15
msgid "Name of User"
msgstr "Nome do Usuário"

#: wms/users/views.py:23
msgid "Information successfully updated"
msgstr "Informação atualizada com sucesso"
